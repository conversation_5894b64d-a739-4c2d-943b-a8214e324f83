"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/store/get/route";
exports.ids = ["app/api/store/get/route"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("net");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fstore%2Fget%2Froute&page=%2Fapi%2Fstore%2Fget%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstore%2Fget%2Froute.ts&appDir=G%3A%5CStudy%5CPython%5Copen-canvas%5Capps%5Cweb%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=G%3A%5CStudy%5CPython%5Copen-canvas%5Capps%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fstore%2Fget%2Froute&page=%2Fapi%2Fstore%2Fget%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstore%2Fget%2Froute.ts&appDir=G%3A%5CStudy%5CPython%5Copen-canvas%5Capps%5Cweb%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=G%3A%5CStudy%5CPython%5Copen-canvas%5Capps%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/../../node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/../../node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/../../node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var G_Study_Python_open_canvas_apps_web_src_app_api_store_get_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/store/get/route.ts */ \"(rsc)/./src/app/api/store/get/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/store/get/route\",\n        pathname: \"/api/store/get\",\n        filename: \"route\",\n        bundlePath: \"app/api/store/get/route\"\n    },\n    resolvedPagePath: \"G:\\\\Study\\\\Python\\\\open-canvas\\\\apps\\\\web\\\\src\\\\app\\\\api\\\\store\\\\get\\\\route.ts\",\n    nextConfigOutput,\n    userland: G_Study_Python_open_canvas_apps_web_src_app_api_store_get_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/store/get/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1hcHAtbG9hZGVyLmpzP25hbWU9YXBwJTJGYXBpJTJGc3RvcmUlMkZnZXQlMkZyb3V0ZSZwYWdlPSUyRmFwaSUyRnN0b3JlJTJGZ2V0JTJGcm91dGUmYXBwUGF0aHM9JnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGYXBpJTJGc3RvcmUlMkZnZXQlMkZyb3V0ZS50cyZhcHBEaXI9RyUzQSU1Q1N0dWR5JTVDUHl0aG9uJTVDb3Blbi1jYW52YXMlNUNhcHBzJTVDd2ViJTVDc3JjJTVDYXBwJnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMmcm9vdERpcj1HJTNBJTVDU3R1ZHklNUNQeXRob24lNUNvcGVuLWNhbnZhcyU1Q2FwcHMlNUN3ZWImaXNEZXY9dHJ1ZSZ0c2NvbmZpZ1BhdGg9dHNjb25maWcuanNvbiZiYXNlUGF0aD0mYXNzZXRQcmVmaXg9Jm5leHRDb25maWdPdXRwdXQ9JnByZWZlcnJlZFJlZ2lvbj0mbWlkZGxld2FyZUNvbmZpZz1lMzAlM0QhIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7OztBQUFzRztBQUN2QztBQUNjO0FBQzhCO0FBQzNHO0FBQ0E7QUFDQTtBQUNBLHdCQUF3QixnSEFBbUI7QUFDM0M7QUFDQSxjQUFjLHlFQUFTO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSxZQUFZO0FBQ1osQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBLFFBQVEsaUVBQWlFO0FBQ3pFO0FBQ0E7QUFDQSxXQUFXLDRFQUFXO0FBQ3RCO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDdUg7O0FBRXZIIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQG9wZW5jYW52YXMvd2ViLz84ODM1Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEFwcFJvdXRlUm91dGVNb2R1bGUgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9mdXR1cmUvcm91dGUtbW9kdWxlcy9hcHAtcm91dGUvbW9kdWxlLmNvbXBpbGVkXCI7XG5pbXBvcnQgeyBSb3V0ZUtpbmQgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9mdXR1cmUvcm91dGUta2luZFwiO1xuaW1wb3J0IHsgcGF0Y2hGZXRjaCBhcyBfcGF0Y2hGZXRjaCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2xpYi9wYXRjaC1mZXRjaFwiO1xuaW1wb3J0ICogYXMgdXNlcmxhbmQgZnJvbSBcIkc6XFxcXFN0dWR5XFxcXFB5dGhvblxcXFxvcGVuLWNhbnZhc1xcXFxhcHBzXFxcXHdlYlxcXFxzcmNcXFxcYXBwXFxcXGFwaVxcXFxzdG9yZVxcXFxnZXRcXFxccm91dGUudHNcIjtcbi8vIFdlIGluamVjdCB0aGUgbmV4dENvbmZpZ091dHB1dCBoZXJlIHNvIHRoYXQgd2UgY2FuIHVzZSB0aGVtIGluIHRoZSByb3V0ZVxuLy8gbW9kdWxlLlxuY29uc3QgbmV4dENvbmZpZ091dHB1dCA9IFwiXCJcbmNvbnN0IHJvdXRlTW9kdWxlID0gbmV3IEFwcFJvdXRlUm91dGVNb2R1bGUoe1xuICAgIGRlZmluaXRpb246IHtcbiAgICAgICAga2luZDogUm91dGVLaW5kLkFQUF9ST1VURSxcbiAgICAgICAgcGFnZTogXCIvYXBpL3N0b3JlL2dldC9yb3V0ZVwiLFxuICAgICAgICBwYXRobmFtZTogXCIvYXBpL3N0b3JlL2dldFwiLFxuICAgICAgICBmaWxlbmFtZTogXCJyb3V0ZVwiLFxuICAgICAgICBidW5kbGVQYXRoOiBcImFwcC9hcGkvc3RvcmUvZ2V0L3JvdXRlXCJcbiAgICB9LFxuICAgIHJlc29sdmVkUGFnZVBhdGg6IFwiRzpcXFxcU3R1ZHlcXFxcUHl0aG9uXFxcXG9wZW4tY2FudmFzXFxcXGFwcHNcXFxcd2ViXFxcXHNyY1xcXFxhcHBcXFxcYXBpXFxcXHN0b3JlXFxcXGdldFxcXFxyb3V0ZS50c1wiLFxuICAgIG5leHRDb25maWdPdXRwdXQsXG4gICAgdXNlcmxhbmRcbn0pO1xuLy8gUHVsbCBvdXQgdGhlIGV4cG9ydHMgdGhhdCB3ZSBuZWVkIHRvIGV4cG9zZSBmcm9tIHRoZSBtb2R1bGUuIFRoaXMgc2hvdWxkXG4vLyBiZSBlbGltaW5hdGVkIHdoZW4gd2UndmUgbW92ZWQgdGhlIG90aGVyIHJvdXRlcyB0byB0aGUgbmV3IGZvcm1hdC4gVGhlc2Vcbi8vIGFyZSB1c2VkIHRvIGhvb2sgaW50byB0aGUgcm91dGUuXG5jb25zdCB7IHJlcXVlc3RBc3luY1N0b3JhZ2UsIHN0YXRpY0dlbmVyYXRpb25Bc3luY1N0b3JhZ2UsIHNlcnZlckhvb2tzIH0gPSByb3V0ZU1vZHVsZTtcbmNvbnN0IG9yaWdpbmFsUGF0aG5hbWUgPSBcIi9hcGkvc3RvcmUvZ2V0L3JvdXRlXCI7XG5mdW5jdGlvbiBwYXRjaEZldGNoKCkge1xuICAgIHJldHVybiBfcGF0Y2hGZXRjaCh7XG4gICAgICAgIHNlcnZlckhvb2tzLFxuICAgICAgICBzdGF0aWNHZW5lcmF0aW9uQXN5bmNTdG9yYWdlXG4gICAgfSk7XG59XG5leHBvcnQgeyByb3V0ZU1vZHVsZSwgcmVxdWVzdEFzeW5jU3RvcmFnZSwgc3RhdGljR2VuZXJhdGlvbkFzeW5jU3RvcmFnZSwgc2VydmVySG9va3MsIG9yaWdpbmFsUGF0aG5hbWUsIHBhdGNoRmV0Y2gsICB9O1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1hcHAtcm91dGUuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fstore%2Fget%2Froute&page=%2Fapi%2Fstore%2Fget%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstore%2Fget%2Froute.ts&appDir=G%3A%5CStudy%5CPython%5Copen-canvas%5Capps%5Cweb%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=G%3A%5CStudy%5CPython%5Copen-canvas%5Capps%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/store/get/route.ts":
/*!****************************************!*\
  !*** ./src/app/api/store/get/route.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/../../node_modules/next/dist/api/server.js\");\n/* harmony import */ var _langchain_langgraph_sdk__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @langchain/langgraph-sdk */ \"(rsc)/../../node_modules/@langchain/langgraph-sdk/index.js\");\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/constants */ \"(rsc)/./src/constants.ts\");\n/* harmony import */ var _lib_supabase_verify_user_server__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../../lib/supabase/verify_user_server */ \"(rsc)/./src/lib/supabase/verify_user_server.ts\");\n\n\n\n\nasync function POST(req) {\n    try {\n        const authRes = await (0,_lib_supabase_verify_user_server__WEBPACK_IMPORTED_MODULE_3__.verifyUserAuthenticated)();\n        if (!authRes?.user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n    } catch (e) {\n        console.error(\"Failed to fetch user\", e);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Unauthorized\"\n        }, {\n            status: 401\n        });\n    }\n    const { namespace, key } = await req.json();\n    const lgClient = new _langchain_langgraph_sdk__WEBPACK_IMPORTED_MODULE_1__.Client({\n        apiKey: process.env.LANGCHAIN_API_KEY,\n        apiUrl: _constants__WEBPACK_IMPORTED_MODULE_2__.LANGGRAPH_API_URL\n    });\n    try {\n        const item = await lgClient.store.getItem(namespace, key);\n        return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(JSON.stringify({\n            item\n        }), {\n            status: 200,\n            headers: {\n                \"Content-Type\": \"application/json\"\n            }\n        });\n    } catch (_) {\n        return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(JSON.stringify({\n            error: \"Failed to share run after multiple attempts.\"\n        }), {\n            status: 500,\n            headers: {\n                \"Content-Type\": \"application/json\"\n            }\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/store/get/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/constants.ts":
/*!**************************!*\
  !*** ./src/constants.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ALLOWED_AUDIO_TYPES: () => (/* binding */ ALLOWED_AUDIO_TYPES),\n/* harmony export */   ALLOWED_AUDIO_TYPE_ENDINGS: () => (/* binding */ ALLOWED_AUDIO_TYPE_ENDINGS),\n/* harmony export */   ALLOWED_VIDEO_TYPES: () => (/* binding */ ALLOWED_VIDEO_TYPES),\n/* harmony export */   ALLOWED_VIDEO_TYPE_ENDINGS: () => (/* binding */ ALLOWED_VIDEO_TYPE_ENDINGS),\n/* harmony export */   ASSISTANT_ID_COOKIE: () => (/* binding */ ASSISTANT_ID_COOKIE),\n/* harmony export */   CHAT_COLLAPSED_QUERY_PARAM: () => (/* binding */ CHAT_COLLAPSED_QUERY_PARAM),\n/* harmony export */   HAS_ASSISTANT_COOKIE_BEEN_SET: () => (/* binding */ HAS_ASSISTANT_COOKIE_BEEN_SET),\n/* harmony export */   LANGGRAPH_API_URL: () => (/* binding */ LANGGRAPH_API_URL),\n/* harmony export */   OC_HAS_SEEN_CUSTOM_ASSISTANTS_ALERT: () => (/* binding */ OC_HAS_SEEN_CUSTOM_ASSISTANTS_ALERT),\n/* harmony export */   WEB_SEARCH_RESULTS_QUERY_PARAM: () => (/* binding */ WEB_SEARCH_RESULTS_QUERY_PARAM)\n/* harmony export */ });\nconst LANGGRAPH_API_URL = process.env.LANGGRAPH_API_URL ?? \"http://localhost:54367\";\n// v2 is tied to the 'open-canvas-prod' deployment.\nconst ASSISTANT_ID_COOKIE = \"oc_assistant_id_v2\";\n// export const ASSISTANT_ID_COOKIE = \"oc_assistant_id\";\nconst HAS_ASSISTANT_COOKIE_BEEN_SET = \"has_oc_assistant_id_been_set\";\nconst OC_HAS_SEEN_CUSTOM_ASSISTANTS_ALERT = \"oc_has_seen_custom_assistants_alert\";\nconst WEB_SEARCH_RESULTS_QUERY_PARAM = \"webSearchResults\";\nconst ALLOWED_AUDIO_TYPES = new Set([\n    \"audio/mp3\",\n    \"audio/mp4\",\n    \"audio/mpeg\",\n    \"audio/mpga\",\n    \"audio/m4a\",\n    \"audio/wav\",\n    \"audio/webm\"\n]);\nconst ALLOWED_AUDIO_TYPE_ENDINGS = [\n    \".mp3\",\n    \".mpga\",\n    \".m4a\",\n    \".wav\",\n    \".webm\"\n];\nconst ALLOWED_VIDEO_TYPES = new Set([\n    \"video/mp4\",\n    \"video/mpeg\",\n    \"video/webm\"\n]);\nconst ALLOWED_VIDEO_TYPE_ENDINGS = [\n    \".mp4\",\n    \".mpeg\",\n    \".webm\"\n];\nconst CHAT_COLLAPSED_QUERY_PARAM = \"chatCollapsed\";\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/constants.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/server.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/server.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createClient: () => (/* binding */ createClient)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/../../node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/../../node_modules/next/dist/api/headers.js\");\n\n\nfunction createClient() {\n    if (false) {}\n    if (false) {}\n    const cookieStore = (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://dkzqtcimgmrnlnsuomiu.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRrenF0Y2ltZ21ybmxuc3VvbWl1Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTU0ODY4NDMsImV4cCI6MjA3MTA2Mjg0M30.m2iAmcSa-l4Qo2C8YdS00J7FtjaeIwzuappMUdQ_n4I\", {\n        cookies: {\n            getAll () {\n                return cookieStore.getAll();\n            },\n            setAll (cookiesToSet) {\n                try {\n                    cookiesToSet.forEach(({ name, value, options })=>cookieStore.set(name, value, options));\n                } catch  {\n                // The `setAll` method was called from a Server Component.\n                // This can be ignored if you have middleware refreshing\n                // user sessions.\n                }\n            }\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/server.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/verify_user_server.ts":
/*!************************************************!*\
  !*** ./src/lib/supabase/verify_user_server.ts ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   verifyUserAuthenticated: () => (/* binding */ verifyUserAuthenticated)\n/* harmony export */ });\n/* harmony import */ var _server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./server */ \"(rsc)/./src/lib/supabase/server.ts\");\n\nasync function verifyUserAuthenticated() {\n    const supabase = (0,_server__WEBPACK_IMPORTED_MODULE_0__.createClient)();\n    const { data: { user } } = await supabase.auth.getUser();\n    const { data: { session } } = await supabase.auth.getSession();\n    if (!user || !session) {\n        return undefined;\n    }\n    return {\n        user,\n        session\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3N1cGFiYXNlL3ZlcmlmeV91c2VyX3NlcnZlci50cyIsIm1hcHBpbmdzIjoiOzs7OztBQUN3QztBQUVqQyxlQUFlQztJQUdwQixNQUFNQyxXQUFXRixxREFBWUE7SUFDN0IsTUFBTSxFQUNKRyxNQUFNLEVBQUVDLElBQUksRUFBRSxFQUNmLEdBQUcsTUFBTUYsU0FBU0csSUFBSSxDQUFDQyxPQUFPO0lBQy9CLE1BQU0sRUFDSkgsTUFBTSxFQUFFSSxPQUFPLEVBQUUsRUFDbEIsR0FBRyxNQUFNTCxTQUFTRyxJQUFJLENBQUNHLFVBQVU7SUFDbEMsSUFBSSxDQUFDSixRQUFRLENBQUNHLFNBQVM7UUFDckIsT0FBT0U7SUFDVDtJQUNBLE9BQU87UUFBRUw7UUFBTUc7SUFBUTtBQUN6QiIsInNvdXJjZXMiOlsid2VicGFjazovL0BvcGVuY2FudmFzL3dlYi8uL3NyYy9saWIvc3VwYWJhc2UvdmVyaWZ5X3VzZXJfc2VydmVyLnRzP2JiNDciXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgU2Vzc2lvbiwgVXNlciB9IGZyb20gXCJAc3VwYWJhc2Uvc3VwYWJhc2UtanNcIjtcbmltcG9ydCB7IGNyZWF0ZUNsaWVudCB9IGZyb20gXCIuL3NlcnZlclwiO1xuXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gdmVyaWZ5VXNlckF1dGhlbnRpY2F0ZWQoKTogUHJvbWlzZTxcbiAgeyB1c2VyOiBVc2VyOyBzZXNzaW9uOiBTZXNzaW9uIH0gfCB1bmRlZmluZWRcbj4ge1xuICBjb25zdCBzdXBhYmFzZSA9IGNyZWF0ZUNsaWVudCgpO1xuICBjb25zdCB7XG4gICAgZGF0YTogeyB1c2VyIH0sXG4gIH0gPSBhd2FpdCBzdXBhYmFzZS5hdXRoLmdldFVzZXIoKTtcbiAgY29uc3Qge1xuICAgIGRhdGE6IHsgc2Vzc2lvbiB9LFxuICB9ID0gYXdhaXQgc3VwYWJhc2UuYXV0aC5nZXRTZXNzaW9uKCk7XG4gIGlmICghdXNlciB8fCAhc2Vzc2lvbikge1xuICAgIHJldHVybiB1bmRlZmluZWQ7XG4gIH1cbiAgcmV0dXJuIHsgdXNlciwgc2Vzc2lvbiB9O1xufVxuIl0sIm5hbWVzIjpbImNyZWF0ZUNsaWVudCIsInZlcmlmeVVzZXJBdXRoZW50aWNhdGVkIiwic3VwYWJhc2UiLCJkYXRhIiwidXNlciIsImF1dGgiLCJnZXRVc2VyIiwic2Vzc2lvbiIsImdldFNlc3Npb24iLCJ1bmRlZmluZWQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/verify_user_server.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/p-queue","vendor-chunks/eventemitter3","vendor-chunks/retry","vendor-chunks/p-retry","vendor-chunks/p-timeout","vendor-chunks/p-finally","vendor-chunks/@langchain","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/whatwg-url","vendor-chunks/cookie","vendor-chunks/webidl-conversions"], () => (__webpack_exec__("(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fstore%2Fget%2Froute&page=%2Fapi%2Fstore%2Fget%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstore%2Fget%2Froute.ts&appDir=G%3A%5CStudy%5CPython%5Copen-canvas%5Capps%5Cweb%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=G%3A%5CStudy%5CPython%5Copen-canvas%5Capps%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();