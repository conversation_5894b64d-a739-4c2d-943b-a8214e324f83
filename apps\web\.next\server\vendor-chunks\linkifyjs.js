"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/linkifyjs";
exports.ids = ["vendor-chunks/linkifyjs"];
exports.modules = {

/***/ "(ssr)/../../node_modules/linkifyjs/dist/linkify.es.js":
/*!*******************************************************!*\
  !*** ../../node_modules/linkifyjs/dist/linkify.es.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MultiToken: () => (/* binding */ MultiToken),\n/* harmony export */   Options: () => (/* binding */ Options),\n/* harmony export */   State: () => (/* binding */ State),\n/* harmony export */   createTokenClass: () => (/* binding */ createTokenClass),\n/* harmony export */   find: () => (/* binding */ find),\n/* harmony export */   init: () => (/* binding */ init),\n/* harmony export */   multi: () => (/* binding */ multi),\n/* harmony export */   options: () => (/* binding */ options),\n/* harmony export */   regexp: () => (/* binding */ regexp),\n/* harmony export */   registerCustomProtocol: () => (/* binding */ registerCustomProtocol),\n/* harmony export */   registerPlugin: () => (/* binding */ registerPlugin),\n/* harmony export */   registerTokenPlugin: () => (/* binding */ registerTokenPlugin),\n/* harmony export */   reset: () => (/* binding */ reset),\n/* harmony export */   stringToArray: () => (/* binding */ stringToArray),\n/* harmony export */   test: () => (/* binding */ test),\n/* harmony export */   text: () => (/* binding */ multi),\n/* harmony export */   tokenize: () => (/* binding */ tokenize)\n/* harmony export */ });\n// THIS FILE IS AUTOMATICALLY GENERATED DO NOT EDIT DIRECTLY\n// See update-tlds.js for encoding/decoding format\n// https://data.iana.org/TLD/tlds-alpha-by-domain.txt\nconst encodedTlds = 'aaa1rp3bb0ott3vie4c1le2ogado5udhabi7c0ademy5centure6ountant0s9o1tor4d0s1ult4e0g1ro2tna4f0l1rica5g0akhan5ency5i0g1rbus3force5tel5kdn3l0ibaba4pay4lfinanz6state5y2sace3tom5m0azon4ericanexpress7family11x2fam3ica3sterdam8nalytics7droid5quan4z2o0l2partments8p0le4q0uarelle8r0ab1mco4chi3my2pa2t0e3s0da2ia2sociates9t0hleta5torney7u0ction5di0ble3o3spost5thor3o0s4w0s2x0a2z0ure5ba0by2idu3namex4d1k2r0celona5laycard4s5efoot5gains6seball5ketball8uhaus5yern5b0c1t1va3cg1n2d1e0ats2uty4er2ntley5rlin4st0buy5t2f1g1h0arti5i0ble3d1ke2ng0o3o1z2j1lack0friday9ockbuster8g1omberg7ue3m0s1w2n0pparibas9o0ats3ehringer8fa2m1nd2o0k0ing5sch2tik2on4t1utique6x2r0adesco6idgestone9oadway5ker3ther5ussels7s1t1uild0ers6siness6y1zz3v1w1y1z0h3ca0b1fe2l0l1vinklein9m0era3p2non3petown5ital0one8r0avan4ds2e0er0s4s2sa1e1h1ino4t0ering5holic7ba1n1re3c1d1enter4o1rn3f0a1d2g1h0anel2nel4rity4se2t2eap3intai5ristmas6ome4urch5i0priani6rcle4sco3tadel4i0c2y3k1l0aims4eaning6ick2nic1que6othing5ud3ub0med6m1n1o0ach3des3ffee4llege4ogne5m0mbank4unity6pany2re3uter5sec4ndos3struction8ulting7tact3ractors9oking4l1p2rsica5untry4pon0s4rses6pa2r0edit0card4union9icket5own3s1uise0s6u0isinella9v1w1x1y0mru3ou3z2dad1nce3ta1e1ing3sun4y2clk3ds2e0al0er2s3gree4livery5l1oitte5ta3mocrat6ntal2ist5si0gn4v2hl2iamonds6et2gital5rect0ory7scount3ver5h2y2j1k1m1np2o0cs1tor4g1mains5t1wnload7rive4tv2ubai3nlop4pont4rban5vag2r2z2earth3t2c0o2deka3u0cation8e1g1mail3erck5nergy4gineer0ing9terprises10pson4quipment8r0icsson6ni3s0q1tate5t1u0rovision8s2vents5xchange6pert3osed4ress5traspace10fage2il1rwinds6th3mily4n0s2rm0ers5shion4t3edex3edback6rrari3ero6i0delity5o2lm2nal1nce1ial7re0stone6mdale6sh0ing5t0ness6j1k1lickr3ghts4r2orist4wers5y2m1o0o0d1tball6rd1ex2sale4um3undation8x2r0ee1senius7l1ogans4ntier7tr2ujitsu5n0d2rniture7tbol5yi3ga0l0lery3o1up4me0s3p1rden4y2b0iz3d0n2e0a1nt0ing5orge5f1g0ee3h1i0ft0s3ves2ing5l0ass3e1obal2o4m0ail3bh2o1x2n1odaddy5ld0point6f2o0dyear5g0le4p1t1v2p1q1r0ainger5phics5tis4een3ipe3ocery4up4s1t1u0cci3ge2ide2tars5ru3w1y2hair2mburg5ngout5us3bo2dfc0bank7ealth0care8lp1sinki6re1mes5iphop4samitsu7tachi5v2k0t2m1n1ockey4ldings5iday5medepot5goods5s0ense7nda3rse3spital5t0ing5t0els3mail5use3w2r1sbc3t1u0ghes5yatt3undai7ibm2cbc2e1u2d1e0ee3fm2kano4l1m0amat4db2mo0bilien9n0c1dustries8finiti5o2g1k1stitute6urance4e4t0ernational10uit4vestments10o1piranga7q1r0ish4s0maili5t0anbul7t0au2v3jaguar4va3cb2e0ep2tzt3welry6io2ll2m0p2nj2o0bs1urg4t1y2p0morgan6rs3uegos4niper7kaufen5ddi3e0rryhotels6logistics9properties14fh2g1h1i0a1ds2m1ndle4tchen5wi3m1n1oeln3matsu5sher5p0mg2n2r0d1ed3uokgroup8w1y0oto4z2la0caixa5mborghini8er3ncaster6d0rover6xess5salle5t0ino3robe5w0yer5b1c1ds2ease3clerc5frak4gal2o2xus4gbt3i0dl2fe0insurance9style7ghting6ke2lly3mited4o2ncoln4k2psy3ve1ing5k1lc1p2oan0s3cker3us3l1ndon4tte1o3ve3pl0financial11r1s1t0d0a3u0ndbeck6xe1ury5v1y2ma0drid4if1son4keup4n0agement7go3p1rket0ing3s4riott5shalls7ttel5ba2c0kinsey7d1e0d0ia3et2lbourne7me1orial6n0u2rckmsd7g1h1iami3crosoft7l1ni1t2t0subishi9k1l0b1s2m0a2n1o0bi0le4da2e1i1m1nash3ey2ster5rmon3tgage6scow4to0rcycles9v0ie4p1q1r1s0d2t0n1r2u0seum3ic4v1w1x1y1z2na0b1goya4me2vy3ba2c1e0c1t0bank4flix4work5ustar5w0s2xt0direct7us4f0l2g0o2hk2i0co2ke1on3nja3ssan1y5l1o0kia3rton4w0ruz3tv4p1r0a1w2tt2u1yc2z2obi1server7ffice5kinawa6layan0group9lo3m0ega4ne1g1l0ine5oo2pen3racle3nge4g0anic5igins6saka4tsuka4t2vh3pa0ge2nasonic7ris2s1tners4s1y3y2ccw3e0t2f0izer5g1h0armacy6d1ilips5one2to0graphy6s4ysio5ics1tet2ures6d1n0g1k2oneer5zza4k1l0ace2y0station9umbing5s3m1n0c2ohl2ker3litie5rn2st3r0america6xi3ess3ime3o0d0uctions8f1gressive8mo2perties3y5tection8u0dential9s1t1ub2w0c2y2qa1pon3uebec3st5racing4dio4e0ad1lestate6tor2y4cipes5d0stone5umbrella9hab3ise0n3t2liance6n0t0als5pair3ort3ublican8st0aurant8view0s5xroth6ich0ardli6oh3l1o1p2o0cks3deo3gers4om3s0vp3u0gby3hr2n2w0e2yukyu6sa0arland6fe0ty4kura4le1on3msclub4ung5ndvik0coromant12ofi4p1rl2s1ve2xo3b0i1s2c0b1haeffler7midt4olarships8ol3ule3warz5ience5ot3d1e0arch3t2cure1ity6ek2lect4ner3rvices6ven3w1x0y3fr2g1h0angrila6rp3ell3ia1ksha5oes2p0ping5uji3w3i0lk2na1gles5te3j1k0i0n2y0pe4l0ing4m0art3ile4n0cf3o0ccer3ial4ftbank4ware6hu2lar2utions7ng1y2y2pa0ce3ort2t3r0l2s1t0ada2ples4r1tebank4farm7c0group6ockholm6rage3e3ream4udio2y3yle4u0cks3pplies3y2ort5rf1gery5zuki5v1watch4iss4x1y0dney4stems6z2tab1ipei4lk2obao4rget4tamotors6r2too4x0i3c0i2d0k2eam2ch0nology8l1masek5nnis4va3f1g1h0d1eater2re6iaa2ckets5enda4ps2res2ol4j0maxx4x2k0maxx5l1m0all4n1o0day3kyo3ols3p1ray3shiba5tal3urs3wn2yota3s3r0ade1ing4ining5vel0ers0insurance16ust3v2t1ube2i1nes3shu4v0s2w1z2ua1bank3s2g1k1nicom3versity8o2ol2ps2s1y1z2va0cations7na1guard7c1e0gas3ntures6risign5mögensberater2ung14sicherung10t2g1i0ajes4deo3g1king4llas4n1p1rgin4sa1ion4va1o3laanderen9n1odka3lvo3te1ing3o2yage5u2wales2mart4ter4ng0gou5tch0es6eather0channel12bcam3er2site5d0ding5ibo2r3f1hoswho6ien2ki2lliamhill9n0dows4e1ners6me2olterskluwer11odside6rk0s2ld3w2s1tc1f3xbox3erox4ihuan4n2xx2yz3yachts4hoo3maxun5ndex5e1odobashi7ga2kohama6u0tube6t1un3za0ppos4ra3ero3ip2m1one3uerich6w2';\n// Internationalized domain names containing non-ASCII\nconst encodedUtlds = 'ελ1υ2бг1ел3дети4ею2католик6ом3мкд2он1сква6онлайн5рг3рус2ф2сайт3рб3укр3қаз3հայ3ישראל5קום3ابوظبي5رامكو5لاردن4بحرين5جزائر5سعودية6عليان5مغرب5مارات5یران5بارت2زار4يتك3ھارت5تونس4سودان3رية5شبكة4عراق2ب2مان4فلسطين6قطر3كاثوليك6وم3مصر2ليسيا5وريتانيا7قع4همراه5پاکستان7ڀارت4कॉम3नेट3भारत0म्3ोत5संगठन5বাংলা5ভারত2ৰত4ਭਾਰਤ4ભારત4ଭାରତ4இந்தியா6லங்கை6சிங்கப்பூர்11భారత్5ಭಾರತ4ഭാരതം5ලංකා4คอม3ไทย3ລາວ3გე2みんな3アマゾン4クラウド4グーグル4コム2ストア3セール3ファッション6ポイント4世界2中信1国1國1文网3亚马逊3企业2佛山2信息2健康2八卦2公司1益2台湾1灣2商城1店1标2嘉里0大酒店5在线2大拿2天主教3娱乐2家電2广东2微博2慈善2我爱你3手机2招聘2政务1府2新加坡2闻2时尚2書籍2机构2淡马锡3游戏2澳門2点看2移动2组织机构4网址1店1站1络2联通2谷歌2购物2通販2集团2電訊盈科4飞利浦3食品2餐厅2香格里拉3港2닷넷1컴2삼성2한국2';\n\n/**\n * @template A\n * @template B\n * @param {A} target\n * @param {B} properties\n * @return {A & B}\n */\nconst assign = (target, properties) => {\n  for (const key in properties) {\n    target[key] = properties[key];\n  }\n  return target;\n};\n\n/**\n * Finite State Machine generation utilities\n */\n\n/**\n * @template T\n * @typedef {{ [group: string]: T[] }} Collections\n */\n\n/**\n * @typedef {{ [group: string]: true }} Flags\n */\n\n// Keys in scanner Collections instances\nconst numeric = 'numeric';\nconst ascii = 'ascii';\nconst alpha = 'alpha';\nconst asciinumeric = 'asciinumeric';\nconst alphanumeric = 'alphanumeric';\nconst domain = 'domain';\nconst emoji = 'emoji';\nconst scheme = 'scheme';\nconst slashscheme = 'slashscheme';\nconst whitespace = 'whitespace';\n\n/**\n * @template T\n * @param {string} name\n * @param {Collections<T>} groups to register in\n * @returns {T[]} Current list of tokens in the given collection\n */\nfunction registerGroup(name, groups) {\n  if (!(name in groups)) {\n    groups[name] = [];\n  }\n  return groups[name];\n}\n\n/**\n * @template T\n * @param {T} t token to add\n * @param {Collections<T>} groups\n * @param {Flags} flags\n */\nfunction addToGroups(t, flags, groups) {\n  if (flags[numeric]) {\n    flags[asciinumeric] = true;\n    flags[alphanumeric] = true;\n  }\n  if (flags[ascii]) {\n    flags[asciinumeric] = true;\n    flags[alpha] = true;\n  }\n  if (flags[asciinumeric]) {\n    flags[alphanumeric] = true;\n  }\n  if (flags[alpha]) {\n    flags[alphanumeric] = true;\n  }\n  if (flags[alphanumeric]) {\n    flags[domain] = true;\n  }\n  if (flags[emoji]) {\n    flags[domain] = true;\n  }\n  for (const k in flags) {\n    const group = registerGroup(k, groups);\n    if (group.indexOf(t) < 0) {\n      group.push(t);\n    }\n  }\n}\n\n/**\n * @template T\n * @param {T} t token to check\n * @param {Collections<T>} groups\n * @returns {Flags} group flags that contain this token\n */\nfunction flagsForToken(t, groups) {\n  const result = {};\n  for (const c in groups) {\n    if (groups[c].indexOf(t) >= 0) {\n      result[c] = true;\n    }\n  }\n  return result;\n}\n\n/**\n * @template T\n * @typedef {null | T } Transition\n */\n\n/**\n * Define a basic state machine state. j is the list of character transitions,\n * jr is the list of regex-match transitions, jd is the default state to\n * transition to t is the accepting token type, if any. If this is the terminal\n * state, then it does not emit a token.\n *\n * The template type T represents the type of the token this state accepts. This\n * should be a string (such as of the token exports in `text.js`) or a\n * MultiToken subclass (from `multi.js`)\n *\n * @template T\n * @param {T} [token] Token that this state emits\n */\nfunction State(token = null) {\n  // this.n = null; // DEBUG: State name\n  /** @type {{ [input: string]: State<T> }} j */\n  this.j = {}; // IMPLEMENTATION 1\n  // this.j = []; // IMPLEMENTATION 2\n  /** @type {[RegExp, State<T>][]} jr */\n  this.jr = [];\n  /** @type {?State<T>} jd */\n  this.jd = null;\n  /** @type {?T} t */\n  this.t = token;\n}\n\n/**\n * Scanner token groups\n * @type Collections<string>\n */\nState.groups = {};\nState.prototype = {\n  accepts() {\n    return !!this.t;\n  },\n  /**\n   * Follow an existing transition from the given input to the next state.\n   * Does not mutate.\n   * @param {string} input character or token type to transition on\n   * @returns {?State<T>} the next state, if any\n   */\n  go(input) {\n    const state = this;\n    const nextState = state.j[input];\n    if (nextState) {\n      return nextState;\n    }\n    for (let i = 0; i < state.jr.length; i++) {\n      const regex = state.jr[i][0];\n      const nextState = state.jr[i][1]; // note: might be empty to prevent default jump\n      if (nextState && regex.test(input)) {\n        return nextState;\n      }\n    }\n    // Nowhere left to jump! Return default, if any\n    return state.jd;\n  },\n  /**\n   * Whether the state has a transition for the given input. Set the second\n   * argument to true to only look for an exact match (and not a default or\n   * regular-expression-based transition)\n   * @param {string} input\n   * @param {boolean} exactOnly\n   */\n  has(input, exactOnly = false) {\n    return exactOnly ? input in this.j : !!this.go(input);\n  },\n  /**\n   * Short for \"transition all\"; create a transition from the array of items\n   * in the given list to the same final resulting state.\n   * @param {string | string[]} inputs Group of inputs to transition on\n   * @param {Transition<T> | State<T>} [next] Transition options\n   * @param {Flags} [flags] Collections flags to add token to\n   * @param {Collections<T>} [groups] Master list of token groups\n   */\n  ta(inputs, next, flags, groups) {\n    for (let i = 0; i < inputs.length; i++) {\n      this.tt(inputs[i], next, flags, groups);\n    }\n  },\n  /**\n   * Short for \"take regexp transition\"; defines a transition for this state\n   * when it encounters a token which matches the given regular expression\n   * @param {RegExp} regexp Regular expression transition (populate first)\n   * @param {T | State<T>} [next] Transition options\n   * @param {Flags} [flags] Collections flags to add token to\n   * @param {Collections<T>} [groups] Master list of token groups\n   * @returns {State<T>} taken after the given input\n   */\n  tr(regexp, next, flags, groups) {\n    groups = groups || State.groups;\n    let nextState;\n    if (next && next.j) {\n      nextState = next;\n    } else {\n      // Token with maybe token groups\n      nextState = new State(next);\n      if (flags && groups) {\n        addToGroups(next, flags, groups);\n      }\n    }\n    this.jr.push([regexp, nextState]);\n    return nextState;\n  },\n  /**\n   * Short for \"take transitions\", will take as many sequential transitions as\n   * the length of the given input and returns the\n   * resulting final state.\n   * @param {string | string[]} input\n   * @param {T | State<T>} [next] Transition options\n   * @param {Flags} [flags] Collections flags to add token to\n   * @param {Collections<T>} [groups] Master list of token groups\n   * @returns {State<T>} taken after the given input\n   */\n  ts(input, next, flags, groups) {\n    let state = this;\n    const len = input.length;\n    if (!len) {\n      return state;\n    }\n    for (let i = 0; i < len - 1; i++) {\n      state = state.tt(input[i]);\n    }\n    return state.tt(input[len - 1], next, flags, groups);\n  },\n  /**\n   * Short for \"take transition\", this is a method for building/working with\n   * state machines.\n   *\n   * If a state already exists for the given input, returns it.\n   *\n   * If a token is specified, that state will emit that token when reached by\n   * the linkify engine.\n   *\n   * If no state exists, it will be initialized with some default transitions\n   * that resemble existing default transitions.\n   *\n   * If a state is given for the second argument, that state will be\n   * transitioned to on the given input regardless of what that input\n   * previously did.\n   *\n   * Specify a token group flags to define groups that this token belongs to.\n   * The token will be added to corresponding entires in the given groups\n   * object.\n   *\n   * @param {string} input character, token type to transition on\n   * @param {T | State<T>} [next] Transition options\n   * @param {Flags} [flags] Collections flags to add token to\n   * @param {Collections<T>} [groups] Master list of groups\n   * @returns {State<T>} taken after the given input\n   */\n  tt(input, next, flags, groups) {\n    groups = groups || State.groups;\n    const state = this;\n\n    // Check if existing state given, just a basic transition\n    if (next && next.j) {\n      state.j[input] = next;\n      return next;\n    }\n    const t = next;\n\n    // Take the transition with the usual default mechanisms and use that as\n    // a template for creating the next state\n    let nextState,\n      templateState = state.go(input);\n    if (templateState) {\n      nextState = new State();\n      assign(nextState.j, templateState.j);\n      nextState.jr.push.apply(nextState.jr, templateState.jr);\n      nextState.jd = templateState.jd;\n      nextState.t = templateState.t;\n    } else {\n      nextState = new State();\n    }\n    if (t) {\n      // Ensure newly token is in the same groups as the old token\n      if (groups) {\n        if (nextState.t && typeof nextState.t === 'string') {\n          const allFlags = assign(flagsForToken(nextState.t, groups), flags);\n          addToGroups(t, allFlags, groups);\n        } else if (flags) {\n          addToGroups(t, flags, groups);\n        }\n      }\n      nextState.t = t; // overwrite anything that was previously there\n    }\n    state.j[input] = nextState;\n    return nextState;\n  }\n};\n\n// Helper functions to improve minification (not exported outside linkifyjs module)\n\n/**\n * @template T\n * @param {State<T>} state\n * @param {string | string[]} input\n * @param {Flags} [flags]\n * @param {Collections<T>} [groups]\n */\nconst ta = (state, input, next, flags, groups) => state.ta(input, next, flags, groups);\n\n/**\n * @template T\n * @param {State<T>} state\n * @param {RegExp} regexp\n * @param {T | State<T>} [next]\n * @param {Flags} [flags]\n * @param {Collections<T>} [groups]\n */\nconst tr = (state, regexp, next, flags, groups) => state.tr(regexp, next, flags, groups);\n\n/**\n * @template T\n * @param {State<T>} state\n * @param {string | string[]} input\n * @param {T | State<T>} [next]\n * @param {Flags} [flags]\n * @param {Collections<T>} [groups]\n */\nconst ts = (state, input, next, flags, groups) => state.ts(input, next, flags, groups);\n\n/**\n * @template T\n * @param {State<T>} state\n * @param {string} input\n * @param {T | State<T>} [next]\n * @param {Collections<T>} [groups]\n * @param {Flags} [flags]\n */\nconst tt = (state, input, next, flags, groups) => state.tt(input, next, flags, groups);\n\n/******************************************************************************\nText Tokens\nIdentifiers for token outputs from the regexp scanner\n******************************************************************************/\n\n// A valid web domain token\nconst WORD = 'WORD'; // only contains a-z\nconst UWORD = 'UWORD'; // contains letters other than a-z, used for IDN\nconst ASCIINUMERICAL = 'ASCIINUMERICAL'; // contains a-z, 0-9\nconst ALPHANUMERICAL = 'ALPHANUMERICAL'; // contains numbers and letters other than a-z, used for IDN\n\n// Special case of word\nconst LOCALHOST = 'LOCALHOST';\n\n// Valid top-level domain, special case of WORD (see tlds.js)\nconst TLD = 'TLD';\n\n// Valid IDN TLD, special case of UWORD (see tlds.js)\nconst UTLD = 'UTLD';\n\n// The scheme portion of a web URI protocol. Supported types include: `mailto`,\n// `file`, and user-defined custom protocols. Limited to schemes that contain\n// only letters\nconst SCHEME = 'SCHEME';\n\n// Similar to SCHEME, except makes distinction for schemes that must always be\n// followed by `://`, not just `:`. Supported types include `http`, `https`,\n// `ftp`, `ftps`\nconst SLASH_SCHEME = 'SLASH_SCHEME';\n\n// Any sequence of digits 0-9\nconst NUM = 'NUM';\n\n// Any number of consecutive whitespace characters that are not newline\nconst WS = 'WS';\n\n// New line (unix style)\nconst NL = 'NL'; // \\n\n\n// Opening/closing bracket classes\n// TODO: Rename OPEN -> LEFT and CLOSE -> RIGHT in v5 to fit with Unicode names\n// Also rename angle brackes to LESSTHAN and GREATER THAN\nconst OPENBRACE = 'OPENBRACE'; // {\nconst CLOSEBRACE = 'CLOSEBRACE'; // }\nconst OPENBRACKET = 'OPENBRACKET'; // [\nconst CLOSEBRACKET = 'CLOSEBRACKET'; // ]\nconst OPENPAREN = 'OPENPAREN'; // (\nconst CLOSEPAREN = 'CLOSEPAREN'; // )\nconst OPENANGLEBRACKET = 'OPENANGLEBRACKET'; // <\nconst CLOSEANGLEBRACKET = 'CLOSEANGLEBRACKET'; // >\nconst FULLWIDTHLEFTPAREN = 'FULLWIDTHLEFTPAREN'; // （\nconst FULLWIDTHRIGHTPAREN = 'FULLWIDTHRIGHTPAREN'; // ）\nconst LEFTCORNERBRACKET = 'LEFTCORNERBRACKET'; // 「\nconst RIGHTCORNERBRACKET = 'RIGHTCORNERBRACKET'; // 」\nconst LEFTWHITECORNERBRACKET = 'LEFTWHITECORNERBRACKET'; // 『\nconst RIGHTWHITECORNERBRACKET = 'RIGHTWHITECORNERBRACKET'; // 』\nconst FULLWIDTHLESSTHAN = 'FULLWIDTHLESSTHAN'; // ＜\nconst FULLWIDTHGREATERTHAN = 'FULLWIDTHGREATERTHAN'; // ＞\n\n// Various symbols\nconst AMPERSAND = 'AMPERSAND'; // &\nconst APOSTROPHE = 'APOSTROPHE'; // '\nconst ASTERISK = 'ASTERISK'; // *\nconst AT = 'AT'; // @\nconst BACKSLASH = 'BACKSLASH'; // \\\nconst BACKTICK = 'BACKTICK'; // `\nconst CARET = 'CARET'; // ^\nconst COLON = 'COLON'; // :\nconst COMMA = 'COMMA'; // ,\nconst DOLLAR = 'DOLLAR'; // $\nconst DOT = 'DOT'; // .\nconst EQUALS = 'EQUALS'; // =\nconst EXCLAMATION = 'EXCLAMATION'; // !\nconst HYPHEN = 'HYPHEN'; // -\nconst PERCENT = 'PERCENT'; // %\nconst PIPE = 'PIPE'; // |\nconst PLUS = 'PLUS'; // +\nconst POUND = 'POUND'; // #\nconst QUERY = 'QUERY'; // ?\nconst QUOTE = 'QUOTE'; // \"\nconst FULLWIDTHMIDDLEDOT = 'FULLWIDTHMIDDLEDOT'; // ・\n\nconst SEMI = 'SEMI'; // ;\nconst SLASH = 'SLASH'; // /\nconst TILDE = 'TILDE'; // ~\nconst UNDERSCORE = 'UNDERSCORE'; // _\n\n// Emoji symbol\nconst EMOJI$1 = 'EMOJI';\n\n// Default token - anything that is not one of the above\nconst SYM = 'SYM';\n\nvar tk = /*#__PURE__*/Object.freeze({\n\t__proto__: null,\n\tWORD: WORD,\n\tUWORD: UWORD,\n\tASCIINUMERICAL: ASCIINUMERICAL,\n\tALPHANUMERICAL: ALPHANUMERICAL,\n\tLOCALHOST: LOCALHOST,\n\tTLD: TLD,\n\tUTLD: UTLD,\n\tSCHEME: SCHEME,\n\tSLASH_SCHEME: SLASH_SCHEME,\n\tNUM: NUM,\n\tWS: WS,\n\tNL: NL,\n\tOPENBRACE: OPENBRACE,\n\tCLOSEBRACE: CLOSEBRACE,\n\tOPENBRACKET: OPENBRACKET,\n\tCLOSEBRACKET: CLOSEBRACKET,\n\tOPENPAREN: OPENPAREN,\n\tCLOSEPAREN: CLOSEPAREN,\n\tOPENANGLEBRACKET: OPENANGLEBRACKET,\n\tCLOSEANGLEBRACKET: CLOSEANGLEBRACKET,\n\tFULLWIDTHLEFTPAREN: FULLWIDTHLEFTPAREN,\n\tFULLWIDTHRIGHTPAREN: FULLWIDTHRIGHTPAREN,\n\tLEFTCORNERBRACKET: LEFTCORNERBRACKET,\n\tRIGHTCORNERBRACKET: RIGHTCORNERBRACKET,\n\tLEFTWHITECORNERBRACKET: LEFTWHITECORNERBRACKET,\n\tRIGHTWHITECORNERBRACKET: RIGHTWHITECORNERBRACKET,\n\tFULLWIDTHLESSTHAN: FULLWIDTHLESSTHAN,\n\tFULLWIDTHGREATERTHAN: FULLWIDTHGREATERTHAN,\n\tAMPERSAND: AMPERSAND,\n\tAPOSTROPHE: APOSTROPHE,\n\tASTERISK: ASTERISK,\n\tAT: AT,\n\tBACKSLASH: BACKSLASH,\n\tBACKTICK: BACKTICK,\n\tCARET: CARET,\n\tCOLON: COLON,\n\tCOMMA: COMMA,\n\tDOLLAR: DOLLAR,\n\tDOT: DOT,\n\tEQUALS: EQUALS,\n\tEXCLAMATION: EXCLAMATION,\n\tHYPHEN: HYPHEN,\n\tPERCENT: PERCENT,\n\tPIPE: PIPE,\n\tPLUS: PLUS,\n\tPOUND: POUND,\n\tQUERY: QUERY,\n\tQUOTE: QUOTE,\n\tFULLWIDTHMIDDLEDOT: FULLWIDTHMIDDLEDOT,\n\tSEMI: SEMI,\n\tSLASH: SLASH,\n\tTILDE: TILDE,\n\tUNDERSCORE: UNDERSCORE,\n\tEMOJI: EMOJI$1,\n\tSYM: SYM\n});\n\n// Note that these two Unicode ones expand into a really big one with Babel\nconst ASCII_LETTER = /[a-z]/;\nconst LETTER = /\\p{L}/u; // Any Unicode character with letter data type\nconst EMOJI = /\\p{Emoji}/u; // Any Unicode emoji character\nconst EMOJI_VARIATION$1 = /\\ufe0f/;\nconst DIGIT = /\\d/;\nconst SPACE = /\\s/;\n\nvar regexp = /*#__PURE__*/Object.freeze({\n\t__proto__: null,\n\tASCII_LETTER: ASCII_LETTER,\n\tLETTER: LETTER,\n\tEMOJI: EMOJI,\n\tEMOJI_VARIATION: EMOJI_VARIATION$1,\n\tDIGIT: DIGIT,\n\tSPACE: SPACE\n});\n\n/**\n\tThe scanner provides an interface that takes a string of text as input, and\n\toutputs an array of tokens instances that can be used for easy URL parsing.\n*/\nconst CR = '\\r'; // carriage-return character\nconst LF = '\\n'; // line-feed character\nconst EMOJI_VARIATION = '\\ufe0f'; // Variation selector, follows heart and others\nconst EMOJI_JOINER = '\\u200d'; // zero-width joiner\nconst OBJECT_REPLACEMENT = '\\ufffc'; // whitespace placeholder that sometimes appears in rich text editors\n\nlet tlds = null,\n  utlds = null; // don't change so only have to be computed once\n\n/**\n * Scanner output token:\n * - `t` is the token name (e.g., 'NUM', 'EMOJI', 'TLD')\n * - `v` is the value of the token (e.g., '123', '❤️', 'com')\n * - `s` is the start index of the token in the original string\n * - `e` is the end index of the token in the original string\n * @typedef {{t: string, v: string, s: number, e: number}} Token\n */\n\n/**\n * @template T\n * @typedef {{ [collection: string]: T[] }} Collections\n */\n\n/**\n * Initialize the scanner character-based state machine for the given start\n * state\n * @param {[string, boolean][]} customSchemes List of custom schemes, where each\n * item is a length-2 tuple with the first element set to the string scheme, and\n * the second element set to `true` if the `://` after the scheme is optional\n */\nfunction init$2(customSchemes = []) {\n  // Frequently used states (name argument removed during minification)\n  /** @type Collections<string> */\n  const groups = {}; // of tokens\n  State.groups = groups;\n  /** @type State<string> */\n  const Start = new State();\n  if (tlds == null) {\n    tlds = decodeTlds(encodedTlds);\n  }\n  if (utlds == null) {\n    utlds = decodeTlds(encodedUtlds);\n  }\n\n  // States for special URL symbols that accept immediately after start\n  tt(Start, \"'\", APOSTROPHE);\n  tt(Start, '{', OPENBRACE);\n  tt(Start, '}', CLOSEBRACE);\n  tt(Start, '[', OPENBRACKET);\n  tt(Start, ']', CLOSEBRACKET);\n  tt(Start, '(', OPENPAREN);\n  tt(Start, ')', CLOSEPAREN);\n  tt(Start, '<', OPENANGLEBRACKET);\n  tt(Start, '>', CLOSEANGLEBRACKET);\n  tt(Start, '（', FULLWIDTHLEFTPAREN);\n  tt(Start, '）', FULLWIDTHRIGHTPAREN);\n  tt(Start, '「', LEFTCORNERBRACKET);\n  tt(Start, '」', RIGHTCORNERBRACKET);\n  tt(Start, '『', LEFTWHITECORNERBRACKET);\n  tt(Start, '』', RIGHTWHITECORNERBRACKET);\n  tt(Start, '＜', FULLWIDTHLESSTHAN);\n  tt(Start, '＞', FULLWIDTHGREATERTHAN);\n  tt(Start, '&', AMPERSAND);\n  tt(Start, '*', ASTERISK);\n  tt(Start, '@', AT);\n  tt(Start, '`', BACKTICK);\n  tt(Start, '^', CARET);\n  tt(Start, ':', COLON);\n  tt(Start, ',', COMMA);\n  tt(Start, '$', DOLLAR);\n  tt(Start, '.', DOT);\n  tt(Start, '=', EQUALS);\n  tt(Start, '!', EXCLAMATION);\n  tt(Start, '-', HYPHEN);\n  tt(Start, '%', PERCENT);\n  tt(Start, '|', PIPE);\n  tt(Start, '+', PLUS);\n  tt(Start, '#', POUND);\n  tt(Start, '?', QUERY);\n  tt(Start, '\"', QUOTE);\n  tt(Start, '/', SLASH);\n  tt(Start, ';', SEMI);\n  tt(Start, '~', TILDE);\n  tt(Start, '_', UNDERSCORE);\n  tt(Start, '\\\\', BACKSLASH);\n  tt(Start, '・', FULLWIDTHMIDDLEDOT);\n  const Num = tr(Start, DIGIT, NUM, {\n    [numeric]: true\n  });\n  tr(Num, DIGIT, Num);\n  const Asciinumeric = tr(Num, ASCII_LETTER, ASCIINUMERICAL, {\n    [asciinumeric]: true\n  });\n  const Alphanumeric = tr(Num, LETTER, ALPHANUMERICAL, {\n    [alphanumeric]: true\n  });\n\n  // State which emits a word token\n  const Word = tr(Start, ASCII_LETTER, WORD, {\n    [ascii]: true\n  });\n  tr(Word, DIGIT, Asciinumeric);\n  tr(Word, ASCII_LETTER, Word);\n  tr(Asciinumeric, DIGIT, Asciinumeric);\n  tr(Asciinumeric, ASCII_LETTER, Asciinumeric);\n\n  // Same as previous, but specific to non-fsm.ascii alphabet words\n  const UWord = tr(Start, LETTER, UWORD, {\n    [alpha]: true\n  });\n  tr(UWord, ASCII_LETTER); // Non-accepting\n  tr(UWord, DIGIT, Alphanumeric);\n  tr(UWord, LETTER, UWord);\n  tr(Alphanumeric, DIGIT, Alphanumeric);\n  tr(Alphanumeric, ASCII_LETTER); // Non-accepting\n  tr(Alphanumeric, LETTER, Alphanumeric); // Non-accepting\n\n  // Whitespace jumps\n  // Tokens of only non-newline whitespace are arbitrarily long\n  // If any whitespace except newline, more whitespace!\n  const Nl = tt(Start, LF, NL, {\n    [whitespace]: true\n  });\n  const Cr = tt(Start, CR, WS, {\n    [whitespace]: true\n  });\n  const Ws = tr(Start, SPACE, WS, {\n    [whitespace]: true\n  });\n  tt(Start, OBJECT_REPLACEMENT, Ws);\n  tt(Cr, LF, Nl); // \\r\\n\n  tt(Cr, OBJECT_REPLACEMENT, Ws);\n  tr(Cr, SPACE, Ws);\n  tt(Ws, CR); // non-accepting state to avoid mixing whitespaces\n  tt(Ws, LF); // non-accepting state to avoid mixing whitespaces\n  tr(Ws, SPACE, Ws);\n  tt(Ws, OBJECT_REPLACEMENT, Ws);\n\n  // Emoji tokens. They are not grouped by the scanner except in cases where a\n  // zero-width joiner is present\n  const Emoji = tr(Start, EMOJI, EMOJI$1, {\n    [emoji]: true\n  });\n  tt(Emoji, '#'); // no transition, emoji regex seems to match #\n  tr(Emoji, EMOJI, Emoji);\n  tt(Emoji, EMOJI_VARIATION, Emoji);\n  // tt(Start, EMOJI_VARIATION, Emoji); // This one is sketchy\n\n  const EmojiJoiner = tt(Emoji, EMOJI_JOINER);\n  tt(EmojiJoiner, '#');\n  tr(EmojiJoiner, EMOJI, Emoji);\n  // tt(EmojiJoiner, EMOJI_VARIATION, Emoji); // also sketchy\n\n  // Generates states for top-level domains\n  // Note that this is most accurate when tlds are in alphabetical order\n  const wordjr = [[ASCII_LETTER, Word], [DIGIT, Asciinumeric]];\n  const uwordjr = [[ASCII_LETTER, null], [LETTER, UWord], [DIGIT, Alphanumeric]];\n  for (let i = 0; i < tlds.length; i++) {\n    fastts(Start, tlds[i], TLD, WORD, wordjr);\n  }\n  for (let i = 0; i < utlds.length; i++) {\n    fastts(Start, utlds[i], UTLD, UWORD, uwordjr);\n  }\n  addToGroups(TLD, {\n    tld: true,\n    ascii: true\n  }, groups);\n  addToGroups(UTLD, {\n    utld: true,\n    alpha: true\n  }, groups);\n\n  // Collect the states generated by different protocols. NOTE: If any new TLDs\n  // get added that are also protocols, set the token to be the same as the\n  // protocol to ensure parsing works as expected.\n  fastts(Start, 'file', SCHEME, WORD, wordjr);\n  fastts(Start, 'mailto', SCHEME, WORD, wordjr);\n  fastts(Start, 'http', SLASH_SCHEME, WORD, wordjr);\n  fastts(Start, 'https', SLASH_SCHEME, WORD, wordjr);\n  fastts(Start, 'ftp', SLASH_SCHEME, WORD, wordjr);\n  fastts(Start, 'ftps', SLASH_SCHEME, WORD, wordjr);\n  addToGroups(SCHEME, {\n    scheme: true,\n    ascii: true\n  }, groups);\n  addToGroups(SLASH_SCHEME, {\n    slashscheme: true,\n    ascii: true\n  }, groups);\n\n  // Register custom schemes. Assumes each scheme is asciinumeric with hyphens\n  customSchemes = customSchemes.sort((a, b) => a[0] > b[0] ? 1 : -1);\n  for (let i = 0; i < customSchemes.length; i++) {\n    const sch = customSchemes[i][0];\n    const optionalSlashSlash = customSchemes[i][1];\n    const flags = optionalSlashSlash ? {\n      [scheme]: true\n    } : {\n      [slashscheme]: true\n    };\n    if (sch.indexOf('-') >= 0) {\n      flags[domain] = true;\n    } else if (!ASCII_LETTER.test(sch)) {\n      flags[numeric] = true; // numbers only\n    } else if (DIGIT.test(sch)) {\n      flags[asciinumeric] = true;\n    } else {\n      flags[ascii] = true;\n    }\n    ts(Start, sch, sch, flags);\n  }\n\n  // Localhost token\n  ts(Start, 'localhost', LOCALHOST, {\n    ascii: true\n  });\n\n  // Set default transition for start state (some symbol)\n  Start.jd = new State(SYM);\n  return {\n    start: Start,\n    tokens: assign({\n      groups\n    }, tk)\n  };\n}\n\n/**\n\tGiven a string, returns an array of TOKEN instances representing the\n\tcomposition of that string.\n\n\t@method run\n\t@param {State<string>} start scanner starting state\n\t@param {string} str input string to scan\n\t@return {Token[]} list of tokens, each with a type and value\n*/\nfunction run$1(start, str) {\n  // State machine is not case sensitive, so input is tokenized in lowercased\n  // form (still returns regular case). Uses selective `toLowerCase` because\n  // lowercasing the entire string causes the length and character position to\n  // vary in some non-English strings with V8-based runtimes.\n  const iterable = stringToArray(str.replace(/[A-Z]/g, c => c.toLowerCase()));\n  const charCount = iterable.length; // <= len if there are emojis, etc\n  const tokens = []; // return value\n\n  // cursor through the string itself, accounting for characters that have\n  // width with length 2 such as emojis\n  let cursor = 0;\n\n  // Cursor through the array-representation of the string\n  let charCursor = 0;\n\n  // Tokenize the string\n  while (charCursor < charCount) {\n    let state = start;\n    let nextState = null;\n    let tokenLength = 0;\n    let latestAccepting = null;\n    let sinceAccepts = -1;\n    let charsSinceAccepts = -1;\n    while (charCursor < charCount && (nextState = state.go(iterable[charCursor]))) {\n      state = nextState;\n\n      // Keep track of the latest accepting state\n      if (state.accepts()) {\n        sinceAccepts = 0;\n        charsSinceAccepts = 0;\n        latestAccepting = state;\n      } else if (sinceAccepts >= 0) {\n        sinceAccepts += iterable[charCursor].length;\n        charsSinceAccepts++;\n      }\n      tokenLength += iterable[charCursor].length;\n      cursor += iterable[charCursor].length;\n      charCursor++;\n    }\n\n    // Roll back to the latest accepting state\n    cursor -= sinceAccepts;\n    charCursor -= charsSinceAccepts;\n    tokenLength -= sinceAccepts;\n\n    // No more jumps, just make a new token from the last accepting one\n    tokens.push({\n      t: latestAccepting.t,\n      // token type/name\n      v: str.slice(cursor - tokenLength, cursor),\n      // string value\n      s: cursor - tokenLength,\n      // start index\n      e: cursor // end index (excluding)\n    });\n  }\n  return tokens;\n}\n\n/**\n * Convert a String to an Array of characters, taking into account that some\n * characters like emojis take up two string indexes.\n *\n * Adapted from core-js (MIT license)\n * https://github.com/zloirock/core-js/blob/2d69cf5f99ab3ea3463c395df81e5a15b68f49d9/packages/core-js/internals/string-multibyte.js\n *\n * @function stringToArray\n * @param {string} str\n * @returns {string[]}\n */\nfunction stringToArray(str) {\n  const result = [];\n  const len = str.length;\n  let index = 0;\n  while (index < len) {\n    let first = str.charCodeAt(index);\n    let second;\n    let char = first < 0xd800 || first > 0xdbff || index + 1 === len || (second = str.charCodeAt(index + 1)) < 0xdc00 || second > 0xdfff ? str[index] // single character\n    : str.slice(index, index + 2); // two-index characters\n    result.push(char);\n    index += char.length;\n  }\n  return result;\n}\n\n/**\n * Fast version of ts function for when transition defaults are well known\n * @param {State<string>} state\n * @param {string} input\n * @param {string} t\n * @param {string} defaultt\n * @param {[RegExp, State<string>][]} jr\n * @returns {State<string>}\n */\nfunction fastts(state, input, t, defaultt, jr) {\n  let next;\n  const len = input.length;\n  for (let i = 0; i < len - 1; i++) {\n    const char = input[i];\n    if (state.j[char]) {\n      next = state.j[char];\n    } else {\n      next = new State(defaultt);\n      next.jr = jr.slice();\n      state.j[char] = next;\n    }\n    state = next;\n  }\n  next = new State(t);\n  next.jr = jr.slice();\n  state.j[input[len - 1]] = next;\n  return next;\n}\n\n/**\n * Converts a string of Top-Level Domain names encoded in update-tlds.js back\n * into a list of strings.\n * @param {str} encoded encoded TLDs string\n * @returns {str[]} original TLDs list\n */\nfunction decodeTlds(encoded) {\n  const words = [];\n  const stack = [];\n  let i = 0;\n  let digits = '0123456789';\n  while (i < encoded.length) {\n    let popDigitCount = 0;\n    while (digits.indexOf(encoded[i + popDigitCount]) >= 0) {\n      popDigitCount++; // encountered some digits, have to pop to go one level up trie\n    }\n    if (popDigitCount > 0) {\n      words.push(stack.join('')); // whatever preceded the pop digits must be a word\n      for (let popCount = parseInt(encoded.substring(i, i + popDigitCount), 10); popCount > 0; popCount--) {\n        stack.pop();\n      }\n      i += popDigitCount;\n    } else {\n      stack.push(encoded[i]); // drop down a level into the trie\n      i++;\n    }\n  }\n  return words;\n}\n\n/**\n * An object where each key is a valid DOM Event Name such as `click` or `focus`\n * and each value is an event handler function.\n *\n * https://developer.mozilla.org/en-US/docs/Web/API/Element#events\n * @typedef {?{ [event: string]: Function }} EventListeners\n */\n\n/**\n * All formatted properties required to render a link, including `tagName`,\n * `attributes`, `content` and `eventListeners`.\n * @typedef {{ tagName: any, attributes: {[attr: string]: any}, content: string,\n * eventListeners: EventListeners }} IntermediateRepresentation\n */\n\n/**\n * Specify either an object described by the template type `O` or a function.\n *\n * The function takes a string value (usually the link's href attribute), the\n * link type (`'url'`, `'hashtag`', etc.) and an internal token representation\n * of the link. It should return an object of the template type `O`\n * @template O\n * @typedef {O | ((value: string, type: string, token: MultiToken) => O)} OptObj\n */\n\n/**\n * Specify either a function described by template type `F` or an object.\n *\n * Each key in the object should be a link type (`'url'`, `'hashtag`', etc.). Each\n * value should be a function with template type `F` that is called when the\n * corresponding link type is encountered.\n * @template F\n * @typedef {F | { [type: string]: F}} OptFn\n */\n\n/**\n * Specify either a value with template type `V`, a function that returns `V` or\n * an object where each value resolves to `V`.\n *\n * The function takes a string value (usually the link's href attribute), the\n * link type (`'url'`, `'hashtag`', etc.) and an internal token representation\n * of the link. It should return an object of the template type `V`\n *\n * For the object, each key should be a link type (`'url'`, `'hashtag`', etc.).\n * Each value should either have type `V` or a function that returns V. This\n * function similarly takes a string value and a token.\n *\n * Example valid types for `Opt<string>`:\n *\n * ```js\n * 'hello'\n * (value, type, token) => 'world'\n * { url: 'hello', email: (value, token) => 'world'}\n * ```\n * @template V\n * @typedef {V | ((value: string, type: string, token: MultiToken) => V) | { [type: string]: V | ((value: string, token: MultiToken) => V) }} Opt\n */\n\n/**\n * See available options: https://linkify.js.org/docs/options.html\n * @typedef {{\n * \tdefaultProtocol?: string,\n *  events?: OptObj<EventListeners>,\n * \tformat?: Opt<string>,\n * \tformatHref?: Opt<string>,\n * \tnl2br?: boolean,\n * \ttagName?: Opt<any>,\n * \ttarget?: Opt<string>,\n * \trel?: Opt<string>,\n * \tvalidate?: Opt<boolean>,\n * \ttruncate?: Opt<number>,\n * \tclassName?: Opt<string>,\n * \tattributes?: OptObj<({ [attr: string]: any })>,\n *  ignoreTags?: string[],\n * \trender?: OptFn<((ir: IntermediateRepresentation) => any)>\n * }} Opts\n */\n\n/**\n * @type Required<Opts>\n */\nconst defaults = {\n  defaultProtocol: 'http',\n  events: null,\n  format: noop,\n  formatHref: noop,\n  nl2br: false,\n  tagName: 'a',\n  target: null,\n  rel: null,\n  validate: true,\n  truncate: Infinity,\n  className: null,\n  attributes: null,\n  ignoreTags: [],\n  render: null\n};\n\n/**\n * Utility class for linkify interfaces to apply specified\n * {@link Opts formatting and rendering options}.\n *\n * @param {Opts | Options} [opts] Option value overrides.\n * @param {(ir: IntermediateRepresentation) => any} [defaultRender] (For\n *   internal use) default render function that determines how to generate an\n *   HTML element based on a link token's derived tagName, attributes and HTML.\n *   Similar to render option\n */\nfunction Options(opts, defaultRender = null) {\n  let o = assign({}, defaults);\n  if (opts) {\n    o = assign(o, opts instanceof Options ? opts.o : opts);\n  }\n\n  // Ensure all ignored tags are uppercase\n  const ignoredTags = o.ignoreTags;\n  const uppercaseIgnoredTags = [];\n  for (let i = 0; i < ignoredTags.length; i++) {\n    uppercaseIgnoredTags.push(ignoredTags[i].toUpperCase());\n  }\n  /** @protected */\n  this.o = o;\n  if (defaultRender) {\n    this.defaultRender = defaultRender;\n  }\n  this.ignoreTags = uppercaseIgnoredTags;\n}\nOptions.prototype = {\n  o: defaults,\n  /**\n   * @type string[]\n   */\n  ignoreTags: [],\n  /**\n   * @param {IntermediateRepresentation} ir\n   * @returns {any}\n   */\n  defaultRender(ir) {\n    return ir;\n  },\n  /**\n   * Returns true or false based on whether a token should be displayed as a\n   * link based on the user options.\n   * @param {MultiToken} token\n   * @returns {boolean}\n   */\n  check(token) {\n    return this.get('validate', token.toString(), token);\n  },\n  // Private methods\n\n  /**\n   * Resolve an option's value based on the value of the option and the given\n   * params. If operator and token are specified and the target option is\n   * callable, automatically calls the function with the given argument.\n   * @template {keyof Opts} K\n   * @param {K} key Name of option to use\n   * @param {string} [operator] will be passed to the target option if it's a\n   * function. If not specified, RAW function value gets returned\n   * @param {MultiToken} [token] The token from linkify.tokenize\n   * @returns {Opts[K] | any}\n   */\n  get(key, operator, token) {\n    const isCallable = operator != null;\n    let option = this.o[key];\n    if (!option) {\n      return option;\n    }\n    if (typeof option === 'object') {\n      option = token.t in option ? option[token.t] : defaults[key];\n      if (typeof option === 'function' && isCallable) {\n        option = option(operator, token);\n      }\n    } else if (typeof option === 'function' && isCallable) {\n      option = option(operator, token.t, token);\n    }\n    return option;\n  },\n  /**\n   * @template {keyof Opts} L\n   * @param {L} key Name of options object to use\n   * @param {string} [operator]\n   * @param {MultiToken} [token]\n   * @returns {Opts[L] | any}\n   */\n  getObj(key, operator, token) {\n    let obj = this.o[key];\n    if (typeof obj === 'function' && operator != null) {\n      obj = obj(operator, token.t, token);\n    }\n    return obj;\n  },\n  /**\n   * Convert the given token to a rendered element that may be added to the\n   * calling-interface's DOM\n   * @param {MultiToken} token Token to render to an HTML element\n   * @returns {any} Render result; e.g., HTML string, DOM element, React\n   *   Component, etc.\n   */\n  render(token) {\n    const ir = token.render(this); // intermediate representation\n    const renderFn = this.get('render', null, token) || this.defaultRender;\n    return renderFn(ir, token.t, token);\n  }\n};\nfunction noop(val) {\n  return val;\n}\n\nvar options = /*#__PURE__*/Object.freeze({\n\t__proto__: null,\n\tdefaults: defaults,\n\tOptions: Options,\n\tassign: assign\n});\n\n/******************************************************************************\n\tMulti-Tokens\n\tTokens composed of arrays of TextTokens\n******************************************************************************/\n\n/**\n * @param {string} value\n * @param {Token[]} tokens\n */\nfunction MultiToken(value, tokens) {\n  this.t = 'token';\n  this.v = value;\n  this.tk = tokens;\n}\n\n/**\n * Abstract class used for manufacturing tokens of text tokens. That is rather\n * than the value for a token being a small string of text, it's value an array\n * of text tokens.\n *\n * Used for grouping together URLs, emails, hashtags, and other potential\n * creations.\n * @class MultiToken\n * @property {string} t\n * @property {string} v\n * @property {Token[]} tk\n * @abstract\n */\nMultiToken.prototype = {\n  isLink: false,\n  /**\n   * Return the string this token represents.\n   * @return {string}\n   */\n  toString() {\n    return this.v;\n  },\n  /**\n   * What should the value for this token be in the `href` HTML attribute?\n   * Returns the `.toString` value by default.\n   * @param {string} [scheme]\n   * @return {string}\n   */\n  toHref(scheme) {\n    return this.toString();\n  },\n  /**\n   * @param {Options} options Formatting options\n   * @returns {string}\n   */\n  toFormattedString(options) {\n    const val = this.toString();\n    const truncate = options.get('truncate', val, this);\n    const formatted = options.get('format', val, this);\n    return truncate && formatted.length > truncate ? formatted.substring(0, truncate) + '…' : formatted;\n  },\n  /**\n   *\n   * @param {Options} options\n   * @returns {string}\n   */\n  toFormattedHref(options) {\n    return options.get('formatHref', this.toHref(options.get('defaultProtocol')), this);\n  },\n  /**\n   * The start index of this token in the original input string\n   * @returns {number}\n   */\n  startIndex() {\n    return this.tk[0].s;\n  },\n  /**\n   * The end index of this token in the original input string (up to this\n   * index but not including it)\n   * @returns {number}\n   */\n  endIndex() {\n    return this.tk[this.tk.length - 1].e;\n  },\n  /**\n  \tReturns an object  of relevant values for this token, which includes keys\n  \t* type - Kind of token ('url', 'email', etc.)\n  \t* value - Original text\n  \t* href - The value that should be added to the anchor tag's href\n  \t\tattribute\n  \t\t@method toObject\n  \t@param {string} [protocol] `'http'` by default\n  */\n  toObject(protocol = defaults.defaultProtocol) {\n    return {\n      type: this.t,\n      value: this.toString(),\n      isLink: this.isLink,\n      href: this.toHref(protocol),\n      start: this.startIndex(),\n      end: this.endIndex()\n    };\n  },\n  /**\n   *\n   * @param {Options} options Formatting option\n   */\n  toFormattedObject(options) {\n    return {\n      type: this.t,\n      value: this.toFormattedString(options),\n      isLink: this.isLink,\n      href: this.toFormattedHref(options),\n      start: this.startIndex(),\n      end: this.endIndex()\n    };\n  },\n  /**\n   * Whether this token should be rendered as a link according to the given options\n   * @param {Options} options\n   * @returns {boolean}\n   */\n  validate(options) {\n    return options.get('validate', this.toString(), this);\n  },\n  /**\n   * Return an object that represents how this link should be rendered.\n   * @param {Options} options Formattinng options\n   */\n  render(options) {\n    const token = this;\n    const href = this.toHref(options.get('defaultProtocol'));\n    const formattedHref = options.get('formatHref', href, this);\n    const tagName = options.get('tagName', href, token);\n    const content = this.toFormattedString(options);\n    const attributes = {};\n    const className = options.get('className', href, token);\n    const target = options.get('target', href, token);\n    const rel = options.get('rel', href, token);\n    const attrs = options.getObj('attributes', href, token);\n    const eventListeners = options.getObj('events', href, token);\n    attributes.href = formattedHref;\n    if (className) {\n      attributes.class = className;\n    }\n    if (target) {\n      attributes.target = target;\n    }\n    if (rel) {\n      attributes.rel = rel;\n    }\n    if (attrs) {\n      assign(attributes, attrs);\n    }\n    return {\n      tagName,\n      attributes,\n      content,\n      eventListeners\n    };\n  }\n};\n\n/**\n * Create a new token that can be emitted by the parser state machine\n * @param {string} type readable type of the token\n * @param {object} props properties to assign or override, including isLink = true or false\n * @returns {new (value: string, tokens: Token[]) => MultiToken} new token class\n */\nfunction createTokenClass(type, props) {\n  class Token extends MultiToken {\n    constructor(value, tokens) {\n      super(value, tokens);\n      this.t = type;\n    }\n  }\n  for (const p in props) {\n    Token.prototype[p] = props[p];\n  }\n  Token.t = type;\n  return Token;\n}\n\n/**\n\tRepresents a list of tokens making up a valid email address\n*/\nconst Email = createTokenClass('email', {\n  isLink: true,\n  toHref() {\n    return 'mailto:' + this.toString();\n  }\n});\n\n/**\n\tRepresents some plain text\n*/\nconst Text = createTokenClass('text');\n\n/**\n\tMulti-linebreak token - represents a line break\n\t@class Nl\n*/\nconst Nl = createTokenClass('nl');\n\n/**\n\tRepresents a list of text tokens making up a valid URL\n\t@class Url\n*/\nconst Url = createTokenClass('url', {\n  isLink: true,\n  /**\n  \tLowercases relevant parts of the domain and adds the protocol if\n  \trequired. Note that this will not escape unsafe HTML characters in the\n  \tURL.\n  \t\t@param {string} [scheme] default scheme (e.g., 'https')\n  \t@return {string} the full href\n  */\n  toHref(scheme = defaults.defaultProtocol) {\n    // Check if already has a prefix scheme\n    return this.hasProtocol() ? this.v : `${scheme}://${this.v}`;\n  },\n  /**\n   * Check whether this URL token has a protocol\n   * @return {boolean}\n   */\n  hasProtocol() {\n    const tokens = this.tk;\n    return tokens.length >= 2 && tokens[0].t !== LOCALHOST && tokens[1].t === COLON;\n  }\n});\n\nvar multi = /*#__PURE__*/Object.freeze({\n\t__proto__: null,\n\tMultiToken: MultiToken,\n\tBase: MultiToken,\n\tcreateTokenClass: createTokenClass,\n\tEmail: Email,\n\tText: Text,\n\tNl: Nl,\n\tUrl: Url\n});\n\n/**\n\tNot exactly parser, more like the second-stage scanner (although we can\n\ttheoretically hotswap the code here with a real parser in the future... but\n\tfor a little URL-finding utility abstract syntax trees may be a little\n\toverkill).\n\n\tURL format: http://en.wikipedia.org/wiki/URI_scheme\n\tEmail format: http://en.wikipedia.org/wiki/EmailAddress (links to RFC in\n\treference)\n\n\t@module linkify\n\t@submodule parser\n\t@main run\n*/\nconst makeState = arg => new State(arg);\n\n/**\n * Generate the parser multi token-based state machine\n * @param {{ groups: Collections<string> }} tokens\n */\nfunction init$1({\n  groups\n}) {\n  // Types of characters the URL can definitely end in\n  const qsAccepting = groups.domain.concat([AMPERSAND, ASTERISK, AT, BACKSLASH, BACKTICK, CARET, DOLLAR, EQUALS, HYPHEN, NUM, PERCENT, PIPE, PLUS, POUND, SLASH, SYM, TILDE, UNDERSCORE]);\n\n  // Types of tokens that can follow a URL and be part of the query string\n  // but cannot be the very last characters\n  // Characters that cannot appear in the URL at all should be excluded\n  const qsNonAccepting = [COLON, COMMA, DOT, EXCLAMATION, PERCENT, QUERY, QUOTE, SEMI, OPENANGLEBRACKET, CLOSEANGLEBRACKET, OPENBRACE, CLOSEBRACE, CLOSEBRACKET, OPENBRACKET, OPENPAREN, CLOSEPAREN, FULLWIDTHLEFTPAREN, FULLWIDTHRIGHTPAREN, LEFTCORNERBRACKET, RIGHTCORNERBRACKET, LEFTWHITECORNERBRACKET, RIGHTWHITECORNERBRACKET, FULLWIDTHLESSTHAN, FULLWIDTHGREATERTHAN];\n\n  // For addresses without the mailto prefix\n  // Tokens allowed in the localpart of the email\n  const localpartAccepting = [AMPERSAND, APOSTROPHE, ASTERISK, BACKSLASH, BACKTICK, CARET, DOLLAR, EQUALS, HYPHEN, OPENBRACE, CLOSEBRACE, PERCENT, PIPE, PLUS, POUND, QUERY, SLASH, SYM, TILDE, UNDERSCORE];\n\n  // The universal starting state.\n  /**\n   * @type State<Token>\n   */\n  const Start = makeState();\n  const Localpart = tt(Start, TILDE); // Local part of the email address\n  ta(Localpart, localpartAccepting, Localpart);\n  ta(Localpart, groups.domain, Localpart);\n  const Domain = makeState(),\n    Scheme = makeState(),\n    SlashScheme = makeState();\n  ta(Start, groups.domain, Domain); // parsed string ends with a potential domain name (A)\n  ta(Start, groups.scheme, Scheme); // e.g., 'mailto'\n  ta(Start, groups.slashscheme, SlashScheme); // e.g., 'http'\n\n  ta(Domain, localpartAccepting, Localpart);\n  ta(Domain, groups.domain, Domain);\n  const LocalpartAt = tt(Domain, AT); // Local part of the email address plus @\n\n  tt(Localpart, AT, LocalpartAt); // close to an email address now\n\n  // Local part of an email address can be e.g. 'http' or 'mailto'\n  tt(Scheme, AT, LocalpartAt);\n  tt(SlashScheme, AT, LocalpartAt);\n  const LocalpartDot = tt(Localpart, DOT); // Local part of the email address plus '.' (localpart cannot end in .)\n  ta(LocalpartDot, localpartAccepting, Localpart);\n  ta(LocalpartDot, groups.domain, Localpart);\n  const EmailDomain = makeState();\n  ta(LocalpartAt, groups.domain, EmailDomain); // parsed string starts with local email info + @ with a potential domain name\n  ta(EmailDomain, groups.domain, EmailDomain);\n  const EmailDomainDot = tt(EmailDomain, DOT); // domain followed by DOT\n  ta(EmailDomainDot, groups.domain, EmailDomain);\n  const Email$1 = makeState(Email); // Possible email address (could have more tlds)\n  ta(EmailDomainDot, groups.tld, Email$1);\n  ta(EmailDomainDot, groups.utld, Email$1);\n  tt(LocalpartAt, LOCALHOST, Email$1);\n\n  // Hyphen can jump back to a domain name\n  const EmailDomainHyphen = tt(EmailDomain, HYPHEN); // parsed string starts with local email info + @ with a potential domain name\n  tt(EmailDomainHyphen, HYPHEN, EmailDomainHyphen);\n  ta(EmailDomainHyphen, groups.domain, EmailDomain);\n  ta(Email$1, groups.domain, EmailDomain);\n  tt(Email$1, DOT, EmailDomainDot);\n  tt(Email$1, HYPHEN, EmailDomainHyphen);\n\n  // Final possible email states\n  const EmailColon = tt(Email$1, COLON); // URL followed by colon (potential port number here)\n  /*const EmailColonPort = */\n  ta(EmailColon, groups.numeric, Email); // URL followed by colon and port number\n\n  // Account for dots and hyphens. Hyphens are usually parts of domain names\n  // (but not TLDs)\n  const DomainHyphen = tt(Domain, HYPHEN); // domain followed by hyphen\n  const DomainDot = tt(Domain, DOT); // domain followed by DOT\n  tt(DomainHyphen, HYPHEN, DomainHyphen);\n  ta(DomainHyphen, groups.domain, Domain);\n  ta(DomainDot, localpartAccepting, Localpart);\n  ta(DomainDot, groups.domain, Domain);\n  const DomainDotTld = makeState(Url); // Simplest possible URL with no query string\n  ta(DomainDot, groups.tld, DomainDotTld);\n  ta(DomainDot, groups.utld, DomainDotTld);\n  ta(DomainDotTld, groups.domain, Domain);\n  ta(DomainDotTld, localpartAccepting, Localpart);\n  tt(DomainDotTld, DOT, DomainDot);\n  tt(DomainDotTld, HYPHEN, DomainHyphen);\n  tt(DomainDotTld, AT, LocalpartAt);\n  const DomainDotTldColon = tt(DomainDotTld, COLON); // URL followed by colon (potential port number here)\n  const DomainDotTldColonPort = makeState(Url); // TLD followed by a port number\n  ta(DomainDotTldColon, groups.numeric, DomainDotTldColonPort);\n\n  // Long URL with optional port and maybe query string\n  const Url$1 = makeState(Url);\n\n  // URL with extra symbols at the end, followed by an opening bracket\n  const UrlNonaccept = makeState(); // URL followed by some symbols (will not be part of the final URL)\n\n  // Query strings\n  ta(Url$1, qsAccepting, Url$1);\n  ta(Url$1, qsNonAccepting, UrlNonaccept);\n  ta(UrlNonaccept, qsAccepting, Url$1);\n  ta(UrlNonaccept, qsNonAccepting, UrlNonaccept);\n\n  // Become real URLs after `SLASH` or `COLON NUM SLASH`\n  // Here works with or without scheme:// prefix\n  tt(DomainDotTld, SLASH, Url$1);\n  tt(DomainDotTldColonPort, SLASH, Url$1);\n\n  // Note that domains that begin with schemes are treated slighly differently\n  const SchemeColon = tt(Scheme, COLON); // e.g., 'mailto:'\n  const SlashSchemeColon = tt(SlashScheme, COLON); // e.g., 'http:'\n  const SlashSchemeColonSlash = tt(SlashSchemeColon, SLASH); // e.g., 'http:/'\n\n  const UriPrefix = tt(SlashSchemeColonSlash, SLASH); // e.g., 'http://'\n\n  // Scheme states can transition to domain states\n  ta(Scheme, groups.domain, Domain);\n  tt(Scheme, DOT, DomainDot);\n  tt(Scheme, HYPHEN, DomainHyphen);\n  ta(SlashScheme, groups.domain, Domain);\n  tt(SlashScheme, DOT, DomainDot);\n  tt(SlashScheme, HYPHEN, DomainHyphen);\n\n  // Force URL with scheme prefix followed by anything sane\n  ta(SchemeColon, groups.domain, Url$1);\n  tt(SchemeColon, SLASH, Url$1);\n  tt(SchemeColon, QUERY, Url$1);\n  ta(UriPrefix, groups.domain, Url$1);\n  ta(UriPrefix, qsAccepting, Url$1);\n  tt(UriPrefix, SLASH, Url$1);\n  const bracketPairs = [[OPENBRACE, CLOSEBRACE],\n  // {}\n  [OPENBRACKET, CLOSEBRACKET],\n  // []\n  [OPENPAREN, CLOSEPAREN],\n  // ()\n  [OPENANGLEBRACKET, CLOSEANGLEBRACKET],\n  // <>\n  [FULLWIDTHLEFTPAREN, FULLWIDTHRIGHTPAREN],\n  // （）\n  [LEFTCORNERBRACKET, RIGHTCORNERBRACKET],\n  // 「」\n  [LEFTWHITECORNERBRACKET, RIGHTWHITECORNERBRACKET],\n  // 『』\n  [FULLWIDTHLESSTHAN, FULLWIDTHGREATERTHAN] // ＜＞\n  ];\n  for (let i = 0; i < bracketPairs.length; i++) {\n    const [OPEN, CLOSE] = bracketPairs[i];\n    const UrlOpen = tt(Url$1, OPEN); // URL followed by open bracket\n\n    // Continue not accepting for open brackets\n    tt(UrlNonaccept, OPEN, UrlOpen);\n\n    // Closing bracket component. This character WILL be included in the URL\n    tt(UrlOpen, CLOSE, Url$1);\n\n    // URL that beings with an opening bracket, followed by a symbols.\n    // Note that the final state can still be `UrlOpen` (if the URL has a\n    // single opening bracket for some reason).\n    const UrlOpenQ = makeState(Url);\n    ta(UrlOpen, qsAccepting, UrlOpenQ);\n    const UrlOpenSyms = makeState(); // UrlOpen followed by some symbols it cannot end it\n    ta(UrlOpen, qsNonAccepting);\n\n    // URL that begins with an opening bracket, followed by some symbols\n    ta(UrlOpenQ, qsAccepting, UrlOpenQ);\n    ta(UrlOpenQ, qsNonAccepting, UrlOpenSyms);\n    ta(UrlOpenSyms, qsAccepting, UrlOpenQ);\n    ta(UrlOpenSyms, qsNonAccepting, UrlOpenSyms);\n\n    // Close brace/bracket to become regular URL\n    tt(UrlOpenQ, CLOSE, Url$1);\n    tt(UrlOpenSyms, CLOSE, Url$1);\n  }\n  tt(Start, LOCALHOST, DomainDotTld); // localhost is a valid URL state\n  tt(Start, NL, Nl); // single new line\n\n  return {\n    start: Start,\n    tokens: tk\n  };\n}\n\n/**\n * Run the parser state machine on a list of scanned string-based tokens to\n * create a list of multi tokens, each of which represents a URL, email address,\n * plain text, etc.\n *\n * @param {State<MultiToken>} start parser start state\n * @param {string} input the original input used to generate the given tokens\n * @param {Token[]} tokens list of scanned tokens\n * @returns {MultiToken[]}\n */\nfunction run(start, input, tokens) {\n  let len = tokens.length;\n  let cursor = 0;\n  let multis = [];\n  let textTokens = [];\n  while (cursor < len) {\n    let state = start;\n    let secondState = null;\n    let nextState = null;\n    let multiLength = 0;\n    let latestAccepting = null;\n    let sinceAccepts = -1;\n    while (cursor < len && !(secondState = state.go(tokens[cursor].t))) {\n      // Starting tokens with nowhere to jump to.\n      // Consider these to be just plain text\n      textTokens.push(tokens[cursor++]);\n    }\n    while (cursor < len && (nextState = secondState || state.go(tokens[cursor].t))) {\n      // Get the next state\n      secondState = null;\n      state = nextState;\n\n      // Keep track of the latest accepting state\n      if (state.accepts()) {\n        sinceAccepts = 0;\n        latestAccepting = state;\n      } else if (sinceAccepts >= 0) {\n        sinceAccepts++;\n      }\n      cursor++;\n      multiLength++;\n    }\n    if (sinceAccepts < 0) {\n      // No accepting state was found, part of a regular text token add\n      // the first text token to the text tokens array and try again from\n      // the next\n      cursor -= multiLength;\n      if (cursor < len) {\n        textTokens.push(tokens[cursor]);\n        cursor++;\n      }\n    } else {\n      // Accepting state!\n      // First close off the textTokens (if available)\n      if (textTokens.length > 0) {\n        multis.push(initMultiToken(Text, input, textTokens));\n        textTokens = [];\n      }\n\n      // Roll back to the latest accepting state\n      cursor -= sinceAccepts;\n      multiLength -= sinceAccepts;\n\n      // Create a new multitoken\n      const Multi = latestAccepting.t;\n      const subtokens = tokens.slice(cursor - multiLength, cursor);\n      multis.push(initMultiToken(Multi, input, subtokens));\n    }\n  }\n\n  // Finally close off the textTokens (if available)\n  if (textTokens.length > 0) {\n    multis.push(initMultiToken(Text, input, textTokens));\n  }\n  return multis;\n}\n\n/**\n * Utility function for instantiating a new multitoken with all the relevant\n * fields during parsing.\n * @param {new (value: string, tokens: Token[]) => MultiToken} Multi class to instantiate\n * @param {string} input original input string\n * @param {Token[]} tokens consecutive tokens scanned from input string\n * @returns {MultiToken}\n */\nfunction initMultiToken(Multi, input, tokens) {\n  const startIdx = tokens[0].s;\n  const endIdx = tokens[tokens.length - 1].e;\n  const value = input.slice(startIdx, endIdx);\n  return new Multi(value, tokens);\n}\n\nconst warn = typeof console !== 'undefined' && console && console.warn || (() => {});\nconst warnAdvice = 'until manual call of linkify.init(). Register all schemes and plugins before invoking linkify the first time.';\n\n// Side-effect initialization state\nconst INIT = {\n  scanner: null,\n  parser: null,\n  tokenQueue: [],\n  pluginQueue: [],\n  customSchemes: [],\n  initialized: false\n};\n\n/**\n * @typedef {{\n * \tstart: State<string>,\n * \ttokens: { groups: Collections<string> } & typeof tk\n * }} ScannerInit\n */\n\n/**\n * @typedef {{\n * \tstart: State<MultiToken>,\n * \ttokens: typeof multi\n * }} ParserInit\n */\n\n/**\n * @typedef {(arg: { scanner: ScannerInit }) => void} TokenPlugin\n */\n\n/**\n * @typedef {(arg: { scanner: ScannerInit, parser: ParserInit }) => void} Plugin\n */\n\n/**\n * De-register all plugins and reset the internal state-machine. Used for\n * testing; not required in practice.\n * @private\n */\nfunction reset() {\n  State.groups = {};\n  INIT.scanner = null;\n  INIT.parser = null;\n  INIT.tokenQueue = [];\n  INIT.pluginQueue = [];\n  INIT.customSchemes = [];\n  INIT.initialized = false;\n  return INIT;\n}\n\n/**\n * Register a token plugin to allow the scanner to recognize additional token\n * types before the parser state machine is constructed from the results.\n * @param {string} name of plugin to register\n * @param {TokenPlugin} plugin function that accepts the scanner state machine\n * and available scanner tokens and collections and extends the state machine to\n * recognize additional tokens or groups.\n */\nfunction registerTokenPlugin(name, plugin) {\n  if (typeof plugin !== 'function') {\n    throw new Error(`linkifyjs: Invalid token plugin ${plugin} (expects function)`);\n  }\n  for (let i = 0; i < INIT.tokenQueue.length; i++) {\n    if (name === INIT.tokenQueue[i][0]) {\n      warn(`linkifyjs: token plugin \"${name}\" already registered - will be overwritten`);\n      INIT.tokenQueue[i] = [name, plugin];\n      return;\n    }\n  }\n  INIT.tokenQueue.push([name, plugin]);\n  if (INIT.initialized) {\n    warn(`linkifyjs: already initialized - will not register token plugin \"${name}\" ${warnAdvice}`);\n  }\n}\n\n/**\n * Register a linkify plugin\n * @param {string} name of plugin to register\n * @param {Plugin} plugin function that accepts the parser state machine and\n * extends the parser to recognize additional link types\n */\nfunction registerPlugin(name, plugin) {\n  if (typeof plugin !== 'function') {\n    throw new Error(`linkifyjs: Invalid plugin ${plugin} (expects function)`);\n  }\n  for (let i = 0; i < INIT.pluginQueue.length; i++) {\n    if (name === INIT.pluginQueue[i][0]) {\n      warn(`linkifyjs: plugin \"${name}\" already registered - will be overwritten`);\n      INIT.pluginQueue[i] = [name, plugin];\n      return;\n    }\n  }\n  INIT.pluginQueue.push([name, plugin]);\n  if (INIT.initialized) {\n    warn(`linkifyjs: already initialized - will not register plugin \"${name}\" ${warnAdvice}`);\n  }\n}\n\n/**\n * Detect URLs with the following additional protocol. Anything with format\n * \"protocol://...\" will be considered a link. If `optionalSlashSlash` is set to\n * `true`, anything with format \"protocol:...\" will be considered a link.\n * @param {string} scheme\n * @param {boolean} [optionalSlashSlash]\n */\nfunction registerCustomProtocol(scheme, optionalSlashSlash = false) {\n  if (INIT.initialized) {\n    warn(`linkifyjs: already initialized - will not register custom scheme \"${scheme}\" ${warnAdvice}`);\n  }\n  if (!/^[0-9a-z]+(-[0-9a-z]+)*$/.test(scheme)) {\n    throw new Error(`linkifyjs: incorrect scheme format.\n1. Must only contain digits, lowercase ASCII letters or \"-\"\n2. Cannot start or end with \"-\"\n3. \"-\" cannot repeat`);\n  }\n  INIT.customSchemes.push([scheme, optionalSlashSlash]);\n}\n\n/**\n * Initialize the linkify state machine. Called automatically the first time\n * linkify is called on a string, but may be called manually as well.\n */\nfunction init() {\n  // Initialize scanner state machine and plugins\n  INIT.scanner = init$2(INIT.customSchemes);\n  for (let i = 0; i < INIT.tokenQueue.length; i++) {\n    INIT.tokenQueue[i][1]({\n      scanner: INIT.scanner\n    });\n  }\n\n  // Initialize parser state machine and plugins\n  INIT.parser = init$1(INIT.scanner.tokens);\n  for (let i = 0; i < INIT.pluginQueue.length; i++) {\n    INIT.pluginQueue[i][1]({\n      scanner: INIT.scanner,\n      parser: INIT.parser\n    });\n  }\n  INIT.initialized = true;\n  return INIT;\n}\n\n/**\n * Parse a string into tokens that represent linkable and non-linkable sub-components\n * @param {string} str\n * @return {MultiToken[]} tokens\n */\nfunction tokenize(str) {\n  if (!INIT.initialized) {\n    init();\n  }\n  return run(INIT.parser.start, str, run$1(INIT.scanner.start, str));\n}\ntokenize.scan = run$1; // for testing\n\n/**\n * Find a list of linkable items in the given string.\n * @param {string} str string to find links in\n * @param {string | Opts} [type] either formatting options or specific type of\n * links to find, e.g., 'url' or 'email'\n * @param {Opts} [opts] formatting options for final output. Cannot be specified\n * if opts already provided in `type` argument\n */\nfunction find(str, type = null, opts = null) {\n  if (type && typeof type === 'object') {\n    if (opts) {\n      throw Error(`linkifyjs: Invalid link type ${type}; must be a string`);\n    }\n    opts = type;\n    type = null;\n  }\n  const options = new Options(opts);\n  const tokens = tokenize(str);\n  const filtered = [];\n  for (let i = 0; i < tokens.length; i++) {\n    const token = tokens[i];\n    if (token.isLink && (!type || token.t === type) && options.check(token)) {\n      filtered.push(token.toFormattedObject(options));\n    }\n  }\n  return filtered;\n}\n\n/**\n * Is the given string valid linkable text of some sort. Note that this does not\n * trim the text for you.\n *\n * Optionally pass in a second `type` param, which is the type of link to test\n * for.\n *\n * For example,\n *\n *     linkify.test(str, 'email');\n *\n * Returns `true` if str is a valid email.\n * @param {string} str string to test for links\n * @param {string} [type] optional specific link type to look for\n * @returns boolean true/false\n */\nfunction test(str, type = null) {\n  const tokens = tokenize(str);\n  return tokens.length === 1 && tokens[0].isLink && (!type || tokens[0].t === type);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/linkifyjs/dist/linkify.es.js\n");

/***/ })

};
;