"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/hast-util-is-body-ok-link";
exports.ids = ["vendor-chunks/hast-util-is-body-ok-link"];
exports.modules = {

/***/ "(ssr)/../../node_modules/hast-util-is-body-ok-link/index.js":
/*!*************************************************************!*\
  !*** ../../node_modules/hast-util-is-body-ok-link/index.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isBodyOkLink: () => (/* binding */ isBodyOkLink)\n/* harmony export */ });\n/* harmony import */ var hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! hast-util-is-element */ \"(ssr)/../../node_modules/hast-util-is-element/index.js\");\n/* harmony import */ var hast_util_has_property__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! hast-util-has-property */ \"(ssr)/../../node_modules/hast-util-has-property/lib/index.js\");\n/**\n * @fileoverview\n *   Check if a `link` element is “Body OK”.\n * @longdescription\n *   ## Use\n *\n *   ```js\n *   import {h} from 'hastscript'\n *   import {isBodyOkLink} from 'hast-util-is-body-ok-link'\n *\n *   isBodyOkLink(h('link', {itemProp: 'foo'})) //=> true\n *   isBodyOkLink(h('link', {rel: ['stylesheet'], href: 'index.css'})) //=> true\n *   isBodyOkLink(h('link', {rel: ['author'], href: 'index.css'})) //=> false\n *   ```\n *\n *   ## API\n *\n *   ### `isBodyOkLink(node)`\n *\n *   * Return `true` for `link` elements with an `itemProp`\n *   * Return `true` for `link` elements with a `rel` list where one or more\n *     entries are `pingback`, `prefetch`, or `stylesheet`.\n */\n\n\n\n\nconst list = new Set(['pingback', 'prefetch', 'stylesheet'])\n\n/**\n * @typedef {import('hast').Root} Root\n * @typedef {Root|Root['children'][number]} Node\n */\n\n/**\n * Check if a `link` element is “Body OK”.\n *\n * @param {Node} node\n * @returns {boolean}\n */\nfunction isBodyOkLink(node) {\n  if (!(0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.isElement)(node, 'link')) {\n    return false\n  }\n\n  if ((0,hast_util_has_property__WEBPACK_IMPORTED_MODULE_1__.hasProperty)(node, 'itemProp')) {\n    return true\n  }\n\n  const props = node.properties || {}\n  const rel = props.rel || []\n  let index = -1\n\n  if (!Array.isArray(rel) || rel.length === 0) {\n    return false\n  }\n\n  while (++index < rel.length) {\n    if (!list.has(String(rel[index]))) {\n      return false\n    }\n  }\n\n  return true\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/hast-util-is-body-ok-link/index.js\n");

/***/ })

};
;