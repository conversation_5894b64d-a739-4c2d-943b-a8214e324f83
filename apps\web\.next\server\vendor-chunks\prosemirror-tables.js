"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/prosemirror-tables";
exports.ids = ["vendor-chunks/prosemirror-tables"];
exports.modules = {

/***/ "(ssr)/../../node_modules/prosemirror-tables/dist/index.js":
/*!***********************************************************!*\
  !*** ../../node_modules/prosemirror-tables/dist/index.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CellBookmark: () => (/* binding */ CellBookmark),\n/* harmony export */   CellSelection: () => (/* binding */ CellSelection),\n/* harmony export */   ResizeState: () => (/* binding */ ResizeState),\n/* harmony export */   TableMap: () => (/* binding */ TableMap),\n/* harmony export */   TableView: () => (/* binding */ TableView),\n/* harmony export */   __clipCells: () => (/* binding */ clipCells),\n/* harmony export */   __insertCells: () => (/* binding */ insertCells),\n/* harmony export */   __pastedCells: () => (/* binding */ pastedCells),\n/* harmony export */   addColSpan: () => (/* binding */ addColSpan),\n/* harmony export */   addColumn: () => (/* binding */ addColumn),\n/* harmony export */   addColumnAfter: () => (/* binding */ addColumnAfter),\n/* harmony export */   addColumnBefore: () => (/* binding */ addColumnBefore),\n/* harmony export */   addRow: () => (/* binding */ addRow),\n/* harmony export */   addRowAfter: () => (/* binding */ addRowAfter),\n/* harmony export */   addRowBefore: () => (/* binding */ addRowBefore),\n/* harmony export */   cellAround: () => (/* binding */ cellAround),\n/* harmony export */   cellNear: () => (/* binding */ cellNear),\n/* harmony export */   colCount: () => (/* binding */ colCount),\n/* harmony export */   columnIsHeader: () => (/* binding */ columnIsHeader),\n/* harmony export */   columnResizing: () => (/* binding */ columnResizing),\n/* harmony export */   columnResizingPluginKey: () => (/* binding */ columnResizingPluginKey),\n/* harmony export */   deleteCellSelection: () => (/* binding */ deleteCellSelection),\n/* harmony export */   deleteColumn: () => (/* binding */ deleteColumn),\n/* harmony export */   deleteRow: () => (/* binding */ deleteRow),\n/* harmony export */   deleteTable: () => (/* binding */ deleteTable),\n/* harmony export */   findCell: () => (/* binding */ findCell),\n/* harmony export */   fixTables: () => (/* binding */ fixTables),\n/* harmony export */   fixTablesKey: () => (/* binding */ fixTablesKey),\n/* harmony export */   goToNextCell: () => (/* binding */ goToNextCell),\n/* harmony export */   handlePaste: () => (/* binding */ handlePaste),\n/* harmony export */   inSameTable: () => (/* binding */ inSameTable),\n/* harmony export */   isInTable: () => (/* binding */ isInTable),\n/* harmony export */   mergeCells: () => (/* binding */ mergeCells),\n/* harmony export */   moveCellForward: () => (/* binding */ moveCellForward),\n/* harmony export */   nextCell: () => (/* binding */ nextCell),\n/* harmony export */   pointsAtCell: () => (/* binding */ pointsAtCell),\n/* harmony export */   removeColSpan: () => (/* binding */ removeColSpan),\n/* harmony export */   removeColumn: () => (/* binding */ removeColumn),\n/* harmony export */   removeRow: () => (/* binding */ removeRow),\n/* harmony export */   rowIsHeader: () => (/* binding */ rowIsHeader),\n/* harmony export */   selectedRect: () => (/* binding */ selectedRect),\n/* harmony export */   selectionCell: () => (/* binding */ selectionCell),\n/* harmony export */   setCellAttr: () => (/* binding */ setCellAttr),\n/* harmony export */   splitCell: () => (/* binding */ splitCell),\n/* harmony export */   splitCellWithType: () => (/* binding */ splitCellWithType),\n/* harmony export */   tableEditing: () => (/* binding */ tableEditing),\n/* harmony export */   tableEditingKey: () => (/* binding */ tableEditingKey),\n/* harmony export */   tableNodeTypes: () => (/* binding */ tableNodeTypes),\n/* harmony export */   tableNodes: () => (/* binding */ tableNodes),\n/* harmony export */   toggleHeader: () => (/* binding */ toggleHeader),\n/* harmony export */   toggleHeaderCell: () => (/* binding */ toggleHeaderCell),\n/* harmony export */   toggleHeaderColumn: () => (/* binding */ toggleHeaderColumn),\n/* harmony export */   toggleHeaderRow: () => (/* binding */ toggleHeaderRow),\n/* harmony export */   updateColumnsOnResize: () => (/* binding */ updateColumnsOnResize)\n/* harmony export */ });\n/* harmony import */ var prosemirror_state__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! prosemirror-state */ \"(ssr)/../../node_modules/prosemirror-state/dist/index.js\");\n/* harmony import */ var prosemirror_model__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! prosemirror-model */ \"(ssr)/../../node_modules/prosemirror-model/dist/index.js\");\n/* harmony import */ var prosemirror_view__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! prosemirror-view */ \"(ssr)/../../node_modules/prosemirror-view/dist/index.js\");\n/* harmony import */ var prosemirror_keymap__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! prosemirror-keymap */ \"(ssr)/../../node_modules/prosemirror-keymap/dist/index.js\");\n/* harmony import */ var prosemirror_transform__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! prosemirror-transform */ \"(ssr)/../../node_modules/prosemirror-transform/dist/index.js\");\n// src/index.ts\n\n\n// src/cellselection.ts\n\n\n\n\n// src/tablemap.ts\nvar readFromCache;\nvar addToCache;\nif (typeof WeakMap != \"undefined\") {\n  let cache = /* @__PURE__ */ new WeakMap();\n  readFromCache = (key) => cache.get(key);\n  addToCache = (key, value) => {\n    cache.set(key, value);\n    return value;\n  };\n} else {\n  const cache = [];\n  const cacheSize = 10;\n  let cachePos = 0;\n  readFromCache = (key) => {\n    for (let i = 0; i < cache.length; i += 2)\n      if (cache[i] == key)\n        return cache[i + 1];\n  };\n  addToCache = (key, value) => {\n    if (cachePos == cacheSize)\n      cachePos = 0;\n    cache[cachePos++] = key;\n    return cache[cachePos++] = value;\n  };\n}\nvar TableMap = class {\n  constructor(width, height, map, problems) {\n    this.width = width;\n    this.height = height;\n    this.map = map;\n    this.problems = problems;\n  }\n  // Find the dimensions of the cell at the given position.\n  findCell(pos) {\n    for (let i = 0; i < this.map.length; i++) {\n      const curPos = this.map[i];\n      if (curPos != pos)\n        continue;\n      const left = i % this.width;\n      const top = i / this.width | 0;\n      let right = left + 1;\n      let bottom = top + 1;\n      for (let j = 1; right < this.width && this.map[i + j] == curPos; j++) {\n        right++;\n      }\n      for (let j = 1; bottom < this.height && this.map[i + this.width * j] == curPos; j++) {\n        bottom++;\n      }\n      return { left, top, right, bottom };\n    }\n    throw new RangeError(`No cell with offset ${pos} found`);\n  }\n  // Find the left side of the cell at the given position.\n  colCount(pos) {\n    for (let i = 0; i < this.map.length; i++) {\n      if (this.map[i] == pos) {\n        return i % this.width;\n      }\n    }\n    throw new RangeError(`No cell with offset ${pos} found`);\n  }\n  // Find the next cell in the given direction, starting from the cell\n  // at `pos`, if any.\n  nextCell(pos, axis, dir) {\n    const { left, right, top, bottom } = this.findCell(pos);\n    if (axis == \"horiz\") {\n      if (dir < 0 ? left == 0 : right == this.width)\n        return null;\n      return this.map[top * this.width + (dir < 0 ? left - 1 : right)];\n    } else {\n      if (dir < 0 ? top == 0 : bottom == this.height)\n        return null;\n      return this.map[left + this.width * (dir < 0 ? top - 1 : bottom)];\n    }\n  }\n  // Get the rectangle spanning the two given cells.\n  rectBetween(a, b) {\n    const {\n      left: leftA,\n      right: rightA,\n      top: topA,\n      bottom: bottomA\n    } = this.findCell(a);\n    const {\n      left: leftB,\n      right: rightB,\n      top: topB,\n      bottom: bottomB\n    } = this.findCell(b);\n    return {\n      left: Math.min(leftA, leftB),\n      top: Math.min(topA, topB),\n      right: Math.max(rightA, rightB),\n      bottom: Math.max(bottomA, bottomB)\n    };\n  }\n  // Return the position of all cells that have the top left corner in\n  // the given rectangle.\n  cellsInRect(rect) {\n    const result = [];\n    const seen = {};\n    for (let row = rect.top; row < rect.bottom; row++) {\n      for (let col = rect.left; col < rect.right; col++) {\n        const index = row * this.width + col;\n        const pos = this.map[index];\n        if (seen[pos])\n          continue;\n        seen[pos] = true;\n        if (col == rect.left && col && this.map[index - 1] == pos || row == rect.top && row && this.map[index - this.width] == pos) {\n          continue;\n        }\n        result.push(pos);\n      }\n    }\n    return result;\n  }\n  // Return the position at which the cell at the given row and column\n  // starts, or would start, if a cell started there.\n  positionAt(row, col, table) {\n    for (let i = 0, rowStart = 0; ; i++) {\n      const rowEnd = rowStart + table.child(i).nodeSize;\n      if (i == row) {\n        let index = col + row * this.width;\n        const rowEndIndex = (row + 1) * this.width;\n        while (index < rowEndIndex && this.map[index] < rowStart)\n          index++;\n        return index == rowEndIndex ? rowEnd - 1 : this.map[index];\n      }\n      rowStart = rowEnd;\n    }\n  }\n  // Find the table map for the given table node.\n  static get(table) {\n    return readFromCache(table) || addToCache(table, computeMap(table));\n  }\n};\nfunction computeMap(table) {\n  if (table.type.spec.tableRole != \"table\")\n    throw new RangeError(\"Not a table node: \" + table.type.name);\n  const width = findWidth(table), height = table.childCount;\n  const map = [];\n  let mapPos = 0;\n  let problems = null;\n  const colWidths = [];\n  for (let i = 0, e = width * height; i < e; i++)\n    map[i] = 0;\n  for (let row = 0, pos = 0; row < height; row++) {\n    const rowNode = table.child(row);\n    pos++;\n    for (let i = 0; ; i++) {\n      while (mapPos < map.length && map[mapPos] != 0)\n        mapPos++;\n      if (i == rowNode.childCount)\n        break;\n      const cellNode = rowNode.child(i);\n      const { colspan, rowspan, colwidth } = cellNode.attrs;\n      for (let h = 0; h < rowspan; h++) {\n        if (h + row >= height) {\n          (problems || (problems = [])).push({\n            type: \"overlong_rowspan\",\n            pos,\n            n: rowspan - h\n          });\n          break;\n        }\n        const start = mapPos + h * width;\n        for (let w = 0; w < colspan; w++) {\n          if (map[start + w] == 0)\n            map[start + w] = pos;\n          else\n            (problems || (problems = [])).push({\n              type: \"collision\",\n              row,\n              pos,\n              n: colspan - w\n            });\n          const colW = colwidth && colwidth[w];\n          if (colW) {\n            const widthIndex = (start + w) % width * 2, prev = colWidths[widthIndex];\n            if (prev == null || prev != colW && colWidths[widthIndex + 1] == 1) {\n              colWidths[widthIndex] = colW;\n              colWidths[widthIndex + 1] = 1;\n            } else if (prev == colW) {\n              colWidths[widthIndex + 1]++;\n            }\n          }\n        }\n      }\n      mapPos += colspan;\n      pos += cellNode.nodeSize;\n    }\n    const expectedPos = (row + 1) * width;\n    let missing = 0;\n    while (mapPos < expectedPos)\n      if (map[mapPos++] == 0)\n        missing++;\n    if (missing)\n      (problems || (problems = [])).push({ type: \"missing\", row, n: missing });\n    pos++;\n  }\n  const tableMap = new TableMap(width, height, map, problems);\n  let badWidths = false;\n  for (let i = 0; !badWidths && i < colWidths.length; i += 2)\n    if (colWidths[i] != null && colWidths[i + 1] < height)\n      badWidths = true;\n  if (badWidths)\n    findBadColWidths(tableMap, colWidths, table);\n  return tableMap;\n}\nfunction findWidth(table) {\n  let width = -1;\n  let hasRowSpan = false;\n  for (let row = 0; row < table.childCount; row++) {\n    const rowNode = table.child(row);\n    let rowWidth = 0;\n    if (hasRowSpan)\n      for (let j = 0; j < row; j++) {\n        const prevRow = table.child(j);\n        for (let i = 0; i < prevRow.childCount; i++) {\n          const cell = prevRow.child(i);\n          if (j + cell.attrs.rowspan > row)\n            rowWidth += cell.attrs.colspan;\n        }\n      }\n    for (let i = 0; i < rowNode.childCount; i++) {\n      const cell = rowNode.child(i);\n      rowWidth += cell.attrs.colspan;\n      if (cell.attrs.rowspan > 1)\n        hasRowSpan = true;\n    }\n    if (width == -1)\n      width = rowWidth;\n    else if (width != rowWidth)\n      width = Math.max(width, rowWidth);\n  }\n  return width;\n}\nfunction findBadColWidths(map, colWidths, table) {\n  if (!map.problems)\n    map.problems = [];\n  const seen = {};\n  for (let i = 0; i < map.map.length; i++) {\n    const pos = map.map[i];\n    if (seen[pos])\n      continue;\n    seen[pos] = true;\n    const node = table.nodeAt(pos);\n    if (!node) {\n      throw new RangeError(`No cell with offset ${pos} found`);\n    }\n    let updated = null;\n    const attrs = node.attrs;\n    for (let j = 0; j < attrs.colspan; j++) {\n      const col = (i + j) % map.width;\n      const colWidth = colWidths[col * 2];\n      if (colWidth != null && (!attrs.colwidth || attrs.colwidth[j] != colWidth))\n        (updated || (updated = freshColWidth(attrs)))[j] = colWidth;\n    }\n    if (updated)\n      map.problems.unshift({\n        type: \"colwidth mismatch\",\n        pos,\n        colwidth: updated\n      });\n  }\n}\nfunction freshColWidth(attrs) {\n  if (attrs.colwidth)\n    return attrs.colwidth.slice();\n  const result = [];\n  for (let i = 0; i < attrs.colspan; i++)\n    result.push(0);\n  return result;\n}\n\n// src/util.ts\n\n\n// src/schema.ts\nfunction getCellAttrs(dom, extraAttrs) {\n  if (typeof dom === \"string\") {\n    return {};\n  }\n  const widthAttr = dom.getAttribute(\"data-colwidth\");\n  const widths = widthAttr && /^\\d+(,\\d+)*$/.test(widthAttr) ? widthAttr.split(\",\").map((s) => Number(s)) : null;\n  const colspan = Number(dom.getAttribute(\"colspan\") || 1);\n  const result = {\n    colspan,\n    rowspan: Number(dom.getAttribute(\"rowspan\") || 1),\n    colwidth: widths && widths.length == colspan ? widths : null\n  };\n  for (const prop in extraAttrs) {\n    const getter = extraAttrs[prop].getFromDOM;\n    const value = getter && getter(dom);\n    if (value != null) {\n      result[prop] = value;\n    }\n  }\n  return result;\n}\nfunction setCellAttrs(node, extraAttrs) {\n  const attrs = {};\n  if (node.attrs.colspan != 1)\n    attrs.colspan = node.attrs.colspan;\n  if (node.attrs.rowspan != 1)\n    attrs.rowspan = node.attrs.rowspan;\n  if (node.attrs.colwidth)\n    attrs[\"data-colwidth\"] = node.attrs.colwidth.join(\",\");\n  for (const prop in extraAttrs) {\n    const setter = extraAttrs[prop].setDOMAttr;\n    if (setter)\n      setter(node.attrs[prop], attrs);\n  }\n  return attrs;\n}\nfunction tableNodes(options) {\n  const extraAttrs = options.cellAttributes || {};\n  const cellAttrs = {\n    colspan: { default: 1 },\n    rowspan: { default: 1 },\n    colwidth: { default: null }\n  };\n  for (const prop in extraAttrs)\n    cellAttrs[prop] = { default: extraAttrs[prop].default };\n  return {\n    table: {\n      content: \"table_row+\",\n      tableRole: \"table\",\n      isolating: true,\n      group: options.tableGroup,\n      parseDOM: [{ tag: \"table\" }],\n      toDOM() {\n        return [\"table\", [\"tbody\", 0]];\n      }\n    },\n    table_row: {\n      content: \"(table_cell | table_header)*\",\n      tableRole: \"row\",\n      parseDOM: [{ tag: \"tr\" }],\n      toDOM() {\n        return [\"tr\", 0];\n      }\n    },\n    table_cell: {\n      content: options.cellContent,\n      attrs: cellAttrs,\n      tableRole: \"cell\",\n      isolating: true,\n      parseDOM: [\n        { tag: \"td\", getAttrs: (dom) => getCellAttrs(dom, extraAttrs) }\n      ],\n      toDOM(node) {\n        return [\"td\", setCellAttrs(node, extraAttrs), 0];\n      }\n    },\n    table_header: {\n      content: options.cellContent,\n      attrs: cellAttrs,\n      tableRole: \"header_cell\",\n      isolating: true,\n      parseDOM: [\n        { tag: \"th\", getAttrs: (dom) => getCellAttrs(dom, extraAttrs) }\n      ],\n      toDOM(node) {\n        return [\"th\", setCellAttrs(node, extraAttrs), 0];\n      }\n    }\n  };\n}\nfunction tableNodeTypes(schema) {\n  let result = schema.cached.tableNodeTypes;\n  if (!result) {\n    result = schema.cached.tableNodeTypes = {};\n    for (const name in schema.nodes) {\n      const type = schema.nodes[name], role = type.spec.tableRole;\n      if (role)\n        result[role] = type;\n    }\n  }\n  return result;\n}\n\n// src/util.ts\nvar tableEditingKey = new prosemirror_state__WEBPACK_IMPORTED_MODULE_0__.PluginKey(\"selectingCells\");\nfunction cellAround($pos) {\n  for (let d = $pos.depth - 1; d > 0; d--)\n    if ($pos.node(d).type.spec.tableRole == \"row\")\n      return $pos.node(0).resolve($pos.before(d + 1));\n  return null;\n}\nfunction cellWrapping($pos) {\n  for (let d = $pos.depth; d > 0; d--) {\n    const role = $pos.node(d).type.spec.tableRole;\n    if (role === \"cell\" || role === \"header_cell\")\n      return $pos.node(d);\n  }\n  return null;\n}\nfunction isInTable(state) {\n  const $head = state.selection.$head;\n  for (let d = $head.depth; d > 0; d--)\n    if ($head.node(d).type.spec.tableRole == \"row\")\n      return true;\n  return false;\n}\nfunction selectionCell(state) {\n  const sel = state.selection;\n  if (\"$anchorCell\" in sel && sel.$anchorCell) {\n    return sel.$anchorCell.pos > sel.$headCell.pos ? sel.$anchorCell : sel.$headCell;\n  } else if (\"node\" in sel && sel.node && sel.node.type.spec.tableRole == \"cell\") {\n    return sel.$anchor;\n  }\n  const $cell = cellAround(sel.$head) || cellNear(sel.$head);\n  if ($cell) {\n    return $cell;\n  }\n  throw new RangeError(`No cell found around position ${sel.head}`);\n}\nfunction cellNear($pos) {\n  for (let after = $pos.nodeAfter, pos = $pos.pos; after; after = after.firstChild, pos++) {\n    const role = after.type.spec.tableRole;\n    if (role == \"cell\" || role == \"header_cell\")\n      return $pos.doc.resolve(pos);\n  }\n  for (let before = $pos.nodeBefore, pos = $pos.pos; before; before = before.lastChild, pos--) {\n    const role = before.type.spec.tableRole;\n    if (role == \"cell\" || role == \"header_cell\")\n      return $pos.doc.resolve(pos - before.nodeSize);\n  }\n}\nfunction pointsAtCell($pos) {\n  return $pos.parent.type.spec.tableRole == \"row\" && !!$pos.nodeAfter;\n}\nfunction moveCellForward($pos) {\n  return $pos.node(0).resolve($pos.pos + $pos.nodeAfter.nodeSize);\n}\nfunction inSameTable($cellA, $cellB) {\n  return $cellA.depth == $cellB.depth && $cellA.pos >= $cellB.start(-1) && $cellA.pos <= $cellB.end(-1);\n}\nfunction findCell($pos) {\n  return TableMap.get($pos.node(-1)).findCell($pos.pos - $pos.start(-1));\n}\nfunction colCount($pos) {\n  return TableMap.get($pos.node(-1)).colCount($pos.pos - $pos.start(-1));\n}\nfunction nextCell($pos, axis, dir) {\n  const table = $pos.node(-1);\n  const map = TableMap.get(table);\n  const tableStart = $pos.start(-1);\n  const moved = map.nextCell($pos.pos - tableStart, axis, dir);\n  return moved == null ? null : $pos.node(0).resolve(tableStart + moved);\n}\nfunction removeColSpan(attrs, pos, n = 1) {\n  const result = { ...attrs, colspan: attrs.colspan - n };\n  if (result.colwidth) {\n    result.colwidth = result.colwidth.slice();\n    result.colwidth.splice(pos, n);\n    if (!result.colwidth.some((w) => w > 0))\n      result.colwidth = null;\n  }\n  return result;\n}\nfunction addColSpan(attrs, pos, n = 1) {\n  const result = { ...attrs, colspan: attrs.colspan + n };\n  if (result.colwidth) {\n    result.colwidth = result.colwidth.slice();\n    for (let i = 0; i < n; i++)\n      result.colwidth.splice(pos, 0, 0);\n  }\n  return result;\n}\nfunction columnIsHeader(map, table, col) {\n  const headerCell = tableNodeTypes(table.type.schema).header_cell;\n  for (let row = 0; row < map.height; row++)\n    if (table.nodeAt(map.map[col + row * map.width]).type != headerCell)\n      return false;\n  return true;\n}\n\n// src/cellselection.ts\nvar CellSelection = class _CellSelection extends prosemirror_state__WEBPACK_IMPORTED_MODULE_0__.Selection {\n  // A table selection is identified by its anchor and head cells. The\n  // positions given to this constructor should point _before_ two\n  // cells in the same table. They may be the same, to select a single\n  // cell.\n  constructor($anchorCell, $headCell = $anchorCell) {\n    const table = $anchorCell.node(-1);\n    const map = TableMap.get(table);\n    const tableStart = $anchorCell.start(-1);\n    const rect = map.rectBetween(\n      $anchorCell.pos - tableStart,\n      $headCell.pos - tableStart\n    );\n    const doc = $anchorCell.node(0);\n    const cells = map.cellsInRect(rect).filter((p) => p != $headCell.pos - tableStart);\n    cells.unshift($headCell.pos - tableStart);\n    const ranges = cells.map((pos) => {\n      const cell = table.nodeAt(pos);\n      if (!cell) {\n        throw RangeError(`No cell with offset ${pos} found`);\n      }\n      const from = tableStart + pos + 1;\n      return new prosemirror_state__WEBPACK_IMPORTED_MODULE_0__.SelectionRange(\n        doc.resolve(from),\n        doc.resolve(from + cell.content.size)\n      );\n    });\n    super(ranges[0].$from, ranges[0].$to, ranges);\n    this.$anchorCell = $anchorCell;\n    this.$headCell = $headCell;\n  }\n  map(doc, mapping) {\n    const $anchorCell = doc.resolve(mapping.map(this.$anchorCell.pos));\n    const $headCell = doc.resolve(mapping.map(this.$headCell.pos));\n    if (pointsAtCell($anchorCell) && pointsAtCell($headCell) && inSameTable($anchorCell, $headCell)) {\n      const tableChanged = this.$anchorCell.node(-1) != $anchorCell.node(-1);\n      if (tableChanged && this.isRowSelection())\n        return _CellSelection.rowSelection($anchorCell, $headCell);\n      else if (tableChanged && this.isColSelection())\n        return _CellSelection.colSelection($anchorCell, $headCell);\n      else\n        return new _CellSelection($anchorCell, $headCell);\n    }\n    return prosemirror_state__WEBPACK_IMPORTED_MODULE_0__.TextSelection.between($anchorCell, $headCell);\n  }\n  // Returns a rectangular slice of table rows containing the selected\n  // cells.\n  content() {\n    const table = this.$anchorCell.node(-1);\n    const map = TableMap.get(table);\n    const tableStart = this.$anchorCell.start(-1);\n    const rect = map.rectBetween(\n      this.$anchorCell.pos - tableStart,\n      this.$headCell.pos - tableStart\n    );\n    const seen = {};\n    const rows = [];\n    for (let row = rect.top; row < rect.bottom; row++) {\n      const rowContent = [];\n      for (let index = row * map.width + rect.left, col = rect.left; col < rect.right; col++, index++) {\n        const pos = map.map[index];\n        if (seen[pos])\n          continue;\n        seen[pos] = true;\n        const cellRect = map.findCell(pos);\n        let cell = table.nodeAt(pos);\n        if (!cell) {\n          throw RangeError(`No cell with offset ${pos} found`);\n        }\n        const extraLeft = rect.left - cellRect.left;\n        const extraRight = cellRect.right - rect.right;\n        if (extraLeft > 0 || extraRight > 0) {\n          let attrs = cell.attrs;\n          if (extraLeft > 0) {\n            attrs = removeColSpan(attrs, 0, extraLeft);\n          }\n          if (extraRight > 0) {\n            attrs = removeColSpan(\n              attrs,\n              attrs.colspan - extraRight,\n              extraRight\n            );\n          }\n          if (cellRect.left < rect.left) {\n            cell = cell.type.createAndFill(attrs);\n            if (!cell) {\n              throw RangeError(\n                `Could not create cell with attrs ${JSON.stringify(attrs)}`\n              );\n            }\n          } else {\n            cell = cell.type.create(attrs, cell.content);\n          }\n        }\n        if (cellRect.top < rect.top || cellRect.bottom > rect.bottom) {\n          const attrs = {\n            ...cell.attrs,\n            rowspan: Math.min(cellRect.bottom, rect.bottom) - Math.max(cellRect.top, rect.top)\n          };\n          if (cellRect.top < rect.top) {\n            cell = cell.type.createAndFill(attrs);\n          } else {\n            cell = cell.type.create(attrs, cell.content);\n          }\n        }\n        rowContent.push(cell);\n      }\n      rows.push(table.child(row).copy(prosemirror_model__WEBPACK_IMPORTED_MODULE_1__.Fragment.from(rowContent)));\n    }\n    const fragment = this.isColSelection() && this.isRowSelection() ? table : rows;\n    return new prosemirror_model__WEBPACK_IMPORTED_MODULE_1__.Slice(prosemirror_model__WEBPACK_IMPORTED_MODULE_1__.Fragment.from(fragment), 1, 1);\n  }\n  replace(tr, content = prosemirror_model__WEBPACK_IMPORTED_MODULE_1__.Slice.empty) {\n    const mapFrom = tr.steps.length, ranges = this.ranges;\n    for (let i = 0; i < ranges.length; i++) {\n      const { $from, $to } = ranges[i], mapping = tr.mapping.slice(mapFrom);\n      tr.replace(\n        mapping.map($from.pos),\n        mapping.map($to.pos),\n        i ? prosemirror_model__WEBPACK_IMPORTED_MODULE_1__.Slice.empty : content\n      );\n    }\n    const sel = prosemirror_state__WEBPACK_IMPORTED_MODULE_0__.Selection.findFrom(\n      tr.doc.resolve(tr.mapping.slice(mapFrom).map(this.to)),\n      -1\n    );\n    if (sel)\n      tr.setSelection(sel);\n  }\n  replaceWith(tr, node) {\n    this.replace(tr, new prosemirror_model__WEBPACK_IMPORTED_MODULE_1__.Slice(prosemirror_model__WEBPACK_IMPORTED_MODULE_1__.Fragment.from(node), 0, 0));\n  }\n  forEachCell(f) {\n    const table = this.$anchorCell.node(-1);\n    const map = TableMap.get(table);\n    const tableStart = this.$anchorCell.start(-1);\n    const cells = map.cellsInRect(\n      map.rectBetween(\n        this.$anchorCell.pos - tableStart,\n        this.$headCell.pos - tableStart\n      )\n    );\n    for (let i = 0; i < cells.length; i++) {\n      f(table.nodeAt(cells[i]), tableStart + cells[i]);\n    }\n  }\n  // True if this selection goes all the way from the top to the\n  // bottom of the table.\n  isColSelection() {\n    const anchorTop = this.$anchorCell.index(-1);\n    const headTop = this.$headCell.index(-1);\n    if (Math.min(anchorTop, headTop) > 0)\n      return false;\n    const anchorBottom = anchorTop + this.$anchorCell.nodeAfter.attrs.rowspan;\n    const headBottom = headTop + this.$headCell.nodeAfter.attrs.rowspan;\n    return Math.max(anchorBottom, headBottom) == this.$headCell.node(-1).childCount;\n  }\n  // Returns the smallest column selection that covers the given anchor\n  // and head cell.\n  static colSelection($anchorCell, $headCell = $anchorCell) {\n    const table = $anchorCell.node(-1);\n    const map = TableMap.get(table);\n    const tableStart = $anchorCell.start(-1);\n    const anchorRect = map.findCell($anchorCell.pos - tableStart);\n    const headRect = map.findCell($headCell.pos - tableStart);\n    const doc = $anchorCell.node(0);\n    if (anchorRect.top <= headRect.top) {\n      if (anchorRect.top > 0)\n        $anchorCell = doc.resolve(tableStart + map.map[anchorRect.left]);\n      if (headRect.bottom < map.height)\n        $headCell = doc.resolve(\n          tableStart + map.map[map.width * (map.height - 1) + headRect.right - 1]\n        );\n    } else {\n      if (headRect.top > 0)\n        $headCell = doc.resolve(tableStart + map.map[headRect.left]);\n      if (anchorRect.bottom < map.height)\n        $anchorCell = doc.resolve(\n          tableStart + map.map[map.width * (map.height - 1) + anchorRect.right - 1]\n        );\n    }\n    return new _CellSelection($anchorCell, $headCell);\n  }\n  // True if this selection goes all the way from the left to the\n  // right of the table.\n  isRowSelection() {\n    const table = this.$anchorCell.node(-1);\n    const map = TableMap.get(table);\n    const tableStart = this.$anchorCell.start(-1);\n    const anchorLeft = map.colCount(this.$anchorCell.pos - tableStart);\n    const headLeft = map.colCount(this.$headCell.pos - tableStart);\n    if (Math.min(anchorLeft, headLeft) > 0)\n      return false;\n    const anchorRight = anchorLeft + this.$anchorCell.nodeAfter.attrs.colspan;\n    const headRight = headLeft + this.$headCell.nodeAfter.attrs.colspan;\n    return Math.max(anchorRight, headRight) == map.width;\n  }\n  eq(other) {\n    return other instanceof _CellSelection && other.$anchorCell.pos == this.$anchorCell.pos && other.$headCell.pos == this.$headCell.pos;\n  }\n  // Returns the smallest row selection that covers the given anchor\n  // and head cell.\n  static rowSelection($anchorCell, $headCell = $anchorCell) {\n    const table = $anchorCell.node(-1);\n    const map = TableMap.get(table);\n    const tableStart = $anchorCell.start(-1);\n    const anchorRect = map.findCell($anchorCell.pos - tableStart);\n    const headRect = map.findCell($headCell.pos - tableStart);\n    const doc = $anchorCell.node(0);\n    if (anchorRect.left <= headRect.left) {\n      if (anchorRect.left > 0)\n        $anchorCell = doc.resolve(\n          tableStart + map.map[anchorRect.top * map.width]\n        );\n      if (headRect.right < map.width)\n        $headCell = doc.resolve(\n          tableStart + map.map[map.width * (headRect.top + 1) - 1]\n        );\n    } else {\n      if (headRect.left > 0)\n        $headCell = doc.resolve(tableStart + map.map[headRect.top * map.width]);\n      if (anchorRect.right < map.width)\n        $anchorCell = doc.resolve(\n          tableStart + map.map[map.width * (anchorRect.top + 1) - 1]\n        );\n    }\n    return new _CellSelection($anchorCell, $headCell);\n  }\n  toJSON() {\n    return {\n      type: \"cell\",\n      anchor: this.$anchorCell.pos,\n      head: this.$headCell.pos\n    };\n  }\n  static fromJSON(doc, json) {\n    return new _CellSelection(doc.resolve(json.anchor), doc.resolve(json.head));\n  }\n  static create(doc, anchorCell, headCell = anchorCell) {\n    return new _CellSelection(doc.resolve(anchorCell), doc.resolve(headCell));\n  }\n  getBookmark() {\n    return new CellBookmark(this.$anchorCell.pos, this.$headCell.pos);\n  }\n};\nCellSelection.prototype.visible = false;\nprosemirror_state__WEBPACK_IMPORTED_MODULE_0__.Selection.jsonID(\"cell\", CellSelection);\nvar CellBookmark = class _CellBookmark {\n  constructor(anchor, head) {\n    this.anchor = anchor;\n    this.head = head;\n  }\n  map(mapping) {\n    return new _CellBookmark(mapping.map(this.anchor), mapping.map(this.head));\n  }\n  resolve(doc) {\n    const $anchorCell = doc.resolve(this.anchor), $headCell = doc.resolve(this.head);\n    if ($anchorCell.parent.type.spec.tableRole == \"row\" && $headCell.parent.type.spec.tableRole == \"row\" && $anchorCell.index() < $anchorCell.parent.childCount && $headCell.index() < $headCell.parent.childCount && inSameTable($anchorCell, $headCell))\n      return new CellSelection($anchorCell, $headCell);\n    else\n      return prosemirror_state__WEBPACK_IMPORTED_MODULE_0__.Selection.near($headCell, 1);\n  }\n};\nfunction drawCellSelection(state) {\n  if (!(state.selection instanceof CellSelection))\n    return null;\n  const cells = [];\n  state.selection.forEachCell((node, pos) => {\n    cells.push(\n      prosemirror_view__WEBPACK_IMPORTED_MODULE_2__.Decoration.node(pos, pos + node.nodeSize, { class: \"selectedCell\" })\n    );\n  });\n  return prosemirror_view__WEBPACK_IMPORTED_MODULE_2__.DecorationSet.create(state.doc, cells);\n}\nfunction isCellBoundarySelection({ $from, $to }) {\n  if ($from.pos == $to.pos || $from.pos < $to.pos - 6)\n    return false;\n  let afterFrom = $from.pos;\n  let beforeTo = $to.pos;\n  let depth = $from.depth;\n  for (; depth >= 0; depth--, afterFrom++)\n    if ($from.after(depth + 1) < $from.end(depth))\n      break;\n  for (let d = $to.depth; d >= 0; d--, beforeTo--)\n    if ($to.before(d + 1) > $to.start(d))\n      break;\n  return afterFrom == beforeTo && /row|table/.test($from.node(depth).type.spec.tableRole);\n}\nfunction isTextSelectionAcrossCells({ $from, $to }) {\n  let fromCellBoundaryNode;\n  let toCellBoundaryNode;\n  for (let i = $from.depth; i > 0; i--) {\n    const node = $from.node(i);\n    if (node.type.spec.tableRole === \"cell\" || node.type.spec.tableRole === \"header_cell\") {\n      fromCellBoundaryNode = node;\n      break;\n    }\n  }\n  for (let i = $to.depth; i > 0; i--) {\n    const node = $to.node(i);\n    if (node.type.spec.tableRole === \"cell\" || node.type.spec.tableRole === \"header_cell\") {\n      toCellBoundaryNode = node;\n      break;\n    }\n  }\n  return fromCellBoundaryNode !== toCellBoundaryNode && $to.parentOffset === 0;\n}\nfunction normalizeSelection(state, tr, allowTableNodeSelection) {\n  const sel = (tr || state).selection;\n  const doc = (tr || state).doc;\n  let normalize;\n  let role;\n  if (sel instanceof prosemirror_state__WEBPACK_IMPORTED_MODULE_0__.NodeSelection && (role = sel.node.type.spec.tableRole)) {\n    if (role == \"cell\" || role == \"header_cell\") {\n      normalize = CellSelection.create(doc, sel.from);\n    } else if (role == \"row\") {\n      const $cell = doc.resolve(sel.from + 1);\n      normalize = CellSelection.rowSelection($cell, $cell);\n    } else if (!allowTableNodeSelection) {\n      const map = TableMap.get(sel.node);\n      const start = sel.from + 1;\n      const lastCell = start + map.map[map.width * map.height - 1];\n      normalize = CellSelection.create(doc, start + 1, lastCell);\n    }\n  } else if (sel instanceof prosemirror_state__WEBPACK_IMPORTED_MODULE_0__.TextSelection && isCellBoundarySelection(sel)) {\n    normalize = prosemirror_state__WEBPACK_IMPORTED_MODULE_0__.TextSelection.create(doc, sel.from);\n  } else if (sel instanceof prosemirror_state__WEBPACK_IMPORTED_MODULE_0__.TextSelection && isTextSelectionAcrossCells(sel)) {\n    normalize = prosemirror_state__WEBPACK_IMPORTED_MODULE_0__.TextSelection.create(doc, sel.$from.start(), sel.$from.end());\n  }\n  if (normalize)\n    (tr || (tr = state.tr)).setSelection(normalize);\n  return tr;\n}\n\n// src/fixtables.ts\n\nvar fixTablesKey = new prosemirror_state__WEBPACK_IMPORTED_MODULE_0__.PluginKey(\"fix-tables\");\nfunction changedDescendants(old, cur, offset, f) {\n  const oldSize = old.childCount, curSize = cur.childCount;\n  outer:\n    for (let i = 0, j = 0; i < curSize; i++) {\n      const child = cur.child(i);\n      for (let scan = j, e = Math.min(oldSize, i + 3); scan < e; scan++) {\n        if (old.child(scan) == child) {\n          j = scan + 1;\n          offset += child.nodeSize;\n          continue outer;\n        }\n      }\n      f(child, offset);\n      if (j < oldSize && old.child(j).sameMarkup(child))\n        changedDescendants(old.child(j), child, offset + 1, f);\n      else\n        child.nodesBetween(0, child.content.size, f, offset + 1);\n      offset += child.nodeSize;\n    }\n}\nfunction fixTables(state, oldState) {\n  let tr;\n  const check = (node, pos) => {\n    if (node.type.spec.tableRole == \"table\")\n      tr = fixTable(state, node, pos, tr);\n  };\n  if (!oldState)\n    state.doc.descendants(check);\n  else if (oldState.doc != state.doc)\n    changedDescendants(oldState.doc, state.doc, 0, check);\n  return tr;\n}\nfunction fixTable(state, table, tablePos, tr) {\n  const map = TableMap.get(table);\n  if (!map.problems)\n    return tr;\n  if (!tr)\n    tr = state.tr;\n  const mustAdd = [];\n  for (let i = 0; i < map.height; i++)\n    mustAdd.push(0);\n  for (let i = 0; i < map.problems.length; i++) {\n    const prob = map.problems[i];\n    if (prob.type == \"collision\") {\n      const cell = table.nodeAt(prob.pos);\n      if (!cell)\n        continue;\n      const attrs = cell.attrs;\n      for (let j = 0; j < attrs.rowspan; j++)\n        mustAdd[prob.row + j] += prob.n;\n      tr.setNodeMarkup(\n        tr.mapping.map(tablePos + 1 + prob.pos),\n        null,\n        removeColSpan(attrs, attrs.colspan - prob.n, prob.n)\n      );\n    } else if (prob.type == \"missing\") {\n      mustAdd[prob.row] += prob.n;\n    } else if (prob.type == \"overlong_rowspan\") {\n      const cell = table.nodeAt(prob.pos);\n      if (!cell)\n        continue;\n      tr.setNodeMarkup(tr.mapping.map(tablePos + 1 + prob.pos), null, {\n        ...cell.attrs,\n        rowspan: cell.attrs.rowspan - prob.n\n      });\n    } else if (prob.type == \"colwidth mismatch\") {\n      const cell = table.nodeAt(prob.pos);\n      if (!cell)\n        continue;\n      tr.setNodeMarkup(tr.mapping.map(tablePos + 1 + prob.pos), null, {\n        ...cell.attrs,\n        colwidth: prob.colwidth\n      });\n    }\n  }\n  let first, last;\n  for (let i = 0; i < mustAdd.length; i++)\n    if (mustAdd[i]) {\n      if (first == null)\n        first = i;\n      last = i;\n    }\n  for (let i = 0, pos = tablePos + 1; i < map.height; i++) {\n    const row = table.child(i);\n    const end = pos + row.nodeSize;\n    const add = mustAdd[i];\n    if (add > 0) {\n      let role = \"cell\";\n      if (row.firstChild) {\n        role = row.firstChild.type.spec.tableRole;\n      }\n      const nodes = [];\n      for (let j = 0; j < add; j++) {\n        const node = tableNodeTypes(state.schema)[role].createAndFill();\n        if (node)\n          nodes.push(node);\n      }\n      const side = (i == 0 || first == i - 1) && last == i ? pos + 1 : end - 1;\n      tr.insert(tr.mapping.map(side), nodes);\n    }\n    pos = end;\n  }\n  return tr.setMeta(fixTablesKey, { fixTables: true });\n}\n\n// src/input.ts\n\n\n\n\n// src/commands.ts\n\n\nfunction selectedRect(state) {\n  const sel = state.selection;\n  const $pos = selectionCell(state);\n  const table = $pos.node(-1);\n  const tableStart = $pos.start(-1);\n  const map = TableMap.get(table);\n  const rect = sel instanceof CellSelection ? map.rectBetween(\n    sel.$anchorCell.pos - tableStart,\n    sel.$headCell.pos - tableStart\n  ) : map.findCell($pos.pos - tableStart);\n  return { ...rect, tableStart, map, table };\n}\nfunction addColumn(tr, { map, tableStart, table }, col) {\n  let refColumn = col > 0 ? -1 : 0;\n  if (columnIsHeader(map, table, col + refColumn)) {\n    refColumn = col == 0 || col == map.width ? null : 0;\n  }\n  for (let row = 0; row < map.height; row++) {\n    const index = row * map.width + col;\n    if (col > 0 && col < map.width && map.map[index - 1] == map.map[index]) {\n      const pos = map.map[index];\n      const cell = table.nodeAt(pos);\n      tr.setNodeMarkup(\n        tr.mapping.map(tableStart + pos),\n        null,\n        addColSpan(cell.attrs, col - map.colCount(pos))\n      );\n      row += cell.attrs.rowspan - 1;\n    } else {\n      const type = refColumn == null ? tableNodeTypes(table.type.schema).cell : table.nodeAt(map.map[index + refColumn]).type;\n      const pos = map.positionAt(row, col, table);\n      tr.insert(tr.mapping.map(tableStart + pos), type.createAndFill());\n    }\n  }\n  return tr;\n}\nfunction addColumnBefore(state, dispatch) {\n  if (!isInTable(state))\n    return false;\n  if (dispatch) {\n    const rect = selectedRect(state);\n    dispatch(addColumn(state.tr, rect, rect.left));\n  }\n  return true;\n}\nfunction addColumnAfter(state, dispatch) {\n  if (!isInTable(state))\n    return false;\n  if (dispatch) {\n    const rect = selectedRect(state);\n    dispatch(addColumn(state.tr, rect, rect.right));\n  }\n  return true;\n}\nfunction removeColumn(tr, { map, table, tableStart }, col) {\n  const mapStart = tr.mapping.maps.length;\n  for (let row = 0; row < map.height; ) {\n    const index = row * map.width + col;\n    const pos = map.map[index];\n    const cell = table.nodeAt(pos);\n    const attrs = cell.attrs;\n    if (col > 0 && map.map[index - 1] == pos || col < map.width - 1 && map.map[index + 1] == pos) {\n      tr.setNodeMarkup(\n        tr.mapping.slice(mapStart).map(tableStart + pos),\n        null,\n        removeColSpan(attrs, col - map.colCount(pos))\n      );\n    } else {\n      const start = tr.mapping.slice(mapStart).map(tableStart + pos);\n      tr.delete(start, start + cell.nodeSize);\n    }\n    row += attrs.rowspan;\n  }\n}\nfunction deleteColumn(state, dispatch) {\n  if (!isInTable(state))\n    return false;\n  if (dispatch) {\n    const rect = selectedRect(state);\n    const tr = state.tr;\n    if (rect.left == 0 && rect.right == rect.map.width)\n      return false;\n    for (let i = rect.right - 1; ; i--) {\n      removeColumn(tr, rect, i);\n      if (i == rect.left)\n        break;\n      const table = rect.tableStart ? tr.doc.nodeAt(rect.tableStart - 1) : tr.doc;\n      if (!table) {\n        throw RangeError(\"No table found\");\n      }\n      rect.table = table;\n      rect.map = TableMap.get(table);\n    }\n    dispatch(tr);\n  }\n  return true;\n}\nfunction rowIsHeader(map, table, row) {\n  var _a;\n  const headerCell = tableNodeTypes(table.type.schema).header_cell;\n  for (let col = 0; col < map.width; col++)\n    if (((_a = table.nodeAt(map.map[col + row * map.width])) == null ? void 0 : _a.type) != headerCell)\n      return false;\n  return true;\n}\nfunction addRow(tr, { map, tableStart, table }, row) {\n  var _a;\n  let rowPos = tableStart;\n  for (let i = 0; i < row; i++)\n    rowPos += table.child(i).nodeSize;\n  const cells = [];\n  let refRow = row > 0 ? -1 : 0;\n  if (rowIsHeader(map, table, row + refRow))\n    refRow = row == 0 || row == map.height ? null : 0;\n  for (let col = 0, index = map.width * row; col < map.width; col++, index++) {\n    if (row > 0 && row < map.height && map.map[index] == map.map[index - map.width]) {\n      const pos = map.map[index];\n      const attrs = table.nodeAt(pos).attrs;\n      tr.setNodeMarkup(tableStart + pos, null, {\n        ...attrs,\n        rowspan: attrs.rowspan + 1\n      });\n      col += attrs.colspan - 1;\n    } else {\n      const type = refRow == null ? tableNodeTypes(table.type.schema).cell : (_a = table.nodeAt(map.map[index + refRow * map.width])) == null ? void 0 : _a.type;\n      const node = type == null ? void 0 : type.createAndFill();\n      if (node)\n        cells.push(node);\n    }\n  }\n  tr.insert(rowPos, tableNodeTypes(table.type.schema).row.create(null, cells));\n  return tr;\n}\nfunction addRowBefore(state, dispatch) {\n  if (!isInTable(state))\n    return false;\n  if (dispatch) {\n    const rect = selectedRect(state);\n    dispatch(addRow(state.tr, rect, rect.top));\n  }\n  return true;\n}\nfunction addRowAfter(state, dispatch) {\n  if (!isInTable(state))\n    return false;\n  if (dispatch) {\n    const rect = selectedRect(state);\n    dispatch(addRow(state.tr, rect, rect.bottom));\n  }\n  return true;\n}\nfunction removeRow(tr, { map, table, tableStart }, row) {\n  let rowPos = 0;\n  for (let i = 0; i < row; i++)\n    rowPos += table.child(i).nodeSize;\n  const nextRow = rowPos + table.child(row).nodeSize;\n  const mapFrom = tr.mapping.maps.length;\n  tr.delete(rowPos + tableStart, nextRow + tableStart);\n  const seen = /* @__PURE__ */ new Set();\n  for (let col = 0, index = row * map.width; col < map.width; col++, index++) {\n    const pos = map.map[index];\n    if (seen.has(pos))\n      continue;\n    seen.add(pos);\n    if (row > 0 && pos == map.map[index - map.width]) {\n      const attrs = table.nodeAt(pos).attrs;\n      tr.setNodeMarkup(tr.mapping.slice(mapFrom).map(pos + tableStart), null, {\n        ...attrs,\n        rowspan: attrs.rowspan - 1\n      });\n      col += attrs.colspan - 1;\n    } else if (row < map.height && pos == map.map[index + map.width]) {\n      const cell = table.nodeAt(pos);\n      const attrs = cell.attrs;\n      const copy = cell.type.create(\n        { ...attrs, rowspan: cell.attrs.rowspan - 1 },\n        cell.content\n      );\n      const newPos = map.positionAt(row + 1, col, table);\n      tr.insert(tr.mapping.slice(mapFrom).map(tableStart + newPos), copy);\n      col += attrs.colspan - 1;\n    }\n  }\n}\nfunction deleteRow(state, dispatch) {\n  if (!isInTable(state))\n    return false;\n  if (dispatch) {\n    const rect = selectedRect(state), tr = state.tr;\n    if (rect.top == 0 && rect.bottom == rect.map.height)\n      return false;\n    for (let i = rect.bottom - 1; ; i--) {\n      removeRow(tr, rect, i);\n      if (i == rect.top)\n        break;\n      const table = rect.tableStart ? tr.doc.nodeAt(rect.tableStart - 1) : tr.doc;\n      if (!table) {\n        throw RangeError(\"No table found\");\n      }\n      rect.table = table;\n      rect.map = TableMap.get(rect.table);\n    }\n    dispatch(tr);\n  }\n  return true;\n}\nfunction isEmpty(cell) {\n  const c = cell.content;\n  return c.childCount == 1 && c.child(0).isTextblock && c.child(0).childCount == 0;\n}\nfunction cellsOverlapRectangle({ width, height, map }, rect) {\n  let indexTop = rect.top * width + rect.left, indexLeft = indexTop;\n  let indexBottom = (rect.bottom - 1) * width + rect.left, indexRight = indexTop + (rect.right - rect.left - 1);\n  for (let i = rect.top; i < rect.bottom; i++) {\n    if (rect.left > 0 && map[indexLeft] == map[indexLeft - 1] || rect.right < width && map[indexRight] == map[indexRight + 1])\n      return true;\n    indexLeft += width;\n    indexRight += width;\n  }\n  for (let i = rect.left; i < rect.right; i++) {\n    if (rect.top > 0 && map[indexTop] == map[indexTop - width] || rect.bottom < height && map[indexBottom] == map[indexBottom + width])\n      return true;\n    indexTop++;\n    indexBottom++;\n  }\n  return false;\n}\nfunction mergeCells(state, dispatch) {\n  const sel = state.selection;\n  if (!(sel instanceof CellSelection) || sel.$anchorCell.pos == sel.$headCell.pos)\n    return false;\n  const rect = selectedRect(state), { map } = rect;\n  if (cellsOverlapRectangle(map, rect))\n    return false;\n  if (dispatch) {\n    const tr = state.tr;\n    const seen = {};\n    let content = prosemirror_model__WEBPACK_IMPORTED_MODULE_1__.Fragment.empty;\n    let mergedPos;\n    let mergedCell;\n    for (let row = rect.top; row < rect.bottom; row++) {\n      for (let col = rect.left; col < rect.right; col++) {\n        const cellPos = map.map[row * map.width + col];\n        const cell = rect.table.nodeAt(cellPos);\n        if (seen[cellPos] || !cell)\n          continue;\n        seen[cellPos] = true;\n        if (mergedPos == null) {\n          mergedPos = cellPos;\n          mergedCell = cell;\n        } else {\n          if (!isEmpty(cell))\n            content = content.append(cell.content);\n          const mapped = tr.mapping.map(cellPos + rect.tableStart);\n          tr.delete(mapped, mapped + cell.nodeSize);\n        }\n      }\n    }\n    if (mergedPos == null || mergedCell == null) {\n      return true;\n    }\n    tr.setNodeMarkup(mergedPos + rect.tableStart, null, {\n      ...addColSpan(\n        mergedCell.attrs,\n        mergedCell.attrs.colspan,\n        rect.right - rect.left - mergedCell.attrs.colspan\n      ),\n      rowspan: rect.bottom - rect.top\n    });\n    if (content.size) {\n      const end = mergedPos + 1 + mergedCell.content.size;\n      const start = isEmpty(mergedCell) ? mergedPos + 1 : end;\n      tr.replaceWith(start + rect.tableStart, end + rect.tableStart, content);\n    }\n    tr.setSelection(\n      new CellSelection(tr.doc.resolve(mergedPos + rect.tableStart))\n    );\n    dispatch(tr);\n  }\n  return true;\n}\nfunction splitCell(state, dispatch) {\n  const nodeTypes = tableNodeTypes(state.schema);\n  return splitCellWithType(({ node }) => {\n    return nodeTypes[node.type.spec.tableRole];\n  })(state, dispatch);\n}\nfunction splitCellWithType(getCellType) {\n  return (state, dispatch) => {\n    var _a;\n    const sel = state.selection;\n    let cellNode;\n    let cellPos;\n    if (!(sel instanceof CellSelection)) {\n      cellNode = cellWrapping(sel.$from);\n      if (!cellNode)\n        return false;\n      cellPos = (_a = cellAround(sel.$from)) == null ? void 0 : _a.pos;\n    } else {\n      if (sel.$anchorCell.pos != sel.$headCell.pos)\n        return false;\n      cellNode = sel.$anchorCell.nodeAfter;\n      cellPos = sel.$anchorCell.pos;\n    }\n    if (cellNode == null || cellPos == null) {\n      return false;\n    }\n    if (cellNode.attrs.colspan == 1 && cellNode.attrs.rowspan == 1) {\n      return false;\n    }\n    if (dispatch) {\n      let baseAttrs = cellNode.attrs;\n      const attrs = [];\n      const colwidth = baseAttrs.colwidth;\n      if (baseAttrs.rowspan > 1)\n        baseAttrs = { ...baseAttrs, rowspan: 1 };\n      if (baseAttrs.colspan > 1)\n        baseAttrs = { ...baseAttrs, colspan: 1 };\n      const rect = selectedRect(state), tr = state.tr;\n      for (let i = 0; i < rect.right - rect.left; i++)\n        attrs.push(\n          colwidth ? {\n            ...baseAttrs,\n            colwidth: colwidth && colwidth[i] ? [colwidth[i]] : null\n          } : baseAttrs\n        );\n      let lastCell;\n      for (let row = rect.top; row < rect.bottom; row++) {\n        let pos = rect.map.positionAt(row, rect.left, rect.table);\n        if (row == rect.top)\n          pos += cellNode.nodeSize;\n        for (let col = rect.left, i = 0; col < rect.right; col++, i++) {\n          if (col == rect.left && row == rect.top)\n            continue;\n          tr.insert(\n            lastCell = tr.mapping.map(pos + rect.tableStart, 1),\n            getCellType({ node: cellNode, row, col }).createAndFill(attrs[i])\n          );\n        }\n      }\n      tr.setNodeMarkup(\n        cellPos,\n        getCellType({ node: cellNode, row: rect.top, col: rect.left }),\n        attrs[0]\n      );\n      if (sel instanceof CellSelection)\n        tr.setSelection(\n          new CellSelection(\n            tr.doc.resolve(sel.$anchorCell.pos),\n            lastCell ? tr.doc.resolve(lastCell) : void 0\n          )\n        );\n      dispatch(tr);\n    }\n    return true;\n  };\n}\nfunction setCellAttr(name, value) {\n  return function(state, dispatch) {\n    if (!isInTable(state))\n      return false;\n    const $cell = selectionCell(state);\n    if ($cell.nodeAfter.attrs[name] === value)\n      return false;\n    if (dispatch) {\n      const tr = state.tr;\n      if (state.selection instanceof CellSelection)\n        state.selection.forEachCell((node, pos) => {\n          if (node.attrs[name] !== value)\n            tr.setNodeMarkup(pos, null, {\n              ...node.attrs,\n              [name]: value\n            });\n        });\n      else\n        tr.setNodeMarkup($cell.pos, null, {\n          ...$cell.nodeAfter.attrs,\n          [name]: value\n        });\n      dispatch(tr);\n    }\n    return true;\n  };\n}\nfunction deprecated_toggleHeader(type) {\n  return function(state, dispatch) {\n    if (!isInTable(state))\n      return false;\n    if (dispatch) {\n      const types = tableNodeTypes(state.schema);\n      const rect = selectedRect(state), tr = state.tr;\n      const cells = rect.map.cellsInRect(\n        type == \"column\" ? {\n          left: rect.left,\n          top: 0,\n          right: rect.right,\n          bottom: rect.map.height\n        } : type == \"row\" ? {\n          left: 0,\n          top: rect.top,\n          right: rect.map.width,\n          bottom: rect.bottom\n        } : rect\n      );\n      const nodes = cells.map((pos) => rect.table.nodeAt(pos));\n      for (let i = 0; i < cells.length; i++)\n        if (nodes[i].type == types.header_cell)\n          tr.setNodeMarkup(\n            rect.tableStart + cells[i],\n            types.cell,\n            nodes[i].attrs\n          );\n      if (tr.steps.length == 0)\n        for (let i = 0; i < cells.length; i++)\n          tr.setNodeMarkup(\n            rect.tableStart + cells[i],\n            types.header_cell,\n            nodes[i].attrs\n          );\n      dispatch(tr);\n    }\n    return true;\n  };\n}\nfunction isHeaderEnabledByType(type, rect, types) {\n  const cellPositions = rect.map.cellsInRect({\n    left: 0,\n    top: 0,\n    right: type == \"row\" ? rect.map.width : 1,\n    bottom: type == \"column\" ? rect.map.height : 1\n  });\n  for (let i = 0; i < cellPositions.length; i++) {\n    const cell = rect.table.nodeAt(cellPositions[i]);\n    if (cell && cell.type !== types.header_cell) {\n      return false;\n    }\n  }\n  return true;\n}\nfunction toggleHeader(type, options) {\n  options = options || { useDeprecatedLogic: false };\n  if (options.useDeprecatedLogic)\n    return deprecated_toggleHeader(type);\n  return function(state, dispatch) {\n    if (!isInTable(state))\n      return false;\n    if (dispatch) {\n      const types = tableNodeTypes(state.schema);\n      const rect = selectedRect(state), tr = state.tr;\n      const isHeaderRowEnabled = isHeaderEnabledByType(\"row\", rect, types);\n      const isHeaderColumnEnabled = isHeaderEnabledByType(\n        \"column\",\n        rect,\n        types\n      );\n      const isHeaderEnabled = type === \"column\" ? isHeaderRowEnabled : type === \"row\" ? isHeaderColumnEnabled : false;\n      const selectionStartsAt = isHeaderEnabled ? 1 : 0;\n      const cellsRect = type == \"column\" ? {\n        left: 0,\n        top: selectionStartsAt,\n        right: 1,\n        bottom: rect.map.height\n      } : type == \"row\" ? {\n        left: selectionStartsAt,\n        top: 0,\n        right: rect.map.width,\n        bottom: 1\n      } : rect;\n      const newType = type == \"column\" ? isHeaderColumnEnabled ? types.cell : types.header_cell : type == \"row\" ? isHeaderRowEnabled ? types.cell : types.header_cell : types.cell;\n      rect.map.cellsInRect(cellsRect).forEach((relativeCellPos) => {\n        const cellPos = relativeCellPos + rect.tableStart;\n        const cell = tr.doc.nodeAt(cellPos);\n        if (cell) {\n          tr.setNodeMarkup(cellPos, newType, cell.attrs);\n        }\n      });\n      dispatch(tr);\n    }\n    return true;\n  };\n}\nvar toggleHeaderRow = toggleHeader(\"row\", {\n  useDeprecatedLogic: true\n});\nvar toggleHeaderColumn = toggleHeader(\"column\", {\n  useDeprecatedLogic: true\n});\nvar toggleHeaderCell = toggleHeader(\"cell\", {\n  useDeprecatedLogic: true\n});\nfunction findNextCell($cell, dir) {\n  if (dir < 0) {\n    const before = $cell.nodeBefore;\n    if (before)\n      return $cell.pos - before.nodeSize;\n    for (let row = $cell.index(-1) - 1, rowEnd = $cell.before(); row >= 0; row--) {\n      const rowNode = $cell.node(-1).child(row);\n      const lastChild = rowNode.lastChild;\n      if (lastChild) {\n        return rowEnd - 1 - lastChild.nodeSize;\n      }\n      rowEnd -= rowNode.nodeSize;\n    }\n  } else {\n    if ($cell.index() < $cell.parent.childCount - 1) {\n      return $cell.pos + $cell.nodeAfter.nodeSize;\n    }\n    const table = $cell.node(-1);\n    for (let row = $cell.indexAfter(-1), rowStart = $cell.after(); row < table.childCount; row++) {\n      const rowNode = table.child(row);\n      if (rowNode.childCount)\n        return rowStart + 1;\n      rowStart += rowNode.nodeSize;\n    }\n  }\n  return null;\n}\nfunction goToNextCell(direction) {\n  return function(state, dispatch) {\n    if (!isInTable(state))\n      return false;\n    const cell = findNextCell(selectionCell(state), direction);\n    if (cell == null)\n      return false;\n    if (dispatch) {\n      const $cell = state.doc.resolve(cell);\n      dispatch(\n        state.tr.setSelection(prosemirror_state__WEBPACK_IMPORTED_MODULE_0__.TextSelection.between($cell, moveCellForward($cell))).scrollIntoView()\n      );\n    }\n    return true;\n  };\n}\nfunction deleteTable(state, dispatch) {\n  const $pos = state.selection.$anchor;\n  for (let d = $pos.depth; d > 0; d--) {\n    const node = $pos.node(d);\n    if (node.type.spec.tableRole == \"table\") {\n      if (dispatch)\n        dispatch(\n          state.tr.delete($pos.before(d), $pos.after(d)).scrollIntoView()\n        );\n      return true;\n    }\n  }\n  return false;\n}\nfunction deleteCellSelection(state, dispatch) {\n  const sel = state.selection;\n  if (!(sel instanceof CellSelection))\n    return false;\n  if (dispatch) {\n    const tr = state.tr;\n    const baseContent = tableNodeTypes(state.schema).cell.createAndFill().content;\n    sel.forEachCell((cell, pos) => {\n      if (!cell.content.eq(baseContent))\n        tr.replace(\n          tr.mapping.map(pos + 1),\n          tr.mapping.map(pos + cell.nodeSize - 1),\n          new prosemirror_model__WEBPACK_IMPORTED_MODULE_1__.Slice(baseContent, 0, 0)\n        );\n    });\n    if (tr.docChanged)\n      dispatch(tr);\n  }\n  return true;\n}\n\n// src/copypaste.ts\n\n\nfunction pastedCells(slice) {\n  if (!slice.size)\n    return null;\n  let { content, openStart, openEnd } = slice;\n  while (content.childCount == 1 && (openStart > 0 && openEnd > 0 || content.child(0).type.spec.tableRole == \"table\")) {\n    openStart--;\n    openEnd--;\n    content = content.child(0).content;\n  }\n  const first = content.child(0);\n  const role = first.type.spec.tableRole;\n  const schema = first.type.schema, rows = [];\n  if (role == \"row\") {\n    for (let i = 0; i < content.childCount; i++) {\n      let cells = content.child(i).content;\n      const left = i ? 0 : Math.max(0, openStart - 1);\n      const right = i < content.childCount - 1 ? 0 : Math.max(0, openEnd - 1);\n      if (left || right)\n        cells = fitSlice(\n          tableNodeTypes(schema).row,\n          new prosemirror_model__WEBPACK_IMPORTED_MODULE_1__.Slice(cells, left, right)\n        ).content;\n      rows.push(cells);\n    }\n  } else if (role == \"cell\" || role == \"header_cell\") {\n    rows.push(\n      openStart || openEnd ? fitSlice(\n        tableNodeTypes(schema).row,\n        new prosemirror_model__WEBPACK_IMPORTED_MODULE_1__.Slice(content, openStart, openEnd)\n      ).content : content\n    );\n  } else {\n    return null;\n  }\n  return ensureRectangular(schema, rows);\n}\nfunction ensureRectangular(schema, rows) {\n  const widths = [];\n  for (let i = 0; i < rows.length; i++) {\n    const row = rows[i];\n    for (let j = row.childCount - 1; j >= 0; j--) {\n      const { rowspan, colspan } = row.child(j).attrs;\n      for (let r = i; r < i + rowspan; r++)\n        widths[r] = (widths[r] || 0) + colspan;\n    }\n  }\n  let width = 0;\n  for (let r = 0; r < widths.length; r++)\n    width = Math.max(width, widths[r]);\n  for (let r = 0; r < widths.length; r++) {\n    if (r >= rows.length)\n      rows.push(prosemirror_model__WEBPACK_IMPORTED_MODULE_1__.Fragment.empty);\n    if (widths[r] < width) {\n      const empty = tableNodeTypes(schema).cell.createAndFill();\n      const cells = [];\n      for (let i = widths[r]; i < width; i++) {\n        cells.push(empty);\n      }\n      rows[r] = rows[r].append(prosemirror_model__WEBPACK_IMPORTED_MODULE_1__.Fragment.from(cells));\n    }\n  }\n  return { height: rows.length, width, rows };\n}\nfunction fitSlice(nodeType, slice) {\n  const node = nodeType.createAndFill();\n  const tr = new prosemirror_transform__WEBPACK_IMPORTED_MODULE_3__.Transform(node).replace(0, node.content.size, slice);\n  return tr.doc;\n}\nfunction clipCells({ width, height, rows }, newWidth, newHeight) {\n  if (width != newWidth) {\n    const added = [];\n    const newRows = [];\n    for (let row = 0; row < rows.length; row++) {\n      const frag = rows[row], cells = [];\n      for (let col = added[row] || 0, i = 0; col < newWidth; i++) {\n        let cell = frag.child(i % frag.childCount);\n        if (col + cell.attrs.colspan > newWidth)\n          cell = cell.type.createChecked(\n            removeColSpan(\n              cell.attrs,\n              cell.attrs.colspan,\n              col + cell.attrs.colspan - newWidth\n            ),\n            cell.content\n          );\n        cells.push(cell);\n        col += cell.attrs.colspan;\n        for (let j = 1; j < cell.attrs.rowspan; j++)\n          added[row + j] = (added[row + j] || 0) + cell.attrs.colspan;\n      }\n      newRows.push(prosemirror_model__WEBPACK_IMPORTED_MODULE_1__.Fragment.from(cells));\n    }\n    rows = newRows;\n    width = newWidth;\n  }\n  if (height != newHeight) {\n    const newRows = [];\n    for (let row = 0, i = 0; row < newHeight; row++, i++) {\n      const cells = [], source = rows[i % height];\n      for (let j = 0; j < source.childCount; j++) {\n        let cell = source.child(j);\n        if (row + cell.attrs.rowspan > newHeight)\n          cell = cell.type.create(\n            {\n              ...cell.attrs,\n              rowspan: Math.max(1, newHeight - cell.attrs.rowspan)\n            },\n            cell.content\n          );\n        cells.push(cell);\n      }\n      newRows.push(prosemirror_model__WEBPACK_IMPORTED_MODULE_1__.Fragment.from(cells));\n    }\n    rows = newRows;\n    height = newHeight;\n  }\n  return { width, height, rows };\n}\nfunction growTable(tr, map, table, start, width, height, mapFrom) {\n  const schema = tr.doc.type.schema;\n  const types = tableNodeTypes(schema);\n  let empty;\n  let emptyHead;\n  if (width > map.width) {\n    for (let row = 0, rowEnd = 0; row < map.height; row++) {\n      const rowNode = table.child(row);\n      rowEnd += rowNode.nodeSize;\n      const cells = [];\n      let add;\n      if (rowNode.lastChild == null || rowNode.lastChild.type == types.cell)\n        add = empty || (empty = types.cell.createAndFill());\n      else\n        add = emptyHead || (emptyHead = types.header_cell.createAndFill());\n      for (let i = map.width; i < width; i++)\n        cells.push(add);\n      tr.insert(tr.mapping.slice(mapFrom).map(rowEnd - 1 + start), cells);\n    }\n  }\n  if (height > map.height) {\n    const cells = [];\n    for (let i = 0, start2 = (map.height - 1) * map.width; i < Math.max(map.width, width); i++) {\n      const header = i >= map.width ? false : table.nodeAt(map.map[start2 + i]).type == types.header_cell;\n      cells.push(\n        header ? emptyHead || (emptyHead = types.header_cell.createAndFill()) : empty || (empty = types.cell.createAndFill())\n      );\n    }\n    const emptyRow = types.row.create(null, prosemirror_model__WEBPACK_IMPORTED_MODULE_1__.Fragment.from(cells)), rows = [];\n    for (let i = map.height; i < height; i++)\n      rows.push(emptyRow);\n    tr.insert(tr.mapping.slice(mapFrom).map(start + table.nodeSize - 2), rows);\n  }\n  return !!(empty || emptyHead);\n}\nfunction isolateHorizontal(tr, map, table, start, left, right, top, mapFrom) {\n  if (top == 0 || top == map.height)\n    return false;\n  let found = false;\n  for (let col = left; col < right; col++) {\n    const index = top * map.width + col, pos = map.map[index];\n    if (map.map[index - map.width] == pos) {\n      found = true;\n      const cell = table.nodeAt(pos);\n      const { top: cellTop, left: cellLeft } = map.findCell(pos);\n      tr.setNodeMarkup(tr.mapping.slice(mapFrom).map(pos + start), null, {\n        ...cell.attrs,\n        rowspan: top - cellTop\n      });\n      tr.insert(\n        tr.mapping.slice(mapFrom).map(map.positionAt(top, cellLeft, table)),\n        cell.type.createAndFill({\n          ...cell.attrs,\n          rowspan: cellTop + cell.attrs.rowspan - top\n        })\n      );\n      col += cell.attrs.colspan - 1;\n    }\n  }\n  return found;\n}\nfunction isolateVertical(tr, map, table, start, top, bottom, left, mapFrom) {\n  if (left == 0 || left == map.width)\n    return false;\n  let found = false;\n  for (let row = top; row < bottom; row++) {\n    const index = row * map.width + left, pos = map.map[index];\n    if (map.map[index - 1] == pos) {\n      found = true;\n      const cell = table.nodeAt(pos);\n      const cellLeft = map.colCount(pos);\n      const updatePos = tr.mapping.slice(mapFrom).map(pos + start);\n      tr.setNodeMarkup(\n        updatePos,\n        null,\n        removeColSpan(\n          cell.attrs,\n          left - cellLeft,\n          cell.attrs.colspan - (left - cellLeft)\n        )\n      );\n      tr.insert(\n        updatePos + cell.nodeSize,\n        cell.type.createAndFill(\n          removeColSpan(cell.attrs, 0, left - cellLeft)\n        )\n      );\n      row += cell.attrs.rowspan - 1;\n    }\n  }\n  return found;\n}\nfunction insertCells(state, dispatch, tableStart, rect, cells) {\n  let table = tableStart ? state.doc.nodeAt(tableStart - 1) : state.doc;\n  if (!table) {\n    throw new Error(\"No table found\");\n  }\n  let map = TableMap.get(table);\n  const { top, left } = rect;\n  const right = left + cells.width, bottom = top + cells.height;\n  const tr = state.tr;\n  let mapFrom = 0;\n  function recomp() {\n    table = tableStart ? tr.doc.nodeAt(tableStart - 1) : tr.doc;\n    if (!table) {\n      throw new Error(\"No table found\");\n    }\n    map = TableMap.get(table);\n    mapFrom = tr.mapping.maps.length;\n  }\n  if (growTable(tr, map, table, tableStart, right, bottom, mapFrom))\n    recomp();\n  if (isolateHorizontal(tr, map, table, tableStart, left, right, top, mapFrom))\n    recomp();\n  if (isolateHorizontal(tr, map, table, tableStart, left, right, bottom, mapFrom))\n    recomp();\n  if (isolateVertical(tr, map, table, tableStart, top, bottom, left, mapFrom))\n    recomp();\n  if (isolateVertical(tr, map, table, tableStart, top, bottom, right, mapFrom))\n    recomp();\n  for (let row = top; row < bottom; row++) {\n    const from = map.positionAt(row, left, table), to = map.positionAt(row, right, table);\n    tr.replace(\n      tr.mapping.slice(mapFrom).map(from + tableStart),\n      tr.mapping.slice(mapFrom).map(to + tableStart),\n      new prosemirror_model__WEBPACK_IMPORTED_MODULE_1__.Slice(cells.rows[row - top], 0, 0)\n    );\n  }\n  recomp();\n  tr.setSelection(\n    new CellSelection(\n      tr.doc.resolve(tableStart + map.positionAt(top, left, table)),\n      tr.doc.resolve(tableStart + map.positionAt(bottom - 1, right - 1, table))\n    )\n  );\n  dispatch(tr);\n}\n\n// src/input.ts\nvar handleKeyDown = (0,prosemirror_keymap__WEBPACK_IMPORTED_MODULE_4__.keydownHandler)({\n  ArrowLeft: arrow(\"horiz\", -1),\n  ArrowRight: arrow(\"horiz\", 1),\n  ArrowUp: arrow(\"vert\", -1),\n  ArrowDown: arrow(\"vert\", 1),\n  \"Shift-ArrowLeft\": shiftArrow(\"horiz\", -1),\n  \"Shift-ArrowRight\": shiftArrow(\"horiz\", 1),\n  \"Shift-ArrowUp\": shiftArrow(\"vert\", -1),\n  \"Shift-ArrowDown\": shiftArrow(\"vert\", 1),\n  Backspace: deleteCellSelection,\n  \"Mod-Backspace\": deleteCellSelection,\n  Delete: deleteCellSelection,\n  \"Mod-Delete\": deleteCellSelection\n});\nfunction maybeSetSelection(state, dispatch, selection) {\n  if (selection.eq(state.selection))\n    return false;\n  if (dispatch)\n    dispatch(state.tr.setSelection(selection).scrollIntoView());\n  return true;\n}\nfunction arrow(axis, dir) {\n  return (state, dispatch, view) => {\n    if (!view)\n      return false;\n    const sel = state.selection;\n    if (sel instanceof CellSelection) {\n      return maybeSetSelection(\n        state,\n        dispatch,\n        prosemirror_state__WEBPACK_IMPORTED_MODULE_0__.Selection.near(sel.$headCell, dir)\n      );\n    }\n    if (axis != \"horiz\" && !sel.empty)\n      return false;\n    const end = atEndOfCell(view, axis, dir);\n    if (end == null)\n      return false;\n    if (axis == \"horiz\") {\n      return maybeSetSelection(\n        state,\n        dispatch,\n        prosemirror_state__WEBPACK_IMPORTED_MODULE_0__.Selection.near(state.doc.resolve(sel.head + dir), dir)\n      );\n    } else {\n      const $cell = state.doc.resolve(end);\n      const $next = nextCell($cell, axis, dir);\n      let newSel;\n      if ($next)\n        newSel = prosemirror_state__WEBPACK_IMPORTED_MODULE_0__.Selection.near($next, 1);\n      else if (dir < 0)\n        newSel = prosemirror_state__WEBPACK_IMPORTED_MODULE_0__.Selection.near(state.doc.resolve($cell.before(-1)), -1);\n      else\n        newSel = prosemirror_state__WEBPACK_IMPORTED_MODULE_0__.Selection.near(state.doc.resolve($cell.after(-1)), 1);\n      return maybeSetSelection(state, dispatch, newSel);\n    }\n  };\n}\nfunction shiftArrow(axis, dir) {\n  return (state, dispatch, view) => {\n    if (!view)\n      return false;\n    const sel = state.selection;\n    let cellSel;\n    if (sel instanceof CellSelection) {\n      cellSel = sel;\n    } else {\n      const end = atEndOfCell(view, axis, dir);\n      if (end == null)\n        return false;\n      cellSel = new CellSelection(state.doc.resolve(end));\n    }\n    const $head = nextCell(cellSel.$headCell, axis, dir);\n    if (!$head)\n      return false;\n    return maybeSetSelection(\n      state,\n      dispatch,\n      new CellSelection(cellSel.$anchorCell, $head)\n    );\n  };\n}\nfunction handleTripleClick(view, pos) {\n  const doc = view.state.doc, $cell = cellAround(doc.resolve(pos));\n  if (!$cell)\n    return false;\n  view.dispatch(view.state.tr.setSelection(new CellSelection($cell)));\n  return true;\n}\nfunction handlePaste(view, _, slice) {\n  if (!isInTable(view.state))\n    return false;\n  let cells = pastedCells(slice);\n  const sel = view.state.selection;\n  if (sel instanceof CellSelection) {\n    if (!cells)\n      cells = {\n        width: 1,\n        height: 1,\n        rows: [\n          prosemirror_model__WEBPACK_IMPORTED_MODULE_1__.Fragment.from(\n            fitSlice(tableNodeTypes(view.state.schema).cell, slice)\n          )\n        ]\n      };\n    const table = sel.$anchorCell.node(-1);\n    const start = sel.$anchorCell.start(-1);\n    const rect = TableMap.get(table).rectBetween(\n      sel.$anchorCell.pos - start,\n      sel.$headCell.pos - start\n    );\n    cells = clipCells(cells, rect.right - rect.left, rect.bottom - rect.top);\n    insertCells(view.state, view.dispatch, start, rect, cells);\n    return true;\n  } else if (cells) {\n    const $cell = selectionCell(view.state);\n    const start = $cell.start(-1);\n    insertCells(\n      view.state,\n      view.dispatch,\n      start,\n      TableMap.get($cell.node(-1)).findCell($cell.pos - start),\n      cells\n    );\n    return true;\n  } else {\n    return false;\n  }\n}\nfunction handleMouseDown(view, startEvent) {\n  var _a;\n  if (startEvent.ctrlKey || startEvent.metaKey)\n    return;\n  const startDOMCell = domInCell(view, startEvent.target);\n  let $anchor;\n  if (startEvent.shiftKey && view.state.selection instanceof CellSelection) {\n    setCellSelection(view.state.selection.$anchorCell, startEvent);\n    startEvent.preventDefault();\n  } else if (startEvent.shiftKey && startDOMCell && ($anchor = cellAround(view.state.selection.$anchor)) != null && ((_a = cellUnderMouse(view, startEvent)) == null ? void 0 : _a.pos) != $anchor.pos) {\n    setCellSelection($anchor, startEvent);\n    startEvent.preventDefault();\n  } else if (!startDOMCell) {\n    return;\n  }\n  function setCellSelection($anchor2, event) {\n    let $head = cellUnderMouse(view, event);\n    const starting = tableEditingKey.getState(view.state) == null;\n    if (!$head || !inSameTable($anchor2, $head)) {\n      if (starting)\n        $head = $anchor2;\n      else\n        return;\n    }\n    const selection = new CellSelection($anchor2, $head);\n    if (starting || !view.state.selection.eq(selection)) {\n      const tr = view.state.tr.setSelection(selection);\n      if (starting)\n        tr.setMeta(tableEditingKey, $anchor2.pos);\n      view.dispatch(tr);\n    }\n  }\n  function stop() {\n    view.root.removeEventListener(\"mouseup\", stop);\n    view.root.removeEventListener(\"dragstart\", stop);\n    view.root.removeEventListener(\"mousemove\", move);\n    if (tableEditingKey.getState(view.state) != null)\n      view.dispatch(view.state.tr.setMeta(tableEditingKey, -1));\n  }\n  function move(_event) {\n    const event = _event;\n    const anchor = tableEditingKey.getState(view.state);\n    let $anchor2;\n    if (anchor != null) {\n      $anchor2 = view.state.doc.resolve(anchor);\n    } else if (domInCell(view, event.target) != startDOMCell) {\n      $anchor2 = cellUnderMouse(view, startEvent);\n      if (!$anchor2)\n        return stop();\n    }\n    if ($anchor2)\n      setCellSelection($anchor2, event);\n  }\n  view.root.addEventListener(\"mouseup\", stop);\n  view.root.addEventListener(\"dragstart\", stop);\n  view.root.addEventListener(\"mousemove\", move);\n}\nfunction atEndOfCell(view, axis, dir) {\n  if (!(view.state.selection instanceof prosemirror_state__WEBPACK_IMPORTED_MODULE_0__.TextSelection))\n    return null;\n  const { $head } = view.state.selection;\n  for (let d = $head.depth - 1; d >= 0; d--) {\n    const parent = $head.node(d), index = dir < 0 ? $head.index(d) : $head.indexAfter(d);\n    if (index != (dir < 0 ? 0 : parent.childCount))\n      return null;\n    if (parent.type.spec.tableRole == \"cell\" || parent.type.spec.tableRole == \"header_cell\") {\n      const cellPos = $head.before(d);\n      const dirStr = axis == \"vert\" ? dir > 0 ? \"down\" : \"up\" : dir > 0 ? \"right\" : \"left\";\n      return view.endOfTextblock(dirStr) ? cellPos : null;\n    }\n  }\n  return null;\n}\nfunction domInCell(view, dom) {\n  for (; dom && dom != view.dom; dom = dom.parentNode) {\n    if (dom.nodeName == \"TD\" || dom.nodeName == \"TH\") {\n      return dom;\n    }\n  }\n  return null;\n}\nfunction cellUnderMouse(view, event) {\n  const mousePos = view.posAtCoords({\n    left: event.clientX,\n    top: event.clientY\n  });\n  if (!mousePos)\n    return null;\n  return mousePos ? cellAround(view.state.doc.resolve(mousePos.pos)) : null;\n}\n\n// src/columnresizing.ts\n\n\n\n// src/tableview.ts\nvar TableView = class {\n  constructor(node, defaultCellMinWidth) {\n    this.node = node;\n    this.defaultCellMinWidth = defaultCellMinWidth;\n    this.dom = document.createElement(\"div\");\n    this.dom.className = \"tableWrapper\";\n    this.table = this.dom.appendChild(document.createElement(\"table\"));\n    this.table.style.setProperty(\n      \"--default-cell-min-width\",\n      `${defaultCellMinWidth}px`\n    );\n    this.colgroup = this.table.appendChild(document.createElement(\"colgroup\"));\n    updateColumnsOnResize(node, this.colgroup, this.table, defaultCellMinWidth);\n    this.contentDOM = this.table.appendChild(document.createElement(\"tbody\"));\n  }\n  update(node) {\n    if (node.type != this.node.type)\n      return false;\n    this.node = node;\n    updateColumnsOnResize(\n      node,\n      this.colgroup,\n      this.table,\n      this.defaultCellMinWidth\n    );\n    return true;\n  }\n  ignoreMutation(record) {\n    return record.type == \"attributes\" && (record.target == this.table || this.colgroup.contains(record.target));\n  }\n};\nfunction updateColumnsOnResize(node, colgroup, table, defaultCellMinWidth, overrideCol, overrideValue) {\n  var _a;\n  let totalWidth = 0;\n  let fixedWidth = true;\n  let nextDOM = colgroup.firstChild;\n  const row = node.firstChild;\n  if (!row)\n    return;\n  for (let i = 0, col = 0; i < row.childCount; i++) {\n    const { colspan, colwidth } = row.child(i).attrs;\n    for (let j = 0; j < colspan; j++, col++) {\n      const hasWidth = overrideCol == col ? overrideValue : colwidth && colwidth[j];\n      const cssWidth = hasWidth ? hasWidth + \"px\" : \"\";\n      totalWidth += hasWidth || defaultCellMinWidth;\n      if (!hasWidth)\n        fixedWidth = false;\n      if (!nextDOM) {\n        const col2 = document.createElement(\"col\");\n        col2.style.width = cssWidth;\n        colgroup.appendChild(col2);\n      } else {\n        if (nextDOM.style.width != cssWidth) {\n          nextDOM.style.width = cssWidth;\n        }\n        nextDOM = nextDOM.nextSibling;\n      }\n    }\n  }\n  while (nextDOM) {\n    const after = nextDOM.nextSibling;\n    (_a = nextDOM.parentNode) == null ? void 0 : _a.removeChild(nextDOM);\n    nextDOM = after;\n  }\n  if (fixedWidth) {\n    table.style.width = totalWidth + \"px\";\n    table.style.minWidth = \"\";\n  } else {\n    table.style.width = \"\";\n    table.style.minWidth = totalWidth + \"px\";\n  }\n}\n\n// src/columnresizing.ts\nvar columnResizingPluginKey = new prosemirror_state__WEBPACK_IMPORTED_MODULE_0__.PluginKey(\n  \"tableColumnResizing\"\n);\nfunction columnResizing({\n  handleWidth = 5,\n  cellMinWidth = 25,\n  defaultCellMinWidth = 100,\n  View = TableView,\n  lastColumnResizable = true\n} = {}) {\n  const plugin = new prosemirror_state__WEBPACK_IMPORTED_MODULE_0__.Plugin({\n    key: columnResizingPluginKey,\n    state: {\n      init(_, state) {\n        var _a, _b;\n        const nodeViews = (_b = (_a = plugin.spec) == null ? void 0 : _a.props) == null ? void 0 : _b.nodeViews;\n        const tableName = tableNodeTypes(state.schema).table.name;\n        if (View && nodeViews) {\n          nodeViews[tableName] = (node, view) => {\n            return new View(node, defaultCellMinWidth, view);\n          };\n        }\n        return new ResizeState(-1, false);\n      },\n      apply(tr, prev) {\n        return prev.apply(tr);\n      }\n    },\n    props: {\n      attributes: (state) => {\n        const pluginState = columnResizingPluginKey.getState(state);\n        return pluginState && pluginState.activeHandle > -1 ? { class: \"resize-cursor\" } : {};\n      },\n      handleDOMEvents: {\n        mousemove: (view, event) => {\n          handleMouseMove(view, event, handleWidth, lastColumnResizable);\n        },\n        mouseleave: (view) => {\n          handleMouseLeave(view);\n        },\n        mousedown: (view, event) => {\n          handleMouseDown2(view, event, cellMinWidth, defaultCellMinWidth);\n        }\n      },\n      decorations: (state) => {\n        const pluginState = columnResizingPluginKey.getState(state);\n        if (pluginState && pluginState.activeHandle > -1) {\n          return handleDecorations(state, pluginState.activeHandle);\n        }\n      },\n      nodeViews: {}\n    }\n  });\n  return plugin;\n}\nvar ResizeState = class _ResizeState {\n  constructor(activeHandle, dragging) {\n    this.activeHandle = activeHandle;\n    this.dragging = dragging;\n  }\n  apply(tr) {\n    const state = this;\n    const action = tr.getMeta(columnResizingPluginKey);\n    if (action && action.setHandle != null)\n      return new _ResizeState(action.setHandle, false);\n    if (action && action.setDragging !== void 0)\n      return new _ResizeState(state.activeHandle, action.setDragging);\n    if (state.activeHandle > -1 && tr.docChanged) {\n      let handle = tr.mapping.map(state.activeHandle, -1);\n      if (!pointsAtCell(tr.doc.resolve(handle))) {\n        handle = -1;\n      }\n      return new _ResizeState(handle, state.dragging);\n    }\n    return state;\n  }\n};\nfunction handleMouseMove(view, event, handleWidth, lastColumnResizable) {\n  if (!view.editable)\n    return;\n  const pluginState = columnResizingPluginKey.getState(view.state);\n  if (!pluginState)\n    return;\n  if (!pluginState.dragging) {\n    const target = domCellAround(event.target);\n    let cell = -1;\n    if (target) {\n      const { left, right } = target.getBoundingClientRect();\n      if (event.clientX - left <= handleWidth)\n        cell = edgeCell(view, event, \"left\", handleWidth);\n      else if (right - event.clientX <= handleWidth)\n        cell = edgeCell(view, event, \"right\", handleWidth);\n    }\n    if (cell != pluginState.activeHandle) {\n      if (!lastColumnResizable && cell !== -1) {\n        const $cell = view.state.doc.resolve(cell);\n        const table = $cell.node(-1);\n        const map = TableMap.get(table);\n        const tableStart = $cell.start(-1);\n        const col = map.colCount($cell.pos - tableStart) + $cell.nodeAfter.attrs.colspan - 1;\n        if (col == map.width - 1) {\n          return;\n        }\n      }\n      updateHandle(view, cell);\n    }\n  }\n}\nfunction handleMouseLeave(view) {\n  if (!view.editable)\n    return;\n  const pluginState = columnResizingPluginKey.getState(view.state);\n  if (pluginState && pluginState.activeHandle > -1 && !pluginState.dragging)\n    updateHandle(view, -1);\n}\nfunction handleMouseDown2(view, event, cellMinWidth, defaultCellMinWidth) {\n  var _a;\n  if (!view.editable)\n    return false;\n  const win = (_a = view.dom.ownerDocument.defaultView) != null ? _a : window;\n  const pluginState = columnResizingPluginKey.getState(view.state);\n  if (!pluginState || pluginState.activeHandle == -1 || pluginState.dragging)\n    return false;\n  const cell = view.state.doc.nodeAt(pluginState.activeHandle);\n  const width = currentColWidth(view, pluginState.activeHandle, cell.attrs);\n  view.dispatch(\n    view.state.tr.setMeta(columnResizingPluginKey, {\n      setDragging: { startX: event.clientX, startWidth: width }\n    })\n  );\n  function finish(event2) {\n    win.removeEventListener(\"mouseup\", finish);\n    win.removeEventListener(\"mousemove\", move);\n    const pluginState2 = columnResizingPluginKey.getState(view.state);\n    if (pluginState2 == null ? void 0 : pluginState2.dragging) {\n      updateColumnWidth(\n        view,\n        pluginState2.activeHandle,\n        draggedWidth(pluginState2.dragging, event2, cellMinWidth)\n      );\n      view.dispatch(\n        view.state.tr.setMeta(columnResizingPluginKey, { setDragging: null })\n      );\n    }\n  }\n  function move(event2) {\n    if (!event2.which)\n      return finish(event2);\n    const pluginState2 = columnResizingPluginKey.getState(view.state);\n    if (!pluginState2)\n      return;\n    if (pluginState2.dragging) {\n      const dragged = draggedWidth(pluginState2.dragging, event2, cellMinWidth);\n      displayColumnWidth(\n        view,\n        pluginState2.activeHandle,\n        dragged,\n        defaultCellMinWidth\n      );\n    }\n  }\n  displayColumnWidth(\n    view,\n    pluginState.activeHandle,\n    width,\n    defaultCellMinWidth\n  );\n  win.addEventListener(\"mouseup\", finish);\n  win.addEventListener(\"mousemove\", move);\n  event.preventDefault();\n  return true;\n}\nfunction currentColWidth(view, cellPos, { colspan, colwidth }) {\n  const width = colwidth && colwidth[colwidth.length - 1];\n  if (width)\n    return width;\n  const dom = view.domAtPos(cellPos);\n  const node = dom.node.childNodes[dom.offset];\n  let domWidth = node.offsetWidth, parts = colspan;\n  if (colwidth) {\n    for (let i = 0; i < colspan; i++)\n      if (colwidth[i]) {\n        domWidth -= colwidth[i];\n        parts--;\n      }\n  }\n  return domWidth / parts;\n}\nfunction domCellAround(target) {\n  while (target && target.nodeName != \"TD\" && target.nodeName != \"TH\")\n    target = target.classList && target.classList.contains(\"ProseMirror\") ? null : target.parentNode;\n  return target;\n}\nfunction edgeCell(view, event, side, handleWidth) {\n  const offset = side == \"right\" ? -handleWidth : handleWidth;\n  const found = view.posAtCoords({\n    left: event.clientX + offset,\n    top: event.clientY\n  });\n  if (!found)\n    return -1;\n  const { pos } = found;\n  const $cell = cellAround(view.state.doc.resolve(pos));\n  if (!$cell)\n    return -1;\n  if (side == \"right\")\n    return $cell.pos;\n  const map = TableMap.get($cell.node(-1)), start = $cell.start(-1);\n  const index = map.map.indexOf($cell.pos - start);\n  return index % map.width == 0 ? -1 : start + map.map[index - 1];\n}\nfunction draggedWidth(dragging, event, resizeMinWidth) {\n  const offset = event.clientX - dragging.startX;\n  return Math.max(resizeMinWidth, dragging.startWidth + offset);\n}\nfunction updateHandle(view, value) {\n  view.dispatch(\n    view.state.tr.setMeta(columnResizingPluginKey, { setHandle: value })\n  );\n}\nfunction updateColumnWidth(view, cell, width) {\n  const $cell = view.state.doc.resolve(cell);\n  const table = $cell.node(-1), map = TableMap.get(table), start = $cell.start(-1);\n  const col = map.colCount($cell.pos - start) + $cell.nodeAfter.attrs.colspan - 1;\n  const tr = view.state.tr;\n  for (let row = 0; row < map.height; row++) {\n    const mapIndex = row * map.width + col;\n    if (row && map.map[mapIndex] == map.map[mapIndex - map.width])\n      continue;\n    const pos = map.map[mapIndex];\n    const attrs = table.nodeAt(pos).attrs;\n    const index = attrs.colspan == 1 ? 0 : col - map.colCount(pos);\n    if (attrs.colwidth && attrs.colwidth[index] == width)\n      continue;\n    const colwidth = attrs.colwidth ? attrs.colwidth.slice() : zeroes(attrs.colspan);\n    colwidth[index] = width;\n    tr.setNodeMarkup(start + pos, null, { ...attrs, colwidth });\n  }\n  if (tr.docChanged)\n    view.dispatch(tr);\n}\nfunction displayColumnWidth(view, cell, width, defaultCellMinWidth) {\n  const $cell = view.state.doc.resolve(cell);\n  const table = $cell.node(-1), start = $cell.start(-1);\n  const col = TableMap.get(table).colCount($cell.pos - start) + $cell.nodeAfter.attrs.colspan - 1;\n  let dom = view.domAtPos($cell.start(-1)).node;\n  while (dom && dom.nodeName != \"TABLE\") {\n    dom = dom.parentNode;\n  }\n  if (!dom)\n    return;\n  updateColumnsOnResize(\n    table,\n    dom.firstChild,\n    dom,\n    defaultCellMinWidth,\n    col,\n    width\n  );\n}\nfunction zeroes(n) {\n  return Array(n).fill(0);\n}\nfunction handleDecorations(state, cell) {\n  var _a;\n  const decorations = [];\n  const $cell = state.doc.resolve(cell);\n  const table = $cell.node(-1);\n  if (!table) {\n    return prosemirror_view__WEBPACK_IMPORTED_MODULE_2__.DecorationSet.empty;\n  }\n  const map = TableMap.get(table);\n  const start = $cell.start(-1);\n  const col = map.colCount($cell.pos - start) + $cell.nodeAfter.attrs.colspan - 1;\n  for (let row = 0; row < map.height; row++) {\n    const index = col + row * map.width;\n    if ((col == map.width - 1 || map.map[index] != map.map[index + 1]) && (row == 0 || map.map[index] != map.map[index - map.width])) {\n      const cellPos = map.map[index];\n      const pos = start + cellPos + table.nodeAt(cellPos).nodeSize - 1;\n      const dom = document.createElement(\"div\");\n      dom.className = \"column-resize-handle\";\n      if ((_a = columnResizingPluginKey.getState(state)) == null ? void 0 : _a.dragging) {\n        decorations.push(\n          prosemirror_view__WEBPACK_IMPORTED_MODULE_2__.Decoration.node(\n            start + cellPos,\n            start + cellPos + table.nodeAt(cellPos).nodeSize,\n            {\n              class: \"column-resize-dragging\"\n            }\n          )\n        );\n      }\n      decorations.push(prosemirror_view__WEBPACK_IMPORTED_MODULE_2__.Decoration.widget(pos, dom));\n    }\n  }\n  return prosemirror_view__WEBPACK_IMPORTED_MODULE_2__.DecorationSet.create(state.doc, decorations);\n}\n\n// src/index.ts\nfunction tableEditing({\n  allowTableNodeSelection = false\n} = {}) {\n  return new prosemirror_state__WEBPACK_IMPORTED_MODULE_0__.Plugin({\n    key: tableEditingKey,\n    // This piece of state is used to remember when a mouse-drag\n    // cell-selection is happening, so that it can continue even as\n    // transactions (which might move its anchor cell) come in.\n    state: {\n      init() {\n        return null;\n      },\n      apply(tr, cur) {\n        const set = tr.getMeta(tableEditingKey);\n        if (set != null)\n          return set == -1 ? null : set;\n        if (cur == null || !tr.docChanged)\n          return cur;\n        const { deleted, pos } = tr.mapping.mapResult(cur);\n        return deleted ? null : pos;\n      }\n    },\n    props: {\n      decorations: drawCellSelection,\n      handleDOMEvents: {\n        mousedown: handleMouseDown\n      },\n      createSelectionBetween(view) {\n        return tableEditingKey.getState(view.state) != null ? view.state.selection : null;\n      },\n      handleTripleClick,\n      handleKeyDown,\n      handlePaste\n    },\n    appendTransaction(_, oldState, state) {\n      return normalizeSelection(\n        state,\n        fixTables(state, oldState),\n        allowTableNodeSelection\n      );\n    }\n  });\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/prosemirror-tables/dist/index.js\n");

/***/ })

};
;