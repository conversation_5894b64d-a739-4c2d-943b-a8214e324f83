# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Common Development Commands

**Root level (monorepo):**
- `yarn build` - Build all packages and apps
- `yarn lint` - Run ESLint on all packages
- `yarn lint:fix` - Fix ESLint issues
- `yarn format` - Format code with Prettier
- `yarn format:check` - Check formatting without changes
- `yarn turbo:command` - Run turbo commands

**Agents app (backend/LLM):**
- `cd apps/agents && yarn dev` - Start LangGraph server on port 54367
- `cd apps/agents && yarn build` - Build TypeScript to dist
- `cd apps/agents && yarn lint` - ESLint the agents code
- `cd apps/agents && yarn format` - Format with Prettier

**Web app (frontend):**
- `cd apps/web && yarn dev` - Start Next.js development server on port 3000
- `cd apps/web && yarn build` - Build Next.js app
- `cd apps/web && yarn start` - Start production server
- `cd apps/web && yarn lint` - Run Next.js ESLint
- `cd apps/web && yarn eval` - Run evaluation tests

### Testing and Evaluation
- `cd apps/web && yarn eval:highlights` - Run highlight evaluation tests
- `cd apps/agents && yarn test` - Run Vitest tests

### Development Workflow

1. **Initial Setup:**
   - Run `yarn install` to install dependencies
   - Copy `.env.example` to `.env` in root and `apps/web/`
   - Set up environment variables (see README.md for details)

2. **Development Mode:**
   - First, run `yarn build` to build workspace dependencies
   - Start LangGraph server: `cd apps/agents && yarn dev`
   - Start Next.js frontend: `cd apps/web && yarn dev`
   - Access app at `http://localhost:3000`

## Architecture

### Monorepo Structure
This is a Turborepo monorepo with two main applications:

**`apps/agents/`** - LangGraph-based LLM agent system
- Main entry point: `src/open-canvas/index.ts`
- Contains multiple agent graphs: main agent, reflection, thread title, summarizer, web search
- Handles LLM interactions, memory management, and content generation

**`apps/web/`** - Next.js React frontend
- Modern React app with TypeScript, Tailwind CSS, and shadcn/ui components
- Real-time chat interface with artifact editor
- Authentication via Supabase
- Markdown and code editing capabilities

**`packages/`** - Shared utilities (workspace packages)

### Agent System Architecture

The agent system uses LangGraph with multiple specialized nodes:

**Core Agent Graph (`apps/agents/src/open-canvas/`):**
- `nodes/generate-artifact/` - Create new content artifacts
- `nodes/rewrite-artifact/` - Modify existing artifacts
- `nodes/generate-path/` - Determine execution path
- `nodes/reflect/` - Memory and reflection system
- `nodes/customAction/` - Custom user-defined actions
- `nodes/generateFollowup/` - Generate follow-up messages
- `nodes/generateTitle/` - Generate thread titles
- `nodes/summarizer/` - Content summarization

**Supporting Agents:**
- `src/reflection/` - Memory and reflection management
- `src/thread-title/` - Thread title generation
- `src/summarizer/` - Content summarization
- `src/web-search/` - Web search functionality

### Frontend Architecture

**Component Structure:**
- `components/artifacts/` - Artifact rendering and editing
- `components/assistant-select/` - Assistant management
- `components/chat-interface/` - Chat and conversation UI
- `components/canvas/` - Main canvas/editor area
- `components/ui/` - Reusable UI components (shadcn/ui)

**Key Systems:**
- `contexts/` - React contexts for state management
- `hooks/` - Custom React hooks
- `lib/` - Utility functions and helpers
- `workers/` - Web Workers for streaming and background tasks

### Key Technologies

**Backend (Agents):**
- LangGraph for agent orchestration
- Multiple LLM providers (OpenAI, Anthropic, Google, Ollama)
- LangSmith for tracing and observability
- Supabase for authentication and storage

**Frontend (Web):**
- Next.js 14 with App Router
- React 18 with TypeScript
- Tailwind CSS for styling
- shadcn/ui component library
- CodeMirror for code editing
- BlockNote for rich text editing
- Zustand for state management

### Communication Flow

1. **Frontend → Backend:** Next.js API routes communicate with LangGraph server
2. **LangGraph Orchestration:** Main agent graph routes requests to specialized nodes
3. **LLM Integration:** Each node interacts with configured LLM providers
4. **Memory System:** Reflection agent maintains user preferences and context
5. **Real-time Updates:** Streaming responses via Web Workers

### Environment Variables

**Root `.env`** (LangGraph server):
- LLM API keys (OpenAI, Anthropic, Google, etc.)
- LangSmith tracing configuration
- External service APIs (Firecrawl, Exa, Groq)

**`apps/web/.env`** (Next.js frontend):
- Supabase credentials
- LangGraph server URL
- Optional service configurations

### Configuration Files

- `langgraph.json` - LangGraph configuration and graph definitions
- `turbo.json` - Turborepo build configuration
- `tsconfig.json` - TypeScript configuration
- `tailwind.config.ts` - Tailwind CSS configuration