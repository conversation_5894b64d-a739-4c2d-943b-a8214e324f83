"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/runs/share/route";
exports.ids = ["app/api/runs/share/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("node:crypto");

/***/ }),

/***/ "(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fruns%2Fshare%2Froute&page=%2Fapi%2Fruns%2Fshare%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fruns%2Fshare%2Froute.ts&appDir=G%3A%5CStudy%5CPython%5Copen-canvas%5Capps%5Cweb%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=G%3A%5CStudy%5CPython%5Copen-canvas%5Capps%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fruns%2Fshare%2Froute&page=%2Fapi%2Fruns%2Fshare%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fruns%2Fshare%2Froute.ts&appDir=G%3A%5CStudy%5CPython%5Copen-canvas%5Capps%5Cweb%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=G%3A%5CStudy%5CPython%5Copen-canvas%5Capps%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/../../node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/../../node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/../../node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var G_Study_Python_open_canvas_apps_web_src_app_api_runs_share_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/runs/share/route.ts */ \"(rsc)/./src/app/api/runs/share/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/runs/share/route\",\n        pathname: \"/api/runs/share\",\n        filename: \"route\",\n        bundlePath: \"app/api/runs/share/route\"\n    },\n    resolvedPagePath: \"G:\\\\Study\\\\Python\\\\open-canvas\\\\apps\\\\web\\\\src\\\\app\\\\api\\\\runs\\\\share\\\\route.ts\",\n    nextConfigOutput,\n    userland: G_Study_Python_open_canvas_apps_web_src_app_api_runs_share_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/runs/share/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fruns%2Fshare%2Froute&page=%2Fapi%2Fruns%2Fshare%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fruns%2Fshare%2Froute.ts&appDir=G%3A%5CStudy%5CPython%5Copen-canvas%5Capps%5Cweb%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=G%3A%5CStudy%5CPython%5Copen-canvas%5Capps%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/runs/share/route.ts":
/*!*****************************************!*\
  !*** ./src/app/api/runs/share/route.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/../../node_modules/next/dist/api/server.js\");\n/* harmony import */ var langsmith__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! langsmith */ \"(rsc)/../../node_modules/langsmith/index.js\");\n\n\nconst MAX_RETRIES = 5;\nconst RETRY_DELAY = 5000; // 5 seconds\nasync function shareRunWithRetry(lsClient, runId) {\n    for(let attempt = 1; attempt <= MAX_RETRIES; attempt++){\n        try {\n            return await lsClient.shareRun(runId);\n        } catch (error) {\n            if (attempt === MAX_RETRIES) {\n                throw error;\n            }\n            console.warn(`Attempt ${attempt} failed. Retrying in ${RETRY_DELAY / 1000} seconds...`);\n            await new Promise((resolve)=>setTimeout(resolve, RETRY_DELAY));\n        }\n    }\n    throw new Error(\"Max retries reached\"); // This line should never be reached due to the throw in the loop\n}\nasync function POST(req) {\n    const { runId } = await req.json();\n    if (!runId) {\n        return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(JSON.stringify({\n            error: \"`runId` is required to share run.\"\n        }), {\n            status: 400,\n            headers: {\n                \"Content-Type\": \"application/json\"\n            }\n        });\n    }\n    const lsClient = new langsmith__WEBPACK_IMPORTED_MODULE_1__.Client({\n        apiKey: process.env.LANGCHAIN_API_KEY\n    });\n    try {\n        const sharedRunURL = await shareRunWithRetry(lsClient, runId);\n        return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(JSON.stringify({\n            sharedRunURL\n        }), {\n            status: 200,\n            headers: {\n                \"Content-Type\": \"application/json\"\n            }\n        });\n    } catch (error) {\n        console.error(`Failed to share run with id ${runId} after ${MAX_RETRIES} attempts:\\n`, error);\n        return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(JSON.stringify({\n            error: \"Failed to share run after multiple attempts.\"\n        }), {\n            status: 500,\n            headers: {\n                \"Content-Type\": \"application/json\"\n            }\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/runs/share/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/p-queue","vendor-chunks/eventemitter3","vendor-chunks/retry","vendor-chunks/p-retry","vendor-chunks/p-timeout","vendor-chunks/p-finally","vendor-chunks/langsmith","vendor-chunks/semver","vendor-chunks/uuid"], () => (__webpack_exec__("(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fruns%2Fshare%2Froute&page=%2Fapi%2Fruns%2Fshare%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fruns%2Fshare%2Froute.ts&appDir=G%3A%5CStudy%5CPython%5Copen-canvas%5Capps%5Cweb%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=G%3A%5CStudy%5CPython%5Copen-canvas%5Capps%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();