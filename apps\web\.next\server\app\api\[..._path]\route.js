"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/[..._path]/route";
exports.ids = ["app/api/[..._path]/route"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("net");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2F%5B..._path%5D%2Froute&page=%2Fapi%2F%5B..._path%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2F%5B..._path%5D%2Froute.ts&appDir=G%3A%5CStudy%5CPython%5Copen-canvas%5Capps%5Cweb%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=G%3A%5CStudy%5CPython%5Copen-canvas%5Capps%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2F%5B..._path%5D%2Froute&page=%2Fapi%2F%5B..._path%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2F%5B..._path%5D%2Froute.ts&appDir=G%3A%5CStudy%5CPython%5Copen-canvas%5Capps%5Cweb%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=G%3A%5CStudy%5CPython%5Copen-canvas%5Capps%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/../../node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/../../node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/../../node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var G_Study_Python_open_canvas_apps_web_src_app_api_path_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/[..._path]/route.ts */ \"(rsc)/./src/app/api/[..._path]/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/[..._path]/route\",\n        pathname: \"/api/[..._path]\",\n        filename: \"route\",\n        bundlePath: \"app/api/[..._path]/route\"\n    },\n    resolvedPagePath: \"G:\\\\Study\\\\Python\\\\open-canvas\\\\apps\\\\web\\\\src\\\\app\\\\api\\\\[..._path]\\\\route.ts\",\n    nextConfigOutput,\n    userland: G_Study_Python_open_canvas_apps_web_src_app_api_path_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/[..._path]/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2F%5B..._path%5D%2Froute&page=%2Fapi%2F%5B..._path%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2F%5B..._path%5D%2Froute.ts&appDir=G%3A%5CStudy%5CPython%5Copen-canvas%5Capps%5Cweb%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=G%3A%5CStudy%5CPython%5Copen-canvas%5Capps%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/[..._path]/route.ts":
/*!*****************************************!*\
  !*** ./src/app/api/[..._path]/route.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   OPTIONS: () => (/* binding */ OPTIONS),\n/* harmony export */   PATCH: () => (/* binding */ PATCH),\n/* harmony export */   POST: () => (/* binding */ POST),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../constants */ \"(rsc)/./src/constants.ts\");\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/server */ \"(rsc)/../../node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabase_verify_user_server__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../lib/supabase/verify_user_server */ \"(rsc)/./src/lib/supabase/verify_user_server.ts\");\n\n\n\nfunction getCorsHeaders() {\n    return {\n        \"Access-Control-Allow-Origin\": \"*\",\n        \"Access-Control-Allow-Methods\": \"GET, POST, PUT, PATCH, DELETE, OPTIONS\",\n        \"Access-Control-Allow-Headers\": \"*\"\n    };\n}\nasync function handleRequest(req, method) {\n    let session;\n    let user;\n    try {\n        const authRes = await (0,_lib_supabase_verify_user_server__WEBPACK_IMPORTED_MODULE_2__.verifyUserAuthenticated)();\n        session = authRes?.session;\n        user = authRes?.user;\n        if (!session || !user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n    } catch (e) {\n        console.error(\"Failed to fetch user\", e);\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n            error: \"Unauthorized\"\n        }, {\n            status: 401\n        });\n    }\n    try {\n        const path = req.nextUrl.pathname.replace(/^\\/?api\\//, \"\");\n        const url = new URL(req.url);\n        const searchParams = new URLSearchParams(url.search);\n        searchParams.delete(\"_path\");\n        searchParams.delete(\"nxtP_path\");\n        const queryString = searchParams.toString() ? `?${searchParams.toString()}` : \"\";\n        const options = {\n            method,\n            headers: {\n                \"x-api-key\": process.env.LANGCHAIN_API_KEY || \"\"\n            }\n        };\n        if ([\n            \"POST\",\n            \"PUT\",\n            \"PATCH\"\n        ].includes(method)) {\n            options.headers = {\n                ...options.headers,\n                \"Content-Type\": \"application/json\"\n            };\n            const bodyText = await req.text();\n            if (typeof bodyText === \"string\" && bodyText.length > 0) {\n                const parsedBody = JSON.parse(bodyText);\n                parsedBody.config = parsedBody.config || {};\n                parsedBody.config.configurable = {\n                    ...parsedBody.config.configurable,\n                    supabase_session: session,\n                    supabase_user_id: user.id\n                };\n                options.body = JSON.stringify(parsedBody);\n            } else {\n                options.body = bodyText;\n            }\n        }\n        const res = await fetch(`${_constants__WEBPACK_IMPORTED_MODULE_0__.LANGGRAPH_API_URL}/${path}${queryString}`, options);\n        if (res.status >= 400) {\n            console.error(\"ERROR IN PROXY\", `${_constants__WEBPACK_IMPORTED_MODULE_0__.LANGGRAPH_API_URL}/${path}${queryString}`, res.status, res.statusText);\n            return new Response(res.body, {\n                status: res.status,\n                statusText: res.statusText\n            });\n        }\n        const headers = new Headers({\n            ...getCorsHeaders()\n        });\n        // Safely add headers from the original response\n        res.headers.forEach((value, key)=>{\n            try {\n                headers.set(key, value);\n            } catch (error) {\n                console.warn(`Failed to set header: ${key}`, error);\n            }\n        });\n        return new Response(res.body, {\n            status: res.status,\n            statusText: res.statusText,\n            headers\n        });\n    } catch (e) {\n        console.error(\"Error in proxy\");\n        console.error(e);\n        console.error(\"\\n\\n\\nEND ERROR\\n\\n\");\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n            error: e.message\n        }, {\n            status: e.status ?? 500\n        });\n    }\n}\nconst GET = (req)=>handleRequest(req, \"GET\");\nconst POST = (req)=>handleRequest(req, \"POST\");\nconst PUT = (req)=>handleRequest(req, \"PUT\");\nconst PATCH = (req)=>handleRequest(req, \"PATCH\");\nconst DELETE = (req)=>handleRequest(req, \"DELETE\");\n// Add a new OPTIONS handler\nconst OPTIONS = ()=>{\n    return new next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse(null, {\n        status: 204,\n        headers: {\n            ...getCorsHeaders()\n        }\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/[..._path]/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/constants.ts":
/*!**************************!*\
  !*** ./src/constants.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ALLOWED_AUDIO_TYPES: () => (/* binding */ ALLOWED_AUDIO_TYPES),\n/* harmony export */   ALLOWED_AUDIO_TYPE_ENDINGS: () => (/* binding */ ALLOWED_AUDIO_TYPE_ENDINGS),\n/* harmony export */   ALLOWED_VIDEO_TYPES: () => (/* binding */ ALLOWED_VIDEO_TYPES),\n/* harmony export */   ALLOWED_VIDEO_TYPE_ENDINGS: () => (/* binding */ ALLOWED_VIDEO_TYPE_ENDINGS),\n/* harmony export */   ASSISTANT_ID_COOKIE: () => (/* binding */ ASSISTANT_ID_COOKIE),\n/* harmony export */   CHAT_COLLAPSED_QUERY_PARAM: () => (/* binding */ CHAT_COLLAPSED_QUERY_PARAM),\n/* harmony export */   HAS_ASSISTANT_COOKIE_BEEN_SET: () => (/* binding */ HAS_ASSISTANT_COOKIE_BEEN_SET),\n/* harmony export */   LANGGRAPH_API_URL: () => (/* binding */ LANGGRAPH_API_URL),\n/* harmony export */   OC_HAS_SEEN_CUSTOM_ASSISTANTS_ALERT: () => (/* binding */ OC_HAS_SEEN_CUSTOM_ASSISTANTS_ALERT),\n/* harmony export */   WEB_SEARCH_RESULTS_QUERY_PARAM: () => (/* binding */ WEB_SEARCH_RESULTS_QUERY_PARAM)\n/* harmony export */ });\nconst LANGGRAPH_API_URL = process.env.LANGGRAPH_API_URL ?? \"http://localhost:54367\";\n// v2 is tied to the 'open-canvas-prod' deployment.\nconst ASSISTANT_ID_COOKIE = \"oc_assistant_id_v2\";\n// export const ASSISTANT_ID_COOKIE = \"oc_assistant_id\";\nconst HAS_ASSISTANT_COOKIE_BEEN_SET = \"has_oc_assistant_id_been_set\";\nconst OC_HAS_SEEN_CUSTOM_ASSISTANTS_ALERT = \"oc_has_seen_custom_assistants_alert\";\nconst WEB_SEARCH_RESULTS_QUERY_PARAM = \"webSearchResults\";\nconst ALLOWED_AUDIO_TYPES = new Set([\n    \"audio/mp3\",\n    \"audio/mp4\",\n    \"audio/mpeg\",\n    \"audio/mpga\",\n    \"audio/m4a\",\n    \"audio/wav\",\n    \"audio/webm\"\n]);\nconst ALLOWED_AUDIO_TYPE_ENDINGS = [\n    \".mp3\",\n    \".mpga\",\n    \".m4a\",\n    \".wav\",\n    \".webm\"\n];\nconst ALLOWED_VIDEO_TYPES = new Set([\n    \"video/mp4\",\n    \"video/mpeg\",\n    \"video/webm\"\n]);\nconst ALLOWED_VIDEO_TYPE_ENDINGS = [\n    \".mp4\",\n    \".mpeg\",\n    \".webm\"\n];\nconst CHAT_COLLAPSED_QUERY_PARAM = \"chatCollapsed\";\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/constants.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/server.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/server.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createClient: () => (/* binding */ createClient)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/../../node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/../../node_modules/next/dist/api/headers.js\");\n\n\nfunction createClient() {\n    if (false) {}\n    if (false) {}\n    const cookieStore = (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://dkzqtcimgmrnlnsuomiu.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRrenF0Y2ltZ21ybmxuc3VvbWl1Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTU0ODY4NDMsImV4cCI6MjA3MTA2Mjg0M30.m2iAmcSa-l4Qo2C8YdS00J7FtjaeIwzuappMUdQ_n4I\", {\n        cookies: {\n            getAll () {\n                return cookieStore.getAll();\n            },\n            setAll (cookiesToSet) {\n                try {\n                    cookiesToSet.forEach(({ name, value, options })=>cookieStore.set(name, value, options));\n                } catch  {\n                // The `setAll` method was called from a Server Component.\n                // This can be ignored if you have middleware refreshing\n                // user sessions.\n                }\n            }\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/server.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/verify_user_server.ts":
/*!************************************************!*\
  !*** ./src/lib/supabase/verify_user_server.ts ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   verifyUserAuthenticated: () => (/* binding */ verifyUserAuthenticated)\n/* harmony export */ });\n/* harmony import */ var _server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./server */ \"(rsc)/./src/lib/supabase/server.ts\");\n\nasync function verifyUserAuthenticated() {\n    const supabase = (0,_server__WEBPACK_IMPORTED_MODULE_0__.createClient)();\n    const { data: { user } } = await supabase.auth.getUser();\n    const { data: { session } } = await supabase.auth.getSession();\n    if (!user || !session) {\n        return undefined;\n    }\n    return {\n        user,\n        session\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3N1cGFiYXNlL3ZlcmlmeV91c2VyX3NlcnZlci50cyIsIm1hcHBpbmdzIjoiOzs7OztBQUN3QztBQUVqQyxlQUFlQztJQUdwQixNQUFNQyxXQUFXRixxREFBWUE7SUFDN0IsTUFBTSxFQUNKRyxNQUFNLEVBQUVDLElBQUksRUFBRSxFQUNmLEdBQUcsTUFBTUYsU0FBU0csSUFBSSxDQUFDQyxPQUFPO0lBQy9CLE1BQU0sRUFDSkgsTUFBTSxFQUFFSSxPQUFPLEVBQUUsRUFDbEIsR0FBRyxNQUFNTCxTQUFTRyxJQUFJLENBQUNHLFVBQVU7SUFDbEMsSUFBSSxDQUFDSixRQUFRLENBQUNHLFNBQVM7UUFDckIsT0FBT0U7SUFDVDtJQUNBLE9BQU87UUFBRUw7UUFBTUc7SUFBUTtBQUN6QiIsInNvdXJjZXMiOlsid2VicGFjazovL0BvcGVuY2FudmFzL3dlYi8uL3NyYy9saWIvc3VwYWJhc2UvdmVyaWZ5X3VzZXJfc2VydmVyLnRzP2JiNDciXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgU2Vzc2lvbiwgVXNlciB9IGZyb20gXCJAc3VwYWJhc2Uvc3VwYWJhc2UtanNcIjtcbmltcG9ydCB7IGNyZWF0ZUNsaWVudCB9IGZyb20gXCIuL3NlcnZlclwiO1xuXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gdmVyaWZ5VXNlckF1dGhlbnRpY2F0ZWQoKTogUHJvbWlzZTxcbiAgeyB1c2VyOiBVc2VyOyBzZXNzaW9uOiBTZXNzaW9uIH0gfCB1bmRlZmluZWRcbj4ge1xuICBjb25zdCBzdXBhYmFzZSA9IGNyZWF0ZUNsaWVudCgpO1xuICBjb25zdCB7XG4gICAgZGF0YTogeyB1c2VyIH0sXG4gIH0gPSBhd2FpdCBzdXBhYmFzZS5hdXRoLmdldFVzZXIoKTtcbiAgY29uc3Qge1xuICAgIGRhdGE6IHsgc2Vzc2lvbiB9LFxuICB9ID0gYXdhaXQgc3VwYWJhc2UuYXV0aC5nZXRTZXNzaW9uKCk7XG4gIGlmICghdXNlciB8fCAhc2Vzc2lvbikge1xuICAgIHJldHVybiB1bmRlZmluZWQ7XG4gIH1cbiAgcmV0dXJuIHsgdXNlciwgc2Vzc2lvbiB9O1xufVxuIl0sIm5hbWVzIjpbImNyZWF0ZUNsaWVudCIsInZlcmlmeVVzZXJBdXRoZW50aWNhdGVkIiwic3VwYWJhc2UiLCJkYXRhIiwidXNlciIsImF1dGgiLCJnZXRVc2VyIiwic2Vzc2lvbiIsImdldFNlc3Npb24iLCJ1bmRlZmluZWQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/verify_user_server.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/whatwg-url","vendor-chunks/cookie","vendor-chunks/webidl-conversions"], () => (__webpack_exec__("(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2F%5B..._path%5D%2Froute&page=%2Fapi%2F%5B..._path%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2F%5B..._path%5D%2Froute.ts&appDir=G%3A%5CStudy%5CPython%5Copen-canvas%5Capps%5Cweb%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=G%3A%5CStudy%5CPython%5Copen-canvas%5Capps%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();