"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/prosemirror-dropcursor";
exports.ids = ["vendor-chunks/prosemirror-dropcursor"];
exports.modules = {

/***/ "(ssr)/../../node_modules/prosemirror-dropcursor/dist/index.js":
/*!***************************************************************!*\
  !*** ../../node_modules/prosemirror-dropcursor/dist/index.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   dropCursor: () => (/* binding */ dropCursor)\n/* harmony export */ });\n/* harmony import */ var prosemirror_state__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! prosemirror-state */ \"(ssr)/../../node_modules/prosemirror-state/dist/index.js\");\n/* harmony import */ var prosemirror_transform__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! prosemirror-transform */ \"(ssr)/../../node_modules/prosemirror-transform/dist/index.js\");\n\n\n\n/**\nCreate a plugin that, when added to a ProseMirror instance,\ncauses a decoration to show up at the drop position when something\nis dragged over the editor.\n\nNodes may add a `disableDropCursor` property to their spec to\ncontrol the showing of a drop cursor inside them. This may be a\nboolean or a function, which will be called with a view and a\nposition, and should return a boolean.\n*/\nfunction dropCursor(options = {}) {\n    return new prosemirror_state__WEBPACK_IMPORTED_MODULE_0__.Plugin({\n        view(editorView) { return new DropCursorView(editorView, options); }\n    });\n}\nclass DropCursorView {\n    constructor(editorView, options) {\n        var _a;\n        this.editorView = editorView;\n        this.cursorPos = null;\n        this.element = null;\n        this.timeout = -1;\n        this.width = (_a = options.width) !== null && _a !== void 0 ? _a : 1;\n        this.color = options.color === false ? undefined : (options.color || \"black\");\n        this.class = options.class;\n        this.handlers = [\"dragover\", \"dragend\", \"drop\", \"dragleave\"].map(name => {\n            let handler = (e) => { this[name](e); };\n            editorView.dom.addEventListener(name, handler);\n            return { name, handler };\n        });\n    }\n    destroy() {\n        this.handlers.forEach(({ name, handler }) => this.editorView.dom.removeEventListener(name, handler));\n    }\n    update(editorView, prevState) {\n        if (this.cursorPos != null && prevState.doc != editorView.state.doc) {\n            if (this.cursorPos > editorView.state.doc.content.size)\n                this.setCursor(null);\n            else\n                this.updateOverlay();\n        }\n    }\n    setCursor(pos) {\n        if (pos == this.cursorPos)\n            return;\n        this.cursorPos = pos;\n        if (pos == null) {\n            this.element.parentNode.removeChild(this.element);\n            this.element = null;\n        }\n        else {\n            this.updateOverlay();\n        }\n    }\n    updateOverlay() {\n        let $pos = this.editorView.state.doc.resolve(this.cursorPos);\n        let isBlock = !$pos.parent.inlineContent, rect;\n        if (isBlock) {\n            let before = $pos.nodeBefore, after = $pos.nodeAfter;\n            if (before || after) {\n                let node = this.editorView.nodeDOM(this.cursorPos - (before ? before.nodeSize : 0));\n                if (node) {\n                    let nodeRect = node.getBoundingClientRect();\n                    let top = before ? nodeRect.bottom : nodeRect.top;\n                    if (before && after)\n                        top = (top + this.editorView.nodeDOM(this.cursorPos).getBoundingClientRect().top) / 2;\n                    rect = { left: nodeRect.left, right: nodeRect.right, top: top - this.width / 2, bottom: top + this.width / 2 };\n                }\n            }\n        }\n        if (!rect) {\n            let coords = this.editorView.coordsAtPos(this.cursorPos);\n            rect = { left: coords.left - this.width / 2, right: coords.left + this.width / 2, top: coords.top, bottom: coords.bottom };\n        }\n        let parent = this.editorView.dom.offsetParent;\n        if (!this.element) {\n            this.element = parent.appendChild(document.createElement(\"div\"));\n            if (this.class)\n                this.element.className = this.class;\n            this.element.style.cssText = \"position: absolute; z-index: 50; pointer-events: none;\";\n            if (this.color) {\n                this.element.style.backgroundColor = this.color;\n            }\n        }\n        this.element.classList.toggle(\"prosemirror-dropcursor-block\", isBlock);\n        this.element.classList.toggle(\"prosemirror-dropcursor-inline\", !isBlock);\n        let parentLeft, parentTop;\n        if (!parent || parent == document.body && getComputedStyle(parent).position == \"static\") {\n            parentLeft = -pageXOffset;\n            parentTop = -pageYOffset;\n        }\n        else {\n            let rect = parent.getBoundingClientRect();\n            parentLeft = rect.left - parent.scrollLeft;\n            parentTop = rect.top - parent.scrollTop;\n        }\n        this.element.style.left = (rect.left - parentLeft) + \"px\";\n        this.element.style.top = (rect.top - parentTop) + \"px\";\n        this.element.style.width = (rect.right - rect.left) + \"px\";\n        this.element.style.height = (rect.bottom - rect.top) + \"px\";\n    }\n    scheduleRemoval(timeout) {\n        clearTimeout(this.timeout);\n        this.timeout = setTimeout(() => this.setCursor(null), timeout);\n    }\n    dragover(event) {\n        if (!this.editorView.editable)\n            return;\n        let pos = this.editorView.posAtCoords({ left: event.clientX, top: event.clientY });\n        let node = pos && pos.inside >= 0 && this.editorView.state.doc.nodeAt(pos.inside);\n        let disableDropCursor = node && node.type.spec.disableDropCursor;\n        let disabled = typeof disableDropCursor == \"function\" ? disableDropCursor(this.editorView, pos, event) : disableDropCursor;\n        if (pos && !disabled) {\n            let target = pos.pos;\n            if (this.editorView.dragging && this.editorView.dragging.slice) {\n                let point = (0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_1__.dropPoint)(this.editorView.state.doc, target, this.editorView.dragging.slice);\n                if (point != null)\n                    target = point;\n            }\n            this.setCursor(target);\n            this.scheduleRemoval(5000);\n        }\n    }\n    dragend() {\n        this.scheduleRemoval(20);\n    }\n    drop() {\n        this.scheduleRemoval(20);\n    }\n    dragleave(event) {\n        if (event.target == this.editorView.dom || !this.editorView.dom.contains(event.relatedTarget))\n            this.setCursor(null);\n    }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/prosemirror-dropcursor/dist/index.js\n");

/***/ })

};
;