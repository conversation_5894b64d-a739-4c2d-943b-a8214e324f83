"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/hast-util-to-mdast";
exports.ids = ["vendor-chunks/hast-util-to-mdast"];
exports.modules = {

/***/ "(ssr)/../../node_modules/hast-util-to-mdast/lib/all.js":
/*!********************************************************!*\
  !*** ../../node_modules/hast-util-to-mdast/lib/all.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   all: () => (/* binding */ all)\n/* harmony export */ });\n/* harmony import */ var _one_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./one.js */ \"(ssr)/../../node_modules/hast-util-to-mdast/lib/one.js\");\n/**\n * @typedef {import('./types.js').H} H\n * @typedef {import('./types.js').Node} Node\n * @typedef {import('./types.js').MdastNode} MdastNode\n */\n\n\n\n/**\n * @param {H} h\n * @param {Node} parent\n * @returns {Array<MdastNode>}\n */\nfunction all(h, parent) {\n  /** @type {Array<Node>} */\n  // @ts-expect-error Assume `parent` is a parent.\n  const nodes = parent.children || []\n  /** @type {Array<MdastNode>} */\n  const values = []\n  let index = -1\n\n  while (++index < nodes.length) {\n    // @ts-expect-error assume `parent` is a parent.\n    const result = (0,_one_js__WEBPACK_IMPORTED_MODULE_0__.one)(h, nodes[index], parent)\n\n    if (Array.isArray(result)) {\n      values.push(...result)\n    } else if (result) {\n      values.push(result)\n    }\n  }\n\n  let start = 0\n  let end = values.length\n\n  while (start < end && values[start].type === 'break') {\n    start++\n  }\n\n  while (end > start && values[end - 1].type === 'break') {\n    end--\n  }\n\n  return start === 0 && end === values.length\n    ? values\n    : values.slice(start, end)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/hast-util-to-mdast/lib/all.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/hast-util-to-mdast/lib/handlers/a.js":
/*!***************************************************************!*\
  !*** ../../node_modules/hast-util-to-mdast/lib/handlers/a.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   a: () => (/* binding */ a)\n/* harmony export */ });\n/* harmony import */ var _all_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../all.js */ \"(ssr)/../../node_modules/hast-util-to-mdast/lib/all.js\");\n/* harmony import */ var _util_resolve_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/resolve.js */ \"(ssr)/../../node_modules/hast-util-to-mdast/lib/util/resolve.js\");\n/**\n * @typedef {import('../types.js').Handle} Handle\n * @typedef {import('../types.js').Element} Element\n * @typedef {import('../types.js').Properties} Properties\n */\n\n\n\n\n/**\n * @type {Handle}\n * @param {Element} node\n */\nfunction a(h, node) {\n  /** @type {Properties} */\n  // @ts-expect-error: `props` are defined.\n  const props = node.properties\n  return h(\n    node,\n    'link',\n    {\n      title: props.title || null,\n      url: (0,_util_resolve_js__WEBPACK_IMPORTED_MODULE_0__.resolve)(h, String(props.href || '') || null)\n    },\n    (0,_all_js__WEBPACK_IMPORTED_MODULE_1__.all)(h, node)\n  )\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2hhc3QtdXRpbC10by1tZGFzdC9saWIvaGFuZGxlcnMvYS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBQTtBQUNBLGFBQWEsOEJBQThCO0FBQzNDLGFBQWEsK0JBQStCO0FBQzVDLGFBQWEsa0NBQWtDO0FBQy9DOztBQUU2QjtBQUNhOztBQUUxQztBQUNBLFVBQVU7QUFDVixXQUFXLFNBQVM7QUFDcEI7QUFDTztBQUNQLGFBQWEsWUFBWTtBQUN6QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcseURBQU87QUFDbEIsS0FBSztBQUNMLElBQUksNENBQUc7QUFDUDtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQG9wZW5jYW52YXMvd2ViLy4uLy4uL25vZGVfbW9kdWxlcy9oYXN0LXV0aWwtdG8tbWRhc3QvbGliL2hhbmRsZXJzL2EuanM/YzQ0OCJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuSGFuZGxlfSBIYW5kbGVcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuRWxlbWVudH0gRWxlbWVudFxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5Qcm9wZXJ0aWVzfSBQcm9wZXJ0aWVzXG4gKi9cblxuaW1wb3J0IHthbGx9IGZyb20gJy4uL2FsbC5qcydcbmltcG9ydCB7cmVzb2x2ZX0gZnJvbSAnLi4vdXRpbC9yZXNvbHZlLmpzJ1xuXG4vKipcbiAqIEB0eXBlIHtIYW5kbGV9XG4gKiBAcGFyYW0ge0VsZW1lbnR9IG5vZGVcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGEoaCwgbm9kZSkge1xuICAvKiogQHR5cGUge1Byb3BlcnRpZXN9ICovXG4gIC8vIEB0cy1leHBlY3QtZXJyb3I6IGBwcm9wc2AgYXJlIGRlZmluZWQuXG4gIGNvbnN0IHByb3BzID0gbm9kZS5wcm9wZXJ0aWVzXG4gIHJldHVybiBoKFxuICAgIG5vZGUsXG4gICAgJ2xpbmsnLFxuICAgIHtcbiAgICAgIHRpdGxlOiBwcm9wcy50aXRsZSB8fCBudWxsLFxuICAgICAgdXJsOiByZXNvbHZlKGgsIFN0cmluZyhwcm9wcy5ocmVmIHx8ICcnKSB8fCBudWxsKVxuICAgIH0sXG4gICAgYWxsKGgsIG5vZGUpXG4gIClcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/hast-util-to-mdast/lib/handlers/a.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/hast-util-to-mdast/lib/handlers/base.js":
/*!******************************************************************!*\
  !*** ../../node_modules/hast-util-to-mdast/lib/handlers/base.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   base: () => (/* binding */ base)\n/* harmony export */ });\n/**\n * @typedef {import('../types.js').Handle} Handle\n * @typedef {import('../types.js').Element} Element\n */\n\n/**\n * @type {Handle}\n * @param {Element} node\n */\nfunction base(h, node) {\n  if (!h.baseFound) {\n    h.frozenBaseUrl =\n      String((node.properties && node.properties.href) || '') || null\n    h.baseFound = true\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2hhc3QtdXRpbC10by1tZGFzdC9saWIvaGFuZGxlcnMvYmFzZS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQSxhQUFhLDhCQUE4QjtBQUMzQyxhQUFhLCtCQUErQjtBQUM1Qzs7QUFFQTtBQUNBLFVBQVU7QUFDVixXQUFXLFNBQVM7QUFDcEI7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL0BvcGVuY2FudmFzL3dlYi8uLi8uLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi9oYW5kbGVycy9iYXNlLmpzP2M2YWMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLkhhbmRsZX0gSGFuZGxlXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLkVsZW1lbnR9IEVsZW1lbnRcbiAqL1xuXG4vKipcbiAqIEB0eXBlIHtIYW5kbGV9XG4gKiBAcGFyYW0ge0VsZW1lbnR9IG5vZGVcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGJhc2UoaCwgbm9kZSkge1xuICBpZiAoIWguYmFzZUZvdW5kKSB7XG4gICAgaC5mcm96ZW5CYXNlVXJsID1cbiAgICAgIFN0cmluZygobm9kZS5wcm9wZXJ0aWVzICYmIG5vZGUucHJvcGVydGllcy5ocmVmKSB8fCAnJykgfHwgbnVsbFxuICAgIGguYmFzZUZvdW5kID0gdHJ1ZVxuICB9XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/hast-util-to-mdast/lib/handlers/base.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/hast-util-to-mdast/lib/handlers/blockquote.js":
/*!************************************************************************!*\
  !*** ../../node_modules/hast-util-to-mdast/lib/handlers/blockquote.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   blockquote: () => (/* binding */ blockquote)\n/* harmony export */ });\n/* harmony import */ var _util_wrap_children_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/wrap-children.js */ \"(ssr)/../../node_modules/hast-util-to-mdast/lib/util/wrap-children.js\");\n/**\n * @typedef {import('../types.js').Handle} Handle\n * @typedef {import('../types.js').Element} Element\n */\n\n\n\n/**\n * @type {Handle}\n * @param {Element} node\n */\nfunction blockquote(h, node) {\n  return h(node, 'blockquote', (0,_util_wrap_children_js__WEBPACK_IMPORTED_MODULE_0__.wrapChildren)(h, node))\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2hhc3QtdXRpbC10by1tZGFzdC9saWIvaGFuZGxlcnMvYmxvY2txdW90ZS5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ0EsYUFBYSw4QkFBOEI7QUFDM0MsYUFBYSwrQkFBK0I7QUFDNUM7O0FBRXFEOztBQUVyRDtBQUNBLFVBQVU7QUFDVixXQUFXLFNBQVM7QUFDcEI7QUFDTztBQUNQLCtCQUErQixvRUFBWTtBQUMzQyIsInNvdXJjZXMiOlsid2VicGFjazovL0BvcGVuY2FudmFzL3dlYi8uLi8uLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi9oYW5kbGVycy9ibG9ja3F1b3RlLmpzPzJiZWUiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLkhhbmRsZX0gSGFuZGxlXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLkVsZW1lbnR9IEVsZW1lbnRcbiAqL1xuXG5pbXBvcnQge3dyYXBDaGlsZHJlbn0gZnJvbSAnLi4vdXRpbC93cmFwLWNoaWxkcmVuLmpzJ1xuXG4vKipcbiAqIEB0eXBlIHtIYW5kbGV9XG4gKiBAcGFyYW0ge0VsZW1lbnR9IG5vZGVcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGJsb2NrcXVvdGUoaCwgbm9kZSkge1xuICByZXR1cm4gaChub2RlLCAnYmxvY2txdW90ZScsIHdyYXBDaGlsZHJlbihoLCBub2RlKSlcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/hast-util-to-mdast/lib/handlers/blockquote.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/hast-util-to-mdast/lib/handlers/br.js":
/*!****************************************************************!*\
  !*** ../../node_modules/hast-util-to-mdast/lib/handlers/br.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   br: () => (/* binding */ br)\n/* harmony export */ });\n/**\n * @typedef {import('../types.js').Handle} Handle\n * @typedef {import('../types.js').Element} Element\n */\n\n/**\n * @type {Handle}\n * @param {Element} node\n */\nfunction br(h, node) {\n  return h.wrapText ? h(node, 'break') : h(node, 'text', ' ')\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2hhc3QtdXRpbC10by1tZGFzdC9saWIvaGFuZGxlcnMvYnIuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0EsYUFBYSw4QkFBOEI7QUFDM0MsYUFBYSwrQkFBK0I7QUFDNUM7O0FBRUE7QUFDQSxVQUFVO0FBQ1YsV0FBVyxTQUFTO0FBQ3BCO0FBQ087QUFDUDtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQG9wZW5jYW52YXMvd2ViLy4uLy4uL25vZGVfbW9kdWxlcy9oYXN0LXV0aWwtdG8tbWRhc3QvbGliL2hhbmRsZXJzL2JyLmpzPzgxY2MiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLkhhbmRsZX0gSGFuZGxlXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLkVsZW1lbnR9IEVsZW1lbnRcbiAqL1xuXG4vKipcbiAqIEB0eXBlIHtIYW5kbGV9XG4gKiBAcGFyYW0ge0VsZW1lbnR9IG5vZGVcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGJyKGgsIG5vZGUpIHtcbiAgcmV0dXJuIGgud3JhcFRleHQgPyBoKG5vZGUsICdicmVhaycpIDogaChub2RlLCAndGV4dCcsICcgJylcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/hast-util-to-mdast/lib/handlers/br.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/hast-util-to-mdast/lib/handlers/code.js":
/*!******************************************************************!*\
  !*** ../../node_modules/hast-util-to-mdast/lib/handlers/code.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   code: () => (/* binding */ code)\n/* harmony export */ });\n/* harmony import */ var hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! hast-util-is-element */ \"(ssr)/../../node_modules/hast-util-is-element/index.js\");\n/* harmony import */ var hast_util_to_text__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! hast-util-to-text */ \"(ssr)/../../node_modules/hast-util-to-mdast/node_modules/hast-util-to-text/lib/index.js\");\n/* harmony import */ var trim_trailing_lines__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! trim-trailing-lines */ \"(ssr)/../../node_modules/trim-trailing-lines/index.js\");\n/* harmony import */ var _util_wrap_text_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../util/wrap-text.js */ \"(ssr)/../../node_modules/hast-util-to-mdast/lib/util/wrap-text.js\");\n/**\n * @typedef {import('../types.js').Handle} Handle\n * @typedef {import('../types.js').Element} Element\n * @typedef {import('../types.js').ElementChild} ElementChild\n */\n\n\n\n\n\n\nconst prefix = 'language-'\n\nconst pre = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)('pre')\nconst isCode = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)('code')\n\n/**\n * @type {Handle}\n * @param {Element} node\n */\nfunction code(h, node) {\n  const children = node.children\n  let index = -1\n  /** @type {Array<string|number>|undefined} */\n  let classList\n  /** @type {string|undefined} */\n  let lang\n\n  if (pre(node)) {\n    while (++index < children.length) {\n      const child = children[index]\n\n      if (\n        isCode(child) &&\n        child.properties &&\n        child.properties.className &&\n        Array.isArray(child.properties.className)\n      ) {\n        classList = child.properties.className\n        break\n      }\n    }\n  }\n\n  if (classList) {\n    index = -1\n\n    while (++index < classList.length) {\n      if (String(classList[index]).slice(0, prefix.length) === prefix) {\n        lang = String(classList[index]).slice(prefix.length)\n        break\n      }\n    }\n  }\n\n  return h(\n    node,\n    'code',\n    {lang: lang || null, meta: null},\n    (0,trim_trailing_lines__WEBPACK_IMPORTED_MODULE_1__.trimTrailingLines)((0,_util_wrap_text_js__WEBPACK_IMPORTED_MODULE_2__.wrapText)(h, (0,hast_util_to_text__WEBPACK_IMPORTED_MODULE_3__.toText)(node)))\n  )\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2hhc3QtdXRpbC10by1tZGFzdC9saWIvaGFuZGxlcnMvY29kZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUFBO0FBQ0EsYUFBYSw4QkFBOEI7QUFDM0MsYUFBYSwrQkFBK0I7QUFDNUMsYUFBYSxvQ0FBb0M7QUFDakQ7O0FBRW1EO0FBQ1g7QUFDYTtBQUNSOztBQUU3Qzs7QUFFQSxZQUFZLG9FQUFjO0FBQzFCLGVBQWUsb0VBQWM7O0FBRTdCO0FBQ0EsVUFBVTtBQUNWLFdBQVcsU0FBUztBQUNwQjtBQUNPO0FBQ1A7QUFDQTtBQUNBLGFBQWEsZ0NBQWdDO0FBQzdDO0FBQ0EsYUFBYSxrQkFBa0I7QUFDL0I7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxLQUFLLCtCQUErQjtBQUNwQyxJQUFJLHNFQUFpQixDQUFDLDREQUFRLElBQUkseURBQU07QUFDeEM7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL0BvcGVuY2FudmFzL3dlYi8uLi8uLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi9oYW5kbGVycy9jb2RlLmpzPzBjOTIiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLkhhbmRsZX0gSGFuZGxlXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLkVsZW1lbnR9IEVsZW1lbnRcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuRWxlbWVudENoaWxkfSBFbGVtZW50Q2hpbGRcbiAqL1xuXG5pbXBvcnQge2NvbnZlcnRFbGVtZW50fSBmcm9tICdoYXN0LXV0aWwtaXMtZWxlbWVudCdcbmltcG9ydCB7dG9UZXh0fSBmcm9tICdoYXN0LXV0aWwtdG8tdGV4dCdcbmltcG9ydCB7dHJpbVRyYWlsaW5nTGluZXN9IGZyb20gJ3RyaW0tdHJhaWxpbmctbGluZXMnXG5pbXBvcnQge3dyYXBUZXh0fSBmcm9tICcuLi91dGlsL3dyYXAtdGV4dC5qcydcblxuY29uc3QgcHJlZml4ID0gJ2xhbmd1YWdlLSdcblxuY29uc3QgcHJlID0gY29udmVydEVsZW1lbnQoJ3ByZScpXG5jb25zdCBpc0NvZGUgPSBjb252ZXJ0RWxlbWVudCgnY29kZScpXG5cbi8qKlxuICogQHR5cGUge0hhbmRsZX1cbiAqIEBwYXJhbSB7RWxlbWVudH0gbm9kZVxuICovXG5leHBvcnQgZnVuY3Rpb24gY29kZShoLCBub2RlKSB7XG4gIGNvbnN0IGNoaWxkcmVuID0gbm9kZS5jaGlsZHJlblxuICBsZXQgaW5kZXggPSAtMVxuICAvKiogQHR5cGUge0FycmF5PHN0cmluZ3xudW1iZXI+fHVuZGVmaW5lZH0gKi9cbiAgbGV0IGNsYXNzTGlzdFxuICAvKiogQHR5cGUge3N0cmluZ3x1bmRlZmluZWR9ICovXG4gIGxldCBsYW5nXG5cbiAgaWYgKHByZShub2RlKSkge1xuICAgIHdoaWxlICgrK2luZGV4IDwgY2hpbGRyZW4ubGVuZ3RoKSB7XG4gICAgICBjb25zdCBjaGlsZCA9IGNoaWxkcmVuW2luZGV4XVxuXG4gICAgICBpZiAoXG4gICAgICAgIGlzQ29kZShjaGlsZCkgJiZcbiAgICAgICAgY2hpbGQucHJvcGVydGllcyAmJlxuICAgICAgICBjaGlsZC5wcm9wZXJ0aWVzLmNsYXNzTmFtZSAmJlxuICAgICAgICBBcnJheS5pc0FycmF5KGNoaWxkLnByb3BlcnRpZXMuY2xhc3NOYW1lKVxuICAgICAgKSB7XG4gICAgICAgIGNsYXNzTGlzdCA9IGNoaWxkLnByb3BlcnRpZXMuY2xhc3NOYW1lXG4gICAgICAgIGJyZWFrXG4gICAgICB9XG4gICAgfVxuICB9XG5cbiAgaWYgKGNsYXNzTGlzdCkge1xuICAgIGluZGV4ID0gLTFcblxuICAgIHdoaWxlICgrK2luZGV4IDwgY2xhc3NMaXN0Lmxlbmd0aCkge1xuICAgICAgaWYgKFN0cmluZyhjbGFzc0xpc3RbaW5kZXhdKS5zbGljZSgwLCBwcmVmaXgubGVuZ3RoKSA9PT0gcHJlZml4KSB7XG4gICAgICAgIGxhbmcgPSBTdHJpbmcoY2xhc3NMaXN0W2luZGV4XSkuc2xpY2UocHJlZml4Lmxlbmd0aClcbiAgICAgICAgYnJlYWtcbiAgICAgIH1cbiAgICB9XG4gIH1cblxuICByZXR1cm4gaChcbiAgICBub2RlLFxuICAgICdjb2RlJyxcbiAgICB7bGFuZzogbGFuZyB8fCBudWxsLCBtZXRhOiBudWxsfSxcbiAgICB0cmltVHJhaWxpbmdMaW5lcyh3cmFwVGV4dChoLCB0b1RleHQobm9kZSkpKVxuICApXG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/hast-util-to-mdast/lib/handlers/code.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/hast-util-to-mdast/lib/handlers/comment.js":
/*!*********************************************************************!*\
  !*** ../../node_modules/hast-util-to-mdast/lib/handlers/comment.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   comment: () => (/* binding */ comment)\n/* harmony export */ });\n/* harmony import */ var _util_wrap_text_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/wrap-text.js */ \"(ssr)/../../node_modules/hast-util-to-mdast/lib/util/wrap-text.js\");\n/**\n * @typedef {import('../types.js').Handle} Handle\n * @typedef {import('../types.js').Comment} Comment\n */\n\n\n/**\n * @type {Handle}\n * @param {Comment} node\n */\nfunction comment(h, node) {\n  return h(node, 'html', '<!--' + (0,_util_wrap_text_js__WEBPACK_IMPORTED_MODULE_0__.wrapText)(h, node.value) + '-->')\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2hhc3QtdXRpbC10by1tZGFzdC9saWIvaGFuZGxlcnMvY29tbWVudC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ0EsYUFBYSw4QkFBOEI7QUFDM0MsYUFBYSwrQkFBK0I7QUFDNUM7QUFDNkM7O0FBRTdDO0FBQ0EsVUFBVTtBQUNWLFdBQVcsU0FBUztBQUNwQjtBQUNPO0FBQ1Asa0NBQWtDLDREQUFRO0FBQzFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQG9wZW5jYW52YXMvd2ViLy4uLy4uL25vZGVfbW9kdWxlcy9oYXN0LXV0aWwtdG8tbWRhc3QvbGliL2hhbmRsZXJzL2NvbW1lbnQuanM/ZmRiZSJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuSGFuZGxlfSBIYW5kbGVcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuQ29tbWVudH0gQ29tbWVudFxuICovXG5pbXBvcnQge3dyYXBUZXh0fSBmcm9tICcuLi91dGlsL3dyYXAtdGV4dC5qcydcblxuLyoqXG4gKiBAdHlwZSB7SGFuZGxlfVxuICogQHBhcmFtIHtDb21tZW50fSBub2RlXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBjb21tZW50KGgsIG5vZGUpIHtcbiAgcmV0dXJuIGgobm9kZSwgJ2h0bWwnLCAnPCEtLScgKyB3cmFwVGV4dChoLCBub2RlLnZhbHVlKSArICctLT4nKVxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/hast-util-to-mdast/lib/handlers/comment.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/hast-util-to-mdast/lib/handlers/del.js":
/*!*****************************************************************!*\
  !*** ../../node_modules/hast-util-to-mdast/lib/handlers/del.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   del: () => (/* binding */ del)\n/* harmony export */ });\n/* harmony import */ var _all_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../all.js */ \"(ssr)/../../node_modules/hast-util-to-mdast/lib/all.js\");\n/**\n * @typedef {import('../types.js').Handle} Handle\n * @typedef {import('../types.js').Element} Element\n */\n\n\n\n/**\n * @type {Handle}\n * @param {Element} node\n */\nfunction del(h, node) {\n  return h(node, 'delete', (0,_all_js__WEBPACK_IMPORTED_MODULE_0__.all)(h, node))\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2hhc3QtdXRpbC10by1tZGFzdC9saWIvaGFuZGxlcnMvZGVsLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDQSxhQUFhLDhCQUE4QjtBQUMzQyxhQUFhLCtCQUErQjtBQUM1Qzs7QUFFNkI7O0FBRTdCO0FBQ0EsVUFBVTtBQUNWLFdBQVcsU0FBUztBQUNwQjtBQUNPO0FBQ1AsMkJBQTJCLDRDQUFHO0FBQzlCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQG9wZW5jYW52YXMvd2ViLy4uLy4uL25vZGVfbW9kdWxlcy9oYXN0LXV0aWwtdG8tbWRhc3QvbGliL2hhbmRsZXJzL2RlbC5qcz85ODU3Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5IYW5kbGV9IEhhbmRsZVxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5FbGVtZW50fSBFbGVtZW50XG4gKi9cblxuaW1wb3J0IHthbGx9IGZyb20gJy4uL2FsbC5qcydcblxuLyoqXG4gKiBAdHlwZSB7SGFuZGxlfVxuICogQHBhcmFtIHtFbGVtZW50fSBub2RlXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBkZWwoaCwgbm9kZSkge1xuICByZXR1cm4gaChub2RlLCAnZGVsZXRlJywgYWxsKGgsIG5vZGUpKVxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/hast-util-to-mdast/lib/handlers/del.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/hast-util-to-mdast/lib/handlers/dl.js":
/*!****************************************************************!*\
  !*** ../../node_modules/hast-util-to-mdast/lib/handlers/dl.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   dl: () => (/* binding */ dl)\n/* harmony export */ });\n/* harmony import */ var hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! hast-util-is-element */ \"(ssr)/../../node_modules/hast-util-is-element/index.js\");\n/* harmony import */ var _util_list_items_spread_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/list-items-spread.js */ \"(ssr)/../../node_modules/hast-util-to-mdast/lib/util/list-items-spread.js\");\n/* harmony import */ var _util_wrap_list_items_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../util/wrap-list-items.js */ \"(ssr)/../../node_modules/hast-util-to-mdast/lib/util/wrap-list-items.js\");\n/**\n * @typedef {import('../types.js').H} H\n * @typedef {import('../types.js').Handle} Handle\n * @typedef {import('../types.js').Element} Element\n * @typedef {import('../types.js').ElementChild} ElementChild\n * @typedef {import('../types.js').MdastNode} MdastNode\n * @typedef {import('../types.js').MdastListContent} MdastListContent\n * @typedef {import('../types.js').MdastBlockContent} MdastBlockContent\n * @typedef {import('../types.js').MdastDefinitionContent} MdastDefinitionContent\n *\n * @typedef Group\n * @property {Array<Element>} titles\n * @property {Array<ElementChild>} definitions\n */\n\n\n\n\n\nconst div = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)('div')\nconst dt = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)('dt')\nconst dd = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)('dd')\n\n/**\n * @type {Handle}\n * @param {Element} node\n */\nfunction dl(h, node) {\n  const children = node.children\n  let index = -1\n  /** @type {Array<ElementChild>} */\n  let clean = []\n  /** @type {Array<Group>} */\n  const groups = []\n  /** @type {Group} */\n  let group = {titles: [], definitions: []}\n  /** @type {ElementChild} */\n  let child\n  /** @type {Array<MdastBlockContent|MdastDefinitionContent>} */\n  let result\n\n  // Unwrap `<div>`s\n  while (++index < children.length) {\n    child = children[index]\n    clean = clean.concat(div(child) ? child.children : child)\n  }\n\n  index = -1\n\n  // Group titles and definitions.\n  while (++index < clean.length) {\n    child = clean[index]\n\n    if (dt(child)) {\n      if (dd(clean[index - 1])) {\n        groups.push(group)\n        group = {titles: [], definitions: []}\n      }\n\n      group.titles.push(child)\n    } else {\n      group.definitions.push(child)\n    }\n  }\n\n  groups.push(group)\n\n  // Create items.\n  index = -1\n  /** @type {Array<MdastListContent>} */\n  const content = []\n\n  while (++index < groups.length) {\n    result = [\n      ...handle(h, groups[index].titles),\n      ...handle(h, groups[index].definitions)\n    ]\n\n    if (result.length > 0) {\n      content.push({\n        type: 'listItem',\n        spread: result.length > 1,\n        checked: null,\n        children: result\n      })\n    }\n  }\n\n  // Create a list if there are items.\n  if (content.length > 0) {\n    return h(\n      node,\n      'list',\n      {ordered: false, start: null, spread: (0,_util_list_items_spread_js__WEBPACK_IMPORTED_MODULE_1__.listItemsSpread)(content)},\n      content\n    )\n  }\n}\n\n/**\n * @param {H} h\n * @param {Array<ElementChild>} children\n * @returns {Array<MdastBlockContent|MdastDefinitionContent>}\n */\nfunction handle(h, children) {\n  const nodes = (0,_util_wrap_list_items_js__WEBPACK_IMPORTED_MODULE_2__.wrapListItems)(h, {type: 'element', tagName: 'x', children})\n\n  if (nodes.length === 0) {\n    return []\n  }\n\n  if (nodes.length === 1) {\n    return nodes[0].children\n  }\n\n  return [\n    {\n      type: 'list',\n      ordered: false,\n      start: null,\n      spread: (0,_util_list_items_spread_js__WEBPACK_IMPORTED_MODULE_1__.listItemsSpread)(nodes),\n      children: nodes\n    }\n  ]\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/hast-util-to-mdast/lib/handlers/dl.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/hast-util-to-mdast/lib/handlers/em.js":
/*!****************************************************************!*\
  !*** ../../node_modules/hast-util-to-mdast/lib/handlers/em.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   em: () => (/* binding */ em)\n/* harmony export */ });\n/* harmony import */ var _all_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../all.js */ \"(ssr)/../../node_modules/hast-util-to-mdast/lib/all.js\");\n/**\n * @typedef {import('../types.js').Handle} Handle\n * @typedef {import('../types.js').Element} Element\n */\n\n\n\n/**\n * @type {Handle}\n * @param {Element} node\n */\nfunction em(h, node) {\n  return h(node, 'emphasis', (0,_all_js__WEBPACK_IMPORTED_MODULE_0__.all)(h, node))\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2hhc3QtdXRpbC10by1tZGFzdC9saWIvaGFuZGxlcnMvZW0uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBLGFBQWEsOEJBQThCO0FBQzNDLGFBQWEsK0JBQStCO0FBQzVDOztBQUU2Qjs7QUFFN0I7QUFDQSxVQUFVO0FBQ1YsV0FBVyxTQUFTO0FBQ3BCO0FBQ087QUFDUCw2QkFBNkIsNENBQUc7QUFDaEMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9Ab3BlbmNhbnZhcy93ZWIvLi4vLi4vbm9kZV9tb2R1bGVzL2hhc3QtdXRpbC10by1tZGFzdC9saWIvaGFuZGxlcnMvZW0uanM/ZTZlMSJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuSGFuZGxlfSBIYW5kbGVcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuRWxlbWVudH0gRWxlbWVudFxuICovXG5cbmltcG9ydCB7YWxsfSBmcm9tICcuLi9hbGwuanMnXG5cbi8qKlxuICogQHR5cGUge0hhbmRsZX1cbiAqIEBwYXJhbSB7RWxlbWVudH0gbm9kZVxuICovXG5leHBvcnQgZnVuY3Rpb24gZW0oaCwgbm9kZSkge1xuICByZXR1cm4gaChub2RlLCAnZW1waGFzaXMnLCBhbGwoaCwgbm9kZSkpXG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/hast-util-to-mdast/lib/handlers/em.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/hast-util-to-mdast/lib/handlers/heading.js":
/*!*********************************************************************!*\
  !*** ../../node_modules/hast-util-to-mdast/lib/handlers/heading.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   heading: () => (/* binding */ heading)\n/* harmony export */ });\n/* harmony import */ var _all_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../all.js */ \"(ssr)/../../node_modules/hast-util-to-mdast/lib/all.js\");\n/**\n * @typedef {import('../types.js').Handle} Handle\n * @typedef {import('../types.js').Element} Element\n * @typedef {import('../types.js').MdastNode} MdastNode\n */\n\n\n\n/**\n * @type {Handle}\n * @param {Element} node\n */\nfunction heading(h, node) {\n  // `else` shouldn’t happen, of course…\n  /* c8 ignore next */\n  const depth = Number(node.tagName.charAt(1)) || 1\n  const wrap = h.wrapText\n\n  h.wrapText = false\n  const result = h(node, 'heading', {depth}, (0,_all_js__WEBPACK_IMPORTED_MODULE_0__.all)(h, node))\n  h.wrapText = wrap\n\n  return result\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2hhc3QtdXRpbC10by1tZGFzdC9saWIvaGFuZGxlcnMvaGVhZGluZy5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ0EsYUFBYSw4QkFBOEI7QUFDM0MsYUFBYSwrQkFBK0I7QUFDNUMsYUFBYSxpQ0FBaUM7QUFDOUM7O0FBRTZCOztBQUU3QjtBQUNBLFVBQVU7QUFDVixXQUFXLFNBQVM7QUFDcEI7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EscUNBQXFDLE1BQU0sRUFBRSw0Q0FBRztBQUNoRDs7QUFFQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQG9wZW5jYW52YXMvd2ViLy4uLy4uL25vZGVfbW9kdWxlcy9oYXN0LXV0aWwtdG8tbWRhc3QvbGliL2hhbmRsZXJzL2hlYWRpbmcuanM/YmViMSJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuSGFuZGxlfSBIYW5kbGVcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuRWxlbWVudH0gRWxlbWVudFxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5NZGFzdE5vZGV9IE1kYXN0Tm9kZVxuICovXG5cbmltcG9ydCB7YWxsfSBmcm9tICcuLi9hbGwuanMnXG5cbi8qKlxuICogQHR5cGUge0hhbmRsZX1cbiAqIEBwYXJhbSB7RWxlbWVudH0gbm9kZVxuICovXG5leHBvcnQgZnVuY3Rpb24gaGVhZGluZyhoLCBub2RlKSB7XG4gIC8vIGBlbHNlYCBzaG91bGRu4oCZdCBoYXBwZW4sIG9mIGNvdXJzZeKAplxuICAvKiBjOCBpZ25vcmUgbmV4dCAqL1xuICBjb25zdCBkZXB0aCA9IE51bWJlcihub2RlLnRhZ05hbWUuY2hhckF0KDEpKSB8fCAxXG4gIGNvbnN0IHdyYXAgPSBoLndyYXBUZXh0XG5cbiAgaC53cmFwVGV4dCA9IGZhbHNlXG4gIGNvbnN0IHJlc3VsdCA9IGgobm9kZSwgJ2hlYWRpbmcnLCB7ZGVwdGh9LCBhbGwoaCwgbm9kZSkpXG4gIGgud3JhcFRleHQgPSB3cmFwXG5cbiAgcmV0dXJuIHJlc3VsdFxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/hast-util-to-mdast/lib/handlers/heading.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/hast-util-to-mdast/lib/handlers/hr.js":
/*!****************************************************************!*\
  !*** ../../node_modules/hast-util-to-mdast/lib/handlers/hr.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   hr: () => (/* binding */ hr)\n/* harmony export */ });\n/**\n * @typedef {import('../types.js').Handle} Handle\n * @typedef {import('../types.js').Element} Element\n */\n\n/**\n * @type {Handle}\n * @param {Element} node\n */\nfunction hr(h, node) {\n  return h(node, 'thematicBreak')\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2hhc3QtdXRpbC10by1tZGFzdC9saWIvaGFuZGxlcnMvaHIuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0EsYUFBYSw4QkFBOEI7QUFDM0MsYUFBYSwrQkFBK0I7QUFDNUM7O0FBRUE7QUFDQSxVQUFVO0FBQ1YsV0FBVyxTQUFTO0FBQ3BCO0FBQ087QUFDUDtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQG9wZW5jYW52YXMvd2ViLy4uLy4uL25vZGVfbW9kdWxlcy9oYXN0LXV0aWwtdG8tbWRhc3QvbGliL2hhbmRsZXJzL2hyLmpzP2Q5NzAiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLkhhbmRsZX0gSGFuZGxlXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLkVsZW1lbnR9IEVsZW1lbnRcbiAqL1xuXG4vKipcbiAqIEB0eXBlIHtIYW5kbGV9XG4gKiBAcGFyYW0ge0VsZW1lbnR9IG5vZGVcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGhyKGgsIG5vZGUpIHtcbiAgcmV0dXJuIGgobm9kZSwgJ3RoZW1hdGljQnJlYWsnKVxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/hast-util-to-mdast/lib/handlers/hr.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/hast-util-to-mdast/lib/handlers/iframe.js":
/*!********************************************************************!*\
  !*** ../../node_modules/hast-util-to-mdast/lib/handlers/iframe.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   iframe: () => (/* binding */ iframe)\n/* harmony export */ });\n/* harmony import */ var _util_resolve_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/resolve.js */ \"(ssr)/../../node_modules/hast-util-to-mdast/lib/util/resolve.js\");\n/* harmony import */ var _util_wrap_text_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/wrap-text.js */ \"(ssr)/../../node_modules/hast-util-to-mdast/lib/util/wrap-text.js\");\n/**\n * @typedef {import('../types.js').Handle} Handle\n * @typedef {import('../types.js').Element} Element\n * @typedef {import('../types.js').Properties} Properties\n */\n\n\n\n\n/**\n * @type {Handle}\n * @param {Element} node\n */\nfunction iframe(h, node) {\n  /** @type {Properties} */\n  // @ts-expect-error: `props` are defined.\n  const props = node.properties\n  const src = String(props.src || '')\n  const title = String(props.title || '')\n\n  // Only create a link if there is a title.\n  // We can’t use the content of the frame because conforming HTML parsers treat\n  // it as text, whereas legacy parsers treat it as HTML, so it will likely\n  // contain tags that will show up in text.\n  if (src && title) {\n    return {\n      type: 'link',\n      title: null,\n      url: (0,_util_resolve_js__WEBPACK_IMPORTED_MODULE_0__.resolve)(h, src),\n      children: [{type: 'text', value: (0,_util_wrap_text_js__WEBPACK_IMPORTED_MODULE_1__.wrapText)(h, title)}]\n    }\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2hhc3QtdXRpbC10by1tZGFzdC9saWIvaGFuZGxlcnMvaWZyYW1lLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBO0FBQ0EsYUFBYSw4QkFBOEI7QUFDM0MsYUFBYSwrQkFBK0I7QUFDNUMsYUFBYSxrQ0FBa0M7QUFDL0M7O0FBRTBDO0FBQ0c7O0FBRTdDO0FBQ0EsVUFBVTtBQUNWLFdBQVcsU0FBUztBQUNwQjtBQUNPO0FBQ1AsYUFBYSxZQUFZO0FBQ3pCO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLHlEQUFPO0FBQ2xCLGtCQUFrQixxQkFBcUIsNERBQVEsV0FBVztBQUMxRDtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9Ab3BlbmNhbnZhcy93ZWIvLi4vLi4vbm9kZV9tb2R1bGVzL2hhc3QtdXRpbC10by1tZGFzdC9saWIvaGFuZGxlcnMvaWZyYW1lLmpzP2VkZjAiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLkhhbmRsZX0gSGFuZGxlXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLkVsZW1lbnR9IEVsZW1lbnRcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuUHJvcGVydGllc30gUHJvcGVydGllc1xuICovXG5cbmltcG9ydCB7cmVzb2x2ZX0gZnJvbSAnLi4vdXRpbC9yZXNvbHZlLmpzJ1xuaW1wb3J0IHt3cmFwVGV4dH0gZnJvbSAnLi4vdXRpbC93cmFwLXRleHQuanMnXG5cbi8qKlxuICogQHR5cGUge0hhbmRsZX1cbiAqIEBwYXJhbSB7RWxlbWVudH0gbm9kZVxuICovXG5leHBvcnQgZnVuY3Rpb24gaWZyYW1lKGgsIG5vZGUpIHtcbiAgLyoqIEB0eXBlIHtQcm9wZXJ0aWVzfSAqL1xuICAvLyBAdHMtZXhwZWN0LWVycm9yOiBgcHJvcHNgIGFyZSBkZWZpbmVkLlxuICBjb25zdCBwcm9wcyA9IG5vZGUucHJvcGVydGllc1xuICBjb25zdCBzcmMgPSBTdHJpbmcocHJvcHMuc3JjIHx8ICcnKVxuICBjb25zdCB0aXRsZSA9IFN0cmluZyhwcm9wcy50aXRsZSB8fCAnJylcblxuICAvLyBPbmx5IGNyZWF0ZSBhIGxpbmsgaWYgdGhlcmUgaXMgYSB0aXRsZS5cbiAgLy8gV2UgY2Fu4oCZdCB1c2UgdGhlIGNvbnRlbnQgb2YgdGhlIGZyYW1lIGJlY2F1c2UgY29uZm9ybWluZyBIVE1MIHBhcnNlcnMgdHJlYXRcbiAgLy8gaXQgYXMgdGV4dCwgd2hlcmVhcyBsZWdhY3kgcGFyc2VycyB0cmVhdCBpdCBhcyBIVE1MLCBzbyBpdCB3aWxsIGxpa2VseVxuICAvLyBjb250YWluIHRhZ3MgdGhhdCB3aWxsIHNob3cgdXAgaW4gdGV4dC5cbiAgaWYgKHNyYyAmJiB0aXRsZSkge1xuICAgIHJldHVybiB7XG4gICAgICB0eXBlOiAnbGluaycsXG4gICAgICB0aXRsZTogbnVsbCxcbiAgICAgIHVybDogcmVzb2x2ZShoLCBzcmMpLFxuICAgICAgY2hpbGRyZW46IFt7dHlwZTogJ3RleHQnLCB2YWx1ZTogd3JhcFRleHQoaCwgdGl0bGUpfV1cbiAgICB9XG4gIH1cbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/hast-util-to-mdast/lib/handlers/iframe.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/hast-util-to-mdast/lib/handlers/img.js":
/*!*****************************************************************!*\
  !*** ../../node_modules/hast-util-to-mdast/lib/handlers/img.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   img: () => (/* binding */ img)\n/* harmony export */ });\n/* harmony import */ var _util_resolve_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/resolve.js */ \"(ssr)/../../node_modules/hast-util-to-mdast/lib/util/resolve.js\");\n/**\n * @typedef {import('../types.js').Handle} Handle\n * @typedef {import('../types.js').Element} Element\n * @typedef {import('../types.js').Properties} Properties\n */\n\n\n\n/**\n * @type {Handle}\n * @param {Element} node\n */\nfunction img(h, node) {\n  /** @type {Properties} */\n  // @ts-expect-error: `props` are defined.\n  const props = node.properties\n  return h(node, 'image', {\n    url: (0,_util_resolve_js__WEBPACK_IMPORTED_MODULE_0__.resolve)(h, String(props.src || '') || null),\n    title: props.title || null,\n    alt: props.alt || ''\n  })\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2hhc3QtdXRpbC10by1tZGFzdC9saWIvaGFuZGxlcnMvaW1nLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDQSxhQUFhLDhCQUE4QjtBQUMzQyxhQUFhLCtCQUErQjtBQUM1QyxhQUFhLGtDQUFrQztBQUMvQzs7QUFFMEM7O0FBRTFDO0FBQ0EsVUFBVTtBQUNWLFdBQVcsU0FBUztBQUNwQjtBQUNPO0FBQ1AsYUFBYSxZQUFZO0FBQ3pCO0FBQ0E7QUFDQTtBQUNBLFNBQVMseURBQU87QUFDaEI7QUFDQTtBQUNBLEdBQUc7QUFDSCIsInNvdXJjZXMiOlsid2VicGFjazovL0BvcGVuY2FudmFzL3dlYi8uLi8uLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi9oYW5kbGVycy9pbWcuanM/ZGEwYiJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuSGFuZGxlfSBIYW5kbGVcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuRWxlbWVudH0gRWxlbWVudFxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5Qcm9wZXJ0aWVzfSBQcm9wZXJ0aWVzXG4gKi9cblxuaW1wb3J0IHtyZXNvbHZlfSBmcm9tICcuLi91dGlsL3Jlc29sdmUuanMnXG5cbi8qKlxuICogQHR5cGUge0hhbmRsZX1cbiAqIEBwYXJhbSB7RWxlbWVudH0gbm9kZVxuICovXG5leHBvcnQgZnVuY3Rpb24gaW1nKGgsIG5vZGUpIHtcbiAgLyoqIEB0eXBlIHtQcm9wZXJ0aWVzfSAqL1xuICAvLyBAdHMtZXhwZWN0LWVycm9yOiBgcHJvcHNgIGFyZSBkZWZpbmVkLlxuICBjb25zdCBwcm9wcyA9IG5vZGUucHJvcGVydGllc1xuICByZXR1cm4gaChub2RlLCAnaW1hZ2UnLCB7XG4gICAgdXJsOiByZXNvbHZlKGgsIFN0cmluZyhwcm9wcy5zcmMgfHwgJycpIHx8IG51bGwpLFxuICAgIHRpdGxlOiBwcm9wcy50aXRsZSB8fCBudWxsLFxuICAgIGFsdDogcHJvcHMuYWx0IHx8ICcnXG4gIH0pXG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/hast-util-to-mdast/lib/handlers/img.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/hast-util-to-mdast/lib/handlers/index.js":
/*!*******************************************************************!*\
  !*** ../../node_modules/hast-util-to-mdast/lib/handlers/index.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   handlers: () => (/* binding */ handlers)\n/* harmony export */ });\n/* harmony import */ var _all_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../all.js */ \"(ssr)/../../node_modules/hast-util-to-mdast/lib/all.js\");\n/* harmony import */ var _util_wrap_children_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../util/wrap-children.js */ \"(ssr)/../../node_modules/hast-util-to-mdast/lib/util/wrap-children.js\");\n/* harmony import */ var _a_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./a.js */ \"(ssr)/../../node_modules/hast-util-to-mdast/lib/handlers/a.js\");\n/* harmony import */ var _base_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./base.js */ \"(ssr)/../../node_modules/hast-util-to-mdast/lib/handlers/base.js\");\n/* harmony import */ var _blockquote_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./blockquote.js */ \"(ssr)/../../node_modules/hast-util-to-mdast/lib/handlers/blockquote.js\");\n/* harmony import */ var _br_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./br.js */ \"(ssr)/../../node_modules/hast-util-to-mdast/lib/handlers/br.js\");\n/* harmony import */ var _code_js__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! ./code.js */ \"(ssr)/../../node_modules/hast-util-to-mdast/lib/handlers/code.js\");\n/* harmony import */ var _comment_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./comment.js */ \"(ssr)/../../node_modules/hast-util-to-mdast/lib/handlers/comment.js\");\n/* harmony import */ var _del_js__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./del.js */ \"(ssr)/../../node_modules/hast-util-to-mdast/lib/handlers/del.js\");\n/* harmony import */ var _dl_js__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./dl.js */ \"(ssr)/../../node_modules/hast-util-to-mdast/lib/handlers/dl.js\");\n/* harmony import */ var _em_js__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./em.js */ \"(ssr)/../../node_modules/hast-util-to-mdast/lib/handlers/em.js\");\n/* harmony import */ var _heading_js__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./heading.js */ \"(ssr)/../../node_modules/hast-util-to-mdast/lib/handlers/heading.js\");\n/* harmony import */ var _hr_js__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ./hr.js */ \"(ssr)/../../node_modules/hast-util-to-mdast/lib/handlers/hr.js\");\n/* harmony import */ var _iframe_js__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./iframe.js */ \"(ssr)/../../node_modules/hast-util-to-mdast/lib/handlers/iframe.js\");\n/* harmony import */ var _img_js__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ./img.js */ \"(ssr)/../../node_modules/hast-util-to-mdast/lib/handlers/img.js\");\n/* harmony import */ var _inline_code_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./inline-code.js */ \"(ssr)/../../node_modules/hast-util-to-mdast/lib/handlers/inline-code.js\");\n/* harmony import */ var _input_js__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ./input.js */ \"(ssr)/../../node_modules/hast-util-to-mdast/lib/handlers/input.js\");\n/* harmony import */ var _li_js__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./li.js */ \"(ssr)/../../node_modules/hast-util-to-mdast/lib/handlers/li.js\");\n/* harmony import */ var _list_js__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./list.js */ \"(ssr)/../../node_modules/hast-util-to-mdast/lib/handlers/list.js\");\n/* harmony import */ var _media_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./media.js */ \"(ssr)/../../node_modules/hast-util-to-mdast/lib/handlers/media.js\");\n/* harmony import */ var _p_js__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! ./p.js */ \"(ssr)/../../node_modules/hast-util-to-mdast/lib/handlers/p.js\");\n/* harmony import */ var _q_js__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! ./q.js */ \"(ssr)/../../node_modules/hast-util-to-mdast/lib/handlers/q.js\");\n/* harmony import */ var _root_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./root.js */ \"(ssr)/../../node_modules/hast-util-to-mdast/lib/handlers/root.js\");\n/* harmony import */ var _select_js__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! ./select.js */ \"(ssr)/../../node_modules/hast-util-to-mdast/lib/handlers/select.js\");\n/* harmony import */ var _strong_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./strong.js */ \"(ssr)/../../node_modules/hast-util-to-mdast/lib/handlers/strong.js\");\n/* harmony import */ var _table_cell_js__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! ./table-cell.js */ \"(ssr)/../../node_modules/hast-util-to-mdast/lib/handlers/table-cell.js\");\n/* harmony import */ var _table_row_js__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! ./table-row.js */ \"(ssr)/../../node_modules/hast-util-to-mdast/lib/handlers/table-row.js\");\n/* harmony import */ var _table_js__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! ./table.js */ \"(ssr)/../../node_modules/hast-util-to-mdast/lib/handlers/table.js\");\n/* harmony import */ var _text_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./text.js */ \"(ssr)/../../node_modules/hast-util-to-mdast/lib/handlers/text.js\");\n/* harmony import */ var _textarea_js__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! ./textarea.js */ \"(ssr)/../../node_modules/hast-util-to-mdast/lib/handlers/textarea.js\");\n/* harmony import */ var _wbr_js__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! ./wbr.js */ \"(ssr)/../../node_modules/hast-util-to-mdast/lib/handlers/wbr.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst handlers = {\n  root: _root_js__WEBPACK_IMPORTED_MODULE_0__.root,\n  text: _text_js__WEBPACK_IMPORTED_MODULE_1__.text,\n  comment: _comment_js__WEBPACK_IMPORTED_MODULE_2__.comment,\n  doctype: ignore,\n\n  applet: ignore,\n  area: ignore,\n  basefont: ignore,\n  bgsound: ignore,\n  caption: ignore,\n  col: ignore,\n  colgroup: ignore,\n  command: ignore,\n  content: ignore,\n  datalist: ignore,\n  dialog: ignore,\n  element: ignore,\n  embed: ignore,\n  frame: ignore,\n  frameset: ignore,\n  isindex: ignore,\n  keygen: ignore,\n  link: ignore,\n  math: ignore,\n  menu: ignore,\n  menuitem: ignore,\n  meta: ignore,\n  nextid: ignore,\n  noembed: ignore,\n  noframes: ignore,\n  optgroup: ignore,\n  option: ignore,\n  param: ignore,\n  script: ignore,\n  shadow: ignore,\n  source: ignore,\n  spacer: ignore,\n  style: ignore,\n  svg: ignore,\n  template: ignore,\n  title: ignore,\n  track: ignore,\n\n  abbr: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  acronym: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  bdi: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  bdo: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  big: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  blink: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  button: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  canvas: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  cite: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  data: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  details: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  dfn: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  font: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  ins: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  label: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  map: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  marquee: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  meter: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  nobr: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  noscript: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  object: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  output: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  progress: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  rb: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  rbc: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  rp: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  rt: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  rtc: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  ruby: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  slot: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  small: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  span: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  sup: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  sub: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  tbody: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  tfoot: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  thead: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  time: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n\n  address: _util_wrap_children_js__WEBPACK_IMPORTED_MODULE_4__.wrapChildren,\n  article: _util_wrap_children_js__WEBPACK_IMPORTED_MODULE_4__.wrapChildren,\n  aside: _util_wrap_children_js__WEBPACK_IMPORTED_MODULE_4__.wrapChildren,\n  body: _util_wrap_children_js__WEBPACK_IMPORTED_MODULE_4__.wrapChildren,\n  center: _util_wrap_children_js__WEBPACK_IMPORTED_MODULE_4__.wrapChildren,\n  div: _util_wrap_children_js__WEBPACK_IMPORTED_MODULE_4__.wrapChildren,\n  fieldset: _util_wrap_children_js__WEBPACK_IMPORTED_MODULE_4__.wrapChildren,\n  figcaption: _util_wrap_children_js__WEBPACK_IMPORTED_MODULE_4__.wrapChildren,\n  figure: _util_wrap_children_js__WEBPACK_IMPORTED_MODULE_4__.wrapChildren,\n  form: _util_wrap_children_js__WEBPACK_IMPORTED_MODULE_4__.wrapChildren,\n  footer: _util_wrap_children_js__WEBPACK_IMPORTED_MODULE_4__.wrapChildren,\n  header: _util_wrap_children_js__WEBPACK_IMPORTED_MODULE_4__.wrapChildren,\n  hgroup: _util_wrap_children_js__WEBPACK_IMPORTED_MODULE_4__.wrapChildren,\n  html: _util_wrap_children_js__WEBPACK_IMPORTED_MODULE_4__.wrapChildren,\n  legend: _util_wrap_children_js__WEBPACK_IMPORTED_MODULE_4__.wrapChildren,\n  main: _util_wrap_children_js__WEBPACK_IMPORTED_MODULE_4__.wrapChildren,\n  multicol: _util_wrap_children_js__WEBPACK_IMPORTED_MODULE_4__.wrapChildren,\n  nav: _util_wrap_children_js__WEBPACK_IMPORTED_MODULE_4__.wrapChildren,\n  picture: _util_wrap_children_js__WEBPACK_IMPORTED_MODULE_4__.wrapChildren,\n  section: _util_wrap_children_js__WEBPACK_IMPORTED_MODULE_4__.wrapChildren,\n\n  a: _a_js__WEBPACK_IMPORTED_MODULE_5__.a,\n  audio: _media_js__WEBPACK_IMPORTED_MODULE_6__.media,\n  b: _strong_js__WEBPACK_IMPORTED_MODULE_7__.strong,\n  base: _base_js__WEBPACK_IMPORTED_MODULE_8__.base,\n  blockquote: _blockquote_js__WEBPACK_IMPORTED_MODULE_9__.blockquote,\n  br: _br_js__WEBPACK_IMPORTED_MODULE_10__.br,\n  code: _inline_code_js__WEBPACK_IMPORTED_MODULE_11__.inlineCode,\n  dir: _list_js__WEBPACK_IMPORTED_MODULE_12__.list,\n  dl: _dl_js__WEBPACK_IMPORTED_MODULE_13__.dl,\n  dt: _li_js__WEBPACK_IMPORTED_MODULE_14__.li,\n  dd: _li_js__WEBPACK_IMPORTED_MODULE_14__.li,\n  del: _del_js__WEBPACK_IMPORTED_MODULE_15__.del,\n  em: _em_js__WEBPACK_IMPORTED_MODULE_16__.em,\n  h1: _heading_js__WEBPACK_IMPORTED_MODULE_17__.heading,\n  h2: _heading_js__WEBPACK_IMPORTED_MODULE_17__.heading,\n  h3: _heading_js__WEBPACK_IMPORTED_MODULE_17__.heading,\n  h4: _heading_js__WEBPACK_IMPORTED_MODULE_17__.heading,\n  h5: _heading_js__WEBPACK_IMPORTED_MODULE_17__.heading,\n  h6: _heading_js__WEBPACK_IMPORTED_MODULE_17__.heading,\n  hr: _hr_js__WEBPACK_IMPORTED_MODULE_18__.hr,\n  i: _em_js__WEBPACK_IMPORTED_MODULE_16__.em,\n  iframe: _iframe_js__WEBPACK_IMPORTED_MODULE_19__.iframe,\n  img: _img_js__WEBPACK_IMPORTED_MODULE_20__.img,\n  image: _img_js__WEBPACK_IMPORTED_MODULE_20__.img,\n  input: _input_js__WEBPACK_IMPORTED_MODULE_21__.input,\n  kbd: _inline_code_js__WEBPACK_IMPORTED_MODULE_11__.inlineCode,\n  li: _li_js__WEBPACK_IMPORTED_MODULE_14__.li,\n  listing: _code_js__WEBPACK_IMPORTED_MODULE_22__.code,\n  mark: _em_js__WEBPACK_IMPORTED_MODULE_16__.em,\n  ol: _list_js__WEBPACK_IMPORTED_MODULE_12__.list,\n  p: _p_js__WEBPACK_IMPORTED_MODULE_23__.p,\n  plaintext: _code_js__WEBPACK_IMPORTED_MODULE_22__.code,\n  pre: _code_js__WEBPACK_IMPORTED_MODULE_22__.code,\n  q: _q_js__WEBPACK_IMPORTED_MODULE_24__.q,\n  s: _del_js__WEBPACK_IMPORTED_MODULE_15__.del,\n  samp: _inline_code_js__WEBPACK_IMPORTED_MODULE_11__.inlineCode,\n  select: _select_js__WEBPACK_IMPORTED_MODULE_25__.select,\n  strike: _del_js__WEBPACK_IMPORTED_MODULE_15__.del,\n  strong: _strong_js__WEBPACK_IMPORTED_MODULE_7__.strong,\n  summary: _p_js__WEBPACK_IMPORTED_MODULE_23__.p,\n  table: _table_js__WEBPACK_IMPORTED_MODULE_26__.table,\n  td: _table_cell_js__WEBPACK_IMPORTED_MODULE_27__.tableCell,\n  textarea: _textarea_js__WEBPACK_IMPORTED_MODULE_28__.textarea,\n  th: _table_cell_js__WEBPACK_IMPORTED_MODULE_27__.tableCell,\n  tr: _table_row_js__WEBPACK_IMPORTED_MODULE_29__.tableRow,\n  tt: _inline_code_js__WEBPACK_IMPORTED_MODULE_11__.inlineCode,\n  u: _em_js__WEBPACK_IMPORTED_MODULE_16__.em,\n  ul: _list_js__WEBPACK_IMPORTED_MODULE_12__.list,\n  var: _inline_code_js__WEBPACK_IMPORTED_MODULE_11__.inlineCode,\n  video: _media_js__WEBPACK_IMPORTED_MODULE_6__.media,\n  wbr: _wbr_js__WEBPACK_IMPORTED_MODULE_30__.wbr,\n  xmp: _code_js__WEBPACK_IMPORTED_MODULE_22__.code\n}\n\nfunction ignore() {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/hast-util-to-mdast/lib/handlers/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/hast-util-to-mdast/lib/handlers/inline-code.js":
/*!*************************************************************************!*\
  !*** ../../node_modules/hast-util-to-mdast/lib/handlers/inline-code.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   inlineCode: () => (/* binding */ inlineCode)\n/* harmony export */ });\n/* harmony import */ var hast_util_to_text__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! hast-util-to-text */ \"(ssr)/../../node_modules/hast-util-to-mdast/node_modules/hast-util-to-text/lib/index.js\");\n/* harmony import */ var _util_wrap_text_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/wrap-text.js */ \"(ssr)/../../node_modules/hast-util-to-mdast/lib/util/wrap-text.js\");\n/**\n * @typedef {import('../types.js').Handle} Handle\n * @typedef {import('../types.js').Element} Element\n */\n\n\n\n\n/**\n * @type {Handle}\n * @param {Element} node\n */\nfunction inlineCode(h, node) {\n  return h(node, 'inlineCode', (0,_util_wrap_text_js__WEBPACK_IMPORTED_MODULE_0__.wrapText)(h, (0,hast_util_to_text__WEBPACK_IMPORTED_MODULE_1__.toText)(node)))\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2hhc3QtdXRpbC10by1tZGFzdC9saWIvaGFuZGxlcnMvaW5saW5lLWNvZGUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQUE7QUFDQSxhQUFhLDhCQUE4QjtBQUMzQyxhQUFhLCtCQUErQjtBQUM1Qzs7QUFFd0M7QUFDSzs7QUFFN0M7QUFDQSxVQUFVO0FBQ1YsV0FBVyxTQUFTO0FBQ3BCO0FBQ087QUFDUCwrQkFBK0IsNERBQVEsSUFBSSx5REFBTTtBQUNqRCIsInNvdXJjZXMiOlsid2VicGFjazovL0BvcGVuY2FudmFzL3dlYi8uLi8uLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi9oYW5kbGVycy9pbmxpbmUtY29kZS5qcz9lYWYzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5IYW5kbGV9IEhhbmRsZVxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5FbGVtZW50fSBFbGVtZW50XG4gKi9cblxuaW1wb3J0IHt0b1RleHR9IGZyb20gJ2hhc3QtdXRpbC10by10ZXh0J1xuaW1wb3J0IHt3cmFwVGV4dH0gZnJvbSAnLi4vdXRpbC93cmFwLXRleHQuanMnXG5cbi8qKlxuICogQHR5cGUge0hhbmRsZX1cbiAqIEBwYXJhbSB7RWxlbWVudH0gbm9kZVxuICovXG5leHBvcnQgZnVuY3Rpb24gaW5saW5lQ29kZShoLCBub2RlKSB7XG4gIHJldHVybiBoKG5vZGUsICdpbmxpbmVDb2RlJywgd3JhcFRleHQoaCwgdG9UZXh0KG5vZGUpKSlcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/hast-util-to-mdast/lib/handlers/inline-code.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/hast-util-to-mdast/lib/handlers/input.js":
/*!*******************************************************************!*\
  !*** ../../node_modules/hast-util-to-mdast/lib/handlers/input.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   input: () => (/* binding */ input)\n/* harmony export */ });\n/* harmony import */ var hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! hast-util-is-element */ \"(ssr)/../../node_modules/hast-util-is-element/index.js\");\n/* harmony import */ var _util_find_selected_options_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../util/find-selected-options.js */ \"(ssr)/../../node_modules/hast-util-to-mdast/lib/util/find-selected-options.js\");\n/* harmony import */ var _util_own_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../util/own.js */ \"(ssr)/../../node_modules/hast-util-to-mdast/lib/util/own.js\");\n/* harmony import */ var _util_resolve_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../util/resolve.js */ \"(ssr)/../../node_modules/hast-util-to-mdast/lib/util/resolve.js\");\n/* harmony import */ var _util_wrap_text_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/wrap-text.js */ \"(ssr)/../../node_modules/hast-util-to-mdast/lib/util/wrap-text.js\");\n/**\n * @typedef {import('../types.js').Handle} Handle\n * @typedef {import('../types.js').Element} Element\n * @typedef {import('../types.js').Properties} Properties\n * @typedef {import('../types.js').MdastNode} MdastNode\n */\n\n\n\n\n\n\n\nconst datalist = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)('datalist')\n\n/**\n * @type {Handle}\n * @param {Element} node\n */\n// eslint-disable-next-line complexity\nfunction input(h, node) {\n  /** @type {Properties} */\n  // @ts-expect-error: `props` are defined.\n  const props = node.properties\n  let value = String(props.value || props.placeholder || '')\n  /** @type {Array<MdastNode>} */\n  const results = []\n  /** @type {Array<string>} */\n  const texts = []\n  /** @type {Array<[string, string|null]>} */\n  let values = []\n  let index = -1\n  /** @type {string} */\n  let list\n\n  if (props.disabled || props.type === 'hidden' || props.type === 'file') {\n    return\n  }\n\n  if (props.type === 'checkbox' || props.type === 'radio') {\n    return h(\n      node,\n      'text',\n      (0,_util_wrap_text_js__WEBPACK_IMPORTED_MODULE_1__.wrapText)(h, h[props.checked ? 'checked' : 'unchecked'])\n    )\n  }\n\n  if (props.type === 'image') {\n    return props.alt || value\n      ? h(node, 'image', {\n          url: (0,_util_resolve_js__WEBPACK_IMPORTED_MODULE_2__.resolve)(h, String(props.src || '') || null),\n          title: (0,_util_wrap_text_js__WEBPACK_IMPORTED_MODULE_1__.wrapText)(h, String(props.title || '')) || null,\n          alt: (0,_util_wrap_text_js__WEBPACK_IMPORTED_MODULE_1__.wrapText)(h, String(props.alt || value))\n        })\n      : []\n  }\n\n  if (value) {\n    values = [[value, null]]\n  } else if (\n    // `list` is not supported on these types:\n    props.type !== 'password' &&\n    props.type !== 'file' &&\n    props.type !== 'submit' &&\n    props.type !== 'reset' &&\n    props.type !== 'button' &&\n    props.list\n  ) {\n    list = String(props.list).toUpperCase()\n\n    if (_util_own_js__WEBPACK_IMPORTED_MODULE_3__.own.call(h.nodeById, list) && datalist(h.nodeById[list])) {\n      values = (0,_util_find_selected_options_js__WEBPACK_IMPORTED_MODULE_4__.findSelectedOptions)(h, h.nodeById[list], props)\n    }\n  }\n\n  if (values.length === 0) {\n    return\n  }\n\n  // Hide password value.\n  if (props.type === 'password') {\n    // Passwords don’t support `list`.\n    values[0] = ['•'.repeat(values[0][0].length), null]\n  }\n\n  if (props.type === 'url' || props.type === 'email') {\n    while (++index < values.length) {\n      value = (0,_util_resolve_js__WEBPACK_IMPORTED_MODULE_2__.resolve)(h, values[index][0])\n\n      results.push(\n        h(\n          node,\n          'link',\n          {\n            title: null,\n            url: (0,_util_wrap_text_js__WEBPACK_IMPORTED_MODULE_1__.wrapText)(h, props.type === 'email' ? 'mailto:' + value : value)\n          },\n          [{type: 'text', value: (0,_util_wrap_text_js__WEBPACK_IMPORTED_MODULE_1__.wrapText)(h, values[index][1] || value)}]\n        )\n      )\n\n      if (index !== values.length - 1) {\n        results.push({type: 'text', value: ', '})\n      }\n    }\n\n    return results\n  }\n\n  while (++index < values.length) {\n    texts.push(\n      values[index][1]\n        ? values[index][1] + ' (' + values[index][0] + ')'\n        : values[index][0]\n    )\n  }\n\n  return h(node, 'text', (0,_util_wrap_text_js__WEBPACK_IMPORTED_MODULE_1__.wrapText)(h, texts.join(', ')))\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/hast-util-to-mdast/lib/handlers/input.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/hast-util-to-mdast/lib/handlers/li.js":
/*!****************************************************************!*\
  !*** ../../node_modules/hast-util-to-mdast/lib/handlers/li.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   li: () => (/* binding */ li)\n/* harmony export */ });\n/* harmony import */ var hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! hast-util-is-element */ \"(ssr)/../../node_modules/hast-util-is-element/index.js\");\n/* harmony import */ var _util_wrap_children_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/wrap-children.js */ \"(ssr)/../../node_modules/hast-util-to-mdast/lib/util/wrap-children.js\");\n/**\n * @typedef {import('../types.js').Handle} Handle\n * @typedef {import('../types.js').Element} Element\n * @typedef {import('../types.js').ElementChild} ElementChild\n * @typedef {import('../types.js').MdastNode} MdastNode\n */\n\n\n\n\nconst p = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)('p')\nconst input = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)('input')\n\n/**\n * @type {Handle}\n * @param {Element} node\n */\nfunction li(h, node) {\n  const head = node.children[0]\n  /** @type {boolean|null} */\n  let checked = null\n  /** @type {ElementChild} */\n  let checkbox\n  /** @type {Element|undefined} */\n  let clone\n\n  // Check if this node starts with a checkbox.\n  if (p(head)) {\n    checkbox = head.children[0]\n\n    if (\n      input(checkbox) &&\n      checkbox.properties &&\n      (checkbox.properties.type === 'checkbox' ||\n        checkbox.properties.type === 'radio')\n    ) {\n      checked = Boolean(checkbox.properties.checked)\n      clone = {\n        ...node,\n        children: [\n          {...head, children: head.children.slice(1)},\n          ...node.children.slice(1)\n        ]\n      }\n    }\n  }\n\n  const content = (0,_util_wrap_children_js__WEBPACK_IMPORTED_MODULE_1__.wrapChildren)(h, clone || node)\n\n  return h(node, 'listItem', {spread: content.length > 1, checked}, content)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/hast-util-to-mdast/lib/handlers/li.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/hast-util-to-mdast/lib/handlers/list.js":
/*!******************************************************************!*\
  !*** ../../node_modules/hast-util-to-mdast/lib/handlers/list.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   list: () => (/* binding */ list)\n/* harmony export */ });\n/* harmony import */ var hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! hast-util-is-element */ \"(ssr)/../../node_modules/hast-util-is-element/index.js\");\n/* harmony import */ var hast_util_has_property__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! hast-util-has-property */ \"(ssr)/../../node_modules/hast-util-has-property/lib/index.js\");\n/* harmony import */ var _util_list_items_spread_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../util/list-items-spread.js */ \"(ssr)/../../node_modules/hast-util-to-mdast/lib/util/list-items-spread.js\");\n/* harmony import */ var _util_wrap_list_items_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/wrap-list-items.js */ \"(ssr)/../../node_modules/hast-util-to-mdast/lib/util/wrap-list-items.js\");\n/**\n * @typedef {import('../types.js').Handle} Handle\n * @typedef {import('../types.js').Element} Element\n */\n\n\n\n\n\n\nconst ol = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)('ol')\n\n/**\n * @type {Handle}\n * @param {Element} node\n */\nfunction list(h, node) {\n  const ordered = ol(node)\n  const children = (0,_util_wrap_list_items_js__WEBPACK_IMPORTED_MODULE_1__.wrapListItems)(h, node)\n  /** @type {number|null} */\n  let start = null\n\n  if (ordered) {\n    start = (0,hast_util_has_property__WEBPACK_IMPORTED_MODULE_2__.hasProperty)(node, 'start')\n      ? // @ts-expect-error: `props` exist.\n        Number.parseInt(String(node.properties.start), 10)\n      : 1\n  }\n\n  return h(\n    node,\n    'list',\n    {ordered, start, spread: (0,_util_list_items_spread_js__WEBPACK_IMPORTED_MODULE_3__.listItemsSpread)(children)},\n    children\n  )\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2hhc3QtdXRpbC10by1tZGFzdC9saWIvaGFuZGxlcnMvbGlzdC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUFBO0FBQ0EsYUFBYSw4QkFBOEI7QUFDM0MsYUFBYSwrQkFBK0I7QUFDNUM7O0FBRW1EO0FBQ0Q7QUFDVTtBQUNKOztBQUV4RCxXQUFXLG9FQUFjOztBQUV6QjtBQUNBLFVBQVU7QUFDVixXQUFXLFNBQVM7QUFDcEI7QUFDTztBQUNQO0FBQ0EsbUJBQW1CLHVFQUFhO0FBQ2hDLGFBQWEsYUFBYTtBQUMxQjs7QUFFQTtBQUNBLFlBQVksbUVBQVc7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsS0FBSyx3QkFBd0IsMkVBQWUsV0FBVztBQUN2RDtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9Ab3BlbmNhbnZhcy93ZWIvLi4vLi4vbm9kZV9tb2R1bGVzL2hhc3QtdXRpbC10by1tZGFzdC9saWIvaGFuZGxlcnMvbGlzdC5qcz9lM2NiIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5IYW5kbGV9IEhhbmRsZVxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5FbGVtZW50fSBFbGVtZW50XG4gKi9cblxuaW1wb3J0IHtjb252ZXJ0RWxlbWVudH0gZnJvbSAnaGFzdC11dGlsLWlzLWVsZW1lbnQnXG5pbXBvcnQge2hhc1Byb3BlcnR5fSBmcm9tICdoYXN0LXV0aWwtaGFzLXByb3BlcnR5J1xuaW1wb3J0IHtsaXN0SXRlbXNTcHJlYWR9IGZyb20gJy4uL3V0aWwvbGlzdC1pdGVtcy1zcHJlYWQuanMnXG5pbXBvcnQge3dyYXBMaXN0SXRlbXN9IGZyb20gJy4uL3V0aWwvd3JhcC1saXN0LWl0ZW1zLmpzJ1xuXG5jb25zdCBvbCA9IGNvbnZlcnRFbGVtZW50KCdvbCcpXG5cbi8qKlxuICogQHR5cGUge0hhbmRsZX1cbiAqIEBwYXJhbSB7RWxlbWVudH0gbm9kZVxuICovXG5leHBvcnQgZnVuY3Rpb24gbGlzdChoLCBub2RlKSB7XG4gIGNvbnN0IG9yZGVyZWQgPSBvbChub2RlKVxuICBjb25zdCBjaGlsZHJlbiA9IHdyYXBMaXN0SXRlbXMoaCwgbm9kZSlcbiAgLyoqIEB0eXBlIHtudW1iZXJ8bnVsbH0gKi9cbiAgbGV0IHN0YXJ0ID0gbnVsbFxuXG4gIGlmIChvcmRlcmVkKSB7XG4gICAgc3RhcnQgPSBoYXNQcm9wZXJ0eShub2RlLCAnc3RhcnQnKVxuICAgICAgPyAvLyBAdHMtZXhwZWN0LWVycm9yOiBgcHJvcHNgIGV4aXN0LlxuICAgICAgICBOdW1iZXIucGFyc2VJbnQoU3RyaW5nKG5vZGUucHJvcGVydGllcy5zdGFydCksIDEwKVxuICAgICAgOiAxXG4gIH1cblxuICByZXR1cm4gaChcbiAgICBub2RlLFxuICAgICdsaXN0JyxcbiAgICB7b3JkZXJlZCwgc3RhcnQsIHNwcmVhZDogbGlzdEl0ZW1zU3ByZWFkKGNoaWxkcmVuKX0sXG4gICAgY2hpbGRyZW5cbiAgKVxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/hast-util-to-mdast/lib/handlers/list.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/hast-util-to-mdast/lib/handlers/media.js":
/*!*******************************************************************!*\
  !*** ../../node_modules/hast-util-to-mdast/lib/handlers/media.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   media: () => (/* binding */ media)\n/* harmony export */ });\n/* harmony import */ var hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! hast-util-is-element */ \"(ssr)/../../node_modules/hast-util-is-element/index.js\");\n/* harmony import */ var mdast_util_to_string__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! mdast-util-to-string */ \"(ssr)/../../node_modules/mdast-util-to-string/lib/index.js\");\n/* harmony import */ var unist_util_visit__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! unist-util-visit */ \"(ssr)/../../node_modules/hast-util-to-mdast/node_modules/unist-util-visit/lib/index.js\");\n/* harmony import */ var unist_util_visit__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! unist-util-visit */ \"(ssr)/../../node_modules/hast-util-to-mdast/node_modules/unist-util-visit-parents/lib/index.js\");\n/* harmony import */ var _all_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../all.js */ \"(ssr)/../../node_modules/hast-util-to-mdast/lib/all.js\");\n/* harmony import */ var _util_resolve_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../util/resolve.js */ \"(ssr)/../../node_modules/hast-util-to-mdast/lib/util/resolve.js\");\n/* harmony import */ var _util_wrap_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../util/wrap.js */ \"(ssr)/../../node_modules/hast-util-to-mdast/lib/util/wrap.js\");\n/**\n * @typedef {import('../types.js').Handle} Handle\n * @typedef {import('../types.js').Element} Element\n * @typedef {import('../types.js').Properties} Properties\n * @typedef {import('../types.js').ElementChild} ElementChild\n */\n\n\n\n\n\n\n\n\nconst source = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)('source')\nconst video = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)('video')\n\n/**\n * @type {Handle}\n * @param {Element} node\n */\nfunction media(h, node) {\n  let nodes = (0,_all_js__WEBPACK_IMPORTED_MODULE_1__.all)(h, node)\n  /** @type {Properties} */\n  // @ts-expect-error: `props` are defined.\n  const properties = node.properties\n  const poster = video(node) && String(properties.poster || '')\n  let src = String(properties.src || '')\n  let index = -1\n  /** @type {boolean} */\n  let linkInFallbackContent = false\n  /** @type {ElementChild} */\n  let child\n\n  ;(0,unist_util_visit__WEBPACK_IMPORTED_MODULE_2__.visit)({type: 'root', children: nodes}, 'link', findLink)\n\n  // If the content links to something, or if it’s not phrasing…\n  if (linkInFallbackContent || (0,_util_wrap_js__WEBPACK_IMPORTED_MODULE_3__.wrapNeeded)(nodes)) {\n    return nodes\n  }\n\n  // Find the source.\n  while (!src && ++index < node.children.length) {\n    child = node.children[index]\n    if (source(child)) {\n      // @ts-expect-error: `props` are defined.\n      src = String(child.properties.src || '')\n    }\n  }\n\n  // If there’s a poster defined on the video, create an image.\n  if (poster) {\n    nodes = [\n      {\n        type: 'image',\n        title: null,\n        url: (0,_util_resolve_js__WEBPACK_IMPORTED_MODULE_4__.resolve)(h, poster),\n        alt: (0,mdast_util_to_string__WEBPACK_IMPORTED_MODULE_5__.toString)({children: nodes})\n      }\n    ]\n  }\n\n  // Link to the media resource.\n  return {\n    type: 'link',\n    // @ts-expect-error Types are broken.\n    title: node.properties.title || null,\n    url: (0,_util_resolve_js__WEBPACK_IMPORTED_MODULE_4__.resolve)(h, src),\n    // @ts-expect-error Assume phrasing content.\n    children: nodes\n  }\n\n  function findLink() {\n    linkInFallbackContent = true\n    return unist_util_visit__WEBPACK_IMPORTED_MODULE_6__.EXIT\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/hast-util-to-mdast/lib/handlers/media.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/hast-util-to-mdast/lib/handlers/p.js":
/*!***************************************************************!*\
  !*** ../../node_modules/hast-util-to-mdast/lib/handlers/p.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   p: () => (/* binding */ p)\n/* harmony export */ });\n/* harmony import */ var _all_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../all.js */ \"(ssr)/../../node_modules/hast-util-to-mdast/lib/all.js\");\n/**\n * @typedef {import('../types.js').Handle} Handle\n * @typedef {import('../types.js').Element} Element\n */\n\n\n\n/**\n * @type {Handle}\n * @param {Element} node\n */\nfunction p(h, node) {\n  const nodes = (0,_all_js__WEBPACK_IMPORTED_MODULE_0__.all)(h, node)\n\n  if (nodes.length > 0) {\n    return h(node, 'paragraph', nodes)\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2hhc3QtdXRpbC10by1tZGFzdC9saWIvaGFuZGxlcnMvcC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ0EsYUFBYSw4QkFBOEI7QUFDM0MsYUFBYSwrQkFBK0I7QUFDNUM7O0FBRTZCOztBQUU3QjtBQUNBLFVBQVU7QUFDVixXQUFXLFNBQVM7QUFDcEI7QUFDTztBQUNQLGdCQUFnQiw0Q0FBRzs7QUFFbkI7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9Ab3BlbmNhbnZhcy93ZWIvLi4vLi4vbm9kZV9tb2R1bGVzL2hhc3QtdXRpbC10by1tZGFzdC9saWIvaGFuZGxlcnMvcC5qcz9jMDdjIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5IYW5kbGV9IEhhbmRsZVxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5FbGVtZW50fSBFbGVtZW50XG4gKi9cblxuaW1wb3J0IHthbGx9IGZyb20gJy4uL2FsbC5qcydcblxuLyoqXG4gKiBAdHlwZSB7SGFuZGxlfVxuICogQHBhcmFtIHtFbGVtZW50fSBub2RlXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBwKGgsIG5vZGUpIHtcbiAgY29uc3Qgbm9kZXMgPSBhbGwoaCwgbm9kZSlcblxuICBpZiAobm9kZXMubGVuZ3RoID4gMCkge1xuICAgIHJldHVybiBoKG5vZGUsICdwYXJhZ3JhcGgnLCBub2RlcylcbiAgfVxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/hast-util-to-mdast/lib/handlers/p.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/hast-util-to-mdast/lib/handlers/q.js":
/*!***************************************************************!*\
  !*** ../../node_modules/hast-util-to-mdast/lib/handlers/q.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   q: () => (/* binding */ q)\n/* harmony export */ });\n/* harmony import */ var _all_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../all.js */ \"(ssr)/../../node_modules/hast-util-to-mdast/lib/all.js\");\n/**\n * @typedef {import('../types.js').Handle} Handle\n * @typedef {import('../types.js').Element} Element\n * @typedef {import('../types.js').MdastNode} MdastNode\n */\n\n\n\n/**\n * @type {Handle}\n * @param {Element} node\n */\nfunction q(h, node) {\n  const expected = h.quotes[h.qNesting % h.quotes.length]\n\n  h.qNesting++\n  const contents = (0,_all_js__WEBPACK_IMPORTED_MODULE_0__.all)(h, node)\n  h.qNesting--\n\n  contents.unshift({type: 'text', value: expected.charAt(0)})\n\n  contents.push({\n    type: 'text',\n    value: expected.length > 1 ? expected.charAt(1) : expected\n  })\n\n  return contents\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2hhc3QtdXRpbC10by1tZGFzdC9saWIvaGFuZGxlcnMvcS5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ0EsYUFBYSw4QkFBOEI7QUFDM0MsYUFBYSwrQkFBK0I7QUFDNUMsYUFBYSxpQ0FBaUM7QUFDOUM7O0FBRTZCOztBQUU3QjtBQUNBLFVBQVU7QUFDVixXQUFXLFNBQVM7QUFDcEI7QUFDTztBQUNQOztBQUVBO0FBQ0EsbUJBQW1CLDRDQUFHO0FBQ3RCOztBQUVBLG9CQUFvQix3Q0FBd0M7O0FBRTVEO0FBQ0E7QUFDQTtBQUNBLEdBQUc7O0FBRUg7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL0BvcGVuY2FudmFzL3dlYi8uLi8uLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi9oYW5kbGVycy9xLmpzPzMwYjAiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLkhhbmRsZX0gSGFuZGxlXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLkVsZW1lbnR9IEVsZW1lbnRcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuTWRhc3ROb2RlfSBNZGFzdE5vZGVcbiAqL1xuXG5pbXBvcnQge2FsbH0gZnJvbSAnLi4vYWxsLmpzJ1xuXG4vKipcbiAqIEB0eXBlIHtIYW5kbGV9XG4gKiBAcGFyYW0ge0VsZW1lbnR9IG5vZGVcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHEoaCwgbm9kZSkge1xuICBjb25zdCBleHBlY3RlZCA9IGgucXVvdGVzW2gucU5lc3RpbmcgJSBoLnF1b3Rlcy5sZW5ndGhdXG5cbiAgaC5xTmVzdGluZysrXG4gIGNvbnN0IGNvbnRlbnRzID0gYWxsKGgsIG5vZGUpXG4gIGgucU5lc3RpbmctLVxuXG4gIGNvbnRlbnRzLnVuc2hpZnQoe3R5cGU6ICd0ZXh0JywgdmFsdWU6IGV4cGVjdGVkLmNoYXJBdCgwKX0pXG5cbiAgY29udGVudHMucHVzaCh7XG4gICAgdHlwZTogJ3RleHQnLFxuICAgIHZhbHVlOiBleHBlY3RlZC5sZW5ndGggPiAxID8gZXhwZWN0ZWQuY2hhckF0KDEpIDogZXhwZWN0ZWRcbiAgfSlcblxuICByZXR1cm4gY29udGVudHNcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/hast-util-to-mdast/lib/handlers/q.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/hast-util-to-mdast/lib/handlers/root.js":
/*!******************************************************************!*\
  !*** ../../node_modules/hast-util-to-mdast/lib/handlers/root.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   root: () => (/* binding */ root)\n/* harmony export */ });\n/* harmony import */ var _all_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../all.js */ \"(ssr)/../../node_modules/hast-util-to-mdast/lib/all.js\");\n/* harmony import */ var _util_wrap_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/wrap.js */ \"(ssr)/../../node_modules/hast-util-to-mdast/lib/util/wrap.js\");\n/**\n * @typedef {import('../types.js').Handle} Handle\n * @typedef {import('../types.js').Root} Root\n */\n\n\n\n\n/**\n * @type {Handle}\n * @param {Root} node\n */\nfunction root(h, node) {\n  let children = (0,_all_js__WEBPACK_IMPORTED_MODULE_0__.all)(h, node)\n\n  if (h.document || (0,_util_wrap_js__WEBPACK_IMPORTED_MODULE_1__.wrapNeeded)(children)) {\n    children = (0,_util_wrap_js__WEBPACK_IMPORTED_MODULE_1__.wrap)(children)\n  }\n\n  return h(node, 'root', children)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2hhc3QtdXRpbC10by1tZGFzdC9saWIvaGFuZGxlcnMvcm9vdC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBQTtBQUNBLGFBQWEsOEJBQThCO0FBQzNDLGFBQWEsNEJBQTRCO0FBQ3pDOztBQUU2QjtBQUNtQjs7QUFFaEQ7QUFDQSxVQUFVO0FBQ1YsV0FBVyxNQUFNO0FBQ2pCO0FBQ087QUFDUCxpQkFBaUIsNENBQUc7O0FBRXBCLG9CQUFvQix5REFBVTtBQUM5QixlQUFlLG1EQUFJO0FBQ25COztBQUVBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9Ab3BlbmNhbnZhcy93ZWIvLi4vLi4vbm9kZV9tb2R1bGVzL2hhc3QtdXRpbC10by1tZGFzdC9saWIvaGFuZGxlcnMvcm9vdC5qcz8yNmY5Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5IYW5kbGV9IEhhbmRsZVxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5Sb290fSBSb290XG4gKi9cblxuaW1wb3J0IHthbGx9IGZyb20gJy4uL2FsbC5qcydcbmltcG9ydCB7d3JhcCwgd3JhcE5lZWRlZH0gZnJvbSAnLi4vdXRpbC93cmFwLmpzJ1xuXG4vKipcbiAqIEB0eXBlIHtIYW5kbGV9XG4gKiBAcGFyYW0ge1Jvb3R9IG5vZGVcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHJvb3QoaCwgbm9kZSkge1xuICBsZXQgY2hpbGRyZW4gPSBhbGwoaCwgbm9kZSlcblxuICBpZiAoaC5kb2N1bWVudCB8fCB3cmFwTmVlZGVkKGNoaWxkcmVuKSkge1xuICAgIGNoaWxkcmVuID0gd3JhcChjaGlsZHJlbilcbiAgfVxuXG4gIHJldHVybiBoKG5vZGUsICdyb290JywgY2hpbGRyZW4pXG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/hast-util-to-mdast/lib/handlers/root.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/hast-util-to-mdast/lib/handlers/select.js":
/*!********************************************************************!*\
  !*** ../../node_modules/hast-util-to-mdast/lib/handlers/select.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   select: () => (/* binding */ select)\n/* harmony export */ });\n/* harmony import */ var _util_find_selected_options_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/find-selected-options.js */ \"(ssr)/../../node_modules/hast-util-to-mdast/lib/util/find-selected-options.js\");\n/* harmony import */ var _util_wrap_text_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/wrap-text.js */ \"(ssr)/../../node_modules/hast-util-to-mdast/lib/util/wrap-text.js\");\n/**\n * @typedef {import('../types.js').Handle} Handle\n * @typedef {import('../types.js').Element} Element\n */\n\n\n\n\n/**\n * @type {Handle}\n * @param {Element} node\n */\nfunction select(h, node) {\n  const values = (0,_util_find_selected_options_js__WEBPACK_IMPORTED_MODULE_0__.findSelectedOptions)(h, node)\n  let index = -1\n  /** @type {Array<string>} */\n  const results = []\n  /** @type {[string, string|null]} */\n  let value\n\n  while (++index < values.length) {\n    value = values[index]\n    results.push(value[1] ? value[1] + ' (' + value[0] + ')' : value[0])\n  }\n\n  if (results.length > 0) {\n    return h(node, 'text', (0,_util_wrap_text_js__WEBPACK_IMPORTED_MODULE_1__.wrapText)(h, results.join(', ')))\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2hhc3QtdXRpbC10by1tZGFzdC9saWIvaGFuZGxlcnMvc2VsZWN0LmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBO0FBQ0EsYUFBYSw4QkFBOEI7QUFDM0MsYUFBYSwrQkFBK0I7QUFDNUM7O0FBRW9FO0FBQ3ZCOztBQUU3QztBQUNBLFVBQVU7QUFDVixXQUFXLFNBQVM7QUFDcEI7QUFDTztBQUNQLGlCQUFpQixtRkFBbUI7QUFDcEM7QUFDQSxhQUFhLGVBQWU7QUFDNUI7QUFDQSxhQUFhLHVCQUF1QjtBQUNwQzs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLDJCQUEyQiw0REFBUTtBQUNuQztBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQG9wZW5jYW52YXMvd2ViLy4uLy4uL25vZGVfbW9kdWxlcy9oYXN0LXV0aWwtdG8tbWRhc3QvbGliL2hhbmRsZXJzL3NlbGVjdC5qcz9mODJkIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5IYW5kbGV9IEhhbmRsZVxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5FbGVtZW50fSBFbGVtZW50XG4gKi9cblxuaW1wb3J0IHtmaW5kU2VsZWN0ZWRPcHRpb25zfSBmcm9tICcuLi91dGlsL2ZpbmQtc2VsZWN0ZWQtb3B0aW9ucy5qcydcbmltcG9ydCB7d3JhcFRleHR9IGZyb20gJy4uL3V0aWwvd3JhcC10ZXh0LmpzJ1xuXG4vKipcbiAqIEB0eXBlIHtIYW5kbGV9XG4gKiBAcGFyYW0ge0VsZW1lbnR9IG5vZGVcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHNlbGVjdChoLCBub2RlKSB7XG4gIGNvbnN0IHZhbHVlcyA9IGZpbmRTZWxlY3RlZE9wdGlvbnMoaCwgbm9kZSlcbiAgbGV0IGluZGV4ID0gLTFcbiAgLyoqIEB0eXBlIHtBcnJheTxzdHJpbmc+fSAqL1xuICBjb25zdCByZXN1bHRzID0gW11cbiAgLyoqIEB0eXBlIHtbc3RyaW5nLCBzdHJpbmd8bnVsbF19ICovXG4gIGxldCB2YWx1ZVxuXG4gIHdoaWxlICgrK2luZGV4IDwgdmFsdWVzLmxlbmd0aCkge1xuICAgIHZhbHVlID0gdmFsdWVzW2luZGV4XVxuICAgIHJlc3VsdHMucHVzaCh2YWx1ZVsxXSA/IHZhbHVlWzFdICsgJyAoJyArIHZhbHVlWzBdICsgJyknIDogdmFsdWVbMF0pXG4gIH1cblxuICBpZiAocmVzdWx0cy5sZW5ndGggPiAwKSB7XG4gICAgcmV0dXJuIGgobm9kZSwgJ3RleHQnLCB3cmFwVGV4dChoLCByZXN1bHRzLmpvaW4oJywgJykpKVxuICB9XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/hast-util-to-mdast/lib/handlers/select.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/hast-util-to-mdast/lib/handlers/strong.js":
/*!********************************************************************!*\
  !*** ../../node_modules/hast-util-to-mdast/lib/handlers/strong.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   strong: () => (/* binding */ strong)\n/* harmony export */ });\n/* harmony import */ var _all_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../all.js */ \"(ssr)/../../node_modules/hast-util-to-mdast/lib/all.js\");\n/**\n * @typedef {import('../types.js').Handle} Handle\n * @typedef {import('../types.js').Element} Element\n */\n\n\n\n/**\n * @type {Handle}\n * @param {Element} node\n */\nfunction strong(h, node) {\n  return h(node, 'strong', (0,_all_js__WEBPACK_IMPORTED_MODULE_0__.all)(h, node))\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2hhc3QtdXRpbC10by1tZGFzdC9saWIvaGFuZGxlcnMvc3Ryb25nLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDQSxhQUFhLDhCQUE4QjtBQUMzQyxhQUFhLCtCQUErQjtBQUM1Qzs7QUFFNkI7O0FBRTdCO0FBQ0EsVUFBVTtBQUNWLFdBQVcsU0FBUztBQUNwQjtBQUNPO0FBQ1AsMkJBQTJCLDRDQUFHO0FBQzlCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQG9wZW5jYW52YXMvd2ViLy4uLy4uL25vZGVfbW9kdWxlcy9oYXN0LXV0aWwtdG8tbWRhc3QvbGliL2hhbmRsZXJzL3N0cm9uZy5qcz9hYzM0Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5IYW5kbGV9IEhhbmRsZVxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5FbGVtZW50fSBFbGVtZW50XG4gKi9cblxuaW1wb3J0IHthbGx9IGZyb20gJy4uL2FsbC5qcydcblxuLyoqXG4gKiBAdHlwZSB7SGFuZGxlfVxuICogQHBhcmFtIHtFbGVtZW50fSBub2RlXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBzdHJvbmcoaCwgbm9kZSkge1xuICByZXR1cm4gaChub2RlLCAnc3Ryb25nJywgYWxsKGgsIG5vZGUpKVxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/hast-util-to-mdast/lib/handlers/strong.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/hast-util-to-mdast/lib/handlers/table-cell.js":
/*!************************************************************************!*\
  !*** ../../node_modules/hast-util-to-mdast/lib/handlers/table-cell.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   tableCell: () => (/* binding */ tableCell)\n/* harmony export */ });\n/* harmony import */ var _all_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../all.js */ \"(ssr)/../../node_modules/hast-util-to-mdast/lib/all.js\");\n/**\n * @typedef {import('../types.js').Handle} Handle\n * @typedef {import('../types.js').Element} Element\n * @typedef {import('../types.js').MdastNode} MdastNode\n */\n\n\n\n/**\n * @type {Handle}\n * @param {Element} node\n */\nfunction tableCell(h, node) {\n  const wrap = h.wrapText\n\n  h.wrapText = false\n\n  const result = h(node, 'tableCell', (0,_all_js__WEBPACK_IMPORTED_MODULE_0__.all)(h, node))\n\n  if (node.properties && (node.properties.rowSpan || node.properties.colSpan)) {\n    const data = result.data || (result.data = {})\n    if (node.properties.rowSpan) data.rowSpan = node.properties.rowSpan\n    if (node.properties.colSpan) data.colSpan = node.properties.colSpan\n  }\n\n  h.wrapText = wrap\n\n  return result\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2hhc3QtdXRpbC10by1tZGFzdC9saWIvaGFuZGxlcnMvdGFibGUtY2VsbC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ0EsYUFBYSw4QkFBOEI7QUFDM0MsYUFBYSwrQkFBK0I7QUFDNUMsYUFBYSxpQ0FBaUM7QUFDOUM7O0FBRTZCOztBQUU3QjtBQUNBLFVBQVU7QUFDVixXQUFXLFNBQVM7QUFDcEI7QUFDTztBQUNQOztBQUVBOztBQUVBLHNDQUFzQyw0Q0FBRzs7QUFFekM7QUFDQSxpREFBaUQ7QUFDakQ7QUFDQTtBQUNBOztBQUVBOztBQUVBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9Ab3BlbmNhbnZhcy93ZWIvLi4vLi4vbm9kZV9tb2R1bGVzL2hhc3QtdXRpbC10by1tZGFzdC9saWIvaGFuZGxlcnMvdGFibGUtY2VsbC5qcz8xZDBiIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5IYW5kbGV9IEhhbmRsZVxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5FbGVtZW50fSBFbGVtZW50XG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLk1kYXN0Tm9kZX0gTWRhc3ROb2RlXG4gKi9cblxuaW1wb3J0IHthbGx9IGZyb20gJy4uL2FsbC5qcydcblxuLyoqXG4gKiBAdHlwZSB7SGFuZGxlfVxuICogQHBhcmFtIHtFbGVtZW50fSBub2RlXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiB0YWJsZUNlbGwoaCwgbm9kZSkge1xuICBjb25zdCB3cmFwID0gaC53cmFwVGV4dFxuXG4gIGgud3JhcFRleHQgPSBmYWxzZVxuXG4gIGNvbnN0IHJlc3VsdCA9IGgobm9kZSwgJ3RhYmxlQ2VsbCcsIGFsbChoLCBub2RlKSlcblxuICBpZiAobm9kZS5wcm9wZXJ0aWVzICYmIChub2RlLnByb3BlcnRpZXMucm93U3BhbiB8fCBub2RlLnByb3BlcnRpZXMuY29sU3BhbikpIHtcbiAgICBjb25zdCBkYXRhID0gcmVzdWx0LmRhdGEgfHwgKHJlc3VsdC5kYXRhID0ge30pXG4gICAgaWYgKG5vZGUucHJvcGVydGllcy5yb3dTcGFuKSBkYXRhLnJvd1NwYW4gPSBub2RlLnByb3BlcnRpZXMucm93U3BhblxuICAgIGlmIChub2RlLnByb3BlcnRpZXMuY29sU3BhbikgZGF0YS5jb2xTcGFuID0gbm9kZS5wcm9wZXJ0aWVzLmNvbFNwYW5cbiAgfVxuXG4gIGgud3JhcFRleHQgPSB3cmFwXG5cbiAgcmV0dXJuIHJlc3VsdFxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/hast-util-to-mdast/lib/handlers/table-cell.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/hast-util-to-mdast/lib/handlers/table-row.js":
/*!***********************************************************************!*\
  !*** ../../node_modules/hast-util-to-mdast/lib/handlers/table-row.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   tableRow: () => (/* binding */ tableRow)\n/* harmony export */ });\n/* harmony import */ var _all_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../all.js */ \"(ssr)/../../node_modules/hast-util-to-mdast/lib/all.js\");\n/**\n * @typedef {import('../types.js').Handle} Handle\n * @typedef {import('../types.js').Element} Element\n */\n\n\n\n/**\n * @type {Handle}\n * @param {Element} node\n */\nfunction tableRow(h, node) {\n  return h(node, 'tableRow', (0,_all_js__WEBPACK_IMPORTED_MODULE_0__.all)(h, node))\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2hhc3QtdXRpbC10by1tZGFzdC9saWIvaGFuZGxlcnMvdGFibGUtcm93LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDQSxhQUFhLDhCQUE4QjtBQUMzQyxhQUFhLCtCQUErQjtBQUM1Qzs7QUFFNkI7O0FBRTdCO0FBQ0EsVUFBVTtBQUNWLFdBQVcsU0FBUztBQUNwQjtBQUNPO0FBQ1AsNkJBQTZCLDRDQUFHO0FBQ2hDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQG9wZW5jYW52YXMvd2ViLy4uLy4uL25vZGVfbW9kdWxlcy9oYXN0LXV0aWwtdG8tbWRhc3QvbGliL2hhbmRsZXJzL3RhYmxlLXJvdy5qcz8wYmNhIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5IYW5kbGV9IEhhbmRsZVxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5FbGVtZW50fSBFbGVtZW50XG4gKi9cblxuaW1wb3J0IHthbGx9IGZyb20gJy4uL2FsbC5qcydcblxuLyoqXG4gKiBAdHlwZSB7SGFuZGxlfVxuICogQHBhcmFtIHtFbGVtZW50fSBub2RlXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiB0YWJsZVJvdyhoLCBub2RlKSB7XG4gIHJldHVybiBoKG5vZGUsICd0YWJsZVJvdycsIGFsbChoLCBub2RlKSlcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/hast-util-to-mdast/lib/handlers/table-row.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/hast-util-to-mdast/lib/handlers/table.js":
/*!*******************************************************************!*\
  !*** ../../node_modules/hast-util-to-mdast/lib/handlers/table.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   table: () => (/* binding */ table)\n/* harmony export */ });\n/* harmony import */ var hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! hast-util-is-element */ \"(ssr)/../../node_modules/hast-util-is-element/index.js\");\n/* harmony import */ var hast_util_to_text__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! hast-util-to-text */ \"(ssr)/../../node_modules/hast-util-to-mdast/node_modules/hast-util-to-text/lib/index.js\");\n/* harmony import */ var unist_util_visit__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! unist-util-visit */ \"(ssr)/../../node_modules/hast-util-to-mdast/node_modules/unist-util-visit/lib/index.js\");\n/* harmony import */ var unist_util_visit__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! unist-util-visit */ \"(ssr)/../../node_modules/hast-util-to-mdast/node_modules/unist-util-visit-parents/lib/index.js\");\n/* harmony import */ var _util_wrap_text_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/wrap-text.js */ \"(ssr)/../../node_modules/hast-util-to-mdast/lib/util/wrap-text.js\");\n/* harmony import */ var _all_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../all.js */ \"(ssr)/../../node_modules/hast-util-to-mdast/lib/all.js\");\n/**\n * @typedef {import('../types.js').Handle} Handle\n * @typedef {import('../types.js').Element} Element\n * @typedef {import('../types.js').MdastNode} MdastNode\n * @typedef {import('../types.js').MdastTableContent} MdastTableContent\n * @typedef {import('../types.js').MdastRowContent} MdastRowContent\n * @typedef {import('../types.js').MdastPhrasingContent} MdastPhrasingContent\n *\n * @typedef Info\n * @property {Array<string|null>} align\n * @property {boolean} headless\n */\n\n\n\n\n\n\n\nconst thead = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)('thead')\nconst tr = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)('tr')\nconst cell = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)(['th', 'td'])\n\n/**\n * @type {Handle}\n * @param {Element} node\n */\nfunction table(h, node) {\n  if (h.inTable) {\n    return h(node, 'text', (0,_util_wrap_text_js__WEBPACK_IMPORTED_MODULE_1__.wrapText)(h, (0,hast_util_to_text__WEBPACK_IMPORTED_MODULE_2__.toText)(node)))\n  }\n\n  h.inTable = true\n\n  const {headless, align} = inspect(node)\n  const rows = toRows((0,_all_js__WEBPACK_IMPORTED_MODULE_3__.all)(h, node), headless)\n  let columns = 1\n  let rowIndex = -1\n\n  while (++rowIndex < rows.length) {\n    const cells = rows[rowIndex].children\n    let cellIndex = -1\n\n    while (++cellIndex < cells.length) {\n      const cell = cells[cellIndex]\n\n      if (cell.data) {\n        const colSpan = Number.parseInt(String(cell.data.colSpan), 10) || 1\n        const rowSpan = Number.parseInt(String(cell.data.rowSpan), 10) || 1\n\n        if (colSpan > 1 || rowSpan > 1) {\n          let otherRowIndex = rowIndex - 1\n\n          while (++otherRowIndex < rowIndex + rowSpan) {\n            let colIndex = cellIndex - 1\n\n            while (++colIndex < cellIndex + colSpan) {\n              if (!rows[otherRowIndex]) {\n                // Don’t add rows that don’t exist.\n                // Browsers don’t render them either.\n                break\n              }\n\n              /** @type {Array<MdastRowContent>} */\n              const newCells = []\n\n              if (otherRowIndex !== rowIndex || colIndex !== cellIndex) {\n                newCells.push({type: 'tableCell', children: []})\n              }\n\n              rows[otherRowIndex].children.splice(colIndex, 0, ...newCells)\n            }\n          }\n        }\n\n        // Clean the data fields.\n        if ('colSpan' in cell.data) delete cell.data.colSpan\n        if ('rowSpan' in cell.data) delete cell.data.rowSpan\n        if (Object.keys(cell.data).length === 0) delete cell.data\n      }\n    }\n\n    if (cells.length > columns) columns = cells.length\n  }\n\n  // Add extra empty cells.\n  rowIndex = -1\n\n  while (++rowIndex < rows.length) {\n    const cells = rows[rowIndex].children\n    let cellIndex = cells.length - 1\n    while (++cellIndex < columns) {\n      cells.push({type: 'tableCell', children: []})\n    }\n  }\n\n  let alignIndex = align.length - 1\n  while (++alignIndex < columns) {\n    align.push(null)\n  }\n\n  h.inTable = false\n\n  return h(node, 'table', {align}, rows)\n}\n\n/**\n * Infer whether the HTML table has a head and how it aligns.\n *\n * @param {Element} node\n * @returns {Info}\n */\nfunction inspect(node) {\n  let headless = true\n  let rowIndex = 0\n  let cellIndex = 0\n  /** @type {Array<string|null>} */\n  const align = [null]\n\n  ;(0,unist_util_visit__WEBPACK_IMPORTED_MODULE_4__.visit)(node, 'element', (child) => {\n    if (child.tagName === 'table' && node !== child) {\n      return unist_util_visit__WEBPACK_IMPORTED_MODULE_5__.SKIP\n    }\n\n    // If there is a `thead`, assume there is a header row.\n    if (cell(child) && child.properties) {\n      if (!align[cellIndex]) {\n        align[cellIndex] = String(child.properties.align || '') || null\n      }\n\n      // If there is a th in the first row, assume there is a header row.\n      if (headless && rowIndex < 2 && child.tagName === 'th') {\n        headless = false\n      }\n\n      cellIndex++\n    } else if (thead(child)) {\n      headless = false\n    } else if (tr(child)) {\n      rowIndex++\n      cellIndex = 0\n    }\n  })\n\n  return {align, headless}\n}\n\n/**\n * Ensure the rows are properly structured.\n *\n * @param {Array<MdastNode>} children\n * @param {boolean} headless\n * @returns {Array<MdastTableContent>}\n */\nfunction toRows(children, headless) {\n  let index = -1\n  /** @type {Array<MdastTableContent>} */\n  const nodes = []\n  /** @type {Array<MdastRowContent>|undefined} */\n  let queue\n\n  // Add an empty header row.\n  if (headless) {\n    nodes.push({type: 'tableRow', children: []})\n  }\n\n  while (++index < children.length) {\n    const node = children[index]\n\n    if (node.type === 'tableRow') {\n      if (queue) {\n        node.children.unshift(...queue)\n        queue = undefined\n      }\n\n      nodes.push(node)\n    } else {\n      if (!queue) queue = []\n      // @ts-expect-error Assume row content.\n      queue.push(node)\n    }\n  }\n\n  if (queue) {\n    nodes[nodes.length - 1].children.push(...queue)\n  }\n\n  index = -1\n\n  while (++index < nodes.length) {\n    nodes[index].children = toCells(nodes[index].children)\n  }\n\n  return nodes\n}\n\n/**\n * Ensure the cells in a row are properly structured.\n *\n * @param {Array<MdastNode>} children\n * @returns {Array<MdastRowContent>}\n */\nfunction toCells(children) {\n  /** @type {Array<MdastRowContent>} */\n  const nodes = []\n  let index = -1\n  /** @type {MdastNode} */\n  let node\n  /** @type {Array<MdastPhrasingContent>|undefined} */\n  let queue\n\n  while (++index < children.length) {\n    node = children[index]\n\n    if (node.type === 'tableCell') {\n      if (queue) {\n        node.children.unshift(...queue)\n        queue = undefined\n      }\n\n      nodes.push(node)\n    } else {\n      if (!queue) queue = []\n      // @ts-expect-error Assume phrasing content.\n      queue.push(node)\n    }\n  }\n\n  if (queue) {\n    node = nodes[nodes.length - 1]\n\n    if (!node) {\n      node = {type: 'tableCell', children: []}\n      nodes.push(node)\n    }\n\n    node.children.push(...queue)\n  }\n\n  return nodes\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2hhc3QtdXRpbC10by1tZGFzdC9saWIvaGFuZGxlcnMvdGFibGUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUFBO0FBQ0EsYUFBYSw4QkFBOEI7QUFDM0MsYUFBYSwrQkFBK0I7QUFDNUMsYUFBYSxpQ0FBaUM7QUFDOUMsYUFBYSx5Q0FBeUM7QUFDdEQsYUFBYSx1Q0FBdUM7QUFDcEQsYUFBYSw0Q0FBNEM7QUFDekQ7QUFDQTtBQUNBLGNBQWMsb0JBQW9CO0FBQ2xDLGNBQWMsU0FBUztBQUN2Qjs7QUFFbUQ7QUFDWDtBQUNJO0FBQ0M7QUFDaEI7O0FBRTdCLGNBQWMsb0VBQWM7QUFDNUIsV0FBVyxvRUFBYztBQUN6QixhQUFhLG9FQUFjOztBQUUzQjtBQUNBLFVBQVU7QUFDVixXQUFXLFNBQVM7QUFDcEI7QUFDTztBQUNQO0FBQ0EsMkJBQTJCLDREQUFRLElBQUkseURBQU07QUFDN0M7O0FBRUE7O0FBRUEsU0FBUyxpQkFBaUI7QUFDMUIsc0JBQXNCLDRDQUFHO0FBQ3pCO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEseUJBQXlCLHdCQUF3QjtBQUNqRDs7QUFFQTtBQUNBLCtCQUErQixnQ0FBZ0M7QUFDL0Q7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtCQUFrQixnQ0FBZ0M7QUFDbEQ7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQSwyQkFBMkIsTUFBTTtBQUNqQzs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFNBQVM7QUFDcEIsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhLG9CQUFvQjtBQUNqQzs7QUFFQSxFQUFFLHdEQUFLO0FBQ1A7QUFDQSxhQUFhLGtEQUFJO0FBQ2pCOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxNQUFNO0FBQ047QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0EsR0FBRzs7QUFFSCxVQUFVO0FBQ1Y7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsV0FBVyxrQkFBa0I7QUFDN0IsV0FBVyxTQUFTO0FBQ3BCLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQSxhQUFhLDBCQUEwQjtBQUN2QztBQUNBLGFBQWEsa0NBQWtDO0FBQy9DOztBQUVBO0FBQ0E7QUFDQSxnQkFBZ0IsK0JBQStCO0FBQy9DOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLGtCQUFrQjtBQUM3QixhQUFhO0FBQ2I7QUFDQTtBQUNBLGFBQWEsd0JBQXdCO0FBQ3JDO0FBQ0E7QUFDQSxhQUFhLFdBQVc7QUFDeEI7QUFDQSxhQUFhLHVDQUF1QztBQUNwRDs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0EsY0FBYztBQUNkO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQG9wZW5jYW52YXMvd2ViLy4uLy4uL25vZGVfbW9kdWxlcy9oYXN0LXV0aWwtdG8tbWRhc3QvbGliL2hhbmRsZXJzL3RhYmxlLmpzPzEwYzEiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLkhhbmRsZX0gSGFuZGxlXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLkVsZW1lbnR9IEVsZW1lbnRcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuTWRhc3ROb2RlfSBNZGFzdE5vZGVcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuTWRhc3RUYWJsZUNvbnRlbnR9IE1kYXN0VGFibGVDb250ZW50XG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLk1kYXN0Um93Q29udGVudH0gTWRhc3RSb3dDb250ZW50XG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLk1kYXN0UGhyYXNpbmdDb250ZW50fSBNZGFzdFBocmFzaW5nQ29udGVudFxuICpcbiAqIEB0eXBlZGVmIEluZm9cbiAqIEBwcm9wZXJ0eSB7QXJyYXk8c3RyaW5nfG51bGw+fSBhbGlnblxuICogQHByb3BlcnR5IHtib29sZWFufSBoZWFkbGVzc1xuICovXG5cbmltcG9ydCB7Y29udmVydEVsZW1lbnR9IGZyb20gJ2hhc3QtdXRpbC1pcy1lbGVtZW50J1xuaW1wb3J0IHt0b1RleHR9IGZyb20gJ2hhc3QtdXRpbC10by10ZXh0J1xuaW1wb3J0IHt2aXNpdCwgU0tJUH0gZnJvbSAndW5pc3QtdXRpbC12aXNpdCdcbmltcG9ydCB7d3JhcFRleHR9IGZyb20gJy4uL3V0aWwvd3JhcC10ZXh0LmpzJ1xuaW1wb3J0IHthbGx9IGZyb20gJy4uL2FsbC5qcydcblxuY29uc3QgdGhlYWQgPSBjb252ZXJ0RWxlbWVudCgndGhlYWQnKVxuY29uc3QgdHIgPSBjb252ZXJ0RWxlbWVudCgndHInKVxuY29uc3QgY2VsbCA9IGNvbnZlcnRFbGVtZW50KFsndGgnLCAndGQnXSlcblxuLyoqXG4gKiBAdHlwZSB7SGFuZGxlfVxuICogQHBhcmFtIHtFbGVtZW50fSBub2RlXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiB0YWJsZShoLCBub2RlKSB7XG4gIGlmIChoLmluVGFibGUpIHtcbiAgICByZXR1cm4gaChub2RlLCAndGV4dCcsIHdyYXBUZXh0KGgsIHRvVGV4dChub2RlKSkpXG4gIH1cblxuICBoLmluVGFibGUgPSB0cnVlXG5cbiAgY29uc3Qge2hlYWRsZXNzLCBhbGlnbn0gPSBpbnNwZWN0KG5vZGUpXG4gIGNvbnN0IHJvd3MgPSB0b1Jvd3MoYWxsKGgsIG5vZGUpLCBoZWFkbGVzcylcbiAgbGV0IGNvbHVtbnMgPSAxXG4gIGxldCByb3dJbmRleCA9IC0xXG5cbiAgd2hpbGUgKCsrcm93SW5kZXggPCByb3dzLmxlbmd0aCkge1xuICAgIGNvbnN0IGNlbGxzID0gcm93c1tyb3dJbmRleF0uY2hpbGRyZW5cbiAgICBsZXQgY2VsbEluZGV4ID0gLTFcblxuICAgIHdoaWxlICgrK2NlbGxJbmRleCA8IGNlbGxzLmxlbmd0aCkge1xuICAgICAgY29uc3QgY2VsbCA9IGNlbGxzW2NlbGxJbmRleF1cblxuICAgICAgaWYgKGNlbGwuZGF0YSkge1xuICAgICAgICBjb25zdCBjb2xTcGFuID0gTnVtYmVyLnBhcnNlSW50KFN0cmluZyhjZWxsLmRhdGEuY29sU3BhbiksIDEwKSB8fCAxXG4gICAgICAgIGNvbnN0IHJvd1NwYW4gPSBOdW1iZXIucGFyc2VJbnQoU3RyaW5nKGNlbGwuZGF0YS5yb3dTcGFuKSwgMTApIHx8IDFcblxuICAgICAgICBpZiAoY29sU3BhbiA+IDEgfHwgcm93U3BhbiA+IDEpIHtcbiAgICAgICAgICBsZXQgb3RoZXJSb3dJbmRleCA9IHJvd0luZGV4IC0gMVxuXG4gICAgICAgICAgd2hpbGUgKCsrb3RoZXJSb3dJbmRleCA8IHJvd0luZGV4ICsgcm93U3Bhbikge1xuICAgICAgICAgICAgbGV0IGNvbEluZGV4ID0gY2VsbEluZGV4IC0gMVxuXG4gICAgICAgICAgICB3aGlsZSAoKytjb2xJbmRleCA8IGNlbGxJbmRleCArIGNvbFNwYW4pIHtcbiAgICAgICAgICAgICAgaWYgKCFyb3dzW290aGVyUm93SW5kZXhdKSB7XG4gICAgICAgICAgICAgICAgLy8gRG9u4oCZdCBhZGQgcm93cyB0aGF0IGRvbuKAmXQgZXhpc3QuXG4gICAgICAgICAgICAgICAgLy8gQnJvd3NlcnMgZG9u4oCZdCByZW5kZXIgdGhlbSBlaXRoZXIuXG4gICAgICAgICAgICAgICAgYnJlYWtcbiAgICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICAgIC8qKiBAdHlwZSB7QXJyYXk8TWRhc3RSb3dDb250ZW50Pn0gKi9cbiAgICAgICAgICAgICAgY29uc3QgbmV3Q2VsbHMgPSBbXVxuXG4gICAgICAgICAgICAgIGlmIChvdGhlclJvd0luZGV4ICE9PSByb3dJbmRleCB8fCBjb2xJbmRleCAhPT0gY2VsbEluZGV4KSB7XG4gICAgICAgICAgICAgICAgbmV3Q2VsbHMucHVzaCh7dHlwZTogJ3RhYmxlQ2VsbCcsIGNoaWxkcmVuOiBbXX0pXG4gICAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgICByb3dzW290aGVyUm93SW5kZXhdLmNoaWxkcmVuLnNwbGljZShjb2xJbmRleCwgMCwgLi4ubmV3Q2VsbHMpXG4gICAgICAgICAgICB9XG4gICAgICAgICAgfVxuICAgICAgICB9XG5cbiAgICAgICAgLy8gQ2xlYW4gdGhlIGRhdGEgZmllbGRzLlxuICAgICAgICBpZiAoJ2NvbFNwYW4nIGluIGNlbGwuZGF0YSkgZGVsZXRlIGNlbGwuZGF0YS5jb2xTcGFuXG4gICAgICAgIGlmICgncm93U3BhbicgaW4gY2VsbC5kYXRhKSBkZWxldGUgY2VsbC5kYXRhLnJvd1NwYW5cbiAgICAgICAgaWYgKE9iamVjdC5rZXlzKGNlbGwuZGF0YSkubGVuZ3RoID09PSAwKSBkZWxldGUgY2VsbC5kYXRhXG4gICAgICB9XG4gICAgfVxuXG4gICAgaWYgKGNlbGxzLmxlbmd0aCA+IGNvbHVtbnMpIGNvbHVtbnMgPSBjZWxscy5sZW5ndGhcbiAgfVxuXG4gIC8vIEFkZCBleHRyYSBlbXB0eSBjZWxscy5cbiAgcm93SW5kZXggPSAtMVxuXG4gIHdoaWxlICgrK3Jvd0luZGV4IDwgcm93cy5sZW5ndGgpIHtcbiAgICBjb25zdCBjZWxscyA9IHJvd3Nbcm93SW5kZXhdLmNoaWxkcmVuXG4gICAgbGV0IGNlbGxJbmRleCA9IGNlbGxzLmxlbmd0aCAtIDFcbiAgICB3aGlsZSAoKytjZWxsSW5kZXggPCBjb2x1bW5zKSB7XG4gICAgICBjZWxscy5wdXNoKHt0eXBlOiAndGFibGVDZWxsJywgY2hpbGRyZW46IFtdfSlcbiAgICB9XG4gIH1cblxuICBsZXQgYWxpZ25JbmRleCA9IGFsaWduLmxlbmd0aCAtIDFcbiAgd2hpbGUgKCsrYWxpZ25JbmRleCA8IGNvbHVtbnMpIHtcbiAgICBhbGlnbi5wdXNoKG51bGwpXG4gIH1cblxuICBoLmluVGFibGUgPSBmYWxzZVxuXG4gIHJldHVybiBoKG5vZGUsICd0YWJsZScsIHthbGlnbn0sIHJvd3MpXG59XG5cbi8qKlxuICogSW5mZXIgd2hldGhlciB0aGUgSFRNTCB0YWJsZSBoYXMgYSBoZWFkIGFuZCBob3cgaXQgYWxpZ25zLlxuICpcbiAqIEBwYXJhbSB7RWxlbWVudH0gbm9kZVxuICogQHJldHVybnMge0luZm99XG4gKi9cbmZ1bmN0aW9uIGluc3BlY3Qobm9kZSkge1xuICBsZXQgaGVhZGxlc3MgPSB0cnVlXG4gIGxldCByb3dJbmRleCA9IDBcbiAgbGV0IGNlbGxJbmRleCA9IDBcbiAgLyoqIEB0eXBlIHtBcnJheTxzdHJpbmd8bnVsbD59ICovXG4gIGNvbnN0IGFsaWduID0gW251bGxdXG5cbiAgdmlzaXQobm9kZSwgJ2VsZW1lbnQnLCAoY2hpbGQpID0+IHtcbiAgICBpZiAoY2hpbGQudGFnTmFtZSA9PT0gJ3RhYmxlJyAmJiBub2RlICE9PSBjaGlsZCkge1xuICAgICAgcmV0dXJuIFNLSVBcbiAgICB9XG5cbiAgICAvLyBJZiB0aGVyZSBpcyBhIGB0aGVhZGAsIGFzc3VtZSB0aGVyZSBpcyBhIGhlYWRlciByb3cuXG4gICAgaWYgKGNlbGwoY2hpbGQpICYmIGNoaWxkLnByb3BlcnRpZXMpIHtcbiAgICAgIGlmICghYWxpZ25bY2VsbEluZGV4XSkge1xuICAgICAgICBhbGlnbltjZWxsSW5kZXhdID0gU3RyaW5nKGNoaWxkLnByb3BlcnRpZXMuYWxpZ24gfHwgJycpIHx8IG51bGxcbiAgICAgIH1cblxuICAgICAgLy8gSWYgdGhlcmUgaXMgYSB0aCBpbiB0aGUgZmlyc3Qgcm93LCBhc3N1bWUgdGhlcmUgaXMgYSBoZWFkZXIgcm93LlxuICAgICAgaWYgKGhlYWRsZXNzICYmIHJvd0luZGV4IDwgMiAmJiBjaGlsZC50YWdOYW1lID09PSAndGgnKSB7XG4gICAgICAgIGhlYWRsZXNzID0gZmFsc2VcbiAgICAgIH1cblxuICAgICAgY2VsbEluZGV4KytcbiAgICB9IGVsc2UgaWYgKHRoZWFkKGNoaWxkKSkge1xuICAgICAgaGVhZGxlc3MgPSBmYWxzZVxuICAgIH0gZWxzZSBpZiAodHIoY2hpbGQpKSB7XG4gICAgICByb3dJbmRleCsrXG4gICAgICBjZWxsSW5kZXggPSAwXG4gICAgfVxuICB9KVxuXG4gIHJldHVybiB7YWxpZ24sIGhlYWRsZXNzfVxufVxuXG4vKipcbiAqIEVuc3VyZSB0aGUgcm93cyBhcmUgcHJvcGVybHkgc3RydWN0dXJlZC5cbiAqXG4gKiBAcGFyYW0ge0FycmF5PE1kYXN0Tm9kZT59IGNoaWxkcmVuXG4gKiBAcGFyYW0ge2Jvb2xlYW59IGhlYWRsZXNzXG4gKiBAcmV0dXJucyB7QXJyYXk8TWRhc3RUYWJsZUNvbnRlbnQ+fVxuICovXG5mdW5jdGlvbiB0b1Jvd3MoY2hpbGRyZW4sIGhlYWRsZXNzKSB7XG4gIGxldCBpbmRleCA9IC0xXG4gIC8qKiBAdHlwZSB7QXJyYXk8TWRhc3RUYWJsZUNvbnRlbnQ+fSAqL1xuICBjb25zdCBub2RlcyA9IFtdXG4gIC8qKiBAdHlwZSB7QXJyYXk8TWRhc3RSb3dDb250ZW50Pnx1bmRlZmluZWR9ICovXG4gIGxldCBxdWV1ZVxuXG4gIC8vIEFkZCBhbiBlbXB0eSBoZWFkZXIgcm93LlxuICBpZiAoaGVhZGxlc3MpIHtcbiAgICBub2Rlcy5wdXNoKHt0eXBlOiAndGFibGVSb3cnLCBjaGlsZHJlbjogW119KVxuICB9XG5cbiAgd2hpbGUgKCsraW5kZXggPCBjaGlsZHJlbi5sZW5ndGgpIHtcbiAgICBjb25zdCBub2RlID0gY2hpbGRyZW5baW5kZXhdXG5cbiAgICBpZiAobm9kZS50eXBlID09PSAndGFibGVSb3cnKSB7XG4gICAgICBpZiAocXVldWUpIHtcbiAgICAgICAgbm9kZS5jaGlsZHJlbi51bnNoaWZ0KC4uLnF1ZXVlKVxuICAgICAgICBxdWV1ZSA9IHVuZGVmaW5lZFxuICAgICAgfVxuXG4gICAgICBub2Rlcy5wdXNoKG5vZGUpXG4gICAgfSBlbHNlIHtcbiAgICAgIGlmICghcXVldWUpIHF1ZXVlID0gW11cbiAgICAgIC8vIEB0cy1leHBlY3QtZXJyb3IgQXNzdW1lIHJvdyBjb250ZW50LlxuICAgICAgcXVldWUucHVzaChub2RlKVxuICAgIH1cbiAgfVxuXG4gIGlmIChxdWV1ZSkge1xuICAgIG5vZGVzW25vZGVzLmxlbmd0aCAtIDFdLmNoaWxkcmVuLnB1c2goLi4ucXVldWUpXG4gIH1cblxuICBpbmRleCA9IC0xXG5cbiAgd2hpbGUgKCsraW5kZXggPCBub2Rlcy5sZW5ndGgpIHtcbiAgICBub2Rlc1tpbmRleF0uY2hpbGRyZW4gPSB0b0NlbGxzKG5vZGVzW2luZGV4XS5jaGlsZHJlbilcbiAgfVxuXG4gIHJldHVybiBub2Rlc1xufVxuXG4vKipcbiAqIEVuc3VyZSB0aGUgY2VsbHMgaW4gYSByb3cgYXJlIHByb3Blcmx5IHN0cnVjdHVyZWQuXG4gKlxuICogQHBhcmFtIHtBcnJheTxNZGFzdE5vZGU+fSBjaGlsZHJlblxuICogQHJldHVybnMge0FycmF5PE1kYXN0Um93Q29udGVudD59XG4gKi9cbmZ1bmN0aW9uIHRvQ2VsbHMoY2hpbGRyZW4pIHtcbiAgLyoqIEB0eXBlIHtBcnJheTxNZGFzdFJvd0NvbnRlbnQ+fSAqL1xuICBjb25zdCBub2RlcyA9IFtdXG4gIGxldCBpbmRleCA9IC0xXG4gIC8qKiBAdHlwZSB7TWRhc3ROb2RlfSAqL1xuICBsZXQgbm9kZVxuICAvKiogQHR5cGUge0FycmF5PE1kYXN0UGhyYXNpbmdDb250ZW50Pnx1bmRlZmluZWR9ICovXG4gIGxldCBxdWV1ZVxuXG4gIHdoaWxlICgrK2luZGV4IDwgY2hpbGRyZW4ubGVuZ3RoKSB7XG4gICAgbm9kZSA9IGNoaWxkcmVuW2luZGV4XVxuXG4gICAgaWYgKG5vZGUudHlwZSA9PT0gJ3RhYmxlQ2VsbCcpIHtcbiAgICAgIGlmIChxdWV1ZSkge1xuICAgICAgICBub2RlLmNoaWxkcmVuLnVuc2hpZnQoLi4ucXVldWUpXG4gICAgICAgIHF1ZXVlID0gdW5kZWZpbmVkXG4gICAgICB9XG5cbiAgICAgIG5vZGVzLnB1c2gobm9kZSlcbiAgICB9IGVsc2Uge1xuICAgICAgaWYgKCFxdWV1ZSkgcXVldWUgPSBbXVxuICAgICAgLy8gQHRzLWV4cGVjdC1lcnJvciBBc3N1bWUgcGhyYXNpbmcgY29udGVudC5cbiAgICAgIHF1ZXVlLnB1c2gobm9kZSlcbiAgICB9XG4gIH1cblxuICBpZiAocXVldWUpIHtcbiAgICBub2RlID0gbm9kZXNbbm9kZXMubGVuZ3RoIC0gMV1cblxuICAgIGlmICghbm9kZSkge1xuICAgICAgbm9kZSA9IHt0eXBlOiAndGFibGVDZWxsJywgY2hpbGRyZW46IFtdfVxuICAgICAgbm9kZXMucHVzaChub2RlKVxuICAgIH1cblxuICAgIG5vZGUuY2hpbGRyZW4ucHVzaCguLi5xdWV1ZSlcbiAgfVxuXG4gIHJldHVybiBub2Rlc1xufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/hast-util-to-mdast/lib/handlers/table.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/hast-util-to-mdast/lib/handlers/text.js":
/*!******************************************************************!*\
  !*** ../../node_modules/hast-util-to-mdast/lib/handlers/text.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   text: () => (/* binding */ text)\n/* harmony export */ });\n/* harmony import */ var _util_wrap_text_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/wrap-text.js */ \"(ssr)/../../node_modules/hast-util-to-mdast/lib/util/wrap-text.js\");\n/**\n * @typedef {import('../types.js').Handle} Handle\n * @typedef {import('../types.js').Text} Text\n */\n\n\n\n/**\n * @type {Handle}\n * @param {Text} node\n */\nfunction text(h, node) {\n  return h(node, 'text', (0,_util_wrap_text_js__WEBPACK_IMPORTED_MODULE_0__.wrapText)(h, node.value))\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2hhc3QtdXRpbC10by1tZGFzdC9saWIvaGFuZGxlcnMvdGV4dC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ0EsYUFBYSw4QkFBOEI7QUFDM0MsYUFBYSw0QkFBNEI7QUFDekM7O0FBRTZDOztBQUU3QztBQUNBLFVBQVU7QUFDVixXQUFXLE1BQU07QUFDakI7QUFDTztBQUNQLHlCQUF5Qiw0REFBUTtBQUNqQyIsInNvdXJjZXMiOlsid2VicGFjazovL0BvcGVuY2FudmFzL3dlYi8uLi8uLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi9oYW5kbGVycy90ZXh0LmpzPzdjM2IiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLkhhbmRsZX0gSGFuZGxlXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLlRleHR9IFRleHRcbiAqL1xuXG5pbXBvcnQge3dyYXBUZXh0fSBmcm9tICcuLi91dGlsL3dyYXAtdGV4dC5qcydcblxuLyoqXG4gKiBAdHlwZSB7SGFuZGxlfVxuICogQHBhcmFtIHtUZXh0fSBub2RlXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiB0ZXh0KGgsIG5vZGUpIHtcbiAgcmV0dXJuIGgobm9kZSwgJ3RleHQnLCB3cmFwVGV4dChoLCBub2RlLnZhbHVlKSlcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/hast-util-to-mdast/lib/handlers/text.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/hast-util-to-mdast/lib/handlers/textarea.js":
/*!**********************************************************************!*\
  !*** ../../node_modules/hast-util-to-mdast/lib/handlers/textarea.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   textarea: () => (/* binding */ textarea)\n/* harmony export */ });\n/* harmony import */ var hast_util_to_text__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! hast-util-to-text */ \"(ssr)/../../node_modules/hast-util-to-mdast/node_modules/hast-util-to-text/lib/index.js\");\n/* harmony import */ var _util_wrap_text_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/wrap-text.js */ \"(ssr)/../../node_modules/hast-util-to-mdast/lib/util/wrap-text.js\");\n/**\n * @typedef {import('../types.js').Handle} Handle\n * @typedef {import('../types.js').Element} Element\n */\n\n\n\n\n/**\n * @type {Handle}\n * @param {Element} node\n */\nfunction textarea(h, node) {\n  return h(node, 'text', (0,_util_wrap_text_js__WEBPACK_IMPORTED_MODULE_0__.wrapText)(h, (0,hast_util_to_text__WEBPACK_IMPORTED_MODULE_1__.toText)(node)))\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2hhc3QtdXRpbC10by1tZGFzdC9saWIvaGFuZGxlcnMvdGV4dGFyZWEuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQUE7QUFDQSxhQUFhLDhCQUE4QjtBQUMzQyxhQUFhLCtCQUErQjtBQUM1Qzs7QUFFd0M7QUFDSzs7QUFFN0M7QUFDQSxVQUFVO0FBQ1YsV0FBVyxTQUFTO0FBQ3BCO0FBQ087QUFDUCx5QkFBeUIsNERBQVEsSUFBSSx5REFBTTtBQUMzQyIsInNvdXJjZXMiOlsid2VicGFjazovL0BvcGVuY2FudmFzL3dlYi8uLi8uLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi9oYW5kbGVycy90ZXh0YXJlYS5qcz9jYjU0Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5IYW5kbGV9IEhhbmRsZVxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5FbGVtZW50fSBFbGVtZW50XG4gKi9cblxuaW1wb3J0IHt0b1RleHR9IGZyb20gJ2hhc3QtdXRpbC10by10ZXh0J1xuaW1wb3J0IHt3cmFwVGV4dH0gZnJvbSAnLi4vdXRpbC93cmFwLXRleHQuanMnXG5cbi8qKlxuICogQHR5cGUge0hhbmRsZX1cbiAqIEBwYXJhbSB7RWxlbWVudH0gbm9kZVxuICovXG5leHBvcnQgZnVuY3Rpb24gdGV4dGFyZWEoaCwgbm9kZSkge1xuICByZXR1cm4gaChub2RlLCAndGV4dCcsIHdyYXBUZXh0KGgsIHRvVGV4dChub2RlKSkpXG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/hast-util-to-mdast/lib/handlers/textarea.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/hast-util-to-mdast/lib/handlers/wbr.js":
/*!*****************************************************************!*\
  !*** ../../node_modules/hast-util-to-mdast/lib/handlers/wbr.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   wbr: () => (/* binding */ wbr)\n/* harmony export */ });\n/**\n * @typedef {import('../types.js').Handle} Handle\n * @typedef {import('../types.js').Element} Element\n */\n\n/**\n * @type {Handle}\n * @param {Element} node\n */\nfunction wbr(h, node) {\n  return h(node, 'text', '\\u200B')\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2hhc3QtdXRpbC10by1tZGFzdC9saWIvaGFuZGxlcnMvd2JyLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBLGFBQWEsOEJBQThCO0FBQzNDLGFBQWEsK0JBQStCO0FBQzVDOztBQUVBO0FBQ0EsVUFBVTtBQUNWLFdBQVcsU0FBUztBQUNwQjtBQUNPO0FBQ1A7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL0BvcGVuY2FudmFzL3dlYi8uLi8uLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi9oYW5kbGVycy93YnIuanM/MzdmOSJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuSGFuZGxlfSBIYW5kbGVcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuRWxlbWVudH0gRWxlbWVudFxuICovXG5cbi8qKlxuICogQHR5cGUge0hhbmRsZX1cbiAqIEBwYXJhbSB7RWxlbWVudH0gbm9kZVxuICovXG5leHBvcnQgZnVuY3Rpb24gd2JyKGgsIG5vZGUpIHtcbiAgcmV0dXJuIGgobm9kZSwgJ3RleHQnLCAnXFx1MjAwQicpXG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/hast-util-to-mdast/lib/handlers/wbr.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/hast-util-to-mdast/lib/index.js":
/*!**********************************************************!*\
  !*** ../../node_modules/hast-util-to-mdast/lib/index.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   all: () => (/* reexport safe */ _all_js__WEBPACK_IMPORTED_MODULE_1__.all),\n/* harmony export */   defaultHandlers: () => (/* reexport safe */ _handlers_index_js__WEBPACK_IMPORTED_MODULE_3__.handlers),\n/* harmony export */   one: () => (/* reexport safe */ _one_js__WEBPACK_IMPORTED_MODULE_0__.one),\n/* harmony export */   toMdast: () => (/* binding */ toMdast)\n/* harmony export */ });\n/* harmony import */ var rehype_minify_whitespace__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! rehype-minify-whitespace */ \"(ssr)/../../node_modules/rehype-minify-whitespace/index.js\");\n/* harmony import */ var unist_util_is__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! unist-util-is */ \"(ssr)/../../node_modules/unist-util-is/lib/index.js\");\n/* harmony import */ var unist_util_visit__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! unist-util-visit */ \"(ssr)/../../node_modules/hast-util-to-mdast/node_modules/unist-util-visit/lib/index.js\");\n/* harmony import */ var _one_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./one.js */ \"(ssr)/../../node_modules/hast-util-to-mdast/lib/one.js\");\n/* harmony import */ var _handlers_index_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./handlers/index.js */ \"(ssr)/../../node_modules/hast-util-to-mdast/lib/handlers/index.js\");\n/* harmony import */ var _util_own_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./util/own.js */ \"(ssr)/../../node_modules/hast-util-to-mdast/lib/util/own.js\");\n/* harmony import */ var _all_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./all.js */ \"(ssr)/../../node_modules/hast-util-to-mdast/lib/all.js\");\n/**\n * @typedef {import('./types.js').Node} Node\n * @typedef {import('./types.js').Element} Element\n * @typedef {import('./types.js').Options} Options\n * @typedef {import('./types.js').Properties} Properties\n * @typedef {import('./types.js').H} H\n * @typedef {import('./types.js').HWithoutProps} HWithoutProps\n * @typedef {import('./types.js').HWithProps} HWithProps\n * @typedef {import('./types.js').MdastNode} MdastNode\n * @typedef {import('./types.js').MdastRoot} MdastRoot\n */\n\n\n\n\n\n\n\n\n\n\n\nconst block = (0,unist_util_is__WEBPACK_IMPORTED_MODULE_2__.convert)(['heading', 'paragraph', 'root'])\n\n/**\n * Transform hast to mdast.\n *\n * @param {Node} tree\n *   Tree (hast).\n * @param {Options} [options]\n *   Configuration (optional).\n */\nfunction toMdast(tree, options = {}) {\n  /** @type {Record<string, Element>} */\n  const byId = {}\n  /** @type {MdastNode|MdastRoot} */\n  let mdast\n\n  /**\n   * @type {H}\n   */\n  const h = Object.assign(\n    /**\n     * @type {HWithProps & HWithoutProps}\n     */\n    (\n      /**\n       * @param {Node} node\n       * @param {string} type\n       * @param {Properties|string|Array<Node>} [props]\n       * @param {string|Array<Node>} [children]\n       */\n      (node, type, props, children) => {\n        /** @type {Properties|undefined} */\n        let properties\n\n        if (typeof props === 'string' || Array.isArray(props)) {\n          children = props\n          properties = {}\n        } else {\n          properties = props\n        }\n\n        /** @type {Node} */\n        // @ts-expect-error Assume valid `type` and `children`/`value`.\n        const result = {type, ...properties}\n\n        if (typeof children === 'string') {\n          // @ts-expect-error: Looks like a literal.\n          result.value = children\n        } else if (children) {\n          // @ts-expect-error: Looks like a parent.\n          result.children = children\n        }\n\n        if (node.position) {\n          result.position = node.position\n        }\n\n        return result\n      }\n    ),\n    {\n      nodeById: byId,\n      baseFound: false,\n      inTable: false,\n      wrapText: true,\n      /** @type {string|null} */\n      frozenBaseUrl: null,\n      qNesting: 0,\n      handlers: options.handlers\n        ? {..._handlers_index_js__WEBPACK_IMPORTED_MODULE_3__.handlers, ...options.handlers}\n        : _handlers_index_js__WEBPACK_IMPORTED_MODULE_3__.handlers,\n      document: options.document,\n      checked: options.checked || '[x]',\n      unchecked: options.unchecked || '[ ]',\n      quotes: options.quotes || ['\"']\n    }\n  )\n\n  ;(0,unist_util_visit__WEBPACK_IMPORTED_MODULE_4__.visit)(tree, 'element', (node) => {\n    const id =\n      node.properties &&\n      'id' in node.properties &&\n      String(node.properties.id).toUpperCase()\n\n    if (id && !_util_own_js__WEBPACK_IMPORTED_MODULE_5__.own.call(byId, id)) {\n      byId[id] = node\n    }\n  })\n\n  // @ts-expect-error: does return a transformer, that does accept any node.\n  ;(0,rehype_minify_whitespace__WEBPACK_IMPORTED_MODULE_6__[\"default\"])({newlines: options.newlines === true})(tree)\n\n  const result = (0,_one_js__WEBPACK_IMPORTED_MODULE_0__.one)(h, tree, undefined)\n\n  if (!result) {\n    mdast = {type: 'root', children: []}\n  } else if (Array.isArray(result)) {\n    mdast = {type: 'root', children: result}\n  } else {\n    mdast = result\n  }\n\n  (0,unist_util_visit__WEBPACK_IMPORTED_MODULE_4__.visit)(mdast, 'text', ontext)\n\n  return mdast\n\n  /**\n   * Collapse text nodes, and fix whitespace.\n   * Most of this is taken care of by `rehype-minify-whitespace`, but\n   * we’re generating some whitespace too, and some nodes are in the end\n   * ignored.\n   * So clean up.\n   *\n   * @type {import('unist-util-visit/complex-types').BuildVisitor<MdastRoot, 'text'>}\n   */\n  function ontext(node, index, parent) {\n    /* c8 ignore next 3 */\n    if (index === null || !parent) {\n      return\n    }\n\n    const previous = parent.children[index - 1]\n\n    if (previous && previous.type === node.type) {\n      previous.value += node.value\n      parent.children.splice(index, 1)\n\n      if (previous.position && node.position) {\n        previous.position.end = node.position.end\n      }\n\n      // Iterate over the previous node again, to handle its total value.\n      return index - 1\n    }\n\n    node.value = node.value.replace(/[\\t ]*(\\r?\\n|\\r)[\\t ]*/, '$1')\n\n    // We don’t care about other phrasing nodes in between (e.g., `[ asd ]()`),\n    // as there the whitespace matters.\n    if (parent && block(parent)) {\n      if (!index) {\n        node.value = node.value.replace(/^[\\t ]+/, '')\n      }\n\n      if (index === parent.children.length - 1) {\n        node.value = node.value.replace(/[\\t ]+$/, '')\n      }\n    }\n\n    if (!node.value) {\n      parent.children.splice(index, 1)\n      return index\n    }\n  }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/hast-util-to-mdast/lib/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/hast-util-to-mdast/lib/one.js":
/*!********************************************************!*\
  !*** ../../node_modules/hast-util-to-mdast/lib/one.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   one: () => (/* binding */ one)\n/* harmony export */ });\n/* harmony import */ var _all_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./all.js */ \"(ssr)/../../node_modules/hast-util-to-mdast/lib/all.js\");\n/* harmony import */ var _util_own_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./util/own.js */ \"(ssr)/../../node_modules/hast-util-to-mdast/lib/util/own.js\");\n/* harmony import */ var _util_wrap_text_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./util/wrap-text.js */ \"(ssr)/../../node_modules/hast-util-to-mdast/lib/util/wrap-text.js\");\n/**\n * @typedef {import('./types.js').H} H\n * @typedef {import('./types.js').Node} Node\n * @typedef {import('./types.js').Parent} Parent\n * @typedef {import('./types.js').Handle} Handle\n * @typedef {import('./types.js').MdastNode} MdastNode\n */\n\n\n\n\n\n/**\n * @param {H} h\n * @param {Node} node\n * @param {Parent|undefined} parent\n * @returns {MdastNode|Array<MdastNode>|void}\n */\nfunction one(h, node, parent) {\n  /** @type {Handle|undefined} */\n  let fn\n\n  if (node.type === 'element') {\n    if (node.properties && node.properties.dataMdast === 'ignore') {\n      return\n    }\n\n    if (_util_own_js__WEBPACK_IMPORTED_MODULE_0__.own.call(h.handlers, node.tagName)) {\n      fn = h.handlers[node.tagName]\n    }\n  } else if (_util_own_js__WEBPACK_IMPORTED_MODULE_0__.own.call(h.handlers, node.type)) {\n    fn = h.handlers[node.type]\n  }\n\n  if (typeof fn === 'function') {\n    return fn(h, node, parent)\n  }\n\n  return unknown(h, node)\n}\n\n/**\n * @type {Handle}\n * @param {Node} node\n */\nfunction unknown(h, node) {\n  // @ts-expect-error: Looks like a literal.\n  if (typeof node.value === 'string') {\n    // @ts-expect-error: Looks like a literal.\n    return h(node, 'text', (0,_util_wrap_text_js__WEBPACK_IMPORTED_MODULE_1__.wrapText)(h, node.value))\n  }\n\n  return (0,_all_js__WEBPACK_IMPORTED_MODULE_2__.all)(h, node)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/hast-util-to-mdast/lib/one.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/hast-util-to-mdast/lib/util/find-selected-options.js":
/*!*******************************************************************************!*\
  !*** ../../node_modules/hast-util-to-mdast/lib/util/find-selected-options.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   findSelectedOptions: () => (/* binding */ findSelectedOptions)\n/* harmony export */ });\n/* harmony import */ var hast_util_has_property__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! hast-util-has-property */ \"(ssr)/../../node_modules/hast-util-has-property/lib/index.js\");\n/* harmony import */ var hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! hast-util-is-element */ \"(ssr)/../../node_modules/hast-util-is-element/index.js\");\n/* harmony import */ var hast_util_to_text__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! hast-util-to-text */ \"(ssr)/../../node_modules/hast-util-to-mdast/node_modules/hast-util-to-text/lib/index.js\");\n/* harmony import */ var _wrap_text_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./wrap-text.js */ \"(ssr)/../../node_modules/hast-util-to-mdast/lib/util/wrap-text.js\");\n/**\n * @typedef {import('../types.js').H} H\n * @typedef {import('../types.js').Parent} Parent\n * @typedef {import('../types.js').Element} Element\n * @typedef {import('../types.js').Child} Child\n * @typedef {import('../types.js').Properties} Properties\n */\n\n\n\n\n\n\nconst option = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)('option')\n\n/**\n * @param {H} h\n * @param {Element} node\n * @param {Properties} [properties]\n * @returns {Array<[string, string|null]>}\n */\nfunction findSelectedOptions(h, node, properties) {\n  /** @type {Properties} */\n  // @ts-expect-error: `props` exist.\n  const props = properties || node.properties\n  let options = findOptions(node)\n  const size =\n    Math.min(Number.parseInt(String(props.size), 10), 0) ||\n    (props.multiple ? 4 : 1)\n  let index = -1\n  /** @type {Array<Element>} */\n  const selectedOptions = []\n  /** @type {Array<[string, string|null]>} */\n  const values = []\n\n  while (++index < options.length) {\n    if ((0,hast_util_has_property__WEBPACK_IMPORTED_MODULE_1__.hasProperty)(options[index], 'selected')) {\n      selectedOptions.push(options[index])\n    }\n  }\n\n  const list = selectedOptions.length > 0 ? selectedOptions : options\n  options = list.slice(0, size)\n  index = -1\n\n  while (++index < options.length) {\n    const option = options[index]\n    const content = (0,_wrap_text_js__WEBPACK_IMPORTED_MODULE_2__.wrapText)(h, (0,hast_util_to_text__WEBPACK_IMPORTED_MODULE_3__.toText)(option))\n    /** @type {Properties} */\n    // @ts-expect-error: `props` exist.\n    const props = option.properties\n    const label = content || String(props.label || '')\n    const value = String(props.value || '') || content\n    values.push([value, label === value ? null : label])\n  }\n\n  return values\n}\n\n/**\n * @param {Parent} node\n */\nfunction findOptions(node) {\n  const children = node.children\n  let index = -1\n  /** @type {Array<Element>} */\n  let results = []\n  /** @type {Child} */\n  let child\n\n  while (++index < children.length) {\n    child = children[index]\n\n    // @ts-expect-error Looks like a parent.\n    if (Array.isArray(child.children)) {\n      // @ts-expect-error Looks like a parent.\n      results = results.concat(findOptions(child))\n    }\n\n    if (option(child) && !(0,hast_util_has_property__WEBPACK_IMPORTED_MODULE_1__.hasProperty)(child, 'disabled')) {\n      results.push(child)\n    }\n  }\n\n  return results\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/hast-util-to-mdast/lib/util/find-selected-options.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/hast-util-to-mdast/lib/util/list-items-spread.js":
/*!***************************************************************************!*\
  !*** ../../node_modules/hast-util-to-mdast/lib/util/list-items-spread.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   listItemsSpread: () => (/* binding */ listItemsSpread)\n/* harmony export */ });\n/**\n * @typedef {import('../types.js').MdastListContent} MdastListContent\n */\n\n/**\n * @param {Array<MdastListContent>} children\n * @returns {boolean}\n */\nfunction listItemsSpread(children) {\n  let index = -1\n\n  if (children.length > 1) {\n    while (++index < children.length) {\n      if (children[index].spread) {\n        return true\n      }\n    }\n  }\n\n  return false\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2hhc3QtdXRpbC10by1tZGFzdC9saWIvdXRpbC9saXN0LWl0ZW1zLXNwcmVhZC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQSxhQUFhLHdDQUF3QztBQUNyRDs7QUFFQTtBQUNBLFdBQVcseUJBQXlCO0FBQ3BDLGFBQWE7QUFDYjtBQUNPO0FBQ1A7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL0BvcGVuY2FudmFzL3dlYi8uLi8uLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi91dGlsL2xpc3QtaXRlbXMtc3ByZWFkLmpzP2IwMmUiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLk1kYXN0TGlzdENvbnRlbnR9IE1kYXN0TGlzdENvbnRlbnRcbiAqL1xuXG4vKipcbiAqIEBwYXJhbSB7QXJyYXk8TWRhc3RMaXN0Q29udGVudD59IGNoaWxkcmVuXG4gKiBAcmV0dXJucyB7Ym9vbGVhbn1cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGxpc3RJdGVtc1NwcmVhZChjaGlsZHJlbikge1xuICBsZXQgaW5kZXggPSAtMVxuXG4gIGlmIChjaGlsZHJlbi5sZW5ndGggPiAxKSB7XG4gICAgd2hpbGUgKCsraW5kZXggPCBjaGlsZHJlbi5sZW5ndGgpIHtcbiAgICAgIGlmIChjaGlsZHJlbltpbmRleF0uc3ByZWFkKSB7XG4gICAgICAgIHJldHVybiB0cnVlXG4gICAgICB9XG4gICAgfVxuICB9XG5cbiAgcmV0dXJuIGZhbHNlXG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/hast-util-to-mdast/lib/util/list-items-spread.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/hast-util-to-mdast/lib/util/own.js":
/*!*************************************************************!*\
  !*** ../../node_modules/hast-util-to-mdast/lib/util/own.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   own: () => (/* binding */ own)\n/* harmony export */ });\nconst own = {}.hasOwnProperty\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2hhc3QtdXRpbC10by1tZGFzdC9saWIvdXRpbC9vd24uanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPLGNBQWMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9Ab3BlbmNhbnZhcy93ZWIvLi4vLi4vbm9kZV9tb2R1bGVzL2hhc3QtdXRpbC10by1tZGFzdC9saWIvdXRpbC9vd24uanM/ZWViZCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3Qgb3duID0ge30uaGFzT3duUHJvcGVydHlcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/hast-util-to-mdast/lib/util/own.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/hast-util-to-mdast/lib/util/resolve.js":
/*!*****************************************************************!*\
  !*** ../../node_modules/hast-util-to-mdast/lib/util/resolve.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resolve: () => (/* binding */ resolve)\n/* harmony export */ });\n/**\n * @typedef {import('../types.js').H} H\n */\n\n/**\n * @param {H} h\n * @param {string|null|undefined} url\n * @returns {string}\n */\nfunction resolve(h, url) {\n  if (url === null || url === undefined) {\n    return ''\n  }\n\n  if (h.frozenBaseUrl) {\n    return String(new URL(url, h.frozenBaseUrl))\n  }\n\n  return url\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2hhc3QtdXRpbC10by1tZGFzdC9saWIvdXRpbC9yZXNvbHZlLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBLGFBQWEseUJBQXlCO0FBQ3RDOztBQUVBO0FBQ0EsV0FBVyxHQUFHO0FBQ2QsV0FBVyx1QkFBdUI7QUFDbEMsYUFBYTtBQUNiO0FBQ087QUFDUDtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9Ab3BlbmNhbnZhcy93ZWIvLi4vLi4vbm9kZV9tb2R1bGVzL2hhc3QtdXRpbC10by1tZGFzdC9saWIvdXRpbC9yZXNvbHZlLmpzP2Q4ZTEiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLkh9IEhcbiAqL1xuXG4vKipcbiAqIEBwYXJhbSB7SH0gaFxuICogQHBhcmFtIHtzdHJpbmd8bnVsbHx1bmRlZmluZWR9IHVybFxuICogQHJldHVybnMge3N0cmluZ31cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHJlc29sdmUoaCwgdXJsKSB7XG4gIGlmICh1cmwgPT09IG51bGwgfHwgdXJsID09PSB1bmRlZmluZWQpIHtcbiAgICByZXR1cm4gJydcbiAgfVxuXG4gIGlmIChoLmZyb3plbkJhc2VVcmwpIHtcbiAgICByZXR1cm4gU3RyaW5nKG5ldyBVUkwodXJsLCBoLmZyb3plbkJhc2VVcmwpKVxuICB9XG5cbiAgcmV0dXJuIHVybFxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/hast-util-to-mdast/lib/util/resolve.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/hast-util-to-mdast/lib/util/wrap-children.js":
/*!***********************************************************************!*\
  !*** ../../node_modules/hast-util-to-mdast/lib/util/wrap-children.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   wrapChildren: () => (/* binding */ wrapChildren)\n/* harmony export */ });\n/* harmony import */ var _all_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../all.js */ \"(ssr)/../../node_modules/hast-util-to-mdast/lib/all.js\");\n/* harmony import */ var _wrap_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./wrap.js */ \"(ssr)/../../node_modules/hast-util-to-mdast/lib/util/wrap.js\");\n/**\n * @typedef {import('../types.js').H} H\n * @typedef {import('../types.js').Node} Node\n * @typedef {import('../types.js').MdastNode} MdastNode\n */\n\n\n\n\n/**\n * @param {H} h\n * @param {Node} node\n * @returns {Array<MdastNode>}\n */\nfunction wrapChildren(h, node) {\n  return (0,_wrap_js__WEBPACK_IMPORTED_MODULE_0__.wrap)((0,_all_js__WEBPACK_IMPORTED_MODULE_1__.all)(h, node))\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2hhc3QtdXRpbC10by1tZGFzdC9saWIvdXRpbC93cmFwLWNoaWxkcmVuLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBO0FBQ0EsYUFBYSx5QkFBeUI7QUFDdEMsYUFBYSw0QkFBNEI7QUFDekMsYUFBYSxpQ0FBaUM7QUFDOUM7O0FBRTZCO0FBQ0M7O0FBRTlCO0FBQ0EsV0FBVyxHQUFHO0FBQ2QsV0FBVyxNQUFNO0FBQ2pCLGFBQWE7QUFDYjtBQUNPO0FBQ1AsU0FBUyw4Q0FBSSxDQUFDLDRDQUFHO0FBQ2pCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQG9wZW5jYW52YXMvd2ViLy4uLy4uL25vZGVfbW9kdWxlcy9oYXN0LXV0aWwtdG8tbWRhc3QvbGliL3V0aWwvd3JhcC1jaGlsZHJlbi5qcz83YmFkIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5IfSBIXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLk5vZGV9IE5vZGVcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuTWRhc3ROb2RlfSBNZGFzdE5vZGVcbiAqL1xuXG5pbXBvcnQge2FsbH0gZnJvbSAnLi4vYWxsLmpzJ1xuaW1wb3J0IHt3cmFwfSBmcm9tICcuL3dyYXAuanMnXG5cbi8qKlxuICogQHBhcmFtIHtIfSBoXG4gKiBAcGFyYW0ge05vZGV9IG5vZGVcbiAqIEByZXR1cm5zIHtBcnJheTxNZGFzdE5vZGU+fVxuICovXG5leHBvcnQgZnVuY3Rpb24gd3JhcENoaWxkcmVuKGgsIG5vZGUpIHtcbiAgcmV0dXJuIHdyYXAoYWxsKGgsIG5vZGUpKVxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/hast-util-to-mdast/lib/util/wrap-children.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/hast-util-to-mdast/lib/util/wrap-list-items.js":
/*!*************************************************************************!*\
  !*** ../../node_modules/hast-util-to-mdast/lib/util/wrap-list-items.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   wrapListItems: () => (/* binding */ wrapListItems)\n/* harmony export */ });\n/* harmony import */ var _all_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../all.js */ \"(ssr)/../../node_modules/hast-util-to-mdast/lib/all.js\");\n/**\n * @typedef {import('../types.js').H} H\n * @typedef {import('../types.js').Child} Child\n * @typedef {import('../types.js').MdastListContent} MdastListContent\n */\n\n\n\n/**\n * @param {H} h\n * @param {Child} node\n * @returns {Array<MdastListContent>}\n */\nfunction wrapListItems(h, node) {\n  const children = (0,_all_js__WEBPACK_IMPORTED_MODULE_0__.all)(h, node)\n  let index = -1\n\n  while (++index < children.length) {\n    const child = children[index]\n    if (child.type !== 'listItem') {\n      children[index] = {\n        type: 'listItem',\n        spread: false,\n        checked: null,\n        // @ts-expect-error Assume `children[index]` is block content.\n        children: [child]\n      }\n    }\n  }\n\n  // @ts-expect-error Assume all `listItem`s\n  return children\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2hhc3QtdXRpbC10by1tZGFzdC9saWIvdXRpbC93cmFwLWxpc3QtaXRlbXMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBLGFBQWEseUJBQXlCO0FBQ3RDLGFBQWEsNkJBQTZCO0FBQzFDLGFBQWEsd0NBQXdDO0FBQ3JEOztBQUU2Qjs7QUFFN0I7QUFDQSxXQUFXLEdBQUc7QUFDZCxXQUFXLE9BQU87QUFDbEIsYUFBYTtBQUNiO0FBQ087QUFDUCxtQkFBbUIsNENBQUc7QUFDdEI7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL0BvcGVuY2FudmFzL3dlYi8uLi8uLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi91dGlsL3dyYXAtbGlzdC1pdGVtcy5qcz83MGU4Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5IfSBIXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLkNoaWxkfSBDaGlsZFxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5NZGFzdExpc3RDb250ZW50fSBNZGFzdExpc3RDb250ZW50XG4gKi9cblxuaW1wb3J0IHthbGx9IGZyb20gJy4uL2FsbC5qcydcblxuLyoqXG4gKiBAcGFyYW0ge0h9IGhcbiAqIEBwYXJhbSB7Q2hpbGR9IG5vZGVcbiAqIEByZXR1cm5zIHtBcnJheTxNZGFzdExpc3RDb250ZW50Pn1cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHdyYXBMaXN0SXRlbXMoaCwgbm9kZSkge1xuICBjb25zdCBjaGlsZHJlbiA9IGFsbChoLCBub2RlKVxuICBsZXQgaW5kZXggPSAtMVxuXG4gIHdoaWxlICgrK2luZGV4IDwgY2hpbGRyZW4ubGVuZ3RoKSB7XG4gICAgY29uc3QgY2hpbGQgPSBjaGlsZHJlbltpbmRleF1cbiAgICBpZiAoY2hpbGQudHlwZSAhPT0gJ2xpc3RJdGVtJykge1xuICAgICAgY2hpbGRyZW5baW5kZXhdID0ge1xuICAgICAgICB0eXBlOiAnbGlzdEl0ZW0nLFxuICAgICAgICBzcHJlYWQ6IGZhbHNlLFxuICAgICAgICBjaGVja2VkOiBudWxsLFxuICAgICAgICAvLyBAdHMtZXhwZWN0LWVycm9yIEFzc3VtZSBgY2hpbGRyZW5baW5kZXhdYCBpcyBibG9jayBjb250ZW50LlxuICAgICAgICBjaGlsZHJlbjogW2NoaWxkXVxuICAgICAgfVxuICAgIH1cbiAgfVxuXG4gIC8vIEB0cy1leHBlY3QtZXJyb3IgQXNzdW1lIGFsbCBgbGlzdEl0ZW1gc1xuICByZXR1cm4gY2hpbGRyZW5cbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/hast-util-to-mdast/lib/util/wrap-list-items.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/hast-util-to-mdast/lib/util/wrap-text.js":
/*!*******************************************************************!*\
  !*** ../../node_modules/hast-util-to-mdast/lib/util/wrap-text.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   wrapText: () => (/* binding */ wrapText)\n/* harmony export */ });\n/**\n * @typedef {import('../types.js').H} H\n */\n\n/**\n * @param {H} h\n * @param {string} value\n * @returns {string}\n */\nfunction wrapText(h, value) {\n  return h.wrapText ? value : value.replace(/\\r?\\n|\\r/g, ' ')\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2hhc3QtdXRpbC10by1tZGFzdC9saWIvdXRpbC93cmFwLXRleHQuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0EsYUFBYSx5QkFBeUI7QUFDdEM7O0FBRUE7QUFDQSxXQUFXLEdBQUc7QUFDZCxXQUFXLFFBQVE7QUFDbkIsYUFBYTtBQUNiO0FBQ087QUFDUDtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQG9wZW5jYW52YXMvd2ViLy4uLy4uL25vZGVfbW9kdWxlcy9oYXN0LXV0aWwtdG8tbWRhc3QvbGliL3V0aWwvd3JhcC10ZXh0LmpzPzg1MDEiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLkh9IEhcbiAqL1xuXG4vKipcbiAqIEBwYXJhbSB7SH0gaFxuICogQHBhcmFtIHtzdHJpbmd9IHZhbHVlXG4gKiBAcmV0dXJucyB7c3RyaW5nfVxuICovXG5leHBvcnQgZnVuY3Rpb24gd3JhcFRleHQoaCwgdmFsdWUpIHtcbiAgcmV0dXJuIGgud3JhcFRleHQgPyB2YWx1ZSA6IHZhbHVlLnJlcGxhY2UoL1xccj9cXG58XFxyL2csICcgJylcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/hast-util-to-mdast/lib/util/wrap-text.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/hast-util-to-mdast/lib/util/wrap.js":
/*!**************************************************************!*\
  !*** ../../node_modules/hast-util-to-mdast/lib/util/wrap.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   wrap: () => (/* binding */ wrap),\n/* harmony export */   wrapNeeded: () => (/* binding */ wrapNeeded)\n/* harmony export */ });\n/* harmony import */ var extend__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! extend */ \"(ssr)/../../node_modules/extend/index.js\");\n/* harmony import */ var hast_util_phrasing__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! hast-util-phrasing */ \"(ssr)/../../node_modules/hast-util-phrasing/lib/index.js\");\n/* harmony import */ var mdast_util_phrasing__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! mdast-util-phrasing */ \"(ssr)/../../node_modules/mdast-util-phrasing/lib/index.js\");\n/**\n * @typedef {import('../types.js').MdastNode} MdastNode\n * @typedef {import('../types.js').MdastPhrasingContent} MdastPhrasingContent\n */\n\n\n\n\n\n/**\n * @param {Array<MdastNode>} nodes\n */\nfunction wrap(nodes) {\n  return runs(nodes, onphrasing)\n\n  /**\n   * @param {Array<MdastPhrasingContent>} nodes\n   * @returns {MdastNode|Array<MdastNode>}\n   */\n  function onphrasing(nodes) {\n    const head = nodes[0]\n\n    if (\n      nodes.length === 1 &&\n      head.type === 'text' &&\n      (head.value === ' ' || head.value === '\\n')\n    ) {\n      return []\n    }\n\n    return {type: 'paragraph', children: nodes}\n  }\n}\n\n/**\n * Check if there are non-phrasing mdast nodes returned.\n * This is needed if a fragment is given, which could just be a sentence, and\n * doesn’t need a wrapper paragraph.\n *\n * @param {Array<MdastNode>} nodes\n * @returns {boolean}\n */\nfunction wrapNeeded(nodes) {\n  let index = -1\n  /** @type {MdastNode} */\n  let node\n\n  while (++index < nodes.length) {\n    node = nodes[index]\n\n    if (!phrasing(node) || ('children' in node && wrapNeeded(node.children))) {\n      return true\n    }\n  }\n\n  return false\n}\n\n/**\n * Wrap all runs of mdast phrasing content in `paragraph` nodes.\n *\n * @param {Array<MdastNode>} nodes\n * @param {(nodes: Array<MdastPhrasingContent>) => MdastNode|Array<MdastNode>} onphrasing\n * @param {(node: MdastNode) => MdastNode} [onnonphrasing]\n */\nfunction runs(nodes, onphrasing, onnonphrasing) {\n  const nonphrasing = onnonphrasing || identity\n  /** @type {Array<MdastNode>} */\n  const flattened = flatten(nodes)\n  /** @type {Array<MdastNode>} */\n  let result = []\n  let index = -1\n  /** @type {Array<MdastPhrasingContent>|undefined} */\n  let queue\n  /** @type {MdastNode} */\n  let node\n\n  while (++index < flattened.length) {\n    node = flattened[index]\n\n    if (phrasing(node)) {\n      if (!queue) queue = []\n      queue.push(node)\n    } else {\n      if (queue) {\n        result = result.concat(onphrasing(queue))\n        queue = undefined\n      }\n\n      result = result.concat(nonphrasing(node))\n    }\n  }\n\n  if (queue) {\n    result = result.concat(onphrasing(queue))\n  }\n\n  return result\n}\n\n/**\n * Flatten a list of nodes.\n *\n * @param {Array<MdastNode>} nodes\n * @returns {Array<MdastNode>}\n */\nfunction flatten(nodes) {\n  /** @type {Array<MdastNode>} */\n  let flattened = []\n  let index = -1\n  /** @type {MdastNode} */\n  let node\n\n  while (++index < nodes.length) {\n    node = nodes[index]\n\n    // Straddling: some elements are *weird*.\n    // Namely: `map`, `ins`, `del`, and `a`, as they are hybrid elements.\n    // See: <https://html.spec.whatwg.org/#paragraphs>.\n    // Paragraphs are the weirdest of them all.\n    // See the straddling fixture for more info!\n    // `ins` is ignored in mdast, so we don’t need to worry about that.\n    // `map` maps to its content, so we don’t need to worry about that either.\n    // `del` maps to `delete` and `a` to `link`, so we do handle those.\n    // What we’ll do is split `node` over each of its children.\n    if (\n      (node.type === 'delete' || node.type === 'link') &&\n      wrapNeeded(node.children)\n    ) {\n      flattened = flattened.concat(split(node))\n    } else {\n      flattened.push(node)\n    }\n  }\n\n  return flattened\n}\n\n/**\n * @param {MdastNode} node\n * @returns {Array<MdastNode>}\n */\nfunction split(node) {\n  // @ts-expect-error Assume parent.\n  return runs(node.children, onphrasing, onnonphrasing)\n\n  /**\n   * Use `child`, add `parent` as its first child, put the original children\n   * into `parent`.\n   * If `child` is not a parent, `parent` will not be added.\n   *\n   * @param {MdastNode} child\n   * @returns {MdastNode}\n   */\n  function onnonphrasing(child) {\n    if ('children' in child && 'children' in node) {\n      const {children, ...rest} = node\n      return {\n        ...child,\n        // @ts-expect-error: assume matching parent & child.\n        children: [{...extend__WEBPACK_IMPORTED_MODULE_0__(true, {}, rest), children: child.children}]\n      }\n    }\n\n    return {...child}\n  }\n\n  /**\n   * Use `parent`, put the phrasing run inside it.\n   *\n   * @param {Array<MdastPhrasingContent>} nodes\n   * @returns {MdastNode}\n   */\n  function onphrasing(nodes) {\n    // @ts-expect-error: assume parent.\n    const {children, ...rest} = node\n    // @ts-expect-error: assume matching parent & child.\n    return {...extend__WEBPACK_IMPORTED_MODULE_0__(true, {}, rest), children: nodes}\n  }\n}\n\n/**\n * Check if an mdast node is phrasing.\n *\n * Also supports checking embedded hast fields.\n *\n * @param {MdastNode} node\n * @returns {node is MdastPhrasingContent}\n */\nfunction phrasing(node) {\n  return node.data && node.data.hName\n    ? (0,hast_util_phrasing__WEBPACK_IMPORTED_MODULE_1__.phrasing)({\n        type: 'element',\n        tagName: node.data.hName,\n        properties: {},\n        children: []\n      })\n    : (0,mdast_util_phrasing__WEBPACK_IMPORTED_MODULE_2__.phrasing)(node)\n}\n\n/**\n * @template {unknown} T\n * @param {T} n\n * @returns {T}\n */\nfunction identity(n) {\n  return n\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/hast-util-to-mdast/lib/util/wrap.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/hast-util-to-mdast/node_modules/hast-util-to-text/lib/index.js":
/*!*****************************************************************************************!*\
  !*** ../../node_modules/hast-util-to-mdast/node_modules/hast-util-to-text/lib/index.js ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   toText: () => (/* binding */ toText)\n/* harmony export */ });\n/* harmony import */ var hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! hast-util-is-element */ \"(ssr)/../../node_modules/hast-util-is-element/index.js\");\n/* harmony import */ var unist_util_find_after__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! unist-util-find-after */ \"(ssr)/../../node_modules/hast-util-to-mdast/node_modules/unist-util-find-after/lib/index.js\");\n/**\n * @typedef {import('hast-util-is-element').TestFunctionAnything} TestFunctionAnything\n * @typedef {import('hast').Content} Content\n * @typedef {import('hast').Text} Text\n * @typedef {import('hast').Comment} Comment\n * @typedef {import('hast').Root} Root\n * @typedef {import('hast').Element} Element\n */\n\n/**\n * @typedef {Content | Root} Node\n *   Any node.\n * @typedef {Extract<Node, import('unist').Parent>} Parent\n *   Any parent.\n * @typedef {'normal' | 'pre' | 'nowrap' | 'pre-wrap'} Whitespace\n *   Valid and useful whitespace values (from CSS).\n * @typedef {0 | 1 | 2} BreakNumber\n *   Specific break:\n *\n *   *   `0` — space\n *   *   `1` — line ending\n *   *   `2` — blank line\n * @typedef {'\\n'} BreakForce\n *   Forced break.\n * @typedef {boolean} BreakValue\n *   Whether there was a break.\n * @typedef {BreakValue | BreakNumber | undefined} BreakBefore\n *   Any value for a break before.\n * @typedef {BreakValue | BreakNumber | BreakForce | undefined} BreakAfter\n *   Any value for a break after.\n *\n * @typedef CollectionInfo\n *   Info on current collection.\n * @property {Whitespace} whitespace\n *   Current whitespace setting.\n * @property {BreakBefore} breakBefore\n *   Whether there was a break before.\n * @property {BreakAfter} breakAfter\n *   Whether there was a break after.\n *\n * @typedef Options\n *   Configuration.\n * @property {Whitespace | null | undefined} [whitespace='normal']\n *   Initial CSS whitespace setting to use.\n */\n\n\n\n\nconst searchLineFeeds = /\\n/g\nconst searchTabOrSpaces = /[\\t ]+/g\n\nconst br = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)('br')\nconst p = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)('p')\nconst cell = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)(['th', 'td'])\nconst row = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)('tr')\n\n// Note that we don’t need to include void elements here as they don’t have text.\n// See: <https://github.com/wooorm/html-void-elements>\nconst notRendered = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)([\n  // List from: <https://html.spec.whatwg.org/#hidden-elements>\n  'datalist',\n  'head',\n  'noembed',\n  'noframes',\n  'noscript', // Act as if we support scripting.\n  'rp',\n  'script',\n  'style',\n  'template',\n  'title',\n  // Hidden attribute.\n  hidden,\n  // From: <https://html.spec.whatwg.org/#flow-content-3>\n  closedDialog\n])\n\n// See: <https://html.spec.whatwg.org/#the-css-user-agent-style-sheet-and-presentational-hints>\nconst blockOrCaption = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)([\n  'address', // Flow content\n  'article', // Sections and headings\n  'aside', // Sections and headings\n  'blockquote', // Flow content\n  'body', // Page\n  'caption', // `table-caption`\n  'center', // Flow content (legacy)\n  'dd', // Lists\n  'dialog', // Flow content\n  'dir', // Lists (legacy)\n  'dl', // Lists\n  'dt', // Lists\n  'div', // Flow content\n  'figure', // Flow content\n  'figcaption', // Flow content\n  'footer', // Flow content\n  'form,', // Flow content\n  'h1', // Sections and headings\n  'h2', // Sections and headings\n  'h3', // Sections and headings\n  'h4', // Sections and headings\n  'h5', // Sections and headings\n  'h6', // Sections and headings\n  'header', // Flow content\n  'hgroup', // Sections and headings\n  'hr', // Flow content\n  'html', // Page\n  'legend', // Flow content\n  'listing', // Flow content (legacy)\n  'main', // Flow content\n  'menu', // Lists\n  'nav', // Sections and headings\n  'ol', // Lists\n  'p', // Flow content\n  'plaintext', // Flow content (legacy)\n  'pre', // Flow content\n  'section', // Sections and headings\n  'ul', // Lists\n  'xmp' // Flow content (legacy)\n])\n\n/**\n * Get the plain-text value of a node.\n *\n * ###### Algorithm\n *\n * *   if `tree` is a comment, returns its `value`\n * *   if `tree` is a text, applies normal whitespace collapsing to its\n *     `value`, as defined by the CSS Text spec\n * *   if `tree` is a root or element, applies an algorithm similar to the\n *     `innerText` getter as defined by HTML\n *\n * ###### Notes\n *\n * > 👉 **Note**: the algorithm acts as if `tree` is being rendered, and as if\n * > we’re a CSS-supporting user agent, with scripting enabled.\n *\n * *   if `tree` is an element that is not displayed (such as a `head`), we’ll\n *     still use the `innerText` algorithm instead of switching to `textContent`\n * *   if descendants of `tree` are elements that are not displayed, they are\n *     ignored\n * *   CSS is not considered, except for the default user agent style sheet\n * *   a line feed is collapsed instead of ignored in cases where Fullwidth, Wide,\n *     or Halfwidth East Asian Width characters are used, the same goes for a case\n *     with Chinese, Japanese, or Yi writing systems\n * *   replaced elements (such as `audio`) are treated like non-replaced elements\n *\n * @param {Node} tree\n *   Tree to turn into text.\n * @param {Options} [options]\n *   Configuration (optional).\n * @returns {string}\n *   Serialized `tree`.\n */\nfunction toText(tree, options = {}) {\n  const children = 'children' in tree ? tree.children : []\n  const block = blockOrCaption(tree)\n  const whitespace = inferWhitespace(tree, {\n    whitespace: options.whitespace || 'normal',\n    breakBefore: false,\n    breakAfter: false\n  })\n\n  /** @type {Array<string | BreakNumber>} */\n  const results = []\n\n  // Treat `text` and `comment` as having normal white-space.\n  // This deviates from the spec as in the DOM the node’s `.data` has to be\n  // returned.\n  // If you want that behavior use `hast-util-to-string`.\n  // All other nodes are later handled as if they are `element`s (so the\n  // algorithm also works on a `root`).\n  // Nodes without children are treated as a void element, so `doctype` is thus\n  // ignored.\n  if (tree.type === 'text' || tree.type === 'comment') {\n    results.push(\n      ...collectText(tree, {\n        whitespace,\n        breakBefore: true,\n        breakAfter: true\n      })\n    )\n  }\n\n  // 1.  If this element is not being rendered, or if the user agent is a\n  //     non-CSS user agent, then return the same value as the textContent IDL\n  //     attribute on this element.\n  //\n  //     Note: we’re not supporting stylesheets so we’re acting as if the node\n  //     is rendered.\n  //\n  //     If you want that behavior use `hast-util-to-string`.\n  //     Important: we’ll have to account for this later though.\n\n  // 2.  Let results be a new empty list.\n  let index = -1\n\n  // 3.  For each child node node of this element:\n  while (++index < children.length) {\n    // 3.1. Let current be the list resulting in running the inner text\n    //      collection steps with node.\n    //      Each item in results will either be a JavaScript string or a\n    //      positive integer (a required line break count).\n    // 3.2. For each item item in current, append item to results.\n    results.push(\n      // @ts-expect-error Looks like a parent.\n      ...innerTextCollection(children[index], tree, {\n        whitespace,\n        breakBefore: index ? undefined : block,\n        breakAfter:\n          index < children.length - 1 ? br(children[index + 1]) : block\n      })\n    )\n  }\n\n  // 4.  Remove any items from results that are the empty string.\n  // 5.  Remove any runs of consecutive required line break count items at the\n  //     start or end of results.\n  // 6.  Replace each remaining run of consecutive required line break count\n  //     items with a string consisting of as many U+000A LINE FEED (LF)\n  //     characters as the maximum of the values in the required line break\n  //     count items.\n  /** @type {Array<string>} */\n  const result = []\n  /** @type {number | undefined} */\n  let count\n\n  index = -1\n\n  while (++index < results.length) {\n    const value = results[index]\n\n    if (typeof value === 'number') {\n      if (count !== undefined && value > count) count = value\n    } else if (value) {\n      if (count !== undefined && count > -1) {\n        result.push('\\n'.repeat(count) || ' ')\n      }\n\n      count = -1\n      result.push(value)\n    }\n  }\n\n  // 7.  Return the concatenation of the string items in results.\n  return result.join('')\n}\n\n/**\n * <https://html.spec.whatwg.org/#inner-text-collection-steps>\n *\n * @param {Node} node\n * @param {Parent} parent\n * @param {CollectionInfo} info\n * @returns {Array<string | BreakNumber>}\n */\nfunction innerTextCollection(node, parent, info) {\n  if (node.type === 'element') {\n    return collectElement(node, parent, info)\n  }\n\n  if (node.type === 'text') {\n    return info.whitespace === 'normal'\n      ? collectText(node, info)\n      : collectPreText(node)\n  }\n\n  return []\n}\n\n/**\n * Collect an element.\n *\n * @param {Element} node\n *   Element node.\n * @param {Parent} parent\n * @param {CollectionInfo} info\n *   Info on current collection.\n * @returns {Array<string | BreakNumber>}\n */\nfunction collectElement(node, parent, info) {\n  // First we infer the `white-space` property.\n  const whitespace = inferWhitespace(node, info)\n  const children = node.children || []\n  let index = -1\n  /** @type {Array<string | BreakNumber>} */\n  let items = []\n\n  // We’re ignoring point 3, and exiting without any content here, because we\n  // deviated from the spec in `toText` at step 3.\n  if (notRendered(node)) {\n    return items\n  }\n\n  /** @type {BreakNumber | undefined} */\n  let prefix\n  /** @type {BreakNumber | BreakForce | undefined} */\n  let suffix\n  // Note: we first detect if there is going to be a break before or after the\n  // contents, as that changes the white-space handling.\n\n  // 2.  If node’s computed value of `visibility` is not `visible`, then return\n  //     items.\n  //\n  //     Note: Ignored, as everything is visible by default user agent styles.\n\n  // 3.  If node is not being rendered, then return items. [...]\n  //\n  //     Note: We already did this above.\n\n  // See `collectText` for step 4.\n\n  // 5.  If node is a `<br>` element, then append a string containing a single\n  //     U+000A LINE FEED (LF) character to items.\n  if (br(node)) {\n    suffix = '\\n'\n  }\n\n  // 7.  If node’s computed value of `display` is `table-row`, and node’s CSS\n  //     box is not the last `table-row` box of the nearest ancestor `table`\n  //     box, then append a string containing a single U+000A LINE FEED (LF)\n  //     character to items.\n  //\n  //     See: <https://html.spec.whatwg.org/#tables-2>\n  //     Note: needs further investigation as this does not account for implicit\n  //     rows.\n  else if (row(node) && (0,unist_util_find_after__WEBPACK_IMPORTED_MODULE_1__.findAfter)(parent, node, row)) {\n    suffix = '\\n'\n  }\n\n  // 8.  If node is a `<p>` element, then append 2 (a required line break count)\n  //     at the beginning and end of items.\n  else if (p(node)) {\n    prefix = 2\n    suffix = 2\n  }\n\n  // 9.  If node’s used value of `display` is block-level or `table-caption`,\n  //     then append 1 (a required line break count) at the beginning and end of\n  //     items.\n  else if (blockOrCaption(node)) {\n    prefix = 1\n    suffix = 1\n  }\n\n  // 1.  Let items be the result of running the inner text collection steps with\n  //     each child node of node in tree order, and then concatenating the\n  //     results to a single list.\n  while (++index < children.length) {\n    items = items.concat(\n      innerTextCollection(children[index], node, {\n        whitespace,\n        breakBefore: index ? undefined : prefix,\n        breakAfter:\n          index < children.length - 1 ? br(children[index + 1]) : suffix\n      })\n    )\n  }\n\n  // 6.  If node’s computed value of `display` is `table-cell`, and node’s CSS\n  //     box is not the last `table-cell` box of its enclosing `table-row` box,\n  //     then append a string containing a single U+0009 CHARACTER TABULATION\n  //     (tab) character to items.\n  //\n  //     See: <https://html.spec.whatwg.org/#tables-2>\n  if (cell(node) && (0,unist_util_find_after__WEBPACK_IMPORTED_MODULE_1__.findAfter)(parent, node, cell)) {\n    items.push('\\t')\n  }\n\n  // Add the pre- and suffix.\n  if (prefix) items.unshift(prefix)\n  if (suffix) items.push(suffix)\n\n  return items\n}\n\n/**\n * 4.  If node is a Text node, then for each CSS text box produced by node,\n *     in content order, compute the text of the box after application of the\n *     CSS `white-space` processing rules and `text-transform` rules, set\n *     items to the list of the resulting strings, and return items.\n *     The CSS `white-space` processing rules are slightly modified:\n *     collapsible spaces at the end of lines are always collapsed, but they\n *     are only removed if the line is the last line of the block, or it ends\n *     with a br element.\n *     Soft hyphens should be preserved.\n *\n *     Note: See `collectText` and `collectPreText`.\n *     Note: we don’t deal with `text-transform`, no element has that by\n *     default.\n *\n * See: <https://drafts.csswg.org/css-text/#white-space-phase-1>\n *\n * @param {Text | Comment} node\n *   Text node.\n * @param {CollectionInfo} info\n *   Info on current collection.\n * @returns {Array<string | BreakNumber>}\n *   Result.\n */\nfunction collectText(node, info) {\n  const value = String(node.value)\n  /** @type {Array<string>} */\n  const lines = []\n  /** @type {Array<string | BreakNumber>} */\n  const result = []\n  let start = 0\n\n  while (start <= value.length) {\n    searchLineFeeds.lastIndex = start\n\n    const match = searchLineFeeds.exec(value)\n    const end = match && 'index' in match ? match.index : value.length\n\n    lines.push(\n      // Any sequence of collapsible spaces and tabs immediately preceding or\n      // following a segment break is removed.\n      trimAndCollapseSpacesAndTabs(\n        // […] ignoring bidi formatting characters (characters with the\n        // Bidi_Control property [UAX9]: ALM, LTR, RTL, LRE-RLO, LRI-PDI) as if\n        // they were not there.\n        value\n          .slice(start, end)\n          .replace(/[\\u061C\\u200E\\u200F\\u202A-\\u202E\\u2066-\\u2069]/g, ''),\n        start === 0 ? info.breakBefore : true,\n        end === value.length ? info.breakAfter : true\n      )\n    )\n\n    start = end + 1\n  }\n\n  // Collapsible segment breaks are transformed for rendering according to the\n  // segment break transformation rules.\n  // So here we jump to 4.1.2 of [CSSTEXT]:\n  // Any collapsible segment break immediately following another collapsible\n  // segment break is removed\n  let index = -1\n  /** @type {BreakNumber | undefined} */\n  let join\n\n  while (++index < lines.length) {\n    // *   If the character immediately before or immediately after the segment\n    //     break is the zero-width space character (U+200B), then the break is\n    //     removed, leaving behind the zero-width space.\n    if (\n      lines[index].charCodeAt(lines[index].length - 1) === 0x200b /* ZWSP */ ||\n      (index < lines.length - 1 &&\n        lines[index + 1].charCodeAt(0) === 0x200b) /* ZWSP */\n    ) {\n      result.push(lines[index])\n      join = undefined\n    }\n\n    // *   Otherwise, if the East Asian Width property [UAX11] of both the\n    //     character before and after the segment break is Fullwidth, Wide, or\n    //     Halfwidth (not Ambiguous), and neither side is Hangul, then the\n    //     segment break is removed.\n    //\n    //     Note: ignored.\n    // *   Otherwise, if the writing system of the segment break is Chinese,\n    //     Japanese, or Yi, and the character before or after the segment break\n    //     is punctuation or a symbol (Unicode general category P* or S*) and\n    //     has an East Asian Width property of Ambiguous, and the character on\n    //     the other side of the segment break is Fullwidth, Wide, or Halfwidth,\n    //     and not Hangul, then the segment break is removed.\n    //\n    //     Note: ignored.\n\n    // *   Otherwise, the segment break is converted to a space (U+0020).\n    else if (lines[index]) {\n      if (typeof join === 'number') result.push(join)\n      result.push(lines[index])\n      join = 0\n    } else if (index === 0 || index === lines.length - 1) {\n      // If this line is empty, and it’s the first or last, add a space.\n      // Note that this function is only called in normal whitespace, so we\n      // don’t worry about `pre`.\n      result.push(0)\n    }\n  }\n\n  return result\n}\n\n/**\n * Collect a text node as “pre” whitespace.\n *\n * @param {Text} node\n *   Text node.\n * @returns {Array<string | BreakNumber>}\n *   Result.\n */\nfunction collectPreText(node) {\n  return [String(node.value)]\n}\n\n/**\n * 3.  Every collapsible tab is converted to a collapsible space (U+0020).\n * 4.  Any collapsible space immediately following another collapsible\n *     space—even one outside the boundary of the inline containing that\n *     space, provided both spaces are within the same inline formatting\n *     context—is collapsed to have zero advance width. (It is invisible,\n *     but retains its soft wrap opportunity, if any.)\n *\n * @param {string} value\n *   Value to collapse.\n * @param {BreakBefore} breakBefore\n *   Whether there was a break before.\n * @param {BreakAfter} breakAfter\n *   Whether there was a break after.\n * @returns {string}\n *   Result.\n */\nfunction trimAndCollapseSpacesAndTabs(value, breakBefore, breakAfter) {\n  /** @type {Array<string>} */\n  const result = []\n  let start = 0\n  /** @type {number | undefined} */\n  let end\n\n  while (start < value.length) {\n    searchTabOrSpaces.lastIndex = start\n    const match = searchTabOrSpaces.exec(value)\n    end = match ? match.index : value.length\n\n    // If we’re not directly after a segment break, but there was white space,\n    // add an empty value that will be turned into a space.\n    if (!start && !end && match && !breakBefore) {\n      result.push('')\n    }\n\n    if (start !== end) {\n      result.push(value.slice(start, end))\n    }\n\n    start = match ? end + match[0].length : end\n  }\n\n  // If we reached the end, there was trailing white space, and there’s no\n  // segment break after this node, add an empty value that will be turned\n  // into a space.\n  if (start !== end && !breakAfter) {\n    result.push('')\n  }\n\n  return result.join(' ')\n}\n\n/**\n * Figure out the whitespace of a node.\n *\n * We don’t support void elements here (so `nobr wbr` -> `normal` is ignored).\n *\n * @param {Node} node\n *   Node (typically `Element`).\n * @param {CollectionInfo} info\n *   Info on current collection.\n * @returns {Whitespace}\n *   Applied whitespace.\n */\nfunction inferWhitespace(node, info) {\n  if (node.type === 'element') {\n    const props = node.properties || {}\n    switch (node.tagName) {\n      case 'listing':\n      case 'plaintext':\n      case 'xmp': {\n        return 'pre'\n      }\n\n      case 'nobr': {\n        return 'nowrap'\n      }\n\n      case 'pre': {\n        return props.wrap ? 'pre-wrap' : 'pre'\n      }\n\n      case 'td':\n      case 'th': {\n        return props.noWrap ? 'nowrap' : info.whitespace\n      }\n\n      case 'textarea': {\n        return 'pre-wrap'\n      }\n\n      default:\n    }\n  }\n\n  return info.whitespace\n}\n\n/** @type {TestFunctionAnything} */\nfunction hidden(node) {\n  return Boolean((node.properties || {}).hidden)\n}\n\n/** @type {TestFunctionAnything} */\nfunction closedDialog(node) {\n  return node.tagName === 'dialog' && !(node.properties || {}).open\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/hast-util-to-mdast/node_modules/hast-util-to-text/lib/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/hast-util-to-mdast/node_modules/unist-util-find-after/lib/index.js":
/*!*********************************************************************************************!*\
  !*** ../../node_modules/hast-util-to-mdast/node_modules/unist-util-find-after/lib/index.js ***!
  \*********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   findAfter: () => (/* binding */ findAfter)\n/* harmony export */ });\n/* harmony import */ var unist_util_is__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! unist-util-is */ \"(ssr)/../../node_modules/unist-util-is/lib/index.js\");\n/**\n * @typedef {import('unist').Node} Node\n * @typedef {import('unist').Parent} Parent\n * @typedef {import('unist-util-is').Test} Test\n */\n\n\n\n/**\n * Find the first node in `parent` after another `node` or after an index,\n * that passes `test`.\n\n * @param parent\n *   Parent node.\n * @param index\n *   Child of `parent` or it’s index.\n * @param test\n *   `unist-util-is`-compatible test.\n * @returns\n *   Child of `parent` or `null`.\n */\nconst findAfter =\n  /**\n   * @type {(\n   *  (<T extends Node>(node: Parent, index: Node | number, test: import('unist-util-is').PredicateTest<T>) => T | null) &\n   *  ((node: Parent, index: Node | number, test?: Test) => Node | null)\n   * )}\n   */\n  (\n    /**\n     * @param {Parent} parent\n     * @param {Node | number} index\n     * @param {Test} [test]\n     * @returns {Node | null}\n     */\n    function (parent, index, test) {\n      const is = (0,unist_util_is__WEBPACK_IMPORTED_MODULE_0__.convert)(test)\n\n      if (!parent || !parent.type || !parent.children) {\n        throw new Error('Expected parent node')\n      }\n\n      if (typeof index === 'number') {\n        if (index < 0 || index === Number.POSITIVE_INFINITY) {\n          throw new Error('Expected positive finite number as index')\n        }\n      } else {\n        index = parent.children.indexOf(index)\n\n        if (index < 0) {\n          throw new Error('Expected child node or index')\n        }\n      }\n\n      while (++index < parent.children.length) {\n        if (is(parent.children[index], index, parent)) {\n          return parent.children[index]\n        }\n      }\n\n      return null\n    }\n  )\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/hast-util-to-mdast/node_modules/unist-util-find-after/lib/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/hast-util-to-mdast/node_modules/unist-util-visit-parents/lib/color.js":
/*!************************************************************************************************!*\
  !*** ../../node_modules/hast-util-to-mdast/node_modules/unist-util-visit-parents/lib/color.js ***!
  \************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   color: () => (/* binding */ color)\n/* harmony export */ });\n/**\n * @param {string} d\n * @returns {string}\n */\nfunction color(d) {\n  return '\\u001B[33m' + d + '\\u001B[39m'\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2hhc3QtdXRpbC10by1tZGFzdC9ub2RlX21vZHVsZXMvdW5pc3QtdXRpbC12aXNpdC1wYXJlbnRzL2xpYi9jb2xvci5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQSxXQUFXLFFBQVE7QUFDbkIsYUFBYTtBQUNiO0FBQ087QUFDUDtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQG9wZW5jYW52YXMvd2ViLy4uLy4uL25vZGVfbW9kdWxlcy9oYXN0LXV0aWwtdG8tbWRhc3Qvbm9kZV9tb2R1bGVzL3VuaXN0LXV0aWwtdmlzaXQtcGFyZW50cy9saWIvY29sb3IuanM/MTYyZiJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBwYXJhbSB7c3RyaW5nfSBkXG4gKiBAcmV0dXJucyB7c3RyaW5nfVxuICovXG5leHBvcnQgZnVuY3Rpb24gY29sb3IoZCkge1xuICByZXR1cm4gJ1xcdTAwMUJbMzNtJyArIGQgKyAnXFx1MDAxQlszOW0nXG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/hast-util-to-mdast/node_modules/unist-util-visit-parents/lib/color.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/hast-util-to-mdast/node_modules/unist-util-visit-parents/lib/index.js":
/*!************************************************************************************************!*\
  !*** ../../node_modules/hast-util-to-mdast/node_modules/unist-util-visit-parents/lib/index.js ***!
  \************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CONTINUE: () => (/* binding */ CONTINUE),\n/* harmony export */   EXIT: () => (/* binding */ EXIT),\n/* harmony export */   SKIP: () => (/* binding */ SKIP),\n/* harmony export */   visitParents: () => (/* binding */ visitParents)\n/* harmony export */ });\n/* harmony import */ var unist_util_is__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! unist-util-is */ \"(ssr)/../../node_modules/unist-util-is/lib/index.js\");\n/* harmony import */ var _color_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./color.js */ \"(ssr)/../../node_modules/hast-util-to-mdast/node_modules/unist-util-visit-parents/lib/color.js\");\n/**\n * @typedef {import('unist').Node} Node\n * @typedef {import('unist').Parent} Parent\n * @typedef {import('unist-util-is').Test} Test\n */\n\n/**\n * @typedef {boolean | 'skip'} Action\n *   Union of the action types.\n *\n * @typedef {number} Index\n *   Move to the sibling at `index` next (after node itself is completely\n *   traversed).\n *\n *   Useful if mutating the tree, such as removing the node the visitor is\n *   currently on, or any of its previous siblings.\n *   Results less than 0 or greater than or equal to `children.length` stop\n *   traversing the parent.\n *\n * @typedef {[(Action | null | undefined | void)?, (Index | null | undefined)?]} ActionTuple\n *   List with one or two values, the first an action, the second an index.\n *\n * @typedef {Action | ActionTuple | Index | null | undefined | void} VisitorResult\n *   Any value that can be returned from a visitor.\n */\n\n/**\n * @template {Node} [Visited=Node]\n *   Visited node type.\n * @template {Parent} [Ancestor=Parent]\n *   Ancestor type.\n * @callback Visitor\n *   Handle a node (matching `test`, if given).\n *\n *   Visitors are free to transform `node`.\n *   They can also transform the parent of node (the last of `ancestors`).\n *\n *   Replacing `node` itself, if `SKIP` is not returned, still causes its\n *   descendants to be walked (which is a bug).\n *\n *   When adding or removing previous siblings of `node` (or next siblings, in\n *   case of reverse), the `Visitor` should return a new `Index` to specify the\n *   sibling to traverse after `node` is traversed.\n *   Adding or removing next siblings of `node` (or previous siblings, in case\n *   of reverse) is handled as expected without needing to return a new `Index`.\n *\n *   Removing the children property of an ancestor still results in them being\n *   traversed.\n * @param {Visited} node\n *   Found node.\n * @param {Array<Ancestor>} ancestors\n *   Ancestors of `node`.\n * @returns {VisitorResult}\n *   What to do next.\n *\n *   An `Index` is treated as a tuple of `[CONTINUE, Index]`.\n *   An `Action` is treated as a tuple of `[Action]`.\n *\n *   Passing a tuple back only makes sense if the `Action` is `SKIP`.\n *   When the `Action` is `EXIT`, that action can be returned.\n *   When the `Action` is `CONTINUE`, `Index` can be returned.\n */\n\n/**\n * @template {Node} [Tree=Node]\n *   Tree type.\n * @template {Test} [Check=string]\n *   Test type.\n * @typedef {Visitor<import('./complex-types.js').Matches<import('./complex-types.js').InclusiveDescendant<Tree>, Check>, Extract<import('./complex-types.js').InclusiveDescendant<Tree>, Parent>>} BuildVisitor\n *   Build a typed `Visitor` function from a tree and a test.\n *\n *   It will infer which values are passed as `node` and which as `parents`.\n */\n\n\n\n\n/**\n * Continue traversing as normal.\n */\nconst CONTINUE = true\n\n/**\n * Stop traversing immediately.\n */\nconst EXIT = false\n\n/**\n * Do not traverse this node’s children.\n */\nconst SKIP = 'skip'\n\n/**\n * Visit nodes, with ancestral information.\n *\n * This algorithm performs *depth-first* *tree traversal* in *preorder*\n * (**NLR**) or if `reverse` is given, in *reverse preorder* (**NRL**).\n *\n * You can choose for which nodes `visitor` is called by passing a `test`.\n * For complex tests, you should test yourself in `visitor`, as it will be\n * faster and will have improved type information.\n *\n * Walking the tree is an intensive task.\n * Make use of the return values of the visitor when possible.\n * Instead of walking a tree multiple times, walk it once, use `unist-util-is`\n * to check if a node matches, and then perform different operations.\n *\n * You can change the tree.\n * See `Visitor` for more info.\n *\n * @param tree\n *   Tree to traverse.\n * @param test\n *   `unist-util-is`-compatible test\n * @param visitor\n *   Handle each node.\n * @param reverse\n *   Traverse in reverse preorder (NRL) instead of the default preorder (NLR).\n * @returns\n *   Nothing.\n */\nconst visitParents =\n  /**\n   * @type {(\n   *   (<Tree extends Node, Check extends Test>(tree: Tree, test: Check, visitor: BuildVisitor<Tree, Check>, reverse?: boolean | null | undefined) => void) &\n   *   (<Tree extends Node>(tree: Tree, visitor: BuildVisitor<Tree>, reverse?: boolean | null | undefined) => void)\n   * )}\n   */\n  (\n    /**\n     * @param {Node} tree\n     * @param {Test} test\n     * @param {Visitor<Node>} visitor\n     * @param {boolean | null | undefined} [reverse]\n     * @returns {void}\n     */\n    function (tree, test, visitor, reverse) {\n      if (typeof test === 'function' && typeof visitor !== 'function') {\n        reverse = visitor\n        // @ts-expect-error no visitor given, so `visitor` is test.\n        visitor = test\n        test = null\n      }\n\n      const is = (0,unist_util_is__WEBPACK_IMPORTED_MODULE_0__.convert)(test)\n      const step = reverse ? -1 : 1\n\n      factory(tree, undefined, [])()\n\n      /**\n       * @param {Node} node\n       * @param {number | undefined} index\n       * @param {Array<Parent>} parents\n       */\n      function factory(node, index, parents) {\n        /** @type {Record<string, unknown>} */\n        // @ts-expect-error: hush\n        const value = node && typeof node === 'object' ? node : {}\n\n        if (typeof value.type === 'string') {\n          const name =\n            // `hast`\n            typeof value.tagName === 'string'\n              ? value.tagName\n              : // `xast`\n              typeof value.name === 'string'\n              ? value.name\n              : undefined\n\n          Object.defineProperty(visit, 'name', {\n            value:\n              'node (' + (0,_color_js__WEBPACK_IMPORTED_MODULE_1__.color)(node.type + (name ? '<' + name + '>' : '')) + ')'\n          })\n        }\n\n        return visit\n\n        function visit() {\n          /** @type {ActionTuple} */\n          let result = []\n          /** @type {ActionTuple} */\n          let subresult\n          /** @type {number} */\n          let offset\n          /** @type {Array<Parent>} */\n          let grandparents\n\n          if (!test || is(node, index, parents[parents.length - 1] || null)) {\n            result = toResult(visitor(node, parents))\n\n            if (result[0] === EXIT) {\n              return result\n            }\n          }\n\n          // @ts-expect-error looks like a parent.\n          if (node.children && result[0] !== SKIP) {\n            // @ts-expect-error looks like a parent.\n            offset = (reverse ? node.children.length : -1) + step\n            // @ts-expect-error looks like a parent.\n            grandparents = parents.concat(node)\n\n            // @ts-expect-error looks like a parent.\n            while (offset > -1 && offset < node.children.length) {\n              // @ts-expect-error looks like a parent.\n              subresult = factory(node.children[offset], offset, grandparents)()\n\n              if (subresult[0] === EXIT) {\n                return subresult\n              }\n\n              offset =\n                typeof subresult[1] === 'number' ? subresult[1] : offset + step\n            }\n          }\n\n          return result\n        }\n      }\n    }\n  )\n\n/**\n * Turn a return value into a clean result.\n *\n * @param {VisitorResult} value\n *   Valid return values from visitors.\n * @returns {ActionTuple}\n *   Clean result.\n */\nfunction toResult(value) {\n  if (Array.isArray(value)) {\n    return value\n  }\n\n  if (typeof value === 'number') {\n    return [CONTINUE, value]\n  }\n\n  return [value]\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/hast-util-to-mdast/node_modules/unist-util-visit-parents/lib/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/hast-util-to-mdast/node_modules/unist-util-visit/lib/index.js":
/*!****************************************************************************************!*\
  !*** ../../node_modules/hast-util-to-mdast/node_modules/unist-util-visit/lib/index.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CONTINUE: () => (/* reexport safe */ unist_util_visit_parents__WEBPACK_IMPORTED_MODULE_0__.CONTINUE),\n/* harmony export */   EXIT: () => (/* reexport safe */ unist_util_visit_parents__WEBPACK_IMPORTED_MODULE_0__.EXIT),\n/* harmony export */   SKIP: () => (/* reexport safe */ unist_util_visit_parents__WEBPACK_IMPORTED_MODULE_0__.SKIP),\n/* harmony export */   visit: () => (/* binding */ visit)\n/* harmony export */ });\n/* harmony import */ var unist_util_visit_parents__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! unist-util-visit-parents */ \"(ssr)/../../node_modules/hast-util-to-mdast/node_modules/unist-util-visit-parents/lib/index.js\");\n/**\n * @typedef {import('unist').Node} Node\n * @typedef {import('unist').Parent} Parent\n * @typedef {import('unist-util-is').Test} Test\n * @typedef {import('unist-util-visit-parents').VisitorResult} VisitorResult\n */\n\n/**\n * Check if `Child` can be a child of `Ancestor`.\n *\n * Returns the ancestor when `Child` can be a child of `Ancestor`, or returns\n * `never`.\n *\n * @template {Node} Ancestor\n *   Node type.\n * @template {Node} Child\n *   Node type.\n * @typedef {(\n *   Ancestor extends Parent\n *     ? Child extends Ancestor['children'][number]\n *       ? Ancestor\n *       : never\n *     : never\n * )} ParentsOf\n */\n\n/**\n * @template {Node} [Visited=Node]\n *   Visited node type.\n * @template {Parent} [Ancestor=Parent]\n *   Ancestor type.\n * @callback Visitor\n *   Handle a node (matching `test`, if given).\n *\n *   Visitors are free to transform `node`.\n *   They can also transform `parent`.\n *\n *   Replacing `node` itself, if `SKIP` is not returned, still causes its\n *   descendants to be walked (which is a bug).\n *\n *   When adding or removing previous siblings of `node` (or next siblings, in\n *   case of reverse), the `Visitor` should return a new `Index` to specify the\n *   sibling to traverse after `node` is traversed.\n *   Adding or removing next siblings of `node` (or previous siblings, in case\n *   of reverse) is handled as expected without needing to return a new `Index`.\n *\n *   Removing the children property of `parent` still results in them being\n *   traversed.\n * @param {Visited} node\n *   Found node.\n * @param {Visited extends Node ? number | null : never} index\n *   Index of `node` in `parent`.\n * @param {Ancestor extends Node ? Ancestor | null : never} parent\n *   Parent of `node`.\n * @returns {VisitorResult}\n *   What to do next.\n *\n *   An `Index` is treated as a tuple of `[CONTINUE, Index]`.\n *   An `Action` is treated as a tuple of `[Action]`.\n *\n *   Passing a tuple back only makes sense if the `Action` is `SKIP`.\n *   When the `Action` is `EXIT`, that action can be returned.\n *   When the `Action` is `CONTINUE`, `Index` can be returned.\n */\n\n/**\n * Build a typed `Visitor` function from a node and all possible parents.\n *\n * It will infer which values are passed as `node` and which as `parent`.\n *\n * @template {Node} Visited\n *   Node type.\n * @template {Parent} Ancestor\n *   Parent type.\n * @typedef {Visitor<Visited, ParentsOf<Ancestor, Visited>>} BuildVisitorFromMatch\n */\n\n/**\n * Build a typed `Visitor` function from a list of descendants and a test.\n *\n * It will infer which values are passed as `node` and which as `parent`.\n *\n * @template {Node} Descendant\n *   Node type.\n * @template {Test} Check\n *   Test type.\n * @typedef {(\n *   BuildVisitorFromMatch<\n *     import('unist-util-visit-parents/complex-types.js').Matches<Descendant, Check>,\n *     Extract<Descendant, Parent>\n *   >\n * )} BuildVisitorFromDescendants\n */\n\n/**\n * Build a typed `Visitor` function from a tree and a test.\n *\n * It will infer which values are passed as `node` and which as `parent`.\n *\n * @template {Node} [Tree=Node]\n *   Node type.\n * @template {Test} [Check=string]\n *   Test type.\n * @typedef {(\n *   BuildVisitorFromDescendants<\n *     import('unist-util-visit-parents/complex-types.js').InclusiveDescendant<Tree>,\n *     Check\n *   >\n * )} BuildVisitor\n */\n\n\n\n/**\n * Visit nodes.\n *\n * This algorithm performs *depth-first* *tree traversal* in *preorder*\n * (**NLR**) or if `reverse` is given, in *reverse preorder* (**NRL**).\n *\n * You can choose for which nodes `visitor` is called by passing a `test`.\n * For complex tests, you should test yourself in `visitor`, as it will be\n * faster and will have improved type information.\n *\n * Walking the tree is an intensive task.\n * Make use of the return values of the visitor when possible.\n * Instead of walking a tree multiple times, walk it once, use `unist-util-is`\n * to check if a node matches, and then perform different operations.\n *\n * You can change the tree.\n * See `Visitor` for more info.\n *\n * @param tree\n *   Tree to traverse.\n * @param test\n *   `unist-util-is`-compatible test\n * @param visitor\n *   Handle each node.\n * @param reverse\n *   Traverse in reverse preorder (NRL) instead of the default preorder (NLR).\n * @returns\n *   Nothing.\n */\nconst visit =\n  /**\n   * @type {(\n   *   (<Tree extends Node, Check extends Test>(tree: Tree, test: Check, visitor: BuildVisitor<Tree, Check>, reverse?: boolean | null | undefined) => void) &\n   *   (<Tree extends Node>(tree: Tree, visitor: BuildVisitor<Tree>, reverse?: boolean | null | undefined) => void)\n   * )}\n   */\n  (\n    /**\n     * @param {Node} tree\n     * @param {Test} test\n     * @param {Visitor} visitor\n     * @param {boolean | null | undefined} [reverse]\n     * @returns {void}\n     */\n    function (tree, test, visitor, reverse) {\n      if (typeof test === 'function' && typeof visitor !== 'function') {\n        reverse = visitor\n        visitor = test\n        test = null\n      }\n\n      (0,unist_util_visit_parents__WEBPACK_IMPORTED_MODULE_0__.visitParents)(tree, test, overload, reverse)\n\n      /**\n       * @param {Node} node\n       * @param {Array<Parent>} parents\n       */\n      function overload(node, parents) {\n        const parent = parents[parents.length - 1]\n        return visitor(\n          node,\n          parent ? parent.children.indexOf(node) : null,\n          parent\n        )\n      }\n    }\n  )\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/hast-util-to-mdast/node_modules/unist-util-visit/lib/index.js\n");

/***/ })

};
;