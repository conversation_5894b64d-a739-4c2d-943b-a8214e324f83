"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@ffmpeg";
exports.ids = ["vendor-chunks/@ffmpeg"];
exports.modules = {

/***/ "(ssr)/../../node_modules/@ffmpeg/ffmpeg/dist/esm/empty.mjs":
/*!************************************************************!*\
  !*** ../../node_modules/@ffmpeg/ffmpeg/dist/esm/empty.mjs ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FFmpeg: () => (/* binding */ FFmpeg)\n/* harmony export */ });\n// File to be imported in node enviroments\nclass FFmpeg {\n    constructor() {\n        throw new Error(\"ffmpeg.wasm does not support nodejs\");\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL0BmZm1wZWcvZmZtcGVnL2Rpc3QvZXNtL2VtcHR5Lm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQG9wZW5jYW52YXMvd2ViLy4uLy4uL25vZGVfbW9kdWxlcy9AZmZtcGVnL2ZmbXBlZy9kaXN0L2VzbS9lbXB0eS5tanM/ZjBlNiJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBGaWxlIHRvIGJlIGltcG9ydGVkIGluIG5vZGUgZW52aXJvbWVudHNcbmV4cG9ydCBjbGFzcyBGRm1wZWcge1xuICAgIGNvbnN0cnVjdG9yKCkge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXCJmZm1wZWcud2FzbSBkb2VzIG5vdCBzdXBwb3J0IG5vZGVqc1wiKTtcbiAgICB9XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@ffmpeg/ffmpeg/dist/esm/empty.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@ffmpeg/util/dist/esm/const.js":
/*!*********************************************************!*\
  !*** ../../node_modules/@ffmpeg/util/dist/esm/const.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HeaderContentLength: () => (/* binding */ HeaderContentLength)\n/* harmony export */ });\nconst HeaderContentLength = \"Content-Length\";\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL0BmZm1wZWcvdXRpbC9kaXN0L2VzbS9jb25zdC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU8iLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9Ab3BlbmNhbnZhcy93ZWIvLi4vLi4vbm9kZV9tb2R1bGVzL0BmZm1wZWcvdXRpbC9kaXN0L2VzbS9jb25zdC5qcz82YzVmIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCBIZWFkZXJDb250ZW50TGVuZ3RoID0gXCJDb250ZW50LUxlbmd0aFwiO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@ffmpeg/util/dist/esm/const.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@ffmpeg/util/dist/esm/errors.js":
/*!**********************************************************!*\
  !*** ../../node_modules/@ffmpeg/util/dist/esm/errors.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ERROR_INCOMPLETED_DOWNLOAD: () => (/* binding */ ERROR_INCOMPLETED_DOWNLOAD),\n/* harmony export */   ERROR_RESPONSE_BODY_READER: () => (/* binding */ ERROR_RESPONSE_BODY_READER)\n/* harmony export */ });\nconst ERROR_RESPONSE_BODY_READER = new Error(\"failed to get response body reader\");\nconst ERROR_INCOMPLETED_DOWNLOAD = new Error(\"failed to complete download\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL0BmZm1wZWcvdXRpbC9kaXN0L2VzbS9lcnJvcnMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBTztBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQG9wZW5jYW52YXMvd2ViLy4uLy4uL25vZGVfbW9kdWxlcy9AZmZtcGVnL3V0aWwvZGlzdC9lc20vZXJyb3JzLmpzPzQxYWYiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IEVSUk9SX1JFU1BPTlNFX0JPRFlfUkVBREVSID0gbmV3IEVycm9yKFwiZmFpbGVkIHRvIGdldCByZXNwb25zZSBib2R5IHJlYWRlclwiKTtcbmV4cG9ydCBjb25zdCBFUlJPUl9JTkNPTVBMRVRFRF9ET1dOTE9BRCA9IG5ldyBFcnJvcihcImZhaWxlZCB0byBjb21wbGV0ZSBkb3dubG9hZFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@ffmpeg/util/dist/esm/errors.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@ffmpeg/util/dist/esm/index.js":
/*!*********************************************************!*\
  !*** ../../node_modules/@ffmpeg/util/dist/esm/index.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   downloadWithProgress: () => (/* binding */ downloadWithProgress),\n/* harmony export */   fetchFile: () => (/* binding */ fetchFile),\n/* harmony export */   importScript: () => (/* binding */ importScript),\n/* harmony export */   toBlobURL: () => (/* binding */ toBlobURL)\n/* harmony export */ });\n/* harmony import */ var _errors_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./errors.js */ \"(ssr)/../../node_modules/@ffmpeg/util/dist/esm/errors.js\");\n/* harmony import */ var _const_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./const.js */ \"(ssr)/../../node_modules/@ffmpeg/util/dist/esm/const.js\");\n\n\nconst readFromBlobOrFile = (blob) => new Promise((resolve, reject) => {\n    const fileReader = new FileReader();\n    fileReader.onload = () => {\n        const { result } = fileReader;\n        if (result instanceof ArrayBuffer) {\n            resolve(new Uint8Array(result));\n        }\n        else {\n            resolve(new Uint8Array());\n        }\n    };\n    fileReader.onerror = (event) => {\n        reject(Error(`File could not be read! Code=${event?.target?.error?.code || -1}`));\n    };\n    fileReader.readAsArrayBuffer(blob);\n});\n/**\n * An util function to fetch data from url string, base64, URL, File or Blob format.\n *\n * Examples:\n * ```ts\n * // URL\n * await fetchFile(\"http://localhost:3000/video.mp4\");\n * // base64\n * await fetchFile(\"data:<type>;base64,wL2dvYWwgbW9yZ...\");\n * // URL\n * await fetchFile(new URL(\"video.mp4\", import.meta.url));\n * // File\n * fileInput.addEventListener('change', (e) => {\n *   await fetchFile(e.target.files[0]);\n * });\n * // Blob\n * const blob = new Blob(...);\n * await fetchFile(blob);\n * ```\n */\nconst fetchFile = async (file) => {\n    let data;\n    if (typeof file === \"string\") {\n        /* From base64 format */\n        if (/data:_data\\/([a-zA-Z]*);base64,([^\"]*)/.test(file)) {\n            data = atob(file.split(\",\")[1])\n                .split(\"\")\n                .map((c) => c.charCodeAt(0));\n            /* From remote server/URL */\n        }\n        else {\n            data = await (await fetch(file)).arrayBuffer();\n        }\n    }\n    else if (file instanceof URL) {\n        data = await (await fetch(file)).arrayBuffer();\n    }\n    else if (file instanceof File || file instanceof Blob) {\n        data = await readFromBlobOrFile(file);\n    }\n    else {\n        return new Uint8Array();\n    }\n    return new Uint8Array(data);\n};\n/**\n * importScript dynamically import a script, useful when you\n * want to use different versions of ffmpeg.wasm based on environment.\n *\n * Example:\n *\n * ```ts\n * await importScript(\"http://localhost:3000/ffmpeg.js\");\n * ```\n */\nconst importScript = async (url) => new Promise((resolve) => {\n    const script = document.createElement(\"script\");\n    const eventHandler = () => {\n        script.removeEventListener(\"load\", eventHandler);\n        resolve();\n    };\n    script.src = url;\n    script.type = \"text/javascript\";\n    script.addEventListener(\"load\", eventHandler);\n    document.getElementsByTagName(\"head\")[0].appendChild(script);\n});\n/**\n * Download content of a URL with progress.\n *\n * Progress only works when Content-Length is provided by the server.\n *\n */\nconst downloadWithProgress = async (url, cb) => {\n    const resp = await fetch(url);\n    let buf;\n    try {\n        // Set total to -1 to indicate that there is not Content-Type Header.\n        const total = parseInt(resp.headers.get(_const_js__WEBPACK_IMPORTED_MODULE_1__.HeaderContentLength) || \"-1\");\n        const reader = resp.body?.getReader();\n        if (!reader)\n            throw _errors_js__WEBPACK_IMPORTED_MODULE_0__.ERROR_RESPONSE_BODY_READER;\n        const chunks = [];\n        let received = 0;\n        for (;;) {\n            const { done, value } = await reader.read();\n            const delta = value ? value.length : 0;\n            if (done) {\n                if (total != -1 && total !== received)\n                    throw _errors_js__WEBPACK_IMPORTED_MODULE_0__.ERROR_INCOMPLETED_DOWNLOAD;\n                cb && cb({ url, total, received, delta, done });\n                break;\n            }\n            chunks.push(value);\n            received += delta;\n            cb && cb({ url, total, received, delta, done });\n        }\n        const data = new Uint8Array(received);\n        let position = 0;\n        for (const chunk of chunks) {\n            data.set(chunk, position);\n            position += chunk.length;\n        }\n        buf = data.buffer;\n    }\n    catch (e) {\n        console.log(`failed to send download progress event: `, e);\n        // Fetch arrayBuffer directly when it is not possible to get progress.\n        buf = await resp.arrayBuffer();\n        cb &&\n            cb({\n                url,\n                total: buf.byteLength,\n                received: buf.byteLength,\n                delta: 0,\n                done: true,\n            });\n    }\n    return buf;\n};\n/**\n * toBlobURL fetches data from an URL and return a blob URL.\n *\n * Example:\n *\n * ```ts\n * await toBlobURL(\"http://localhost:3000/ffmpeg.js\", \"text/javascript\");\n * ```\n */\nconst toBlobURL = async (url, mimeType, progress = false, cb) => {\n    const buf = progress\n        ? await downloadWithProgress(url, cb)\n        : await (await fetch(url)).arrayBuffer();\n    const blob = new Blob([buf], { type: mimeType });\n    return URL.createObjectURL(blob);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@ffmpeg/util/dist/esm/index.js\n");

/***/ })

};
;