"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/prosemirror-transform";
exports.ids = ["vendor-chunks/prosemirror-transform"];
exports.modules = {

/***/ "(ssr)/../../node_modules/prosemirror-transform/dist/index.js":
/*!**************************************************************!*\
  !*** ../../node_modules/prosemirror-transform/dist/index.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AddMarkStep: () => (/* binding */ AddMarkStep),\n/* harmony export */   AddNodeMarkStep: () => (/* binding */ AddNodeMarkStep),\n/* harmony export */   AttrStep: () => (/* binding */ AttrStep),\n/* harmony export */   DocAttrStep: () => (/* binding */ DocAttrStep),\n/* harmony export */   MapResult: () => (/* binding */ MapResult),\n/* harmony export */   Mapping: () => (/* binding */ Mapping),\n/* harmony export */   RemoveMarkStep: () => (/* binding */ RemoveMarkStep),\n/* harmony export */   RemoveNodeMarkStep: () => (/* binding */ RemoveNodeMarkStep),\n/* harmony export */   ReplaceAroundStep: () => (/* binding */ ReplaceAroundStep),\n/* harmony export */   ReplaceStep: () => (/* binding */ ReplaceStep),\n/* harmony export */   Step: () => (/* binding */ Step),\n/* harmony export */   StepMap: () => (/* binding */ StepMap),\n/* harmony export */   StepResult: () => (/* binding */ StepResult),\n/* harmony export */   Transform: () => (/* binding */ Transform),\n/* harmony export */   TransformError: () => (/* binding */ TransformError),\n/* harmony export */   canJoin: () => (/* binding */ canJoin),\n/* harmony export */   canSplit: () => (/* binding */ canSplit),\n/* harmony export */   dropPoint: () => (/* binding */ dropPoint),\n/* harmony export */   findWrapping: () => (/* binding */ findWrapping),\n/* harmony export */   insertPoint: () => (/* binding */ insertPoint),\n/* harmony export */   joinPoint: () => (/* binding */ joinPoint),\n/* harmony export */   liftTarget: () => (/* binding */ liftTarget),\n/* harmony export */   replaceStep: () => (/* binding */ replaceStep)\n/* harmony export */ });\n/* harmony import */ var prosemirror_model__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! prosemirror-model */ \"(ssr)/../../node_modules/prosemirror-model/dist/index.js\");\n\n\n// Recovery values encode a range index and an offset. They are\n// represented as numbers, because tons of them will be created when\n// mapping, for example, a large number of decorations. The number's\n// lower 16 bits provide the index, the remaining bits the offset.\n//\n// Note: We intentionally don't use bit shift operators to en- and\n// decode these, since those clip to 32 bits, which we might in rare\n// cases want to overflow. A 64-bit float can represent 48-bit\n// integers precisely.\nconst lower16 = 0xffff;\nconst factor16 = Math.pow(2, 16);\nfunction makeRecover(index, offset) { return index + offset * factor16; }\nfunction recoverIndex(value) { return value & lower16; }\nfunction recoverOffset(value) { return (value - (value & lower16)) / factor16; }\nconst DEL_BEFORE = 1, DEL_AFTER = 2, DEL_ACROSS = 4, DEL_SIDE = 8;\n/**\nAn object representing a mapped position with extra\ninformation.\n*/\nclass MapResult {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    The mapped version of the position.\n    */\n    pos, \n    /**\n    @internal\n    */\n    delInfo, \n    /**\n    @internal\n    */\n    recover) {\n        this.pos = pos;\n        this.delInfo = delInfo;\n        this.recover = recover;\n    }\n    /**\n    Tells you whether the position was deleted, that is, whether the\n    step removed the token on the side queried (via the `assoc`)\n    argument from the document.\n    */\n    get deleted() { return (this.delInfo & DEL_SIDE) > 0; }\n    /**\n    Tells you whether the token before the mapped position was deleted.\n    */\n    get deletedBefore() { return (this.delInfo & (DEL_BEFORE | DEL_ACROSS)) > 0; }\n    /**\n    True when the token after the mapped position was deleted.\n    */\n    get deletedAfter() { return (this.delInfo & (DEL_AFTER | DEL_ACROSS)) > 0; }\n    /**\n    Tells whether any of the steps mapped through deletes across the\n    position (including both the token before and after the\n    position).\n    */\n    get deletedAcross() { return (this.delInfo & DEL_ACROSS) > 0; }\n}\n/**\nA map describing the deletions and insertions made by a step, which\ncan be used to find the correspondence between positions in the\npre-step version of a document and the same position in the\npost-step version.\n*/\nclass StepMap {\n    /**\n    Create a position map. The modifications to the document are\n    represented as an array of numbers, in which each group of three\n    represents a modified chunk as `[start, oldSize, newSize]`.\n    */\n    constructor(\n    /**\n    @internal\n    */\n    ranges, \n    /**\n    @internal\n    */\n    inverted = false) {\n        this.ranges = ranges;\n        this.inverted = inverted;\n        if (!ranges.length && StepMap.empty)\n            return StepMap.empty;\n    }\n    /**\n    @internal\n    */\n    recover(value) {\n        let diff = 0, index = recoverIndex(value);\n        if (!this.inverted)\n            for (let i = 0; i < index; i++)\n                diff += this.ranges[i * 3 + 2] - this.ranges[i * 3 + 1];\n        return this.ranges[index * 3] + diff + recoverOffset(value);\n    }\n    mapResult(pos, assoc = 1) { return this._map(pos, assoc, false); }\n    map(pos, assoc = 1) { return this._map(pos, assoc, true); }\n    /**\n    @internal\n    */\n    _map(pos, assoc, simple) {\n        let diff = 0, oldIndex = this.inverted ? 2 : 1, newIndex = this.inverted ? 1 : 2;\n        for (let i = 0; i < this.ranges.length; i += 3) {\n            let start = this.ranges[i] - (this.inverted ? diff : 0);\n            if (start > pos)\n                break;\n            let oldSize = this.ranges[i + oldIndex], newSize = this.ranges[i + newIndex], end = start + oldSize;\n            if (pos <= end) {\n                let side = !oldSize ? assoc : pos == start ? -1 : pos == end ? 1 : assoc;\n                let result = start + diff + (side < 0 ? 0 : newSize);\n                if (simple)\n                    return result;\n                let recover = pos == (assoc < 0 ? start : end) ? null : makeRecover(i / 3, pos - start);\n                let del = pos == start ? DEL_AFTER : pos == end ? DEL_BEFORE : DEL_ACROSS;\n                if (assoc < 0 ? pos != start : pos != end)\n                    del |= DEL_SIDE;\n                return new MapResult(result, del, recover);\n            }\n            diff += newSize - oldSize;\n        }\n        return simple ? pos + diff : new MapResult(pos + diff, 0, null);\n    }\n    /**\n    @internal\n    */\n    touches(pos, recover) {\n        let diff = 0, index = recoverIndex(recover);\n        let oldIndex = this.inverted ? 2 : 1, newIndex = this.inverted ? 1 : 2;\n        for (let i = 0; i < this.ranges.length; i += 3) {\n            let start = this.ranges[i] - (this.inverted ? diff : 0);\n            if (start > pos)\n                break;\n            let oldSize = this.ranges[i + oldIndex], end = start + oldSize;\n            if (pos <= end && i == index * 3)\n                return true;\n            diff += this.ranges[i + newIndex] - oldSize;\n        }\n        return false;\n    }\n    /**\n    Calls the given function on each of the changed ranges included in\n    this map.\n    */\n    forEach(f) {\n        let oldIndex = this.inverted ? 2 : 1, newIndex = this.inverted ? 1 : 2;\n        for (let i = 0, diff = 0; i < this.ranges.length; i += 3) {\n            let start = this.ranges[i], oldStart = start - (this.inverted ? diff : 0), newStart = start + (this.inverted ? 0 : diff);\n            let oldSize = this.ranges[i + oldIndex], newSize = this.ranges[i + newIndex];\n            f(oldStart, oldStart + oldSize, newStart, newStart + newSize);\n            diff += newSize - oldSize;\n        }\n    }\n    /**\n    Create an inverted version of this map. The result can be used to\n    map positions in the post-step document to the pre-step document.\n    */\n    invert() {\n        return new StepMap(this.ranges, !this.inverted);\n    }\n    /**\n    @internal\n    */\n    toString() {\n        return (this.inverted ? \"-\" : \"\") + JSON.stringify(this.ranges);\n    }\n    /**\n    Create a map that moves all positions by offset `n` (which may be\n    negative). This can be useful when applying steps meant for a\n    sub-document to a larger document, or vice-versa.\n    */\n    static offset(n) {\n        return n == 0 ? StepMap.empty : new StepMap(n < 0 ? [0, -n, 0] : [0, 0, n]);\n    }\n}\n/**\nA StepMap that contains no changed ranges.\n*/\nStepMap.empty = new StepMap([]);\n/**\nA mapping represents a pipeline of zero or more [step\nmaps](https://prosemirror.net/docs/ref/#transform.StepMap). It has special provisions for losslessly\nhandling mapping positions through a series of steps in which some\nsteps are inverted versions of earlier steps. (This comes up when\n‘[rebasing](/docs/guide/#transform.rebasing)’ steps for\ncollaboration or history management.)\n*/\nclass Mapping {\n    /**\n    Create a new mapping with the given position maps.\n    */\n    constructor(\n    /**\n    The step maps in this mapping.\n    */\n    maps = [], \n    /**\n    @internal\n    */\n    mirror, \n    /**\n    The starting position in the `maps` array, used when `map` or\n    `mapResult` is called.\n    */\n    from = 0, \n    /**\n    The end position in the `maps` array.\n    */\n    to = maps.length) {\n        this.maps = maps;\n        this.mirror = mirror;\n        this.from = from;\n        this.to = to;\n    }\n    /**\n    Create a mapping that maps only through a part of this one.\n    */\n    slice(from = 0, to = this.maps.length) {\n        return new Mapping(this.maps, this.mirror, from, to);\n    }\n    /**\n    @internal\n    */\n    copy() {\n        return new Mapping(this.maps.slice(), this.mirror && this.mirror.slice(), this.from, this.to);\n    }\n    /**\n    Add a step map to the end of this mapping. If `mirrors` is\n    given, it should be the index of the step map that is the mirror\n    image of this one.\n    */\n    appendMap(map, mirrors) {\n        this.to = this.maps.push(map);\n        if (mirrors != null)\n            this.setMirror(this.maps.length - 1, mirrors);\n    }\n    /**\n    Add all the step maps in a given mapping to this one (preserving\n    mirroring information).\n    */\n    appendMapping(mapping) {\n        for (let i = 0, startSize = this.maps.length; i < mapping.maps.length; i++) {\n            let mirr = mapping.getMirror(i);\n            this.appendMap(mapping.maps[i], mirr != null && mirr < i ? startSize + mirr : undefined);\n        }\n    }\n    /**\n    Finds the offset of the step map that mirrors the map at the\n    given offset, in this mapping (as per the second argument to\n    `appendMap`).\n    */\n    getMirror(n) {\n        if (this.mirror)\n            for (let i = 0; i < this.mirror.length; i++)\n                if (this.mirror[i] == n)\n                    return this.mirror[i + (i % 2 ? -1 : 1)];\n    }\n    /**\n    @internal\n    */\n    setMirror(n, m) {\n        if (!this.mirror)\n            this.mirror = [];\n        this.mirror.push(n, m);\n    }\n    /**\n    Append the inverse of the given mapping to this one.\n    */\n    appendMappingInverted(mapping) {\n        for (let i = mapping.maps.length - 1, totalSize = this.maps.length + mapping.maps.length; i >= 0; i--) {\n            let mirr = mapping.getMirror(i);\n            this.appendMap(mapping.maps[i].invert(), mirr != null && mirr > i ? totalSize - mirr - 1 : undefined);\n        }\n    }\n    /**\n    Create an inverted version of this mapping.\n    */\n    invert() {\n        let inverse = new Mapping;\n        inverse.appendMappingInverted(this);\n        return inverse;\n    }\n    /**\n    Map a position through this mapping.\n    */\n    map(pos, assoc = 1) {\n        if (this.mirror)\n            return this._map(pos, assoc, true);\n        for (let i = this.from; i < this.to; i++)\n            pos = this.maps[i].map(pos, assoc);\n        return pos;\n    }\n    /**\n    Map a position through this mapping, returning a mapping\n    result.\n    */\n    mapResult(pos, assoc = 1) { return this._map(pos, assoc, false); }\n    /**\n    @internal\n    */\n    _map(pos, assoc, simple) {\n        let delInfo = 0;\n        for (let i = this.from; i < this.to; i++) {\n            let map = this.maps[i], result = map.mapResult(pos, assoc);\n            if (result.recover != null) {\n                let corr = this.getMirror(i);\n                if (corr != null && corr > i && corr < this.to) {\n                    i = corr;\n                    pos = this.maps[corr].recover(result.recover);\n                    continue;\n                }\n            }\n            delInfo |= result.delInfo;\n            pos = result.pos;\n        }\n        return simple ? pos : new MapResult(pos, delInfo, null);\n    }\n}\n\nconst stepsByID = Object.create(null);\n/**\nA step object represents an atomic change. It generally applies\nonly to the document it was created for, since the positions\nstored in it will only make sense for that document.\n\nNew steps are defined by creating classes that extend `Step`,\noverriding the `apply`, `invert`, `map`, `getMap` and `fromJSON`\nmethods, and registering your class with a unique\nJSON-serialization identifier using\n[`Step.jsonID`](https://prosemirror.net/docs/ref/#transform.Step^jsonID).\n*/\nclass Step {\n    /**\n    Get the step map that represents the changes made by this step,\n    and which can be used to transform between positions in the old\n    and the new document.\n    */\n    getMap() { return StepMap.empty; }\n    /**\n    Try to merge this step with another one, to be applied directly\n    after it. Returns the merged step when possible, null if the\n    steps can't be merged.\n    */\n    merge(other) { return null; }\n    /**\n    Deserialize a step from its JSON representation. Will call\n    through to the step class' own implementation of this method.\n    */\n    static fromJSON(schema, json) {\n        if (!json || !json.stepType)\n            throw new RangeError(\"Invalid input for Step.fromJSON\");\n        let type = stepsByID[json.stepType];\n        if (!type)\n            throw new RangeError(`No step type ${json.stepType} defined`);\n        return type.fromJSON(schema, json);\n    }\n    /**\n    To be able to serialize steps to JSON, each step needs a string\n    ID to attach to its JSON representation. Use this method to\n    register an ID for your step classes. Try to pick something\n    that's unlikely to clash with steps from other modules.\n    */\n    static jsonID(id, stepClass) {\n        if (id in stepsByID)\n            throw new RangeError(\"Duplicate use of step JSON ID \" + id);\n        stepsByID[id] = stepClass;\n        stepClass.prototype.jsonID = id;\n        return stepClass;\n    }\n}\n/**\nThe result of [applying](https://prosemirror.net/docs/ref/#transform.Step.apply) a step. Contains either a\nnew document or a failure value.\n*/\nclass StepResult {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    The transformed document, if successful.\n    */\n    doc, \n    /**\n    The failure message, if unsuccessful.\n    */\n    failed) {\n        this.doc = doc;\n        this.failed = failed;\n    }\n    /**\n    Create a successful step result.\n    */\n    static ok(doc) { return new StepResult(doc, null); }\n    /**\n    Create a failed step result.\n    */\n    static fail(message) { return new StepResult(null, message); }\n    /**\n    Call [`Node.replace`](https://prosemirror.net/docs/ref/#model.Node.replace) with the given\n    arguments. Create a successful result if it succeeds, and a\n    failed one if it throws a `ReplaceError`.\n    */\n    static fromReplace(doc, from, to, slice) {\n        try {\n            return StepResult.ok(doc.replace(from, to, slice));\n        }\n        catch (e) {\n            if (e instanceof prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.ReplaceError)\n                return StepResult.fail(e.message);\n            throw e;\n        }\n    }\n}\n\nfunction mapFragment(fragment, f, parent) {\n    let mapped = [];\n    for (let i = 0; i < fragment.childCount; i++) {\n        let child = fragment.child(i);\n        if (child.content.size)\n            child = child.copy(mapFragment(child.content, f, child));\n        if (child.isInline)\n            child = f(child, parent, i);\n        mapped.push(child);\n    }\n    return prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.fromArray(mapped);\n}\n/**\nAdd a mark to all inline content between two positions.\n*/\nclass AddMarkStep extends Step {\n    /**\n    Create a mark step.\n    */\n    constructor(\n    /**\n    The start of the marked range.\n    */\n    from, \n    /**\n    The end of the marked range.\n    */\n    to, \n    /**\n    The mark to add.\n    */\n    mark) {\n        super();\n        this.from = from;\n        this.to = to;\n        this.mark = mark;\n    }\n    apply(doc) {\n        let oldSlice = doc.slice(this.from, this.to), $from = doc.resolve(this.from);\n        let parent = $from.node($from.sharedDepth(this.to));\n        let slice = new prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice(mapFragment(oldSlice.content, (node, parent) => {\n            if (!node.isAtom || !parent.type.allowsMarkType(this.mark.type))\n                return node;\n            return node.mark(this.mark.addToSet(node.marks));\n        }, parent), oldSlice.openStart, oldSlice.openEnd);\n        return StepResult.fromReplace(doc, this.from, this.to, slice);\n    }\n    invert() {\n        return new RemoveMarkStep(this.from, this.to, this.mark);\n    }\n    map(mapping) {\n        let from = mapping.mapResult(this.from, 1), to = mapping.mapResult(this.to, -1);\n        if (from.deleted && to.deleted || from.pos >= to.pos)\n            return null;\n        return new AddMarkStep(from.pos, to.pos, this.mark);\n    }\n    merge(other) {\n        if (other instanceof AddMarkStep &&\n            other.mark.eq(this.mark) &&\n            this.from <= other.to && this.to >= other.from)\n            return new AddMarkStep(Math.min(this.from, other.from), Math.max(this.to, other.to), this.mark);\n        return null;\n    }\n    toJSON() {\n        return { stepType: \"addMark\", mark: this.mark.toJSON(),\n            from: this.from, to: this.to };\n    }\n    /**\n    @internal\n    */\n    static fromJSON(schema, json) {\n        if (typeof json.from != \"number\" || typeof json.to != \"number\")\n            throw new RangeError(\"Invalid input for AddMarkStep.fromJSON\");\n        return new AddMarkStep(json.from, json.to, schema.markFromJSON(json.mark));\n    }\n}\nStep.jsonID(\"addMark\", AddMarkStep);\n/**\nRemove a mark from all inline content between two positions.\n*/\nclass RemoveMarkStep extends Step {\n    /**\n    Create a mark-removing step.\n    */\n    constructor(\n    /**\n    The start of the unmarked range.\n    */\n    from, \n    /**\n    The end of the unmarked range.\n    */\n    to, \n    /**\n    The mark to remove.\n    */\n    mark) {\n        super();\n        this.from = from;\n        this.to = to;\n        this.mark = mark;\n    }\n    apply(doc) {\n        let oldSlice = doc.slice(this.from, this.to);\n        let slice = new prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice(mapFragment(oldSlice.content, node => {\n            return node.mark(this.mark.removeFromSet(node.marks));\n        }, doc), oldSlice.openStart, oldSlice.openEnd);\n        return StepResult.fromReplace(doc, this.from, this.to, slice);\n    }\n    invert() {\n        return new AddMarkStep(this.from, this.to, this.mark);\n    }\n    map(mapping) {\n        let from = mapping.mapResult(this.from, 1), to = mapping.mapResult(this.to, -1);\n        if (from.deleted && to.deleted || from.pos >= to.pos)\n            return null;\n        return new RemoveMarkStep(from.pos, to.pos, this.mark);\n    }\n    merge(other) {\n        if (other instanceof RemoveMarkStep &&\n            other.mark.eq(this.mark) &&\n            this.from <= other.to && this.to >= other.from)\n            return new RemoveMarkStep(Math.min(this.from, other.from), Math.max(this.to, other.to), this.mark);\n        return null;\n    }\n    toJSON() {\n        return { stepType: \"removeMark\", mark: this.mark.toJSON(),\n            from: this.from, to: this.to };\n    }\n    /**\n    @internal\n    */\n    static fromJSON(schema, json) {\n        if (typeof json.from != \"number\" || typeof json.to != \"number\")\n            throw new RangeError(\"Invalid input for RemoveMarkStep.fromJSON\");\n        return new RemoveMarkStep(json.from, json.to, schema.markFromJSON(json.mark));\n    }\n}\nStep.jsonID(\"removeMark\", RemoveMarkStep);\n/**\nAdd a mark to a specific node.\n*/\nclass AddNodeMarkStep extends Step {\n    /**\n    Create a node mark step.\n    */\n    constructor(\n    /**\n    The position of the target node.\n    */\n    pos, \n    /**\n    The mark to add.\n    */\n    mark) {\n        super();\n        this.pos = pos;\n        this.mark = mark;\n    }\n    apply(doc) {\n        let node = doc.nodeAt(this.pos);\n        if (!node)\n            return StepResult.fail(\"No node at mark step's position\");\n        let updated = node.type.create(node.attrs, null, this.mark.addToSet(node.marks));\n        return StepResult.fromReplace(doc, this.pos, this.pos + 1, new prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice(prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.from(updated), 0, node.isLeaf ? 0 : 1));\n    }\n    invert(doc) {\n        let node = doc.nodeAt(this.pos);\n        if (node) {\n            let newSet = this.mark.addToSet(node.marks);\n            if (newSet.length == node.marks.length) {\n                for (let i = 0; i < node.marks.length; i++)\n                    if (!node.marks[i].isInSet(newSet))\n                        return new AddNodeMarkStep(this.pos, node.marks[i]);\n                return new AddNodeMarkStep(this.pos, this.mark);\n            }\n        }\n        return new RemoveNodeMarkStep(this.pos, this.mark);\n    }\n    map(mapping) {\n        let pos = mapping.mapResult(this.pos, 1);\n        return pos.deletedAfter ? null : new AddNodeMarkStep(pos.pos, this.mark);\n    }\n    toJSON() {\n        return { stepType: \"addNodeMark\", pos: this.pos, mark: this.mark.toJSON() };\n    }\n    /**\n    @internal\n    */\n    static fromJSON(schema, json) {\n        if (typeof json.pos != \"number\")\n            throw new RangeError(\"Invalid input for AddNodeMarkStep.fromJSON\");\n        return new AddNodeMarkStep(json.pos, schema.markFromJSON(json.mark));\n    }\n}\nStep.jsonID(\"addNodeMark\", AddNodeMarkStep);\n/**\nRemove a mark from a specific node.\n*/\nclass RemoveNodeMarkStep extends Step {\n    /**\n    Create a mark-removing step.\n    */\n    constructor(\n    /**\n    The position of the target node.\n    */\n    pos, \n    /**\n    The mark to remove.\n    */\n    mark) {\n        super();\n        this.pos = pos;\n        this.mark = mark;\n    }\n    apply(doc) {\n        let node = doc.nodeAt(this.pos);\n        if (!node)\n            return StepResult.fail(\"No node at mark step's position\");\n        let updated = node.type.create(node.attrs, null, this.mark.removeFromSet(node.marks));\n        return StepResult.fromReplace(doc, this.pos, this.pos + 1, new prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice(prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.from(updated), 0, node.isLeaf ? 0 : 1));\n    }\n    invert(doc) {\n        let node = doc.nodeAt(this.pos);\n        if (!node || !this.mark.isInSet(node.marks))\n            return this;\n        return new AddNodeMarkStep(this.pos, this.mark);\n    }\n    map(mapping) {\n        let pos = mapping.mapResult(this.pos, 1);\n        return pos.deletedAfter ? null : new RemoveNodeMarkStep(pos.pos, this.mark);\n    }\n    toJSON() {\n        return { stepType: \"removeNodeMark\", pos: this.pos, mark: this.mark.toJSON() };\n    }\n    /**\n    @internal\n    */\n    static fromJSON(schema, json) {\n        if (typeof json.pos != \"number\")\n            throw new RangeError(\"Invalid input for RemoveNodeMarkStep.fromJSON\");\n        return new RemoveNodeMarkStep(json.pos, schema.markFromJSON(json.mark));\n    }\n}\nStep.jsonID(\"removeNodeMark\", RemoveNodeMarkStep);\n\n/**\nReplace a part of the document with a slice of new content.\n*/\nclass ReplaceStep extends Step {\n    /**\n    The given `slice` should fit the 'gap' between `from` and\n    `to`—the depths must line up, and the surrounding nodes must be\n    able to be joined with the open sides of the slice. When\n    `structure` is true, the step will fail if the content between\n    from and to is not just a sequence of closing and then opening\n    tokens (this is to guard against rebased replace steps\n    overwriting something they weren't supposed to).\n    */\n    constructor(\n    /**\n    The start position of the replaced range.\n    */\n    from, \n    /**\n    The end position of the replaced range.\n    */\n    to, \n    /**\n    The slice to insert.\n    */\n    slice, \n    /**\n    @internal\n    */\n    structure = false) {\n        super();\n        this.from = from;\n        this.to = to;\n        this.slice = slice;\n        this.structure = structure;\n    }\n    apply(doc) {\n        if (this.structure && contentBetween(doc, this.from, this.to))\n            return StepResult.fail(\"Structure replace would overwrite content\");\n        return StepResult.fromReplace(doc, this.from, this.to, this.slice);\n    }\n    getMap() {\n        return new StepMap([this.from, this.to - this.from, this.slice.size]);\n    }\n    invert(doc) {\n        return new ReplaceStep(this.from, this.from + this.slice.size, doc.slice(this.from, this.to));\n    }\n    map(mapping) {\n        let from = mapping.mapResult(this.from, 1), to = mapping.mapResult(this.to, -1);\n        if (from.deletedAcross && to.deletedAcross)\n            return null;\n        return new ReplaceStep(from.pos, Math.max(from.pos, to.pos), this.slice);\n    }\n    merge(other) {\n        if (!(other instanceof ReplaceStep) || other.structure || this.structure)\n            return null;\n        if (this.from + this.slice.size == other.from && !this.slice.openEnd && !other.slice.openStart) {\n            let slice = this.slice.size + other.slice.size == 0 ? prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice.empty\n                : new prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice(this.slice.content.append(other.slice.content), this.slice.openStart, other.slice.openEnd);\n            return new ReplaceStep(this.from, this.to + (other.to - other.from), slice, this.structure);\n        }\n        else if (other.to == this.from && !this.slice.openStart && !other.slice.openEnd) {\n            let slice = this.slice.size + other.slice.size == 0 ? prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice.empty\n                : new prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice(other.slice.content.append(this.slice.content), other.slice.openStart, this.slice.openEnd);\n            return new ReplaceStep(other.from, this.to, slice, this.structure);\n        }\n        else {\n            return null;\n        }\n    }\n    toJSON() {\n        let json = { stepType: \"replace\", from: this.from, to: this.to };\n        if (this.slice.size)\n            json.slice = this.slice.toJSON();\n        if (this.structure)\n            json.structure = true;\n        return json;\n    }\n    /**\n    @internal\n    */\n    static fromJSON(schema, json) {\n        if (typeof json.from != \"number\" || typeof json.to != \"number\")\n            throw new RangeError(\"Invalid input for ReplaceStep.fromJSON\");\n        return new ReplaceStep(json.from, json.to, prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice.fromJSON(schema, json.slice), !!json.structure);\n    }\n}\nStep.jsonID(\"replace\", ReplaceStep);\n/**\nReplace a part of the document with a slice of content, but\npreserve a range of the replaced content by moving it into the\nslice.\n*/\nclass ReplaceAroundStep extends Step {\n    /**\n    Create a replace-around step with the given range and gap.\n    `insert` should be the point in the slice into which the content\n    of the gap should be moved. `structure` has the same meaning as\n    it has in the [`ReplaceStep`](https://prosemirror.net/docs/ref/#transform.ReplaceStep) class.\n    */\n    constructor(\n    /**\n    The start position of the replaced range.\n    */\n    from, \n    /**\n    The end position of the replaced range.\n    */\n    to, \n    /**\n    The start of preserved range.\n    */\n    gapFrom, \n    /**\n    The end of preserved range.\n    */\n    gapTo, \n    /**\n    The slice to insert.\n    */\n    slice, \n    /**\n    The position in the slice where the preserved range should be\n    inserted.\n    */\n    insert, \n    /**\n    @internal\n    */\n    structure = false) {\n        super();\n        this.from = from;\n        this.to = to;\n        this.gapFrom = gapFrom;\n        this.gapTo = gapTo;\n        this.slice = slice;\n        this.insert = insert;\n        this.structure = structure;\n    }\n    apply(doc) {\n        if (this.structure && (contentBetween(doc, this.from, this.gapFrom) ||\n            contentBetween(doc, this.gapTo, this.to)))\n            return StepResult.fail(\"Structure gap-replace would overwrite content\");\n        let gap = doc.slice(this.gapFrom, this.gapTo);\n        if (gap.openStart || gap.openEnd)\n            return StepResult.fail(\"Gap is not a flat range\");\n        let inserted = this.slice.insertAt(this.insert, gap.content);\n        if (!inserted)\n            return StepResult.fail(\"Content does not fit in gap\");\n        return StepResult.fromReplace(doc, this.from, this.to, inserted);\n    }\n    getMap() {\n        return new StepMap([this.from, this.gapFrom - this.from, this.insert,\n            this.gapTo, this.to - this.gapTo, this.slice.size - this.insert]);\n    }\n    invert(doc) {\n        let gap = this.gapTo - this.gapFrom;\n        return new ReplaceAroundStep(this.from, this.from + this.slice.size + gap, this.from + this.insert, this.from + this.insert + gap, doc.slice(this.from, this.to).removeBetween(this.gapFrom - this.from, this.gapTo - this.from), this.gapFrom - this.from, this.structure);\n    }\n    map(mapping) {\n        let from = mapping.mapResult(this.from, 1), to = mapping.mapResult(this.to, -1);\n        let gapFrom = this.from == this.gapFrom ? from.pos : mapping.map(this.gapFrom, -1);\n        let gapTo = this.to == this.gapTo ? to.pos : mapping.map(this.gapTo, 1);\n        if ((from.deletedAcross && to.deletedAcross) || gapFrom < from.pos || gapTo > to.pos)\n            return null;\n        return new ReplaceAroundStep(from.pos, to.pos, gapFrom, gapTo, this.slice, this.insert, this.structure);\n    }\n    toJSON() {\n        let json = { stepType: \"replaceAround\", from: this.from, to: this.to,\n            gapFrom: this.gapFrom, gapTo: this.gapTo, insert: this.insert };\n        if (this.slice.size)\n            json.slice = this.slice.toJSON();\n        if (this.structure)\n            json.structure = true;\n        return json;\n    }\n    /**\n    @internal\n    */\n    static fromJSON(schema, json) {\n        if (typeof json.from != \"number\" || typeof json.to != \"number\" ||\n            typeof json.gapFrom != \"number\" || typeof json.gapTo != \"number\" || typeof json.insert != \"number\")\n            throw new RangeError(\"Invalid input for ReplaceAroundStep.fromJSON\");\n        return new ReplaceAroundStep(json.from, json.to, json.gapFrom, json.gapTo, prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice.fromJSON(schema, json.slice), json.insert, !!json.structure);\n    }\n}\nStep.jsonID(\"replaceAround\", ReplaceAroundStep);\nfunction contentBetween(doc, from, to) {\n    let $from = doc.resolve(from), dist = to - from, depth = $from.depth;\n    while (dist > 0 && depth > 0 && $from.indexAfter(depth) == $from.node(depth).childCount) {\n        depth--;\n        dist--;\n    }\n    if (dist > 0) {\n        let next = $from.node(depth).maybeChild($from.indexAfter(depth));\n        while (dist > 0) {\n            if (!next || next.isLeaf)\n                return true;\n            next = next.firstChild;\n            dist--;\n        }\n    }\n    return false;\n}\n\nfunction addMark(tr, from, to, mark) {\n    let removed = [], added = [];\n    let removing, adding;\n    tr.doc.nodesBetween(from, to, (node, pos, parent) => {\n        if (!node.isInline)\n            return;\n        let marks = node.marks;\n        if (!mark.isInSet(marks) && parent.type.allowsMarkType(mark.type)) {\n            let start = Math.max(pos, from), end = Math.min(pos + node.nodeSize, to);\n            let newSet = mark.addToSet(marks);\n            for (let i = 0; i < marks.length; i++) {\n                if (!marks[i].isInSet(newSet)) {\n                    if (removing && removing.to == start && removing.mark.eq(marks[i]))\n                        removing.to = end;\n                    else\n                        removed.push(removing = new RemoveMarkStep(start, end, marks[i]));\n                }\n            }\n            if (adding && adding.to == start)\n                adding.to = end;\n            else\n                added.push(adding = new AddMarkStep(start, end, mark));\n        }\n    });\n    removed.forEach(s => tr.step(s));\n    added.forEach(s => tr.step(s));\n}\nfunction removeMark(tr, from, to, mark) {\n    let matched = [], step = 0;\n    tr.doc.nodesBetween(from, to, (node, pos) => {\n        if (!node.isInline)\n            return;\n        step++;\n        let toRemove = null;\n        if (mark instanceof prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.MarkType) {\n            let set = node.marks, found;\n            while (found = mark.isInSet(set)) {\n                (toRemove || (toRemove = [])).push(found);\n                set = found.removeFromSet(set);\n            }\n        }\n        else if (mark) {\n            if (mark.isInSet(node.marks))\n                toRemove = [mark];\n        }\n        else {\n            toRemove = node.marks;\n        }\n        if (toRemove && toRemove.length) {\n            let end = Math.min(pos + node.nodeSize, to);\n            for (let i = 0; i < toRemove.length; i++) {\n                let style = toRemove[i], found;\n                for (let j = 0; j < matched.length; j++) {\n                    let m = matched[j];\n                    if (m.step == step - 1 && style.eq(matched[j].style))\n                        found = m;\n                }\n                if (found) {\n                    found.to = end;\n                    found.step = step;\n                }\n                else {\n                    matched.push({ style, from: Math.max(pos, from), to: end, step });\n                }\n            }\n        }\n    });\n    matched.forEach(m => tr.step(new RemoveMarkStep(m.from, m.to, m.style)));\n}\nfunction clearIncompatible(tr, pos, parentType, match = parentType.contentMatch, clearNewlines = true) {\n    let node = tr.doc.nodeAt(pos);\n    let replSteps = [], cur = pos + 1;\n    for (let i = 0; i < node.childCount; i++) {\n        let child = node.child(i), end = cur + child.nodeSize;\n        let allowed = match.matchType(child.type);\n        if (!allowed) {\n            replSteps.push(new ReplaceStep(cur, end, prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice.empty));\n        }\n        else {\n            match = allowed;\n            for (let j = 0; j < child.marks.length; j++)\n                if (!parentType.allowsMarkType(child.marks[j].type))\n                    tr.step(new RemoveMarkStep(cur, end, child.marks[j]));\n            if (clearNewlines && child.isText && parentType.whitespace != \"pre\") {\n                let m, newline = /\\r?\\n|\\r/g, slice;\n                while (m = newline.exec(child.text)) {\n                    if (!slice)\n                        slice = new prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice(prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.from(parentType.schema.text(\" \", parentType.allowedMarks(child.marks))), 0, 0);\n                    replSteps.push(new ReplaceStep(cur + m.index, cur + m.index + m[0].length, slice));\n                }\n            }\n        }\n        cur = end;\n    }\n    if (!match.validEnd) {\n        let fill = match.fillBefore(prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.empty, true);\n        tr.replace(cur, cur, new prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice(fill, 0, 0));\n    }\n    for (let i = replSteps.length - 1; i >= 0; i--)\n        tr.step(replSteps[i]);\n}\n\nfunction canCut(node, start, end) {\n    return (start == 0 || node.canReplace(start, node.childCount)) &&\n        (end == node.childCount || node.canReplace(0, end));\n}\n/**\nTry to find a target depth to which the content in the given range\ncan be lifted. Will not go across\n[isolating](https://prosemirror.net/docs/ref/#model.NodeSpec.isolating) parent nodes.\n*/\nfunction liftTarget(range) {\n    let parent = range.parent;\n    let content = parent.content.cutByIndex(range.startIndex, range.endIndex);\n    for (let depth = range.depth;; --depth) {\n        let node = range.$from.node(depth);\n        let index = range.$from.index(depth), endIndex = range.$to.indexAfter(depth);\n        if (depth < range.depth && node.canReplace(index, endIndex, content))\n            return depth;\n        if (depth == 0 || node.type.spec.isolating || !canCut(node, index, endIndex))\n            break;\n    }\n    return null;\n}\nfunction lift(tr, range, target) {\n    let { $from, $to, depth } = range;\n    let gapStart = $from.before(depth + 1), gapEnd = $to.after(depth + 1);\n    let start = gapStart, end = gapEnd;\n    let before = prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.empty, openStart = 0;\n    for (let d = depth, splitting = false; d > target; d--)\n        if (splitting || $from.index(d) > 0) {\n            splitting = true;\n            before = prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.from($from.node(d).copy(before));\n            openStart++;\n        }\n        else {\n            start--;\n        }\n    let after = prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.empty, openEnd = 0;\n    for (let d = depth, splitting = false; d > target; d--)\n        if (splitting || $to.after(d + 1) < $to.end(d)) {\n            splitting = true;\n            after = prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.from($to.node(d).copy(after));\n            openEnd++;\n        }\n        else {\n            end++;\n        }\n    tr.step(new ReplaceAroundStep(start, end, gapStart, gapEnd, new prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice(before.append(after), openStart, openEnd), before.size - openStart, true));\n}\n/**\nTry to find a valid way to wrap the content in the given range in a\nnode of the given type. May introduce extra nodes around and inside\nthe wrapper node, if necessary. Returns null if no valid wrapping\ncould be found. When `innerRange` is given, that range's content is\nused as the content to fit into the wrapping, instead of the\ncontent of `range`.\n*/\nfunction findWrapping(range, nodeType, attrs = null, innerRange = range) {\n    let around = findWrappingOutside(range, nodeType);\n    let inner = around && findWrappingInside(innerRange, nodeType);\n    if (!inner)\n        return null;\n    return around.map(withAttrs)\n        .concat({ type: nodeType, attrs }).concat(inner.map(withAttrs));\n}\nfunction withAttrs(type) { return { type, attrs: null }; }\nfunction findWrappingOutside(range, type) {\n    let { parent, startIndex, endIndex } = range;\n    let around = parent.contentMatchAt(startIndex).findWrapping(type);\n    if (!around)\n        return null;\n    let outer = around.length ? around[0] : type;\n    return parent.canReplaceWith(startIndex, endIndex, outer) ? around : null;\n}\nfunction findWrappingInside(range, type) {\n    let { parent, startIndex, endIndex } = range;\n    let inner = parent.child(startIndex);\n    let inside = type.contentMatch.findWrapping(inner.type);\n    if (!inside)\n        return null;\n    let lastType = inside.length ? inside[inside.length - 1] : type;\n    let innerMatch = lastType.contentMatch;\n    for (let i = startIndex; innerMatch && i < endIndex; i++)\n        innerMatch = innerMatch.matchType(parent.child(i).type);\n    if (!innerMatch || !innerMatch.validEnd)\n        return null;\n    return inside;\n}\nfunction wrap(tr, range, wrappers) {\n    let content = prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.empty;\n    for (let i = wrappers.length - 1; i >= 0; i--) {\n        if (content.size) {\n            let match = wrappers[i].type.contentMatch.matchFragment(content);\n            if (!match || !match.validEnd)\n                throw new RangeError(\"Wrapper type given to Transform.wrap does not form valid content of its parent wrapper\");\n        }\n        content = prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.from(wrappers[i].type.create(wrappers[i].attrs, content));\n    }\n    let start = range.start, end = range.end;\n    tr.step(new ReplaceAroundStep(start, end, start, end, new prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice(content, 0, 0), wrappers.length, true));\n}\nfunction setBlockType(tr, from, to, type, attrs) {\n    if (!type.isTextblock)\n        throw new RangeError(\"Type given to setBlockType should be a textblock\");\n    let mapFrom = tr.steps.length;\n    tr.doc.nodesBetween(from, to, (node, pos) => {\n        let attrsHere = typeof attrs == \"function\" ? attrs(node) : attrs;\n        if (node.isTextblock && !node.hasMarkup(type, attrsHere) &&\n            canChangeType(tr.doc, tr.mapping.slice(mapFrom).map(pos), type)) {\n            let convertNewlines = null;\n            if (type.schema.linebreakReplacement) {\n                let pre = type.whitespace == \"pre\", supportLinebreak = !!type.contentMatch.matchType(type.schema.linebreakReplacement);\n                if (pre && !supportLinebreak)\n                    convertNewlines = false;\n                else if (!pre && supportLinebreak)\n                    convertNewlines = true;\n            }\n            // Ensure all markup that isn't allowed in the new node type is cleared\n            if (convertNewlines === false)\n                replaceLinebreaks(tr, node, pos, mapFrom);\n            clearIncompatible(tr, tr.mapping.slice(mapFrom).map(pos, 1), type, undefined, convertNewlines === null);\n            let mapping = tr.mapping.slice(mapFrom);\n            let startM = mapping.map(pos, 1), endM = mapping.map(pos + node.nodeSize, 1);\n            tr.step(new ReplaceAroundStep(startM, endM, startM + 1, endM - 1, new prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice(prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.from(type.create(attrsHere, null, node.marks)), 0, 0), 1, true));\n            if (convertNewlines === true)\n                replaceNewlines(tr, node, pos, mapFrom);\n            return false;\n        }\n    });\n}\nfunction replaceNewlines(tr, node, pos, mapFrom) {\n    node.forEach((child, offset) => {\n        if (child.isText) {\n            let m, newline = /\\r?\\n|\\r/g;\n            while (m = newline.exec(child.text)) {\n                let start = tr.mapping.slice(mapFrom).map(pos + 1 + offset + m.index);\n                tr.replaceWith(start, start + 1, node.type.schema.linebreakReplacement.create());\n            }\n        }\n    });\n}\nfunction replaceLinebreaks(tr, node, pos, mapFrom) {\n    node.forEach((child, offset) => {\n        if (child.type == child.type.schema.linebreakReplacement) {\n            let start = tr.mapping.slice(mapFrom).map(pos + 1 + offset);\n            tr.replaceWith(start, start + 1, node.type.schema.text(\"\\n\"));\n        }\n    });\n}\nfunction canChangeType(doc, pos, type) {\n    let $pos = doc.resolve(pos), index = $pos.index();\n    return $pos.parent.canReplaceWith(index, index + 1, type);\n}\n/**\nChange the type, attributes, and/or marks of the node at `pos`.\nWhen `type` isn't given, the existing node type is preserved,\n*/\nfunction setNodeMarkup(tr, pos, type, attrs, marks) {\n    let node = tr.doc.nodeAt(pos);\n    if (!node)\n        throw new RangeError(\"No node at given position\");\n    if (!type)\n        type = node.type;\n    let newNode = type.create(attrs, null, marks || node.marks);\n    if (node.isLeaf)\n        return tr.replaceWith(pos, pos + node.nodeSize, newNode);\n    if (!type.validContent(node.content))\n        throw new RangeError(\"Invalid content for node type \" + type.name);\n    tr.step(new ReplaceAroundStep(pos, pos + node.nodeSize, pos + 1, pos + node.nodeSize - 1, new prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice(prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.from(newNode), 0, 0), 1, true));\n}\n/**\nCheck whether splitting at the given position is allowed.\n*/\nfunction canSplit(doc, pos, depth = 1, typesAfter) {\n    let $pos = doc.resolve(pos), base = $pos.depth - depth;\n    let innerType = (typesAfter && typesAfter[typesAfter.length - 1]) || $pos.parent;\n    if (base < 0 || $pos.parent.type.spec.isolating ||\n        !$pos.parent.canReplace($pos.index(), $pos.parent.childCount) ||\n        !innerType.type.validContent($pos.parent.content.cutByIndex($pos.index(), $pos.parent.childCount)))\n        return false;\n    for (let d = $pos.depth - 1, i = depth - 2; d > base; d--, i--) {\n        let node = $pos.node(d), index = $pos.index(d);\n        if (node.type.spec.isolating)\n            return false;\n        let rest = node.content.cutByIndex(index, node.childCount);\n        let overrideChild = typesAfter && typesAfter[i + 1];\n        if (overrideChild)\n            rest = rest.replaceChild(0, overrideChild.type.create(overrideChild.attrs));\n        let after = (typesAfter && typesAfter[i]) || node;\n        if (!node.canReplace(index + 1, node.childCount) || !after.type.validContent(rest))\n            return false;\n    }\n    let index = $pos.indexAfter(base);\n    let baseType = typesAfter && typesAfter[0];\n    return $pos.node(base).canReplaceWith(index, index, baseType ? baseType.type : $pos.node(base + 1).type);\n}\nfunction split(tr, pos, depth = 1, typesAfter) {\n    let $pos = tr.doc.resolve(pos), before = prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.empty, after = prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.empty;\n    for (let d = $pos.depth, e = $pos.depth - depth, i = depth - 1; d > e; d--, i--) {\n        before = prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.from($pos.node(d).copy(before));\n        let typeAfter = typesAfter && typesAfter[i];\n        after = prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.from(typeAfter ? typeAfter.type.create(typeAfter.attrs, after) : $pos.node(d).copy(after));\n    }\n    tr.step(new ReplaceStep(pos, pos, new prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice(before.append(after), depth, depth), true));\n}\n/**\nTest whether the blocks before and after a given position can be\njoined.\n*/\nfunction canJoin(doc, pos) {\n    let $pos = doc.resolve(pos), index = $pos.index();\n    return joinable($pos.nodeBefore, $pos.nodeAfter) &&\n        $pos.parent.canReplace(index, index + 1);\n}\nfunction canAppendWithSubstitutedLinebreaks(a, b) {\n    if (!b.content.size)\n        a.type.compatibleContent(b.type);\n    let match = a.contentMatchAt(a.childCount);\n    let { linebreakReplacement } = a.type.schema;\n    for (let i = 0; i < b.childCount; i++) {\n        let child = b.child(i);\n        let type = child.type == linebreakReplacement ? a.type.schema.nodes.text : child.type;\n        match = match.matchType(type);\n        if (!match)\n            return false;\n        if (!a.type.allowsMarks(child.marks))\n            return false;\n    }\n    return match.validEnd;\n}\nfunction joinable(a, b) {\n    return !!(a && b && !a.isLeaf && canAppendWithSubstitutedLinebreaks(a, b));\n}\n/**\nFind an ancestor of the given position that can be joined to the\nblock before (or after if `dir` is positive). Returns the joinable\npoint, if any.\n*/\nfunction joinPoint(doc, pos, dir = -1) {\n    let $pos = doc.resolve(pos);\n    for (let d = $pos.depth;; d--) {\n        let before, after, index = $pos.index(d);\n        if (d == $pos.depth) {\n            before = $pos.nodeBefore;\n            after = $pos.nodeAfter;\n        }\n        else if (dir > 0) {\n            before = $pos.node(d + 1);\n            index++;\n            after = $pos.node(d).maybeChild(index);\n        }\n        else {\n            before = $pos.node(d).maybeChild(index - 1);\n            after = $pos.node(d + 1);\n        }\n        if (before && !before.isTextblock && joinable(before, after) &&\n            $pos.node(d).canReplace(index, index + 1))\n            return pos;\n        if (d == 0)\n            break;\n        pos = dir < 0 ? $pos.before(d) : $pos.after(d);\n    }\n}\nfunction join(tr, pos, depth) {\n    let convertNewlines = null;\n    let { linebreakReplacement } = tr.doc.type.schema;\n    let $before = tr.doc.resolve(pos - depth), beforeType = $before.node().type;\n    if (linebreakReplacement && beforeType.inlineContent) {\n        let pre = beforeType.whitespace == \"pre\";\n        let supportLinebreak = !!beforeType.contentMatch.matchType(linebreakReplacement);\n        if (pre && !supportLinebreak)\n            convertNewlines = false;\n        else if (!pre && supportLinebreak)\n            convertNewlines = true;\n    }\n    let mapFrom = tr.steps.length;\n    if (convertNewlines === false) {\n        let $after = tr.doc.resolve(pos + depth);\n        replaceLinebreaks(tr, $after.node(), $after.before(), mapFrom);\n    }\n    if (beforeType.inlineContent)\n        clearIncompatible(tr, pos + depth - 1, beforeType, $before.node().contentMatchAt($before.index()), convertNewlines == null);\n    let mapping = tr.mapping.slice(mapFrom), start = mapping.map(pos - depth);\n    tr.step(new ReplaceStep(start, mapping.map(pos + depth, -1), prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice.empty, true));\n    if (convertNewlines === true) {\n        let $full = tr.doc.resolve(start);\n        replaceNewlines(tr, $full.node(), $full.before(), tr.steps.length);\n    }\n    return tr;\n}\n/**\nTry to find a point where a node of the given type can be inserted\nnear `pos`, by searching up the node hierarchy when `pos` itself\nisn't a valid place but is at the start or end of a node. Return\nnull if no position was found.\n*/\nfunction insertPoint(doc, pos, nodeType) {\n    let $pos = doc.resolve(pos);\n    if ($pos.parent.canReplaceWith($pos.index(), $pos.index(), nodeType))\n        return pos;\n    if ($pos.parentOffset == 0)\n        for (let d = $pos.depth - 1; d >= 0; d--) {\n            let index = $pos.index(d);\n            if ($pos.node(d).canReplaceWith(index, index, nodeType))\n                return $pos.before(d + 1);\n            if (index > 0)\n                return null;\n        }\n    if ($pos.parentOffset == $pos.parent.content.size)\n        for (let d = $pos.depth - 1; d >= 0; d--) {\n            let index = $pos.indexAfter(d);\n            if ($pos.node(d).canReplaceWith(index, index, nodeType))\n                return $pos.after(d + 1);\n            if (index < $pos.node(d).childCount)\n                return null;\n        }\n    return null;\n}\n/**\nFinds a position at or around the given position where the given\nslice can be inserted. Will look at parent nodes' nearest boundary\nand try there, even if the original position wasn't directly at the\nstart or end of that node. Returns null when no position was found.\n*/\nfunction dropPoint(doc, pos, slice) {\n    let $pos = doc.resolve(pos);\n    if (!slice.content.size)\n        return pos;\n    let content = slice.content;\n    for (let i = 0; i < slice.openStart; i++)\n        content = content.firstChild.content;\n    for (let pass = 1; pass <= (slice.openStart == 0 && slice.size ? 2 : 1); pass++) {\n        for (let d = $pos.depth; d >= 0; d--) {\n            let bias = d == $pos.depth ? 0 : $pos.pos <= ($pos.start(d + 1) + $pos.end(d + 1)) / 2 ? -1 : 1;\n            let insertPos = $pos.index(d) + (bias > 0 ? 1 : 0);\n            let parent = $pos.node(d), fits = false;\n            if (pass == 1) {\n                fits = parent.canReplace(insertPos, insertPos, content);\n            }\n            else {\n                let wrapping = parent.contentMatchAt(insertPos).findWrapping(content.firstChild.type);\n                fits = wrapping && parent.canReplaceWith(insertPos, insertPos, wrapping[0]);\n            }\n            if (fits)\n                return bias == 0 ? $pos.pos : bias < 0 ? $pos.before(d + 1) : $pos.after(d + 1);\n        }\n    }\n    return null;\n}\n\n/**\n‘Fit’ a slice into a given position in the document, producing a\n[step](https://prosemirror.net/docs/ref/#transform.Step) that inserts it. Will return null if\nthere's no meaningful way to insert the slice here, or inserting it\nwould be a no-op (an empty slice over an empty range).\n*/\nfunction replaceStep(doc, from, to = from, slice = prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice.empty) {\n    if (from == to && !slice.size)\n        return null;\n    let $from = doc.resolve(from), $to = doc.resolve(to);\n    // Optimization -- avoid work if it's obvious that it's not needed.\n    if (fitsTrivially($from, $to, slice))\n        return new ReplaceStep(from, to, slice);\n    return new Fitter($from, $to, slice).fit();\n}\nfunction fitsTrivially($from, $to, slice) {\n    return !slice.openStart && !slice.openEnd && $from.start() == $to.start() &&\n        $from.parent.canReplace($from.index(), $to.index(), slice.content);\n}\n// Algorithm for 'placing' the elements of a slice into a gap:\n//\n// We consider the content of each node that is open to the left to be\n// independently placeable. I.e. in <p(\"foo\"), p(\"bar\")>, when the\n// paragraph on the left is open, \"foo\" can be placed (somewhere on\n// the left side of the replacement gap) independently from p(\"bar\").\n//\n// This class tracks the state of the placement progress in the\n// following properties:\n//\n//  - `frontier` holds a stack of `{type, match}` objects that\n//    represent the open side of the replacement. It starts at\n//    `$from`, then moves forward as content is placed, and is finally\n//    reconciled with `$to`.\n//\n//  - `unplaced` is a slice that represents the content that hasn't\n//    been placed yet.\n//\n//  - `placed` is a fragment of placed content. Its open-start value\n//    is implicit in `$from`, and its open-end value in `frontier`.\nclass Fitter {\n    constructor($from, $to, unplaced) {\n        this.$from = $from;\n        this.$to = $to;\n        this.unplaced = unplaced;\n        this.frontier = [];\n        this.placed = prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.empty;\n        for (let i = 0; i <= $from.depth; i++) {\n            let node = $from.node(i);\n            this.frontier.push({\n                type: node.type,\n                match: node.contentMatchAt($from.indexAfter(i))\n            });\n        }\n        for (let i = $from.depth; i > 0; i--)\n            this.placed = prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.from($from.node(i).copy(this.placed));\n    }\n    get depth() { return this.frontier.length - 1; }\n    fit() {\n        // As long as there's unplaced content, try to place some of it.\n        // If that fails, either increase the open score of the unplaced\n        // slice, or drop nodes from it, and then try again.\n        while (this.unplaced.size) {\n            let fit = this.findFittable();\n            if (fit)\n                this.placeNodes(fit);\n            else\n                this.openMore() || this.dropNode();\n        }\n        // When there's inline content directly after the frontier _and_\n        // directly after `this.$to`, we must generate a `ReplaceAround`\n        // step that pulls that content into the node after the frontier.\n        // That means the fitting must be done to the end of the textblock\n        // node after `this.$to`, not `this.$to` itself.\n        let moveInline = this.mustMoveInline(), placedSize = this.placed.size - this.depth - this.$from.depth;\n        let $from = this.$from, $to = this.close(moveInline < 0 ? this.$to : $from.doc.resolve(moveInline));\n        if (!$to)\n            return null;\n        // If closing to `$to` succeeded, create a step\n        let content = this.placed, openStart = $from.depth, openEnd = $to.depth;\n        while (openStart && openEnd && content.childCount == 1) { // Normalize by dropping open parent nodes\n            content = content.firstChild.content;\n            openStart--;\n            openEnd--;\n        }\n        let slice = new prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice(content, openStart, openEnd);\n        if (moveInline > -1)\n            return new ReplaceAroundStep($from.pos, moveInline, this.$to.pos, this.$to.end(), slice, placedSize);\n        if (slice.size || $from.pos != this.$to.pos) // Don't generate no-op steps\n            return new ReplaceStep($from.pos, $to.pos, slice);\n        return null;\n    }\n    // Find a position on the start spine of `this.unplaced` that has\n    // content that can be moved somewhere on the frontier. Returns two\n    // depths, one for the slice and one for the frontier.\n    findFittable() {\n        let startDepth = this.unplaced.openStart;\n        for (let cur = this.unplaced.content, d = 0, openEnd = this.unplaced.openEnd; d < startDepth; d++) {\n            let node = cur.firstChild;\n            if (cur.childCount > 1)\n                openEnd = 0;\n            if (node.type.spec.isolating && openEnd <= d) {\n                startDepth = d;\n                break;\n            }\n            cur = node.content;\n        }\n        // Only try wrapping nodes (pass 2) after finding a place without\n        // wrapping failed.\n        for (let pass = 1; pass <= 2; pass++) {\n            for (let sliceDepth = pass == 1 ? startDepth : this.unplaced.openStart; sliceDepth >= 0; sliceDepth--) {\n                let fragment, parent = null;\n                if (sliceDepth) {\n                    parent = contentAt(this.unplaced.content, sliceDepth - 1).firstChild;\n                    fragment = parent.content;\n                }\n                else {\n                    fragment = this.unplaced.content;\n                }\n                let first = fragment.firstChild;\n                for (let frontierDepth = this.depth; frontierDepth >= 0; frontierDepth--) {\n                    let { type, match } = this.frontier[frontierDepth], wrap, inject = null;\n                    // In pass 1, if the next node matches, or there is no next\n                    // node but the parents look compatible, we've found a\n                    // place.\n                    if (pass == 1 && (first ? match.matchType(first.type) || (inject = match.fillBefore(prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.from(first), false))\n                        : parent && type.compatibleContent(parent.type)))\n                        return { sliceDepth, frontierDepth, parent, inject };\n                    // In pass 2, look for a set of wrapping nodes that make\n                    // `first` fit here.\n                    else if (pass == 2 && first && (wrap = match.findWrapping(first.type)))\n                        return { sliceDepth, frontierDepth, parent, wrap };\n                    // Don't continue looking further up if the parent node\n                    // would fit here.\n                    if (parent && match.matchType(parent.type))\n                        break;\n                }\n            }\n        }\n    }\n    openMore() {\n        let { content, openStart, openEnd } = this.unplaced;\n        let inner = contentAt(content, openStart);\n        if (!inner.childCount || inner.firstChild.isLeaf)\n            return false;\n        this.unplaced = new prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice(content, openStart + 1, Math.max(openEnd, inner.size + openStart >= content.size - openEnd ? openStart + 1 : 0));\n        return true;\n    }\n    dropNode() {\n        let { content, openStart, openEnd } = this.unplaced;\n        let inner = contentAt(content, openStart);\n        if (inner.childCount <= 1 && openStart > 0) {\n            let openAtEnd = content.size - openStart <= openStart + inner.size;\n            this.unplaced = new prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice(dropFromFragment(content, openStart - 1, 1), openStart - 1, openAtEnd ? openStart - 1 : openEnd);\n        }\n        else {\n            this.unplaced = new prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice(dropFromFragment(content, openStart, 1), openStart, openEnd);\n        }\n    }\n    // Move content from the unplaced slice at `sliceDepth` to the\n    // frontier node at `frontierDepth`. Close that frontier node when\n    // applicable.\n    placeNodes({ sliceDepth, frontierDepth, parent, inject, wrap }) {\n        while (this.depth > frontierDepth)\n            this.closeFrontierNode();\n        if (wrap)\n            for (let i = 0; i < wrap.length; i++)\n                this.openFrontierNode(wrap[i]);\n        let slice = this.unplaced, fragment = parent ? parent.content : slice.content;\n        let openStart = slice.openStart - sliceDepth;\n        let taken = 0, add = [];\n        let { match, type } = this.frontier[frontierDepth];\n        if (inject) {\n            for (let i = 0; i < inject.childCount; i++)\n                add.push(inject.child(i));\n            match = match.matchFragment(inject);\n        }\n        // Computes the amount of (end) open nodes at the end of the\n        // fragment. When 0, the parent is open, but no more. When\n        // negative, nothing is open.\n        let openEndCount = (fragment.size + sliceDepth) - (slice.content.size - slice.openEnd);\n        // Scan over the fragment, fitting as many child nodes as\n        // possible.\n        while (taken < fragment.childCount) {\n            let next = fragment.child(taken), matches = match.matchType(next.type);\n            if (!matches)\n                break;\n            taken++;\n            if (taken > 1 || openStart == 0 || next.content.size) { // Drop empty open nodes\n                match = matches;\n                add.push(closeNodeStart(next.mark(type.allowedMarks(next.marks)), taken == 1 ? openStart : 0, taken == fragment.childCount ? openEndCount : -1));\n            }\n        }\n        let toEnd = taken == fragment.childCount;\n        if (!toEnd)\n            openEndCount = -1;\n        this.placed = addToFragment(this.placed, frontierDepth, prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.from(add));\n        this.frontier[frontierDepth].match = match;\n        // If the parent types match, and the entire node was moved, and\n        // it's not open, close this frontier node right away.\n        if (toEnd && openEndCount < 0 && parent && parent.type == this.frontier[this.depth].type && this.frontier.length > 1)\n            this.closeFrontierNode();\n        // Add new frontier nodes for any open nodes at the end.\n        for (let i = 0, cur = fragment; i < openEndCount; i++) {\n            let node = cur.lastChild;\n            this.frontier.push({ type: node.type, match: node.contentMatchAt(node.childCount) });\n            cur = node.content;\n        }\n        // Update `this.unplaced`. Drop the entire node from which we\n        // placed it we got to its end, otherwise just drop the placed\n        // nodes.\n        this.unplaced = !toEnd ? new prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice(dropFromFragment(slice.content, sliceDepth, taken), slice.openStart, slice.openEnd)\n            : sliceDepth == 0 ? prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice.empty\n                : new prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice(dropFromFragment(slice.content, sliceDepth - 1, 1), sliceDepth - 1, openEndCount < 0 ? slice.openEnd : sliceDepth - 1);\n    }\n    mustMoveInline() {\n        if (!this.$to.parent.isTextblock)\n            return -1;\n        let top = this.frontier[this.depth], level;\n        if (!top.type.isTextblock || !contentAfterFits(this.$to, this.$to.depth, top.type, top.match, false) ||\n            (this.$to.depth == this.depth && (level = this.findCloseLevel(this.$to)) && level.depth == this.depth))\n            return -1;\n        let { depth } = this.$to, after = this.$to.after(depth);\n        while (depth > 1 && after == this.$to.end(--depth))\n            ++after;\n        return after;\n    }\n    findCloseLevel($to) {\n        scan: for (let i = Math.min(this.depth, $to.depth); i >= 0; i--) {\n            let { match, type } = this.frontier[i];\n            let dropInner = i < $to.depth && $to.end(i + 1) == $to.pos + ($to.depth - (i + 1));\n            let fit = contentAfterFits($to, i, type, match, dropInner);\n            if (!fit)\n                continue;\n            for (let d = i - 1; d >= 0; d--) {\n                let { match, type } = this.frontier[d];\n                let matches = contentAfterFits($to, d, type, match, true);\n                if (!matches || matches.childCount)\n                    continue scan;\n            }\n            return { depth: i, fit, move: dropInner ? $to.doc.resolve($to.after(i + 1)) : $to };\n        }\n    }\n    close($to) {\n        let close = this.findCloseLevel($to);\n        if (!close)\n            return null;\n        while (this.depth > close.depth)\n            this.closeFrontierNode();\n        if (close.fit.childCount)\n            this.placed = addToFragment(this.placed, close.depth, close.fit);\n        $to = close.move;\n        for (let d = close.depth + 1; d <= $to.depth; d++) {\n            let node = $to.node(d), add = node.type.contentMatch.fillBefore(node.content, true, $to.index(d));\n            this.openFrontierNode(node.type, node.attrs, add);\n        }\n        return $to;\n    }\n    openFrontierNode(type, attrs = null, content) {\n        let top = this.frontier[this.depth];\n        top.match = top.match.matchType(type);\n        this.placed = addToFragment(this.placed, this.depth, prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.from(type.create(attrs, content)));\n        this.frontier.push({ type, match: type.contentMatch });\n    }\n    closeFrontierNode() {\n        let open = this.frontier.pop();\n        let add = open.match.fillBefore(prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.empty, true);\n        if (add.childCount)\n            this.placed = addToFragment(this.placed, this.frontier.length, add);\n    }\n}\nfunction dropFromFragment(fragment, depth, count) {\n    if (depth == 0)\n        return fragment.cutByIndex(count, fragment.childCount);\n    return fragment.replaceChild(0, fragment.firstChild.copy(dropFromFragment(fragment.firstChild.content, depth - 1, count)));\n}\nfunction addToFragment(fragment, depth, content) {\n    if (depth == 0)\n        return fragment.append(content);\n    return fragment.replaceChild(fragment.childCount - 1, fragment.lastChild.copy(addToFragment(fragment.lastChild.content, depth - 1, content)));\n}\nfunction contentAt(fragment, depth) {\n    for (let i = 0; i < depth; i++)\n        fragment = fragment.firstChild.content;\n    return fragment;\n}\nfunction closeNodeStart(node, openStart, openEnd) {\n    if (openStart <= 0)\n        return node;\n    let frag = node.content;\n    if (openStart > 1)\n        frag = frag.replaceChild(0, closeNodeStart(frag.firstChild, openStart - 1, frag.childCount == 1 ? openEnd - 1 : 0));\n    if (openStart > 0) {\n        frag = node.type.contentMatch.fillBefore(frag).append(frag);\n        if (openEnd <= 0)\n            frag = frag.append(node.type.contentMatch.matchFragment(frag).fillBefore(prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.empty, true));\n    }\n    return node.copy(frag);\n}\nfunction contentAfterFits($to, depth, type, match, open) {\n    let node = $to.node(depth), index = open ? $to.indexAfter(depth) : $to.index(depth);\n    if (index == node.childCount && !type.compatibleContent(node.type))\n        return null;\n    let fit = match.fillBefore(node.content, true, index);\n    return fit && !invalidMarks(type, node.content, index) ? fit : null;\n}\nfunction invalidMarks(type, fragment, start) {\n    for (let i = start; i < fragment.childCount; i++)\n        if (!type.allowsMarks(fragment.child(i).marks))\n            return true;\n    return false;\n}\nfunction definesContent(type) {\n    return type.spec.defining || type.spec.definingForContent;\n}\nfunction replaceRange(tr, from, to, slice) {\n    if (!slice.size)\n        return tr.deleteRange(from, to);\n    let $from = tr.doc.resolve(from), $to = tr.doc.resolve(to);\n    if (fitsTrivially($from, $to, slice))\n        return tr.step(new ReplaceStep(from, to, slice));\n    let targetDepths = coveredDepths($from, tr.doc.resolve(to));\n    // Can't replace the whole document, so remove 0 if it's present\n    if (targetDepths[targetDepths.length - 1] == 0)\n        targetDepths.pop();\n    // Negative numbers represent not expansion over the whole node at\n    // that depth, but replacing from $from.before(-D) to $to.pos.\n    let preferredTarget = -($from.depth + 1);\n    targetDepths.unshift(preferredTarget);\n    // This loop picks a preferred target depth, if one of the covering\n    // depths is not outside of a defining node, and adds negative\n    // depths for any depth that has $from at its start and does not\n    // cross a defining node.\n    for (let d = $from.depth, pos = $from.pos - 1; d > 0; d--, pos--) {\n        let spec = $from.node(d).type.spec;\n        if (spec.defining || spec.definingAsContext || spec.isolating)\n            break;\n        if (targetDepths.indexOf(d) > -1)\n            preferredTarget = d;\n        else if ($from.before(d) == pos)\n            targetDepths.splice(1, 0, -d);\n    }\n    // Try to fit each possible depth of the slice into each possible\n    // target depth, starting with the preferred depths.\n    let preferredTargetIndex = targetDepths.indexOf(preferredTarget);\n    let leftNodes = [], preferredDepth = slice.openStart;\n    for (let content = slice.content, i = 0;; i++) {\n        let node = content.firstChild;\n        leftNodes.push(node);\n        if (i == slice.openStart)\n            break;\n        content = node.content;\n    }\n    // Back up preferredDepth to cover defining textblocks directly\n    // above it, possibly skipping a non-defining textblock.\n    for (let d = preferredDepth - 1; d >= 0; d--) {\n        let leftNode = leftNodes[d], def = definesContent(leftNode.type);\n        if (def && !leftNode.sameMarkup($from.node(Math.abs(preferredTarget) - 1)))\n            preferredDepth = d;\n        else if (def || !leftNode.type.isTextblock)\n            break;\n    }\n    for (let j = slice.openStart; j >= 0; j--) {\n        let openDepth = (j + preferredDepth + 1) % (slice.openStart + 1);\n        let insert = leftNodes[openDepth];\n        if (!insert)\n            continue;\n        for (let i = 0; i < targetDepths.length; i++) {\n            // Loop over possible expansion levels, starting with the\n            // preferred one\n            let targetDepth = targetDepths[(i + preferredTargetIndex) % targetDepths.length], expand = true;\n            if (targetDepth < 0) {\n                expand = false;\n                targetDepth = -targetDepth;\n            }\n            let parent = $from.node(targetDepth - 1), index = $from.index(targetDepth - 1);\n            if (parent.canReplaceWith(index, index, insert.type, insert.marks))\n                return tr.replace($from.before(targetDepth), expand ? $to.after(targetDepth) : to, new prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice(closeFragment(slice.content, 0, slice.openStart, openDepth), openDepth, slice.openEnd));\n        }\n    }\n    let startSteps = tr.steps.length;\n    for (let i = targetDepths.length - 1; i >= 0; i--) {\n        tr.replace(from, to, slice);\n        if (tr.steps.length > startSteps)\n            break;\n        let depth = targetDepths[i];\n        if (depth < 0)\n            continue;\n        from = $from.before(depth);\n        to = $to.after(depth);\n    }\n}\nfunction closeFragment(fragment, depth, oldOpen, newOpen, parent) {\n    if (depth < oldOpen) {\n        let first = fragment.firstChild;\n        fragment = fragment.replaceChild(0, first.copy(closeFragment(first.content, depth + 1, oldOpen, newOpen, first)));\n    }\n    if (depth > newOpen) {\n        let match = parent.contentMatchAt(0);\n        let start = match.fillBefore(fragment).append(fragment);\n        fragment = start.append(match.matchFragment(start).fillBefore(prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.empty, true));\n    }\n    return fragment;\n}\nfunction replaceRangeWith(tr, from, to, node) {\n    if (!node.isInline && from == to && tr.doc.resolve(from).parent.content.size) {\n        let point = insertPoint(tr.doc, from, node.type);\n        if (point != null)\n            from = to = point;\n    }\n    tr.replaceRange(from, to, new prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice(prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.from(node), 0, 0));\n}\nfunction deleteRange(tr, from, to) {\n    let $from = tr.doc.resolve(from), $to = tr.doc.resolve(to);\n    let covered = coveredDepths($from, $to);\n    for (let i = 0; i < covered.length; i++) {\n        let depth = covered[i], last = i == covered.length - 1;\n        if ((last && depth == 0) || $from.node(depth).type.contentMatch.validEnd)\n            return tr.delete($from.start(depth), $to.end(depth));\n        if (depth > 0 && (last || $from.node(depth - 1).canReplace($from.index(depth - 1), $to.indexAfter(depth - 1))))\n            return tr.delete($from.before(depth), $to.after(depth));\n    }\n    for (let d = 1; d <= $from.depth && d <= $to.depth; d++) {\n        if (from - $from.start(d) == $from.depth - d && to > $from.end(d) && $to.end(d) - to != $to.depth - d &&\n            $from.start(d - 1) == $to.start(d - 1) && $from.node(d - 1).canReplace($from.index(d - 1), $to.index(d - 1)))\n            return tr.delete($from.before(d), to);\n    }\n    tr.delete(from, to);\n}\n// Returns an array of all depths for which $from - $to spans the\n// whole content of the nodes at that depth.\nfunction coveredDepths($from, $to) {\n    let result = [], minDepth = Math.min($from.depth, $to.depth);\n    for (let d = minDepth; d >= 0; d--) {\n        let start = $from.start(d);\n        if (start < $from.pos - ($from.depth - d) ||\n            $to.end(d) > $to.pos + ($to.depth - d) ||\n            $from.node(d).type.spec.isolating ||\n            $to.node(d).type.spec.isolating)\n            break;\n        if (start == $to.start(d) ||\n            (d == $from.depth && d == $to.depth && $from.parent.inlineContent && $to.parent.inlineContent &&\n                d && $to.start(d - 1) == start - 1))\n            result.push(d);\n    }\n    return result;\n}\n\n/**\nUpdate an attribute in a specific node.\n*/\nclass AttrStep extends Step {\n    /**\n    Construct an attribute step.\n    */\n    constructor(\n    /**\n    The position of the target node.\n    */\n    pos, \n    /**\n    The attribute to set.\n    */\n    attr, \n    // The attribute's new value.\n    value) {\n        super();\n        this.pos = pos;\n        this.attr = attr;\n        this.value = value;\n    }\n    apply(doc) {\n        let node = doc.nodeAt(this.pos);\n        if (!node)\n            return StepResult.fail(\"No node at attribute step's position\");\n        let attrs = Object.create(null);\n        for (let name in node.attrs)\n            attrs[name] = node.attrs[name];\n        attrs[this.attr] = this.value;\n        let updated = node.type.create(attrs, null, node.marks);\n        return StepResult.fromReplace(doc, this.pos, this.pos + 1, new prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice(prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.from(updated), 0, node.isLeaf ? 0 : 1));\n    }\n    getMap() {\n        return StepMap.empty;\n    }\n    invert(doc) {\n        return new AttrStep(this.pos, this.attr, doc.nodeAt(this.pos).attrs[this.attr]);\n    }\n    map(mapping) {\n        let pos = mapping.mapResult(this.pos, 1);\n        return pos.deletedAfter ? null : new AttrStep(pos.pos, this.attr, this.value);\n    }\n    toJSON() {\n        return { stepType: \"attr\", pos: this.pos, attr: this.attr, value: this.value };\n    }\n    static fromJSON(schema, json) {\n        if (typeof json.pos != \"number\" || typeof json.attr != \"string\")\n            throw new RangeError(\"Invalid input for AttrStep.fromJSON\");\n        return new AttrStep(json.pos, json.attr, json.value);\n    }\n}\nStep.jsonID(\"attr\", AttrStep);\n/**\nUpdate an attribute in the doc node.\n*/\nclass DocAttrStep extends Step {\n    /**\n    Construct an attribute step.\n    */\n    constructor(\n    /**\n    The attribute to set.\n    */\n    attr, \n    // The attribute's new value.\n    value) {\n        super();\n        this.attr = attr;\n        this.value = value;\n    }\n    apply(doc) {\n        let attrs = Object.create(null);\n        for (let name in doc.attrs)\n            attrs[name] = doc.attrs[name];\n        attrs[this.attr] = this.value;\n        let updated = doc.type.create(attrs, doc.content, doc.marks);\n        return StepResult.ok(updated);\n    }\n    getMap() {\n        return StepMap.empty;\n    }\n    invert(doc) {\n        return new DocAttrStep(this.attr, doc.attrs[this.attr]);\n    }\n    map(mapping) {\n        return this;\n    }\n    toJSON() {\n        return { stepType: \"docAttr\", attr: this.attr, value: this.value };\n    }\n    static fromJSON(schema, json) {\n        if (typeof json.attr != \"string\")\n            throw new RangeError(\"Invalid input for DocAttrStep.fromJSON\");\n        return new DocAttrStep(json.attr, json.value);\n    }\n}\nStep.jsonID(\"docAttr\", DocAttrStep);\n\n/**\n@internal\n*/\nlet TransformError = class extends Error {\n};\nTransformError = function TransformError(message) {\n    let err = Error.call(this, message);\n    err.__proto__ = TransformError.prototype;\n    return err;\n};\nTransformError.prototype = Object.create(Error.prototype);\nTransformError.prototype.constructor = TransformError;\nTransformError.prototype.name = \"TransformError\";\n/**\nAbstraction to build up and track an array of\n[steps](https://prosemirror.net/docs/ref/#transform.Step) representing a document transformation.\n\nMost transforming methods return the `Transform` object itself, so\nthat they can be chained.\n*/\nclass Transform {\n    /**\n    Create a transform that starts with the given document.\n    */\n    constructor(\n    /**\n    The current document (the result of applying the steps in the\n    transform).\n    */\n    doc) {\n        this.doc = doc;\n        /**\n        The steps in this transform.\n        */\n        this.steps = [];\n        /**\n        The documents before each of the steps.\n        */\n        this.docs = [];\n        /**\n        A mapping with the maps for each of the steps in this transform.\n        */\n        this.mapping = new Mapping;\n    }\n    /**\n    The starting document.\n    */\n    get before() { return this.docs.length ? this.docs[0] : this.doc; }\n    /**\n    Apply a new step in this transform, saving the result. Throws an\n    error when the step fails.\n    */\n    step(step) {\n        let result = this.maybeStep(step);\n        if (result.failed)\n            throw new TransformError(result.failed);\n        return this;\n    }\n    /**\n    Try to apply a step in this transformation, ignoring it if it\n    fails. Returns the step result.\n    */\n    maybeStep(step) {\n        let result = step.apply(this.doc);\n        if (!result.failed)\n            this.addStep(step, result.doc);\n        return result;\n    }\n    /**\n    True when the document has been changed (when there are any\n    steps).\n    */\n    get docChanged() {\n        return this.steps.length > 0;\n    }\n    /**\n    @internal\n    */\n    addStep(step, doc) {\n        this.docs.push(this.doc);\n        this.steps.push(step);\n        this.mapping.appendMap(step.getMap());\n        this.doc = doc;\n    }\n    /**\n    Replace the part of the document between `from` and `to` with the\n    given `slice`.\n    */\n    replace(from, to = from, slice = prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice.empty) {\n        let step = replaceStep(this.doc, from, to, slice);\n        if (step)\n            this.step(step);\n        return this;\n    }\n    /**\n    Replace the given range with the given content, which may be a\n    fragment, node, or array of nodes.\n    */\n    replaceWith(from, to, content) {\n        return this.replace(from, to, new prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice(prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.from(content), 0, 0));\n    }\n    /**\n    Delete the content between the given positions.\n    */\n    delete(from, to) {\n        return this.replace(from, to, prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice.empty);\n    }\n    /**\n    Insert the given content at the given position.\n    */\n    insert(pos, content) {\n        return this.replaceWith(pos, pos, content);\n    }\n    /**\n    Replace a range of the document with a given slice, using\n    `from`, `to`, and the slice's\n    [`openStart`](https://prosemirror.net/docs/ref/#model.Slice.openStart) property as hints, rather\n    than fixed start and end points. This method may grow the\n    replaced area or close open nodes in the slice in order to get a\n    fit that is more in line with WYSIWYG expectations, by dropping\n    fully covered parent nodes of the replaced region when they are\n    marked [non-defining as\n    context](https://prosemirror.net/docs/ref/#model.NodeSpec.definingAsContext), or including an\n    open parent node from the slice that _is_ marked as [defining\n    its content](https://prosemirror.net/docs/ref/#model.NodeSpec.definingForContent).\n    \n    This is the method, for example, to handle paste. The similar\n    [`replace`](https://prosemirror.net/docs/ref/#transform.Transform.replace) method is a more\n    primitive tool which will _not_ move the start and end of its given\n    range, and is useful in situations where you need more precise\n    control over what happens.\n    */\n    replaceRange(from, to, slice) {\n        replaceRange(this, from, to, slice);\n        return this;\n    }\n    /**\n    Replace the given range with a node, but use `from` and `to` as\n    hints, rather than precise positions. When from and to are the same\n    and are at the start or end of a parent node in which the given\n    node doesn't fit, this method may _move_ them out towards a parent\n    that does allow the given node to be placed. When the given range\n    completely covers a parent node, this method may completely replace\n    that parent node.\n    */\n    replaceRangeWith(from, to, node) {\n        replaceRangeWith(this, from, to, node);\n        return this;\n    }\n    /**\n    Delete the given range, expanding it to cover fully covered\n    parent nodes until a valid replace is found.\n    */\n    deleteRange(from, to) {\n        deleteRange(this, from, to);\n        return this;\n    }\n    /**\n    Split the content in the given range off from its parent, if there\n    is sibling content before or after it, and move it up the tree to\n    the depth specified by `target`. You'll probably want to use\n    [`liftTarget`](https://prosemirror.net/docs/ref/#transform.liftTarget) to compute `target`, to make\n    sure the lift is valid.\n    */\n    lift(range, target) {\n        lift(this, range, target);\n        return this;\n    }\n    /**\n    Join the blocks around the given position. If depth is 2, their\n    last and first siblings are also joined, and so on.\n    */\n    join(pos, depth = 1) {\n        join(this, pos, depth);\n        return this;\n    }\n    /**\n    Wrap the given [range](https://prosemirror.net/docs/ref/#model.NodeRange) in the given set of wrappers.\n    The wrappers are assumed to be valid in this position, and should\n    probably be computed with [`findWrapping`](https://prosemirror.net/docs/ref/#transform.findWrapping).\n    */\n    wrap(range, wrappers) {\n        wrap(this, range, wrappers);\n        return this;\n    }\n    /**\n    Set the type of all textblocks (partly) between `from` and `to` to\n    the given node type with the given attributes.\n    */\n    setBlockType(from, to = from, type, attrs = null) {\n        setBlockType(this, from, to, type, attrs);\n        return this;\n    }\n    /**\n    Change the type, attributes, and/or marks of the node at `pos`.\n    When `type` isn't given, the existing node type is preserved,\n    */\n    setNodeMarkup(pos, type, attrs = null, marks) {\n        setNodeMarkup(this, pos, type, attrs, marks);\n        return this;\n    }\n    /**\n    Set a single attribute on a given node to a new value.\n    The `pos` addresses the document content. Use `setDocAttribute`\n    to set attributes on the document itself.\n    */\n    setNodeAttribute(pos, attr, value) {\n        this.step(new AttrStep(pos, attr, value));\n        return this;\n    }\n    /**\n    Set a single attribute on the document to a new value.\n    */\n    setDocAttribute(attr, value) {\n        this.step(new DocAttrStep(attr, value));\n        return this;\n    }\n    /**\n    Add a mark to the node at position `pos`.\n    */\n    addNodeMark(pos, mark) {\n        this.step(new AddNodeMarkStep(pos, mark));\n        return this;\n    }\n    /**\n    Remove a mark (or a mark of the given type) from the node at\n    position `pos`.\n    */\n    removeNodeMark(pos, mark) {\n        if (!(mark instanceof prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Mark)) {\n            let node = this.doc.nodeAt(pos);\n            if (!node)\n                throw new RangeError(\"No node at position \" + pos);\n            mark = mark.isInSet(node.marks);\n            if (!mark)\n                return this;\n        }\n        this.step(new RemoveNodeMarkStep(pos, mark));\n        return this;\n    }\n    /**\n    Split the node at the given position, and optionally, if `depth` is\n    greater than one, any number of nodes above that. By default, the\n    parts split off will inherit the node type of the original node.\n    This can be changed by passing an array of types and attributes to\n    use after the split.\n    */\n    split(pos, depth = 1, typesAfter) {\n        split(this, pos, depth, typesAfter);\n        return this;\n    }\n    /**\n    Add the given mark to the inline content between `from` and `to`.\n    */\n    addMark(from, to, mark) {\n        addMark(this, from, to, mark);\n        return this;\n    }\n    /**\n    Remove marks from inline nodes between `from` and `to`. When\n    `mark` is a single mark, remove precisely that mark. When it is\n    a mark type, remove all marks of that type. When it is null,\n    remove all marks of any type.\n    */\n    removeMark(from, to, mark) {\n        removeMark(this, from, to, mark);\n        return this;\n    }\n    /**\n    Removes all marks and nodes from the content of the node at\n    `pos` that don't match the given new parent node type. Accepts\n    an optional starting [content match](https://prosemirror.net/docs/ref/#model.ContentMatch) as\n    third argument.\n    */\n    clearIncompatible(pos, parentType, match) {\n        clearIncompatible(this, pos, parentType, match);\n        return this;\n    }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/prosemirror-transform/dist/index.js\n");

/***/ })

};
;