"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/prosemirror-state";
exports.ids = ["vendor-chunks/prosemirror-state"];
exports.modules = {

/***/ "(ssr)/../../node_modules/prosemirror-state/dist/index.js":
/*!**********************************************************!*\
  !*** ../../node_modules/prosemirror-state/dist/index.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AllSelection: () => (/* binding */ AllSelection),\n/* harmony export */   EditorState: () => (/* binding */ EditorState),\n/* harmony export */   NodeSelection: () => (/* binding */ NodeSelection),\n/* harmony export */   Plugin: () => (/* binding */ Plugin),\n/* harmony export */   PluginKey: () => (/* binding */ PluginKey),\n/* harmony export */   Selection: () => (/* binding */ Selection),\n/* harmony export */   SelectionRange: () => (/* binding */ SelectionRange),\n/* harmony export */   TextSelection: () => (/* binding */ TextSelection),\n/* harmony export */   Transaction: () => (/* binding */ Transaction)\n/* harmony export */ });\n/* harmony import */ var prosemirror_model__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! prosemirror-model */ \"(ssr)/../../node_modules/prosemirror-model/dist/index.js\");\n/* harmony import */ var prosemirror_transform__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! prosemirror-transform */ \"(ssr)/../../node_modules/prosemirror-transform/dist/index.js\");\n\n\n\nconst classesById = Object.create(null);\n/**\nSuperclass for editor selections. Every selection type should\nextend this. Should not be instantiated directly.\n*/\nclass Selection {\n    /**\n    Initialize a selection with the head and anchor and ranges. If no\n    ranges are given, constructs a single range across `$anchor` and\n    `$head`.\n    */\n    constructor(\n    /**\n    The resolved anchor of the selection (the side that stays in\n    place when the selection is modified).\n    */\n    $anchor, \n    /**\n    The resolved head of the selection (the side that moves when\n    the selection is modified).\n    */\n    $head, ranges) {\n        this.$anchor = $anchor;\n        this.$head = $head;\n        this.ranges = ranges || [new SelectionRange($anchor.min($head), $anchor.max($head))];\n    }\n    /**\n    The selection's anchor, as an unresolved position.\n    */\n    get anchor() { return this.$anchor.pos; }\n    /**\n    The selection's head.\n    */\n    get head() { return this.$head.pos; }\n    /**\n    The lower bound of the selection's main range.\n    */\n    get from() { return this.$from.pos; }\n    /**\n    The upper bound of the selection's main range.\n    */\n    get to() { return this.$to.pos; }\n    /**\n    The resolved lower  bound of the selection's main range.\n    */\n    get $from() {\n        return this.ranges[0].$from;\n    }\n    /**\n    The resolved upper bound of the selection's main range.\n    */\n    get $to() {\n        return this.ranges[0].$to;\n    }\n    /**\n    Indicates whether the selection contains any content.\n    */\n    get empty() {\n        let ranges = this.ranges;\n        for (let i = 0; i < ranges.length; i++)\n            if (ranges[i].$from.pos != ranges[i].$to.pos)\n                return false;\n        return true;\n    }\n    /**\n    Get the content of this selection as a slice.\n    */\n    content() {\n        return this.$from.doc.slice(this.from, this.to, true);\n    }\n    /**\n    Replace the selection with a slice or, if no slice is given,\n    delete the selection. Will append to the given transaction.\n    */\n    replace(tr, content = prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice.empty) {\n        // Put the new selection at the position after the inserted\n        // content. When that ended in an inline node, search backwards,\n        // to get the position after that node. If not, search forward.\n        let lastNode = content.content.lastChild, lastParent = null;\n        for (let i = 0; i < content.openEnd; i++) {\n            lastParent = lastNode;\n            lastNode = lastNode.lastChild;\n        }\n        let mapFrom = tr.steps.length, ranges = this.ranges;\n        for (let i = 0; i < ranges.length; i++) {\n            let { $from, $to } = ranges[i], mapping = tr.mapping.slice(mapFrom);\n            tr.replaceRange(mapping.map($from.pos), mapping.map($to.pos), i ? prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice.empty : content);\n            if (i == 0)\n                selectionToInsertionEnd(tr, mapFrom, (lastNode ? lastNode.isInline : lastParent && lastParent.isTextblock) ? -1 : 1);\n        }\n    }\n    /**\n    Replace the selection with the given node, appending the changes\n    to the given transaction.\n    */\n    replaceWith(tr, node) {\n        let mapFrom = tr.steps.length, ranges = this.ranges;\n        for (let i = 0; i < ranges.length; i++) {\n            let { $from, $to } = ranges[i], mapping = tr.mapping.slice(mapFrom);\n            let from = mapping.map($from.pos), to = mapping.map($to.pos);\n            if (i) {\n                tr.deleteRange(from, to);\n            }\n            else {\n                tr.replaceRangeWith(from, to, node);\n                selectionToInsertionEnd(tr, mapFrom, node.isInline ? -1 : 1);\n            }\n        }\n    }\n    /**\n    Find a valid cursor or leaf node selection starting at the given\n    position and searching back if `dir` is negative, and forward if\n    positive. When `textOnly` is true, only consider cursor\n    selections. Will return null when no valid selection position is\n    found.\n    */\n    static findFrom($pos, dir, textOnly = false) {\n        let inner = $pos.parent.inlineContent ? new TextSelection($pos)\n            : findSelectionIn($pos.node(0), $pos.parent, $pos.pos, $pos.index(), dir, textOnly);\n        if (inner)\n            return inner;\n        for (let depth = $pos.depth - 1; depth >= 0; depth--) {\n            let found = dir < 0\n                ? findSelectionIn($pos.node(0), $pos.node(depth), $pos.before(depth + 1), $pos.index(depth), dir, textOnly)\n                : findSelectionIn($pos.node(0), $pos.node(depth), $pos.after(depth + 1), $pos.index(depth) + 1, dir, textOnly);\n            if (found)\n                return found;\n        }\n        return null;\n    }\n    /**\n    Find a valid cursor or leaf node selection near the given\n    position. Searches forward first by default, but if `bias` is\n    negative, it will search backwards first.\n    */\n    static near($pos, bias = 1) {\n        return this.findFrom($pos, bias) || this.findFrom($pos, -bias) || new AllSelection($pos.node(0));\n    }\n    /**\n    Find the cursor or leaf node selection closest to the start of\n    the given document. Will return an\n    [`AllSelection`](https://prosemirror.net/docs/ref/#state.AllSelection) if no valid position\n    exists.\n    */\n    static atStart(doc) {\n        return findSelectionIn(doc, doc, 0, 0, 1) || new AllSelection(doc);\n    }\n    /**\n    Find the cursor or leaf node selection closest to the end of the\n    given document.\n    */\n    static atEnd(doc) {\n        return findSelectionIn(doc, doc, doc.content.size, doc.childCount, -1) || new AllSelection(doc);\n    }\n    /**\n    Deserialize the JSON representation of a selection. Must be\n    implemented for custom classes (as a static class method).\n    */\n    static fromJSON(doc, json) {\n        if (!json || !json.type)\n            throw new RangeError(\"Invalid input for Selection.fromJSON\");\n        let cls = classesById[json.type];\n        if (!cls)\n            throw new RangeError(`No selection type ${json.type} defined`);\n        return cls.fromJSON(doc, json);\n    }\n    /**\n    To be able to deserialize selections from JSON, custom selection\n    classes must register themselves with an ID string, so that they\n    can be disambiguated. Try to pick something that's unlikely to\n    clash with classes from other modules.\n    */\n    static jsonID(id, selectionClass) {\n        if (id in classesById)\n            throw new RangeError(\"Duplicate use of selection JSON ID \" + id);\n        classesById[id] = selectionClass;\n        selectionClass.prototype.jsonID = id;\n        return selectionClass;\n    }\n    /**\n    Get a [bookmark](https://prosemirror.net/docs/ref/#state.SelectionBookmark) for this selection,\n    which is a value that can be mapped without having access to a\n    current document, and later resolved to a real selection for a\n    given document again. (This is used mostly by the history to\n    track and restore old selections.) The default implementation of\n    this method just converts the selection to a text selection and\n    returns the bookmark for that.\n    */\n    getBookmark() {\n        return TextSelection.between(this.$anchor, this.$head).getBookmark();\n    }\n}\nSelection.prototype.visible = true;\n/**\nRepresents a selected range in a document.\n*/\nclass SelectionRange {\n    /**\n    Create a range.\n    */\n    constructor(\n    /**\n    The lower bound of the range.\n    */\n    $from, \n    /**\n    The upper bound of the range.\n    */\n    $to) {\n        this.$from = $from;\n        this.$to = $to;\n    }\n}\nlet warnedAboutTextSelection = false;\nfunction checkTextSelection($pos) {\n    if (!warnedAboutTextSelection && !$pos.parent.inlineContent) {\n        warnedAboutTextSelection = true;\n        console[\"warn\"](\"TextSelection endpoint not pointing into a node with inline content (\" + $pos.parent.type.name + \")\");\n    }\n}\n/**\nA text selection represents a classical editor selection, with a\nhead (the moving side) and anchor (immobile side), both of which\npoint into textblock nodes. It can be empty (a regular cursor\nposition).\n*/\nclass TextSelection extends Selection {\n    /**\n    Construct a text selection between the given points.\n    */\n    constructor($anchor, $head = $anchor) {\n        checkTextSelection($anchor);\n        checkTextSelection($head);\n        super($anchor, $head);\n    }\n    /**\n    Returns a resolved position if this is a cursor selection (an\n    empty text selection), and null otherwise.\n    */\n    get $cursor() { return this.$anchor.pos == this.$head.pos ? this.$head : null; }\n    map(doc, mapping) {\n        let $head = doc.resolve(mapping.map(this.head));\n        if (!$head.parent.inlineContent)\n            return Selection.near($head);\n        let $anchor = doc.resolve(mapping.map(this.anchor));\n        return new TextSelection($anchor.parent.inlineContent ? $anchor : $head, $head);\n    }\n    replace(tr, content = prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice.empty) {\n        super.replace(tr, content);\n        if (content == prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice.empty) {\n            let marks = this.$from.marksAcross(this.$to);\n            if (marks)\n                tr.ensureMarks(marks);\n        }\n    }\n    eq(other) {\n        return other instanceof TextSelection && other.anchor == this.anchor && other.head == this.head;\n    }\n    getBookmark() {\n        return new TextBookmark(this.anchor, this.head);\n    }\n    toJSON() {\n        return { type: \"text\", anchor: this.anchor, head: this.head };\n    }\n    /**\n    @internal\n    */\n    static fromJSON(doc, json) {\n        if (typeof json.anchor != \"number\" || typeof json.head != \"number\")\n            throw new RangeError(\"Invalid input for TextSelection.fromJSON\");\n        return new TextSelection(doc.resolve(json.anchor), doc.resolve(json.head));\n    }\n    /**\n    Create a text selection from non-resolved positions.\n    */\n    static create(doc, anchor, head = anchor) {\n        let $anchor = doc.resolve(anchor);\n        return new this($anchor, head == anchor ? $anchor : doc.resolve(head));\n    }\n    /**\n    Return a text selection that spans the given positions or, if\n    they aren't text positions, find a text selection near them.\n    `bias` determines whether the method searches forward (default)\n    or backwards (negative number) first. Will fall back to calling\n    [`Selection.near`](https://prosemirror.net/docs/ref/#state.Selection^near) when the document\n    doesn't contain a valid text position.\n    */\n    static between($anchor, $head, bias) {\n        let dPos = $anchor.pos - $head.pos;\n        if (!bias || dPos)\n            bias = dPos >= 0 ? 1 : -1;\n        if (!$head.parent.inlineContent) {\n            let found = Selection.findFrom($head, bias, true) || Selection.findFrom($head, -bias, true);\n            if (found)\n                $head = found.$head;\n            else\n                return Selection.near($head, bias);\n        }\n        if (!$anchor.parent.inlineContent) {\n            if (dPos == 0) {\n                $anchor = $head;\n            }\n            else {\n                $anchor = (Selection.findFrom($anchor, -bias, true) || Selection.findFrom($anchor, bias, true)).$anchor;\n                if (($anchor.pos < $head.pos) != (dPos < 0))\n                    $anchor = $head;\n            }\n        }\n        return new TextSelection($anchor, $head);\n    }\n}\nSelection.jsonID(\"text\", TextSelection);\nclass TextBookmark {\n    constructor(anchor, head) {\n        this.anchor = anchor;\n        this.head = head;\n    }\n    map(mapping) {\n        return new TextBookmark(mapping.map(this.anchor), mapping.map(this.head));\n    }\n    resolve(doc) {\n        return TextSelection.between(doc.resolve(this.anchor), doc.resolve(this.head));\n    }\n}\n/**\nA node selection is a selection that points at a single node. All\nnodes marked [selectable](https://prosemirror.net/docs/ref/#model.NodeSpec.selectable) can be the\ntarget of a node selection. In such a selection, `from` and `to`\npoint directly before and after the selected node, `anchor` equals\n`from`, and `head` equals `to`..\n*/\nclass NodeSelection extends Selection {\n    /**\n    Create a node selection. Does not verify the validity of its\n    argument.\n    */\n    constructor($pos) {\n        let node = $pos.nodeAfter;\n        let $end = $pos.node(0).resolve($pos.pos + node.nodeSize);\n        super($pos, $end);\n        this.node = node;\n    }\n    map(doc, mapping) {\n        let { deleted, pos } = mapping.mapResult(this.anchor);\n        let $pos = doc.resolve(pos);\n        if (deleted)\n            return Selection.near($pos);\n        return new NodeSelection($pos);\n    }\n    content() {\n        return new prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice(prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.from(this.node), 0, 0);\n    }\n    eq(other) {\n        return other instanceof NodeSelection && other.anchor == this.anchor;\n    }\n    toJSON() {\n        return { type: \"node\", anchor: this.anchor };\n    }\n    getBookmark() { return new NodeBookmark(this.anchor); }\n    /**\n    @internal\n    */\n    static fromJSON(doc, json) {\n        if (typeof json.anchor != \"number\")\n            throw new RangeError(\"Invalid input for NodeSelection.fromJSON\");\n        return new NodeSelection(doc.resolve(json.anchor));\n    }\n    /**\n    Create a node selection from non-resolved positions.\n    */\n    static create(doc, from) {\n        return new NodeSelection(doc.resolve(from));\n    }\n    /**\n    Determines whether the given node may be selected as a node\n    selection.\n    */\n    static isSelectable(node) {\n        return !node.isText && node.type.spec.selectable !== false;\n    }\n}\nNodeSelection.prototype.visible = false;\nSelection.jsonID(\"node\", NodeSelection);\nclass NodeBookmark {\n    constructor(anchor) {\n        this.anchor = anchor;\n    }\n    map(mapping) {\n        let { deleted, pos } = mapping.mapResult(this.anchor);\n        return deleted ? new TextBookmark(pos, pos) : new NodeBookmark(pos);\n    }\n    resolve(doc) {\n        let $pos = doc.resolve(this.anchor), node = $pos.nodeAfter;\n        if (node && NodeSelection.isSelectable(node))\n            return new NodeSelection($pos);\n        return Selection.near($pos);\n    }\n}\n/**\nA selection type that represents selecting the whole document\n(which can not necessarily be expressed with a text selection, when\nthere are for example leaf block nodes at the start or end of the\ndocument).\n*/\nclass AllSelection extends Selection {\n    /**\n    Create an all-selection over the given document.\n    */\n    constructor(doc) {\n        super(doc.resolve(0), doc.resolve(doc.content.size));\n    }\n    replace(tr, content = prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice.empty) {\n        if (content == prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice.empty) {\n            tr.delete(0, tr.doc.content.size);\n            let sel = Selection.atStart(tr.doc);\n            if (!sel.eq(tr.selection))\n                tr.setSelection(sel);\n        }\n        else {\n            super.replace(tr, content);\n        }\n    }\n    toJSON() { return { type: \"all\" }; }\n    /**\n    @internal\n    */\n    static fromJSON(doc) { return new AllSelection(doc); }\n    map(doc) { return new AllSelection(doc); }\n    eq(other) { return other instanceof AllSelection; }\n    getBookmark() { return AllBookmark; }\n}\nSelection.jsonID(\"all\", AllSelection);\nconst AllBookmark = {\n    map() { return this; },\n    resolve(doc) { return new AllSelection(doc); }\n};\n// FIXME we'll need some awareness of text direction when scanning for selections\n// Try to find a selection inside the given node. `pos` points at the\n// position where the search starts. When `text` is true, only return\n// text selections.\nfunction findSelectionIn(doc, node, pos, index, dir, text = false) {\n    if (node.inlineContent)\n        return TextSelection.create(doc, pos);\n    for (let i = index - (dir > 0 ? 0 : 1); dir > 0 ? i < node.childCount : i >= 0; i += dir) {\n        let child = node.child(i);\n        if (!child.isAtom) {\n            let inner = findSelectionIn(doc, child, pos + dir, dir < 0 ? child.childCount : 0, dir, text);\n            if (inner)\n                return inner;\n        }\n        else if (!text && NodeSelection.isSelectable(child)) {\n            return NodeSelection.create(doc, pos - (dir < 0 ? child.nodeSize : 0));\n        }\n        pos += child.nodeSize * dir;\n    }\n    return null;\n}\nfunction selectionToInsertionEnd(tr, startLen, bias) {\n    let last = tr.steps.length - 1;\n    if (last < startLen)\n        return;\n    let step = tr.steps[last];\n    if (!(step instanceof prosemirror_transform__WEBPACK_IMPORTED_MODULE_1__.ReplaceStep || step instanceof prosemirror_transform__WEBPACK_IMPORTED_MODULE_1__.ReplaceAroundStep))\n        return;\n    let map = tr.mapping.maps[last], end;\n    map.forEach((_from, _to, _newFrom, newTo) => { if (end == null)\n        end = newTo; });\n    tr.setSelection(Selection.near(tr.doc.resolve(end), bias));\n}\n\nconst UPDATED_SEL = 1, UPDATED_MARKS = 2, UPDATED_SCROLL = 4;\n/**\nAn editor state transaction, which can be applied to a state to\ncreate an updated state. Use\n[`EditorState.tr`](https://prosemirror.net/docs/ref/#state.EditorState.tr) to create an instance.\n\nTransactions track changes to the document (they are a subclass of\n[`Transform`](https://prosemirror.net/docs/ref/#transform.Transform)), but also other state changes,\nlike selection updates and adjustments of the set of [stored\nmarks](https://prosemirror.net/docs/ref/#state.EditorState.storedMarks). In addition, you can store\nmetadata properties in a transaction, which are extra pieces of\ninformation that client code or plugins can use to describe what a\ntransaction represents, so that they can update their [own\nstate](https://prosemirror.net/docs/ref/#state.StateField) accordingly.\n\nThe [editor view](https://prosemirror.net/docs/ref/#view.EditorView) uses a few metadata\nproperties: it will attach a property `\"pointer\"` with the value\n`true` to selection transactions directly caused by mouse or touch\ninput, a `\"composition\"` property holding an ID identifying the\ncomposition that caused it to transactions caused by composed DOM\ninput, and a `\"uiEvent\"` property of that may be `\"paste\"`,\n`\"cut\"`, or `\"drop\"`.\n*/\nclass Transaction extends prosemirror_transform__WEBPACK_IMPORTED_MODULE_1__.Transform {\n    /**\n    @internal\n    */\n    constructor(state) {\n        super(state.doc);\n        // The step count for which the current selection is valid.\n        this.curSelectionFor = 0;\n        // Bitfield to track which aspects of the state were updated by\n        // this transaction.\n        this.updated = 0;\n        // Object used to store metadata properties for the transaction.\n        this.meta = Object.create(null);\n        this.time = Date.now();\n        this.curSelection = state.selection;\n        this.storedMarks = state.storedMarks;\n    }\n    /**\n    The transaction's current selection. This defaults to the editor\n    selection [mapped](https://prosemirror.net/docs/ref/#state.Selection.map) through the steps in the\n    transaction, but can be overwritten with\n    [`setSelection`](https://prosemirror.net/docs/ref/#state.Transaction.setSelection).\n    */\n    get selection() {\n        if (this.curSelectionFor < this.steps.length) {\n            this.curSelection = this.curSelection.map(this.doc, this.mapping.slice(this.curSelectionFor));\n            this.curSelectionFor = this.steps.length;\n        }\n        return this.curSelection;\n    }\n    /**\n    Update the transaction's current selection. Will determine the\n    selection that the editor gets when the transaction is applied.\n    */\n    setSelection(selection) {\n        if (selection.$from.doc != this.doc)\n            throw new RangeError(\"Selection passed to setSelection must point at the current document\");\n        this.curSelection = selection;\n        this.curSelectionFor = this.steps.length;\n        this.updated = (this.updated | UPDATED_SEL) & ~UPDATED_MARKS;\n        this.storedMarks = null;\n        return this;\n    }\n    /**\n    Whether the selection was explicitly updated by this transaction.\n    */\n    get selectionSet() {\n        return (this.updated & UPDATED_SEL) > 0;\n    }\n    /**\n    Set the current stored marks.\n    */\n    setStoredMarks(marks) {\n        this.storedMarks = marks;\n        this.updated |= UPDATED_MARKS;\n        return this;\n    }\n    /**\n    Make sure the current stored marks or, if that is null, the marks\n    at the selection, match the given set of marks. Does nothing if\n    this is already the case.\n    */\n    ensureMarks(marks) {\n        if (!prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Mark.sameSet(this.storedMarks || this.selection.$from.marks(), marks))\n            this.setStoredMarks(marks);\n        return this;\n    }\n    /**\n    Add a mark to the set of stored marks.\n    */\n    addStoredMark(mark) {\n        return this.ensureMarks(mark.addToSet(this.storedMarks || this.selection.$head.marks()));\n    }\n    /**\n    Remove a mark or mark type from the set of stored marks.\n    */\n    removeStoredMark(mark) {\n        return this.ensureMarks(mark.removeFromSet(this.storedMarks || this.selection.$head.marks()));\n    }\n    /**\n    Whether the stored marks were explicitly set for this transaction.\n    */\n    get storedMarksSet() {\n        return (this.updated & UPDATED_MARKS) > 0;\n    }\n    /**\n    @internal\n    */\n    addStep(step, doc) {\n        super.addStep(step, doc);\n        this.updated = this.updated & ~UPDATED_MARKS;\n        this.storedMarks = null;\n    }\n    /**\n    Update the timestamp for the transaction.\n    */\n    setTime(time) {\n        this.time = time;\n        return this;\n    }\n    /**\n    Replace the current selection with the given slice.\n    */\n    replaceSelection(slice) {\n        this.selection.replace(this, slice);\n        return this;\n    }\n    /**\n    Replace the selection with the given node. When `inheritMarks` is\n    true and the content is inline, it inherits the marks from the\n    place where it is inserted.\n    */\n    replaceSelectionWith(node, inheritMarks = true) {\n        let selection = this.selection;\n        if (inheritMarks)\n            node = node.mark(this.storedMarks || (selection.empty ? selection.$from.marks() : (selection.$from.marksAcross(selection.$to) || prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Mark.none)));\n        selection.replaceWith(this, node);\n        return this;\n    }\n    /**\n    Delete the selection.\n    */\n    deleteSelection() {\n        this.selection.replace(this);\n        return this;\n    }\n    /**\n    Replace the given range, or the selection if no range is given,\n    with a text node containing the given string.\n    */\n    insertText(text, from, to) {\n        let schema = this.doc.type.schema;\n        if (from == null) {\n            if (!text)\n                return this.deleteSelection();\n            return this.replaceSelectionWith(schema.text(text), true);\n        }\n        else {\n            if (to == null)\n                to = from;\n            to = to == null ? from : to;\n            if (!text)\n                return this.deleteRange(from, to);\n            let marks = this.storedMarks;\n            if (!marks) {\n                let $from = this.doc.resolve(from);\n                marks = to == from ? $from.marks() : $from.marksAcross(this.doc.resolve(to));\n            }\n            this.replaceRangeWith(from, to, schema.text(text, marks));\n            if (!this.selection.empty)\n                this.setSelection(Selection.near(this.selection.$to));\n            return this;\n        }\n    }\n    /**\n    Store a metadata property in this transaction, keyed either by\n    name or by plugin.\n    */\n    setMeta(key, value) {\n        this.meta[typeof key == \"string\" ? key : key.key] = value;\n        return this;\n    }\n    /**\n    Retrieve a metadata property for a given name or plugin.\n    */\n    getMeta(key) {\n        return this.meta[typeof key == \"string\" ? key : key.key];\n    }\n    /**\n    Returns true if this transaction doesn't contain any metadata,\n    and can thus safely be extended.\n    */\n    get isGeneric() {\n        for (let _ in this.meta)\n            return false;\n        return true;\n    }\n    /**\n    Indicate that the editor should scroll the selection into view\n    when updated to the state produced by this transaction.\n    */\n    scrollIntoView() {\n        this.updated |= UPDATED_SCROLL;\n        return this;\n    }\n    /**\n    True when this transaction has had `scrollIntoView` called on it.\n    */\n    get scrolledIntoView() {\n        return (this.updated & UPDATED_SCROLL) > 0;\n    }\n}\n\nfunction bind(f, self) {\n    return !self || !f ? f : f.bind(self);\n}\nclass FieldDesc {\n    constructor(name, desc, self) {\n        this.name = name;\n        this.init = bind(desc.init, self);\n        this.apply = bind(desc.apply, self);\n    }\n}\nconst baseFields = [\n    new FieldDesc(\"doc\", {\n        init(config) { return config.doc || config.schema.topNodeType.createAndFill(); },\n        apply(tr) { return tr.doc; }\n    }),\n    new FieldDesc(\"selection\", {\n        init(config, instance) { return config.selection || Selection.atStart(instance.doc); },\n        apply(tr) { return tr.selection; }\n    }),\n    new FieldDesc(\"storedMarks\", {\n        init(config) { return config.storedMarks || null; },\n        apply(tr, _marks, _old, state) { return state.selection.$cursor ? tr.storedMarks : null; }\n    }),\n    new FieldDesc(\"scrollToSelection\", {\n        init() { return 0; },\n        apply(tr, prev) { return tr.scrolledIntoView ? prev + 1 : prev; }\n    })\n];\n// Object wrapping the part of a state object that stays the same\n// across transactions. Stored in the state's `config` property.\nclass Configuration {\n    constructor(schema, plugins) {\n        this.schema = schema;\n        this.plugins = [];\n        this.pluginsByKey = Object.create(null);\n        this.fields = baseFields.slice();\n        if (plugins)\n            plugins.forEach(plugin => {\n                if (this.pluginsByKey[plugin.key])\n                    throw new RangeError(\"Adding different instances of a keyed plugin (\" + plugin.key + \")\");\n                this.plugins.push(plugin);\n                this.pluginsByKey[plugin.key] = plugin;\n                if (plugin.spec.state)\n                    this.fields.push(new FieldDesc(plugin.key, plugin.spec.state, plugin));\n            });\n    }\n}\n/**\nThe state of a ProseMirror editor is represented by an object of\nthis type. A state is a persistent data structure—it isn't\nupdated, but rather a new state value is computed from an old one\nusing the [`apply`](https://prosemirror.net/docs/ref/#state.EditorState.apply) method.\n\nA state holds a number of built-in fields, and plugins can\n[define](https://prosemirror.net/docs/ref/#state.PluginSpec.state) additional fields.\n*/\nclass EditorState {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    @internal\n    */\n    config) {\n        this.config = config;\n    }\n    /**\n    The schema of the state's document.\n    */\n    get schema() {\n        return this.config.schema;\n    }\n    /**\n    The plugins that are active in this state.\n    */\n    get plugins() {\n        return this.config.plugins;\n    }\n    /**\n    Apply the given transaction to produce a new state.\n    */\n    apply(tr) {\n        return this.applyTransaction(tr).state;\n    }\n    /**\n    @internal\n    */\n    filterTransaction(tr, ignore = -1) {\n        for (let i = 0; i < this.config.plugins.length; i++)\n            if (i != ignore) {\n                let plugin = this.config.plugins[i];\n                if (plugin.spec.filterTransaction && !plugin.spec.filterTransaction.call(plugin, tr, this))\n                    return false;\n            }\n        return true;\n    }\n    /**\n    Verbose variant of [`apply`](https://prosemirror.net/docs/ref/#state.EditorState.apply) that\n    returns the precise transactions that were applied (which might\n    be influenced by the [transaction\n    hooks](https://prosemirror.net/docs/ref/#state.PluginSpec.filterTransaction) of\n    plugins) along with the new state.\n    */\n    applyTransaction(rootTr) {\n        if (!this.filterTransaction(rootTr))\n            return { state: this, transactions: [] };\n        let trs = [rootTr], newState = this.applyInner(rootTr), seen = null;\n        // This loop repeatedly gives plugins a chance to respond to\n        // transactions as new transactions are added, making sure to only\n        // pass the transactions the plugin did not see before.\n        for (;;) {\n            let haveNew = false;\n            for (let i = 0; i < this.config.plugins.length; i++) {\n                let plugin = this.config.plugins[i];\n                if (plugin.spec.appendTransaction) {\n                    let n = seen ? seen[i].n : 0, oldState = seen ? seen[i].state : this;\n                    let tr = n < trs.length &&\n                        plugin.spec.appendTransaction.call(plugin, n ? trs.slice(n) : trs, oldState, newState);\n                    if (tr && newState.filterTransaction(tr, i)) {\n                        tr.setMeta(\"appendedTransaction\", rootTr);\n                        if (!seen) {\n                            seen = [];\n                            for (let j = 0; j < this.config.plugins.length; j++)\n                                seen.push(j < i ? { state: newState, n: trs.length } : { state: this, n: 0 });\n                        }\n                        trs.push(tr);\n                        newState = newState.applyInner(tr);\n                        haveNew = true;\n                    }\n                    if (seen)\n                        seen[i] = { state: newState, n: trs.length };\n                }\n            }\n            if (!haveNew)\n                return { state: newState, transactions: trs };\n        }\n    }\n    /**\n    @internal\n    */\n    applyInner(tr) {\n        if (!tr.before.eq(this.doc))\n            throw new RangeError(\"Applying a mismatched transaction\");\n        let newInstance = new EditorState(this.config), fields = this.config.fields;\n        for (let i = 0; i < fields.length; i++) {\n            let field = fields[i];\n            newInstance[field.name] = field.apply(tr, this[field.name], this, newInstance);\n        }\n        return newInstance;\n    }\n    /**\n    Start a [transaction](https://prosemirror.net/docs/ref/#state.Transaction) from this state.\n    */\n    get tr() { return new Transaction(this); }\n    /**\n    Create a new state.\n    */\n    static create(config) {\n        let $config = new Configuration(config.doc ? config.doc.type.schema : config.schema, config.plugins);\n        let instance = new EditorState($config);\n        for (let i = 0; i < $config.fields.length; i++)\n            instance[$config.fields[i].name] = $config.fields[i].init(config, instance);\n        return instance;\n    }\n    /**\n    Create a new state based on this one, but with an adjusted set\n    of active plugins. State fields that exist in both sets of\n    plugins are kept unchanged. Those that no longer exist are\n    dropped, and those that are new are initialized using their\n    [`init`](https://prosemirror.net/docs/ref/#state.StateField.init) method, passing in the new\n    configuration object..\n    */\n    reconfigure(config) {\n        let $config = new Configuration(this.schema, config.plugins);\n        let fields = $config.fields, instance = new EditorState($config);\n        for (let i = 0; i < fields.length; i++) {\n            let name = fields[i].name;\n            instance[name] = this.hasOwnProperty(name) ? this[name] : fields[i].init(config, instance);\n        }\n        return instance;\n    }\n    /**\n    Serialize this state to JSON. If you want to serialize the state\n    of plugins, pass an object mapping property names to use in the\n    resulting JSON object to plugin objects. The argument may also be\n    a string or number, in which case it is ignored, to support the\n    way `JSON.stringify` calls `toString` methods.\n    */\n    toJSON(pluginFields) {\n        let result = { doc: this.doc.toJSON(), selection: this.selection.toJSON() };\n        if (this.storedMarks)\n            result.storedMarks = this.storedMarks.map(m => m.toJSON());\n        if (pluginFields && typeof pluginFields == 'object')\n            for (let prop in pluginFields) {\n                if (prop == \"doc\" || prop == \"selection\")\n                    throw new RangeError(\"The JSON fields `doc` and `selection` are reserved\");\n                let plugin = pluginFields[prop], state = plugin.spec.state;\n                if (state && state.toJSON)\n                    result[prop] = state.toJSON.call(plugin, this[plugin.key]);\n            }\n        return result;\n    }\n    /**\n    Deserialize a JSON representation of a state. `config` should\n    have at least a `schema` field, and should contain array of\n    plugins to initialize the state with. `pluginFields` can be used\n    to deserialize the state of plugins, by associating plugin\n    instances with the property names they use in the JSON object.\n    */\n    static fromJSON(config, json, pluginFields) {\n        if (!json)\n            throw new RangeError(\"Invalid input for EditorState.fromJSON\");\n        if (!config.schema)\n            throw new RangeError(\"Required config field 'schema' missing\");\n        let $config = new Configuration(config.schema, config.plugins);\n        let instance = new EditorState($config);\n        $config.fields.forEach(field => {\n            if (field.name == \"doc\") {\n                instance.doc = prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Node.fromJSON(config.schema, json.doc);\n            }\n            else if (field.name == \"selection\") {\n                instance.selection = Selection.fromJSON(instance.doc, json.selection);\n            }\n            else if (field.name == \"storedMarks\") {\n                if (json.storedMarks)\n                    instance.storedMarks = json.storedMarks.map(config.schema.markFromJSON);\n            }\n            else {\n                if (pluginFields)\n                    for (let prop in pluginFields) {\n                        let plugin = pluginFields[prop], state = plugin.spec.state;\n                        if (plugin.key == field.name && state && state.fromJSON &&\n                            Object.prototype.hasOwnProperty.call(json, prop)) {\n                            instance[field.name] = state.fromJSON.call(plugin, config, json[prop], instance);\n                            return;\n                        }\n                    }\n                instance[field.name] = field.init(config, instance);\n            }\n        });\n        return instance;\n    }\n}\n\nfunction bindProps(obj, self, target) {\n    for (let prop in obj) {\n        let val = obj[prop];\n        if (val instanceof Function)\n            val = val.bind(self);\n        else if (prop == \"handleDOMEvents\")\n            val = bindProps(val, self, {});\n        target[prop] = val;\n    }\n    return target;\n}\n/**\nPlugins bundle functionality that can be added to an editor.\nThey are part of the [editor state](https://prosemirror.net/docs/ref/#state.EditorState) and\nmay influence that state and the view that contains it.\n*/\nclass Plugin {\n    /**\n    Create a plugin.\n    */\n    constructor(\n    /**\n    The plugin's [spec object](https://prosemirror.net/docs/ref/#state.PluginSpec).\n    */\n    spec) {\n        this.spec = spec;\n        /**\n        The [props](https://prosemirror.net/docs/ref/#view.EditorProps) exported by this plugin.\n        */\n        this.props = {};\n        if (spec.props)\n            bindProps(spec.props, this, this.props);\n        this.key = spec.key ? spec.key.key : createKey(\"plugin\");\n    }\n    /**\n    Extract the plugin's state field from an editor state.\n    */\n    getState(state) { return state[this.key]; }\n}\nconst keys = Object.create(null);\nfunction createKey(name) {\n    if (name in keys)\n        return name + \"$\" + ++keys[name];\n    keys[name] = 0;\n    return name + \"$\";\n}\n/**\nA key is used to [tag](https://prosemirror.net/docs/ref/#state.PluginSpec.key) plugins in a way\nthat makes it possible to find them, given an editor state.\nAssigning a key does mean only one plugin of that type can be\nactive in a state.\n*/\nclass PluginKey {\n    /**\n    Create a plugin key.\n    */\n    constructor(name = \"key\") { this.key = createKey(name); }\n    /**\n    Get the active plugin with this key, if any, from an editor\n    state.\n    */\n    get(state) { return state.config.pluginsByKey[this.key]; }\n    /**\n    Get the plugin's state from an editor state.\n    */\n    getState(state) { return state[this.key]; }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/prosemirror-state/dist/index.js\n");

/***/ })

};
;