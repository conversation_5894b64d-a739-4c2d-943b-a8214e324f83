"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_hast-util-from-dom_index_js"],{

/***/ "(app-pages-browser)/../../node_modules/hast-util-from-dom/index.js":
/*!******************************************************!*\
  !*** ../../node_modules/hast-util-from-dom/index.js ***!
  \******************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fromDom: function() { return /* reexport safe */ _lib_index_js__WEBPACK_IMPORTED_MODULE_0__.fromDom; }\n/* harmony export */ });\n/* harmony import */ var _lib_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./lib/index.js */ \"(app-pages-browser)/../../node_modules/hast-util-from-dom/lib/index.js\");\n/**\n * @typedef {import('./lib/index.js').AfterTransform} AfterTransform\n * @typedef {import('./lib/index.js').Options} Options\n */\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLWZyb20tZG9tL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDQSxhQUFhLHlDQUF5QztBQUN0RCxhQUFhLGtDQUFrQztBQUMvQzs7QUFFc0MiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL25vZGVfbW9kdWxlcy9oYXN0LXV0aWwtZnJvbS1kb20vaW5kZXguanM/MWMzZSJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4vbGliL2luZGV4LmpzJykuQWZ0ZXJUcmFuc2Zvcm19IEFmdGVyVHJhbnNmb3JtXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuL2xpYi9pbmRleC5qcycpLk9wdGlvbnN9IE9wdGlvbnNcbiAqL1xuXG5leHBvcnQge2Zyb21Eb219IGZyb20gJy4vbGliL2luZGV4LmpzJ1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/hast-util-from-dom/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/hast-util-from-dom/lib/index.js":
/*!**********************************************************!*\
  !*** ../../node_modules/hast-util-from-dom/lib/index.js ***!
  \**********************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fromDom: function() { return /* binding */ fromDom; }\n/* harmony export */ });\n/* harmony import */ var web_namespaces__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! web-namespaces */ \"(app-pages-browser)/../../node_modules/web-namespaces/index.js\");\n/* harmony import */ var hastscript__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! hastscript */ \"(app-pages-browser)/../../node_modules/hastscript/lib/svg.js\");\n/* harmony import */ var hastscript__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! hastscript */ \"(app-pages-browser)/../../node_modules/hastscript/lib/html.js\");\n/**\n * @typedef {import('hast').Root} HastRoot\n * @typedef {import('hast').DocType} HastDoctype\n * @typedef {import('hast').Element} HastElement\n * @typedef {import('hast').Text} HastText\n * @typedef {import('hast').Comment} HastComment\n * @typedef {import('hast').Content} HastContent\n */\n\n/**\n * @typedef {HastContent | HastRoot} HastNode\n *\n * @callback AfterTransform\n *   Callback called when each node is transformed.\n * @param {Node} domNode\n *   DOM node that was handled.\n * @param {HastNode} hastNode\n *   Corresponding hast node.\n * @returns {void}\n *   Nothing.\n *\n * @typedef Options\n *   Configuration.\n * @property {AfterTransform | null | undefined} [afterTransform]\n *   Callback called when each node is transformed.\n */\n\n\n\n\n/**\n * Transform a DOM tree to a hast tree.\n *\n * @param {Node} tree\n *   DOM tree to transform.\n * @param {Options | null | undefined} [options]\n *   Configuration (optional).\n * @returns {HastNode}\n *   Equivalent hast node.\n */\nfunction fromDom(tree, options) {\n  const result = tree ? transform(tree, options || {}) : undefined\n  return result || {type: 'root', children: []}\n}\n\n/**\n * @param {Node} node\n *   DOM node to transform.\n * @param {Options} options\n *   Configuration.\n * @returns {HastNode | undefined}\n *   Equivalent hast node.\n *\n *   Note that certain legacy DOM nodes (i.e., Attr nodes (2),  CDATA, processing instructions)\n */\nfunction transform(node, options) {\n  const transformed = one(node, options)\n  if (transformed && options.afterTransform)\n    options.afterTransform(node, transformed)\n  return transformed\n}\n\n/**\n * @param {Node} node\n *   DOM node to transform.\n * @param {Options} options\n *   Configuration.\n * @returns {HastNode | undefined}\n *   Equivalent hast node.\n */\nfunction one(node, options) {\n  switch (node.nodeType) {\n    case 1 /* Element */: {\n      // @ts-expect-error TypeScript is wrong.\n      return element(node, options)\n    }\n\n    // Ignore: Attr (2).\n\n    case 3 /* Text */: {\n      // @ts-expect-error TypeScript is wrong.\n      return text(node)\n    }\n\n    // Ignore: CDATA (4).\n    // Removed: Entity reference (5)\n    // Removed: Entity (6)\n    // Ignore: Processing instruction (7).\n\n    case 8 /* Comment */: {\n      // @ts-expect-error TypeScript is wrong.\n      return comment(node)\n    }\n\n    case 9 /* Document */: {\n      // @ts-expect-error TypeScript is wrong.\n      return root(node, options)\n    }\n\n    case 10 /* Document type */: {\n      return doctype()\n    }\n\n    case 11 /* Document fragment */: {\n      // @ts-expect-error TypeScript is wrong.\n      return root(node, options)\n    }\n\n    default: {\n      return undefined\n    }\n  }\n}\n\n/**\n * Transform a document.\n *\n * @param {Document | DocumentFragment} node\n *   DOM node to transform.\n * @param {Options} options\n *   Configuration.\n * @returns {HastRoot}\n *   Equivalent hast node.\n */\nfunction root(node, options) {\n  return {type: 'root', children: all(node, options)}\n}\n\n/**\n * Transform a doctype.\n *\n * @returns {HastDoctype}\n *   Equivalent hast node.\n */\nfunction doctype() {\n  // @ts-expect-error hast types out of date.\n  return {type: 'doctype'}\n}\n\n/**\n * Transform a text.\n *\n * @param {Text} node\n *   DOM node to transform.\n * @returns {HastText}\n *   Equivalent hast node.\n */\nfunction text(node) {\n  return {type: 'text', value: node.nodeValue || ''}\n}\n\n/**\n * Transform a comment.\n *\n * @param {Comment} node\n *   DOM node to transform.\n * @returns {HastComment}\n *   Equivalent hast node.\n */\nfunction comment(node) {\n  return {type: 'comment', value: node.nodeValue || ''}\n}\n\n/**\n * Transform an element.\n *\n * @param {Element} node\n *   DOM node to transform.\n * @param {Options} options\n *   Configuration.\n * @returns {HastElement}\n *   Equivalent hast node.\n */\nfunction element(node, options) {\n  const space = node.namespaceURI\n  const fn = space === web_namespaces__WEBPACK_IMPORTED_MODULE_0__.webNamespaces.svg ? hastscript__WEBPACK_IMPORTED_MODULE_1__.s : hastscript__WEBPACK_IMPORTED_MODULE_2__.h\n  const tagName =\n    space === web_namespaces__WEBPACK_IMPORTED_MODULE_0__.webNamespaces.html ? node.tagName.toLowerCase() : node.tagName\n  /** @type {DocumentFragment | Element} */\n  const content =\n    // @ts-expect-error Types are wrong.\n    space === web_namespaces__WEBPACK_IMPORTED_MODULE_0__.webNamespaces.html && tagName === 'template' ? node.content : node\n  const attributes = node.getAttributeNames()\n  /** @type {Record<string, string>} */\n  const props = {}\n  let index = -1\n\n  while (++index < attributes.length) {\n    props[attributes[index]] = node.getAttribute(attributes[index]) || ''\n  }\n\n  return fn(tagName, props, all(content, options))\n}\n\n/**\n * Transform child nodes in a parent.\n *\n * @param {Document | DocumentFragment | Element} node\n *   DOM node to transform.\n * @param {Options} options\n *   Configuration.\n * @returns {Array<HastContent>}\n *   Equivalent hast nodes.\n */\nfunction all(node, options) {\n  const nodes = node.childNodes\n  /** @type {Array<HastContent>} */\n  const children = []\n  let index = -1\n\n  while (++index < nodes.length) {\n    const child = transform(nodes[index], options)\n\n    if (child !== undefined) {\n      // @ts-expect-error Assume no document inside document.\n      children.push(child)\n    }\n  }\n\n  return children\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/hast-util-from-dom/lib/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/hast-util-parse-selector/lib/index.js":
/*!****************************************************************!*\
  !*** ../../node_modules/hast-util-parse-selector/lib/index.js ***!
  \****************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   parseSelector: function() { return /* binding */ parseSelector; }\n/* harmony export */ });\n/**\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('hast').Element} Element\n */\n\nconst search = /[#.]/g\n\n/**\n * Create a hast element from a simple CSS selector.\n *\n * @template {string} Selector\n *   Type of selector.\n * @template {string} [DefaultTagName='div']\n *   Type of default tag name.\n * @param {Selector | null | undefined} [selector]\n *   Simple CSS selector.\n *\n *   Can contain a tag name (`foo`), classes (`.bar`), and an ID (`#baz`).\n *   Multiple classes are allowed.\n *   Uses the last ID if multiple IDs are found.\n * @param {DefaultTagName | null | undefined} [defaultTagName='div']\n *   Tag name to use if `selector` does not specify one (default: `'div'`).\n * @returns {Element & {tagName: import('./extract.js').ExtractTagName<Selector, DefaultTagName>}}\n *   Built element.\n */\nfunction parseSelector(selector, defaultTagName) {\n  const value = selector || ''\n  /** @type {Properties} */\n  const props = {}\n  let start = 0\n  /** @type {string | undefined} */\n  let previous\n  /** @type {string | undefined} */\n  let tagName\n\n  while (start < value.length) {\n    search.lastIndex = start\n    const match = search.exec(value)\n    const subvalue = value.slice(start, match ? match.index : value.length)\n\n    if (subvalue) {\n      if (!previous) {\n        tagName = subvalue\n      } else if (previous === '#') {\n        props.id = subvalue\n      } else if (Array.isArray(props.className)) {\n        props.className.push(subvalue)\n      } else {\n        props.className = [subvalue]\n      }\n\n      start += subvalue.length\n    }\n\n    if (match) {\n      previous = match[0]\n      start++\n    }\n  }\n\n  return {\n    type: 'element',\n    // @ts-expect-error: fine.\n    tagName: tagName || defaultTagName || 'div',\n    properties: props,\n    children: []\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/hast-util-parse-selector/lib/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/hastscript/lib/core.js":
/*!*************************************************!*\
  !*** ../../node_modules/hastscript/lib/core.js ***!
  \*************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   core: function() { return /* binding */ core; }\n/* harmony export */ });\n/* harmony import */ var property_information__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! property-information */ \"(app-pages-browser)/../../node_modules/property-information/lib/find.js\");\n/* harmony import */ var property_information__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! property-information */ \"(app-pages-browser)/../../node_modules/property-information/lib/normalize.js\");\n/* harmony import */ var hast_util_parse_selector__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! hast-util-parse-selector */ \"(app-pages-browser)/../../node_modules/hast-util-parse-selector/lib/index.js\");\n/* harmony import */ var space_separated_tokens__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! space-separated-tokens */ \"(app-pages-browser)/../../node_modules/space-separated-tokens/index.js\");\n/* harmony import */ var comma_separated_tokens__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! comma-separated-tokens */ \"(app-pages-browser)/../../node_modules/comma-separated-tokens/index.js\");\n/**\n * @typedef {import('hast').Root} Root\n * @typedef {import('hast').Content} Content\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('property-information').Info} Info\n * @typedef {import('property-information').Schema} Schema\n */\n\n/**\n * @typedef {Content | Root} Node\n *   Any concrete `hast` node.\n * @typedef {Root | Element} HResult\n *   Result from a `h` (or `s`) call.\n *\n * @typedef {string | number} HStyleValue\n *   Value for a CSS style field.\n * @typedef {Record<string, HStyleValue>} HStyle\n *   Supported value of a `style` prop.\n * @typedef {string | number | boolean | null | undefined} HPrimitiveValue\n *   Primitive property value.\n * @typedef {Array<string | number>} HArrayValue\n *   List of property values for space- or comma separated values (such as `className`).\n * @typedef {HPrimitiveValue | HArrayValue} HPropertyValue\n *   Primitive value or list value.\n * @typedef {{[property: string]: HPropertyValue | HStyle}} HProperties\n *   Acceptable value for element properties.\n *\n * @typedef {string | number | null | undefined} HPrimitiveChild\n *   Primitive children, either ignored (nullish), or turned into text nodes.\n * @typedef {Array<Node | HPrimitiveChild>} HArrayChild\n *   List of children.\n * @typedef {Node | HPrimitiveChild | HArrayChild} HChild\n *   Acceptable child value.\n */\n\n\n\n\n\n\nconst buttonTypes = new Set(['menu', 'submit', 'reset', 'button'])\n\nconst own = {}.hasOwnProperty\n\n/**\n * @param {Schema} schema\n * @param {string} defaultTagName\n * @param {Array<string>} [caseSensitive]\n */\nfunction core(schema, defaultTagName, caseSensitive) {\n  const adjust = caseSensitive && createAdjustMap(caseSensitive)\n\n  const h =\n    /**\n     * @type {{\n     *   (): Root\n     *   (selector: null | undefined, ...children: Array<HChild>): Root\n     *   (selector: string, properties?: HProperties, ...children: Array<HChild>): Element\n     *   (selector: string, ...children: Array<HChild>): Element\n     * }}\n     */\n    (\n      /**\n       * Hyperscript compatible DSL for creating virtual hast trees.\n       *\n       * @param {string | null} [selector]\n       * @param {HProperties | HChild} [properties]\n       * @param {Array<HChild>} children\n       * @returns {HResult}\n       */\n      function (selector, properties, ...children) {\n        let index = -1\n        /** @type {HResult} */\n        let node\n\n        if (selector === undefined || selector === null) {\n          node = {type: 'root', children: []}\n          // @ts-expect-error Properties are not supported for roots.\n          children.unshift(properties)\n        } else {\n          node = (0,hast_util_parse_selector__WEBPACK_IMPORTED_MODULE_0__.parseSelector)(selector, defaultTagName)\n          // Normalize the name.\n          node.tagName = node.tagName.toLowerCase()\n          if (adjust && own.call(adjust, node.tagName)) {\n            node.tagName = adjust[node.tagName]\n          }\n\n          // Handle props.\n          if (isProperties(properties, node.tagName)) {\n            /** @type {string} */\n            let key\n\n            for (key in properties) {\n              if (own.call(properties, key)) {\n                // @ts-expect-error `node.properties` is set.\n                addProperty(schema, node.properties, key, properties[key])\n              }\n            }\n          } else {\n            children.unshift(properties)\n          }\n        }\n\n        // Handle children.\n        while (++index < children.length) {\n          addChild(node.children, children[index])\n        }\n\n        if (node.type === 'element' && node.tagName === 'template') {\n          node.content = {type: 'root', children: node.children}\n          node.children = []\n        }\n\n        return node\n      }\n    )\n\n  return h\n}\n\n/**\n * @param {HProperties | HChild} value\n * @param {string} name\n * @returns {value is HProperties}\n */\nfunction isProperties(value, name) {\n  if (\n    value === null ||\n    value === undefined ||\n    typeof value !== 'object' ||\n    Array.isArray(value)\n  ) {\n    return false\n  }\n\n  if (name === 'input' || !value.type || typeof value.type !== 'string') {\n    return true\n  }\n\n  if ('children' in value && Array.isArray(value.children)) {\n    return false\n  }\n\n  if (name === 'button') {\n    return buttonTypes.has(value.type.toLowerCase())\n  }\n\n  return !('value' in value)\n}\n\n/**\n * @param {Schema} schema\n * @param {Properties} properties\n * @param {string} key\n * @param {HStyle | HPropertyValue} value\n * @returns {void}\n */\nfunction addProperty(schema, properties, key, value) {\n  const info = (0,property_information__WEBPACK_IMPORTED_MODULE_1__.find)(schema, key)\n  let index = -1\n  /** @type {HPropertyValue} */\n  let result\n\n  // Ignore nullish and NaN values.\n  if (value === undefined || value === null) return\n\n  if (typeof value === 'number') {\n    // Ignore NaN.\n    if (Number.isNaN(value)) return\n\n    result = value\n  }\n  // Booleans.\n  else if (typeof value === 'boolean') {\n    result = value\n  }\n  // Handle list values.\n  else if (typeof value === 'string') {\n    if (info.spaceSeparated) {\n      result = (0,space_separated_tokens__WEBPACK_IMPORTED_MODULE_2__.parse)(value)\n    } else if (info.commaSeparated) {\n      result = (0,comma_separated_tokens__WEBPACK_IMPORTED_MODULE_3__.parse)(value)\n    } else if (info.commaOrSpaceSeparated) {\n      result = (0,space_separated_tokens__WEBPACK_IMPORTED_MODULE_2__.parse)((0,comma_separated_tokens__WEBPACK_IMPORTED_MODULE_3__.parse)(value).join(' '))\n    } else {\n      result = parsePrimitive(info, info.property, value)\n    }\n  } else if (Array.isArray(value)) {\n    result = value.concat()\n  } else {\n    result = info.property === 'style' ? style(value) : String(value)\n  }\n\n  if (Array.isArray(result)) {\n    /** @type {Array<string | number>} */\n    const finalResult = []\n\n    while (++index < result.length) {\n      // @ts-expect-error Assume no booleans in array.\n      finalResult[index] = parsePrimitive(info, info.property, result[index])\n    }\n\n    result = finalResult\n  }\n\n  // Class names (which can be added both on the `selector` and here).\n  if (info.property === 'className' && Array.isArray(properties.className)) {\n    // @ts-expect-error Assume no booleans in `className`.\n    result = properties.className.concat(result)\n  }\n\n  properties[info.property] = result\n}\n\n/**\n * @param {Array<Content>} nodes\n * @param {HChild} value\n * @returns {void}\n */\nfunction addChild(nodes, value) {\n  let index = -1\n\n  if (value === undefined || value === null) {\n    // Empty.\n  } else if (typeof value === 'string' || typeof value === 'number') {\n    nodes.push({type: 'text', value: String(value)})\n  } else if (Array.isArray(value)) {\n    while (++index < value.length) {\n      addChild(nodes, value[index])\n    }\n  } else if (typeof value === 'object' && 'type' in value) {\n    if (value.type === 'root') {\n      addChild(nodes, value.children)\n    } else {\n      nodes.push(value)\n    }\n  } else {\n    throw new Error('Expected node, nodes, or string, got `' + value + '`')\n  }\n}\n\n/**\n * Parse a single primitives.\n *\n * @param {Info} info\n * @param {string} name\n * @param {HPrimitiveValue} value\n * @returns {HPrimitiveValue}\n */\nfunction parsePrimitive(info, name, value) {\n  if (typeof value === 'string') {\n    if (info.number && value && !Number.isNaN(Number(value))) {\n      return Number(value)\n    }\n\n    if (\n      (info.boolean || info.overloadedBoolean) &&\n      (value === '' || (0,property_information__WEBPACK_IMPORTED_MODULE_4__.normalize)(value) === (0,property_information__WEBPACK_IMPORTED_MODULE_4__.normalize)(name))\n    ) {\n      return true\n    }\n  }\n\n  return value\n}\n\n/**\n * Serialize a `style` object as a string.\n *\n * @param {HStyle} value\n *   Style object.\n * @returns {string}\n *   CSS string.\n */\nfunction style(value) {\n  /** @type {Array<string>} */\n  const result = []\n  /** @type {string} */\n  let key\n\n  for (key in value) {\n    if (own.call(value, key)) {\n      result.push([key, value[key]].join(': '))\n    }\n  }\n\n  return result.join('; ')\n}\n\n/**\n * Create a map to adjust casing.\n *\n * @param {Array<string>} values\n *   List of properly cased keys.\n * @returns {Record<string, string>}\n *   Map of lowercase keys to uppercase keys.\n */\nfunction createAdjustMap(values) {\n  /** @type {Record<string, string>} */\n  const result = {}\n  let index = -1\n\n  while (++index < values.length) {\n    result[values[index].toLowerCase()] = values[index]\n  }\n\n  return result\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/hastscript/lib/core.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/hastscript/lib/html.js":
/*!*************************************************!*\
  !*** ../../node_modules/hastscript/lib/html.js ***!
  \*************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   h: function() { return /* binding */ h; }\n/* harmony export */ });\n/* harmony import */ var property_information__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! property-information */ \"(app-pages-browser)/../../node_modules/property-information/index.js\");\n/* harmony import */ var _core_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./core.js */ \"(app-pages-browser)/../../node_modules/hastscript/lib/core.js\");\n/**\n * @typedef {import('./core.js').HChild} Child\n *   Acceptable child value.\n * @typedef {import('./core.js').HProperties} Properties\n *   Acceptable value for element properties.\n * @typedef {import('./core.js').HResult} Result\n *   Result from a `h` (or `s`) call.\n *\n * @typedef {import('./jsx-classic.js').Element} h.JSX.Element\n * @typedef {import('./jsx-classic.js').IntrinsicAttributes} h.JSX.IntrinsicAttributes\n * @typedef {import('./jsx-classic.js').IntrinsicElements} h.JSX.IntrinsicElements\n * @typedef {import('./jsx-classic.js').ElementChildrenAttribute} h.JSX.ElementChildrenAttribute\n */\n\n\n\n\nconst h = (0,_core_js__WEBPACK_IMPORTED_MODULE_0__.core)(property_information__WEBPACK_IMPORTED_MODULE_1__.html, 'div')\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvaGFzdHNjcmlwdC9saWIvaHRtbC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBQTtBQUNBLGFBQWEsNEJBQTRCO0FBQ3pDO0FBQ0EsYUFBYSxpQ0FBaUM7QUFDOUM7QUFDQSxhQUFhLDZCQUE2QjtBQUMxQztBQUNBO0FBQ0EsYUFBYSxvQ0FBb0M7QUFDakQsYUFBYSxnREFBZ0Q7QUFDN0QsYUFBYSw4Q0FBOEM7QUFDM0QsYUFBYSxxREFBcUQ7QUFDbEU7O0FBRXlDO0FBQ1g7O0FBRXZCLFVBQVUsOENBQUksQ0FBQyxzREFBSSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vbm9kZV9tb2R1bGVzL2hhc3RzY3JpcHQvbGliL2h0bWwuanM/ZTZhZiJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4vY29yZS5qcycpLkhDaGlsZH0gQ2hpbGRcbiAqICAgQWNjZXB0YWJsZSBjaGlsZCB2YWx1ZS5cbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4vY29yZS5qcycpLkhQcm9wZXJ0aWVzfSBQcm9wZXJ0aWVzXG4gKiAgIEFjY2VwdGFibGUgdmFsdWUgZm9yIGVsZW1lbnQgcHJvcGVydGllcy5cbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4vY29yZS5qcycpLkhSZXN1bHR9IFJlc3VsdFxuICogICBSZXN1bHQgZnJvbSBhIGBoYCAob3IgYHNgKSBjYWxsLlxuICpcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4vanN4LWNsYXNzaWMuanMnKS5FbGVtZW50fSBoLkpTWC5FbGVtZW50XG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuL2pzeC1jbGFzc2ljLmpzJykuSW50cmluc2ljQXR0cmlidXRlc30gaC5KU1guSW50cmluc2ljQXR0cmlidXRlc1xuICogQHR5cGVkZWYge2ltcG9ydCgnLi9qc3gtY2xhc3NpYy5qcycpLkludHJpbnNpY0VsZW1lbnRzfSBoLkpTWC5JbnRyaW5zaWNFbGVtZW50c1xuICogQHR5cGVkZWYge2ltcG9ydCgnLi9qc3gtY2xhc3NpYy5qcycpLkVsZW1lbnRDaGlsZHJlbkF0dHJpYnV0ZX0gaC5KU1guRWxlbWVudENoaWxkcmVuQXR0cmlidXRlXG4gKi9cblxuaW1wb3J0IHtodG1sfSBmcm9tICdwcm9wZXJ0eS1pbmZvcm1hdGlvbidcbmltcG9ydCB7Y29yZX0gZnJvbSAnLi9jb3JlLmpzJ1xuXG5leHBvcnQgY29uc3QgaCA9IGNvcmUoaHRtbCwgJ2RpdicpXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/hastscript/lib/html.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/hastscript/lib/svg-case-sensitive-tag-names.js":
/*!*************************************************************************!*\
  !*** ../../node_modules/hastscript/lib/svg-case-sensitive-tag-names.js ***!
  \*************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   svgCaseSensitiveTagNames: function() { return /* binding */ svgCaseSensitiveTagNames; }\n/* harmony export */ });\nconst svgCaseSensitiveTagNames = [\n  'altGlyph',\n  'altGlyphDef',\n  'altGlyphItem',\n  'animateColor',\n  'animateMotion',\n  'animateTransform',\n  'clipPath',\n  'feBlend',\n  'feColorMatrix',\n  'feComponentTransfer',\n  'feComposite',\n  'feConvolveMatrix',\n  'feDiffuseLighting',\n  'feDisplacementMap',\n  'feDistantLight',\n  'feDropShadow',\n  'feFlood',\n  'feFuncA',\n  'feFuncB',\n  'feFuncG',\n  'feFuncR',\n  'feGaussianBlur',\n  'feImage',\n  'feMerge',\n  'feMergeNode',\n  'feMorphology',\n  'feOffset',\n  'fePointLight',\n  'feSpecularLighting',\n  'feSpotLight',\n  'feTile',\n  'feTurbulence',\n  'foreignObject',\n  'glyphRef',\n  'linearGradient',\n  'radialGradient',\n  'solidColor',\n  'textArea',\n  'textPath'\n]\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvaGFzdHNjcmlwdC9saWIvc3ZnLWNhc2Utc2Vuc2l0aXZlLXRhZy1uYW1lcy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vbm9kZV9tb2R1bGVzL2hhc3RzY3JpcHQvbGliL3N2Zy1jYXNlLXNlbnNpdGl2ZS10YWctbmFtZXMuanM/YThmZCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3Qgc3ZnQ2FzZVNlbnNpdGl2ZVRhZ05hbWVzID0gW1xuICAnYWx0R2x5cGgnLFxuICAnYWx0R2x5cGhEZWYnLFxuICAnYWx0R2x5cGhJdGVtJyxcbiAgJ2FuaW1hdGVDb2xvcicsXG4gICdhbmltYXRlTW90aW9uJyxcbiAgJ2FuaW1hdGVUcmFuc2Zvcm0nLFxuICAnY2xpcFBhdGgnLFxuICAnZmVCbGVuZCcsXG4gICdmZUNvbG9yTWF0cml4JyxcbiAgJ2ZlQ29tcG9uZW50VHJhbnNmZXInLFxuICAnZmVDb21wb3NpdGUnLFxuICAnZmVDb252b2x2ZU1hdHJpeCcsXG4gICdmZURpZmZ1c2VMaWdodGluZycsXG4gICdmZURpc3BsYWNlbWVudE1hcCcsXG4gICdmZURpc3RhbnRMaWdodCcsXG4gICdmZURyb3BTaGFkb3cnLFxuICAnZmVGbG9vZCcsXG4gICdmZUZ1bmNBJyxcbiAgJ2ZlRnVuY0InLFxuICAnZmVGdW5jRycsXG4gICdmZUZ1bmNSJyxcbiAgJ2ZlR2F1c3NpYW5CbHVyJyxcbiAgJ2ZlSW1hZ2UnLFxuICAnZmVNZXJnZScsXG4gICdmZU1lcmdlTm9kZScsXG4gICdmZU1vcnBob2xvZ3knLFxuICAnZmVPZmZzZXQnLFxuICAnZmVQb2ludExpZ2h0JyxcbiAgJ2ZlU3BlY3VsYXJMaWdodGluZycsXG4gICdmZVNwb3RMaWdodCcsXG4gICdmZVRpbGUnLFxuICAnZmVUdXJidWxlbmNlJyxcbiAgJ2ZvcmVpZ25PYmplY3QnLFxuICAnZ2x5cGhSZWYnLFxuICAnbGluZWFyR3JhZGllbnQnLFxuICAncmFkaWFsR3JhZGllbnQnLFxuICAnc29saWRDb2xvcicsXG4gICd0ZXh0QXJlYScsXG4gICd0ZXh0UGF0aCdcbl1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/hastscript/lib/svg-case-sensitive-tag-names.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/hastscript/lib/svg.js":
/*!************************************************!*\
  !*** ../../node_modules/hastscript/lib/svg.js ***!
  \************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   s: function() { return /* binding */ s; }\n/* harmony export */ });\n/* harmony import */ var property_information__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! property-information */ \"(app-pages-browser)/../../node_modules/property-information/index.js\");\n/* harmony import */ var _core_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./core.js */ \"(app-pages-browser)/../../node_modules/hastscript/lib/core.js\");\n/* harmony import */ var _svg_case_sensitive_tag_names_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./svg-case-sensitive-tag-names.js */ \"(app-pages-browser)/../../node_modules/hastscript/lib/svg-case-sensitive-tag-names.js\");\n/**\n * @typedef {import('./core.js').HChild} Child\n *   Acceptable child value.\n * @typedef {import('./core.js').HProperties} Properties\n *   Acceptable value for element properties.\n * @typedef {import('./core.js').HResult} Result\n *   Result from a `h` (or `s`) call.\n *\n * @typedef {import('./jsx-classic.js').Element} s.JSX.Element\n * @typedef {import('./jsx-classic.js').IntrinsicAttributes} s.JSX.IntrinsicAttributes\n * @typedef {import('./jsx-classic.js').IntrinsicElements} s.JSX.IntrinsicElements\n * @typedef {import('./jsx-classic.js').ElementChildrenAttribute} s.JSX.ElementChildrenAttribute\n */\n\n\n\n\n\nconst s = (0,_core_js__WEBPACK_IMPORTED_MODULE_0__.core)(property_information__WEBPACK_IMPORTED_MODULE_1__.svg, 'g', _svg_case_sensitive_tag_names_js__WEBPACK_IMPORTED_MODULE_2__.svgCaseSensitiveTagNames)\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvaGFzdHNjcmlwdC9saWIvc3ZnLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBQTtBQUNBLGFBQWEsNEJBQTRCO0FBQ3pDO0FBQ0EsYUFBYSxpQ0FBaUM7QUFDOUM7QUFDQSxhQUFhLDZCQUE2QjtBQUMxQztBQUNBO0FBQ0EsYUFBYSxvQ0FBb0M7QUFDakQsYUFBYSxnREFBZ0Q7QUFDN0QsYUFBYSw4Q0FBOEM7QUFDM0QsYUFBYSxxREFBcUQ7QUFDbEU7O0FBRXdDO0FBQ1Y7QUFDNEM7O0FBRW5FLFVBQVUsOENBQUksQ0FBQyxxREFBRyxPQUFPLHNGQUF3QiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vbm9kZV9tb2R1bGVzL2hhc3RzY3JpcHQvbGliL3N2Zy5qcz9mMWFiIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnLi9jb3JlLmpzJykuSENoaWxkfSBDaGlsZFxuICogICBBY2NlcHRhYmxlIGNoaWxkIHZhbHVlLlxuICogQHR5cGVkZWYge2ltcG9ydCgnLi9jb3JlLmpzJykuSFByb3BlcnRpZXN9IFByb3BlcnRpZXNcbiAqICAgQWNjZXB0YWJsZSB2YWx1ZSBmb3IgZWxlbWVudCBwcm9wZXJ0aWVzLlxuICogQHR5cGVkZWYge2ltcG9ydCgnLi9jb3JlLmpzJykuSFJlc3VsdH0gUmVzdWx0XG4gKiAgIFJlc3VsdCBmcm9tIGEgYGhgIChvciBgc2ApIGNhbGwuXG4gKlxuICogQHR5cGVkZWYge2ltcG9ydCgnLi9qc3gtY2xhc3NpYy5qcycpLkVsZW1lbnR9IHMuSlNYLkVsZW1lbnRcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4vanN4LWNsYXNzaWMuanMnKS5JbnRyaW5zaWNBdHRyaWJ1dGVzfSBzLkpTWC5JbnRyaW5zaWNBdHRyaWJ1dGVzXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuL2pzeC1jbGFzc2ljLmpzJykuSW50cmluc2ljRWxlbWVudHN9IHMuSlNYLkludHJpbnNpY0VsZW1lbnRzXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuL2pzeC1jbGFzc2ljLmpzJykuRWxlbWVudENoaWxkcmVuQXR0cmlidXRlfSBzLkpTWC5FbGVtZW50Q2hpbGRyZW5BdHRyaWJ1dGVcbiAqL1xuXG5pbXBvcnQge3N2Z30gZnJvbSAncHJvcGVydHktaW5mb3JtYXRpb24nXG5pbXBvcnQge2NvcmV9IGZyb20gJy4vY29yZS5qcydcbmltcG9ydCB7c3ZnQ2FzZVNlbnNpdGl2ZVRhZ05hbWVzfSBmcm9tICcuL3N2Zy1jYXNlLXNlbnNpdGl2ZS10YWctbmFtZXMuanMnXG5cbmV4cG9ydCBjb25zdCBzID0gY29yZShzdmcsICdnJywgc3ZnQ2FzZVNlbnNpdGl2ZVRhZ05hbWVzKVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/hastscript/lib/svg.js\n"));

/***/ })

}]);