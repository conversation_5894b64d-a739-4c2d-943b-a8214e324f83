"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/micromark-factory-whitespace";
exports.ids = ["vendor-chunks/micromark-factory-whitespace"];
exports.modules = {

/***/ "(ssr)/../../node_modules/micromark-factory-whitespace/dev/index.js":
/*!********************************************************************!*\
  !*** ../../node_modules/micromark-factory-whitespace/dev/index.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   factoryWhitespace: () => (/* binding */ factoryWhitespace)\n/* harmony export */ });\n/* harmony import */ var micromark_factory_space__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-factory-space */ \"(ssr)/../../node_modules/micromark-factory-whitespace/node_modules/micromark-factory-space/dev/index.js\");\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-character */ \"(ssr)/../../node_modules/micromark-factory-whitespace/node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-symbol/types.js */ \"(ssr)/../../node_modules/micromark-factory-whitespace/node_modules/micromark-util-symbol/types.js\");\n/**\n * @typedef {import('micromark-util-types').Effects} Effects\n * @typedef {import('micromark-util-types').State} State\n */\n\n\n\n\n\n/**\n * Parse spaces and tabs.\n *\n * There is no `nok` parameter:\n *\n * *   line endings or spaces in markdown are often optional, in which case this\n *     factory can be used and `ok` will be switched to whether spaces were found\n *     or not\n * *   one line ending or space can be detected with\n *     `markdownLineEndingOrSpace(code)` right before using `factoryWhitespace`\n *\n * @param {Effects} effects\n *   Context.\n * @param {State} ok\n *   State switched to when successful.\n * @returns\n *   Start state.\n */\nfunction factoryWhitespace(effects, ok) {\n  /** @type {boolean} */\n  let seen\n\n  return start\n\n  /** @type {State} */\n  function start(code) {\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_0__.markdownLineEnding)(code)) {\n      effects.enter(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.lineEnding)\n      effects.consume(code)\n      effects.exit(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.lineEnding)\n      seen = true\n      return start\n    }\n\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_0__.markdownSpace)(code)) {\n      return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_2__.factorySpace)(\n        effects,\n        start,\n        seen ? micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.linePrefix : micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.lineSuffix\n      )(code)\n    }\n\n    return ok(code)\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/micromark-factory-whitespace/dev/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/micromark-factory-whitespace/node_modules/micromark-factory-space/dev/index.js":
/*!*********************************************************************************************************!*\
  !*** ../../node_modules/micromark-factory-whitespace/node_modules/micromark-factory-space/dev/index.js ***!
  \*********************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   factorySpace: () => (/* binding */ factorySpace)\n/* harmony export */ });\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-character */ \"(ssr)/../../node_modules/micromark-factory-whitespace/node_modules/micromark-util-character/dev/index.js\");\n/**\n * @typedef {import('micromark-util-types').Effects} Effects\n * @typedef {import('micromark-util-types').State} State\n * @typedef {import('micromark-util-types').TokenType} TokenType\n */\n\n\n\n// To do: implement `spaceOrTab`, `spaceOrTabMinMax`, `spaceOrTabWithOptions`.\n\n/**\n * Parse spaces and tabs.\n *\n * There is no `nok` parameter:\n *\n * *   spaces in markdown are often optional, in which case this factory can be\n *     used and `ok` will be switched to whether spaces were found or not\n * *   one line ending or space can be detected with `markdownSpace(code)` right\n *     before using `factorySpace`\n *\n * ###### Examples\n *\n * Where `␉` represents a tab (plus how much it expands) and `␠` represents a\n * single space.\n *\n * ```markdown\n * ␉\n * ␠␠␠␠\n * ␉␠\n * ```\n *\n * @param {Effects} effects\n *   Context.\n * @param {State} ok\n *   State switched to when successful.\n * @param {TokenType} type\n *   Type (`' \\t'`).\n * @param {number | undefined} [max=Infinity]\n *   Max (exclusive).\n * @returns\n *   Start state.\n */\nfunction factorySpace(effects, ok, type, max) {\n  const limit = max ? max - 1 : Number.POSITIVE_INFINITY\n  let size = 0\n\n  return start\n\n  /** @type {State} */\n  function start(code) {\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_0__.markdownSpace)(code)) {\n      effects.enter(type)\n      return prefix(code)\n    }\n\n    return ok(code)\n  }\n\n  /** @type {State} */\n  function prefix(code) {\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_0__.markdownSpace)(code) && size++ < limit) {\n      effects.consume(code)\n      return prefix\n    }\n\n    effects.exit(type)\n    return ok(code)\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/micromark-factory-whitespace/node_modules/micromark-factory-space/dev/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/micromark-factory-whitespace/node_modules/micromark-util-character/dev/index.js":
/*!**********************************************************************************************************!*\
  !*** ../../node_modules/micromark-factory-whitespace/node_modules/micromark-util-character/dev/index.js ***!
  \**********************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   asciiAlpha: () => (/* binding */ asciiAlpha),\n/* harmony export */   asciiAlphanumeric: () => (/* binding */ asciiAlphanumeric),\n/* harmony export */   asciiAtext: () => (/* binding */ asciiAtext),\n/* harmony export */   asciiControl: () => (/* binding */ asciiControl),\n/* harmony export */   asciiDigit: () => (/* binding */ asciiDigit),\n/* harmony export */   asciiHexDigit: () => (/* binding */ asciiHexDigit),\n/* harmony export */   asciiPunctuation: () => (/* binding */ asciiPunctuation),\n/* harmony export */   markdownLineEnding: () => (/* binding */ markdownLineEnding),\n/* harmony export */   markdownLineEndingOrSpace: () => (/* binding */ markdownLineEndingOrSpace),\n/* harmony export */   markdownSpace: () => (/* binding */ markdownSpace),\n/* harmony export */   unicodePunctuation: () => (/* binding */ unicodePunctuation),\n/* harmony export */   unicodeWhitespace: () => (/* binding */ unicodeWhitespace)\n/* harmony export */ });\n/* harmony import */ var micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-symbol/codes.js */ \"(ssr)/../../node_modules/micromark-factory-whitespace/node_modules/micromark-util-symbol/codes.js\");\n/* harmony import */ var _lib_unicode_punctuation_regex_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./lib/unicode-punctuation-regex.js */ \"(ssr)/../../node_modules/micromark-factory-whitespace/node_modules/micromark-util-character/dev/lib/unicode-punctuation-regex.js\");\n/**\n * @typedef {import('micromark-util-types').Code} Code\n */\n\n\n\n\n/**\n * Check whether the character code represents an ASCII alpha (`a` through `z`,\n * case insensitive).\n *\n * An **ASCII alpha** is an ASCII upper alpha or ASCII lower alpha.\n *\n * An **ASCII upper alpha** is a character in the inclusive range U+0041 (`A`)\n * to U+005A (`Z`).\n *\n * An **ASCII lower alpha** is a character in the inclusive range U+0061 (`a`)\n * to U+007A (`z`).\n *\n * @param code\n *   Code.\n * @returns\n *   Whether it matches.\n */\nconst asciiAlpha = regexCheck(/[A-Za-z]/)\n\n/**\n * Check whether the character code represents an ASCII alphanumeric (`a`\n * through `z`, case insensitive, or `0` through `9`).\n *\n * An **ASCII alphanumeric** is an ASCII digit (see `asciiDigit`) or ASCII alpha\n * (see `asciiAlpha`).\n *\n * @param code\n *   Code.\n * @returns\n *   Whether it matches.\n */\nconst asciiAlphanumeric = regexCheck(/[\\dA-Za-z]/)\n\n/**\n * Check whether the character code represents an ASCII atext.\n *\n * atext is an ASCII alphanumeric (see `asciiAlphanumeric`), or a character in\n * the inclusive ranges U+0023 NUMBER SIGN (`#`) to U+0027 APOSTROPHE (`'`),\n * U+002A ASTERISK (`*`), U+002B PLUS SIGN (`+`), U+002D DASH (`-`), U+002F\n * SLASH (`/`), U+003D EQUALS TO (`=`), U+003F QUESTION MARK (`?`), U+005E\n * CARET (`^`) to U+0060 GRAVE ACCENT (`` ` ``), or U+007B LEFT CURLY BRACE\n * (`{`) to U+007E TILDE (`~`).\n *\n * See:\n * **\\[RFC5322]**:\n * [Internet Message Format](https://tools.ietf.org/html/rfc5322).\n * P. Resnick.\n * IETF.\n *\n * @param code\n *   Code.\n * @returns\n *   Whether it matches.\n */\nconst asciiAtext = regexCheck(/[#-'*+\\--9=?A-Z^-~]/)\n\n/**\n * Check whether a character code is an ASCII control character.\n *\n * An **ASCII control** is a character in the inclusive range U+0000 NULL (NUL)\n * to U+001F (US), or U+007F (DEL).\n *\n * @param {Code} code\n *   Code.\n * @returns {boolean}\n *   Whether it matches.\n */\nfunction asciiControl(code) {\n  return (\n    // Special whitespace codes (which have negative values), C0 and Control\n    // character DEL\n    code !== null && (code < micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.space || code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.del)\n  )\n}\n\n/**\n * Check whether the character code represents an ASCII digit (`0` through `9`).\n *\n * An **ASCII digit** is a character in the inclusive range U+0030 (`0`) to\n * U+0039 (`9`).\n *\n * @param code\n *   Code.\n * @returns\n *   Whether it matches.\n */\nconst asciiDigit = regexCheck(/\\d/)\n\n/**\n * Check whether the character code represents an ASCII hex digit (`a` through\n * `f`, case insensitive, or `0` through `9`).\n *\n * An **ASCII hex digit** is an ASCII digit (see `asciiDigit`), ASCII upper hex\n * digit, or an ASCII lower hex digit.\n *\n * An **ASCII upper hex digit** is a character in the inclusive range U+0041\n * (`A`) to U+0046 (`F`).\n *\n * An **ASCII lower hex digit** is a character in the inclusive range U+0061\n * (`a`) to U+0066 (`f`).\n *\n * @param code\n *   Code.\n * @returns\n *   Whether it matches.\n */\nconst asciiHexDigit = regexCheck(/[\\dA-Fa-f]/)\n\n/**\n * Check whether the character code represents ASCII punctuation.\n *\n * An **ASCII punctuation** is a character in the inclusive ranges U+0021\n * EXCLAMATION MARK (`!`) to U+002F SLASH (`/`), U+003A COLON (`:`) to U+0040 AT\n * SIGN (`@`), U+005B LEFT SQUARE BRACKET (`[`) to U+0060 GRAVE ACCENT\n * (`` ` ``), or U+007B LEFT CURLY BRACE (`{`) to U+007E TILDE (`~`).\n *\n * @param code\n *   Code.\n * @returns\n *   Whether it matches.\n */\nconst asciiPunctuation = regexCheck(/[!-/:-@[-`{-~]/)\n\n/**\n * Check whether a character code is a markdown line ending.\n *\n * A **markdown line ending** is the virtual characters M-0003 CARRIAGE RETURN\n * LINE FEED (CRLF), M-0004 LINE FEED (LF) and M-0005 CARRIAGE RETURN (CR).\n *\n * In micromark, the actual character U+000A LINE FEED (LF) and U+000D CARRIAGE\n * RETURN (CR) are replaced by these virtual characters depending on whether\n * they occurred together.\n *\n * @param {Code} code\n *   Code.\n * @returns {boolean}\n *   Whether it matches.\n */\nfunction markdownLineEnding(code) {\n  return code !== null && code < micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.horizontalTab\n}\n\n/**\n * Check whether a character code is a markdown line ending (see\n * `markdownLineEnding`) or markdown space (see `markdownSpace`).\n *\n * @param {Code} code\n *   Code.\n * @returns {boolean}\n *   Whether it matches.\n */\nfunction markdownLineEndingOrSpace(code) {\n  return code !== null && (code < micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.nul || code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.space)\n}\n\n/**\n * Check whether a character code is a markdown space.\n *\n * A **markdown space** is the concrete character U+0020 SPACE (SP) and the\n * virtual characters M-0001 VIRTUAL SPACE (VS) and M-0002 HORIZONTAL TAB (HT).\n *\n * In micromark, the actual character U+0009 CHARACTER TABULATION (HT) is\n * replaced by one M-0002 HORIZONTAL TAB (HT) and between 0 and 3 M-0001 VIRTUAL\n * SPACE (VS) characters, depending on the column at which the tab occurred.\n *\n * @param {Code} code\n *   Code.\n * @returns {boolean}\n *   Whether it matches.\n */\nfunction markdownSpace(code) {\n  return (\n    code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.horizontalTab ||\n    code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.virtualSpace ||\n    code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.space\n  )\n}\n\n// Size note: removing ASCII from the regex and using `asciiPunctuation` here\n// In fact adds to the bundle size.\n/**\n * Check whether the character code represents Unicode punctuation.\n *\n * A **Unicode punctuation** is a character in the Unicode `Pc` (Punctuation,\n * Connector), `Pd` (Punctuation, Dash), `Pe` (Punctuation, Close), `Pf`\n * (Punctuation, Final quote), `Pi` (Punctuation, Initial quote), `Po`\n * (Punctuation, Other), or `Ps` (Punctuation, Open) categories, or an ASCII\n * punctuation (see `asciiPunctuation`).\n *\n * See:\n * **\\[UNICODE]**:\n * [The Unicode Standard](https://www.unicode.org/versions/).\n * Unicode Consortium.\n *\n * @param code\n *   Code.\n * @returns\n *   Whether it matches.\n */\nconst unicodePunctuation = regexCheck(_lib_unicode_punctuation_regex_js__WEBPACK_IMPORTED_MODULE_1__.unicodePunctuationRegex)\n\n/**\n * Check whether the character code represents Unicode whitespace.\n *\n * Note that this does handle micromark specific markdown whitespace characters.\n * See `markdownLineEndingOrSpace` to check that.\n *\n * A **Unicode whitespace** is a character in the Unicode `Zs` (Separator,\n * Space) category, or U+0009 CHARACTER TABULATION (HT), U+000A LINE FEED (LF),\n * U+000C (FF), or U+000D CARRIAGE RETURN (CR) (**\\[UNICODE]**).\n *\n * See:\n * **\\[UNICODE]**:\n * [The Unicode Standard](https://www.unicode.org/versions/).\n * Unicode Consortium.\n *\n * @param code\n *   Code.\n * @returns\n *   Whether it matches.\n */\nconst unicodeWhitespace = regexCheck(/\\s/)\n\n/**\n * Create a code check from a regex.\n *\n * @param {RegExp} regex\n * @returns {(code: Code) => boolean}\n */\nfunction regexCheck(regex) {\n  return check\n\n  /**\n   * Check whether a code matches the bound regex.\n   *\n   * @param {Code} code\n   *   Character code.\n   * @returns {boolean}\n   *   Whether the character code matches the bound regex.\n   */\n  function check(code) {\n    return code !== null && regex.test(String.fromCharCode(code))\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/micromark-factory-whitespace/node_modules/micromark-util-character/dev/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/micromark-factory-whitespace/node_modules/micromark-util-character/dev/lib/unicode-punctuation-regex.js":
/*!**********************************************************************************************************************************!*\
  !*** ../../node_modules/micromark-factory-whitespace/node_modules/micromark-util-character/dev/lib/unicode-punctuation-regex.js ***!
  \**********************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   unicodePunctuationRegex: () => (/* binding */ unicodePunctuationRegex)\n/* harmony export */ });\n// This module is generated by `script/`.\n//\n// CommonMark handles attention (emphasis, strong) markers based on what comes\n// before or after them.\n// One such difference is if those characters are Unicode punctuation.\n// This script is generated from the Unicode data.\n\n/**\n * Regular expression that matches a unicode punctuation character.\n */\nconst unicodePunctuationRegex =\n  /[!-/:-@[-`{-~\\u00A1\\u00A7\\u00AB\\u00B6\\u00B7\\u00BB\\u00BF\\u037E\\u0387\\u055A-\\u055F\\u0589\\u058A\\u05BE\\u05C0\\u05C3\\u05C6\\u05F3\\u05F4\\u0609\\u060A\\u060C\\u060D\\u061B\\u061D-\\u061F\\u066A-\\u066D\\u06D4\\u0700-\\u070D\\u07F7-\\u07F9\\u0830-\\u083E\\u085E\\u0964\\u0965\\u0970\\u09FD\\u0A76\\u0AF0\\u0C77\\u0C84\\u0DF4\\u0E4F\\u0E5A\\u0E5B\\u0F04-\\u0F12\\u0F14\\u0F3A-\\u0F3D\\u0F85\\u0FD0-\\u0FD4\\u0FD9\\u0FDA\\u104A-\\u104F\\u10FB\\u1360-\\u1368\\u1400\\u166E\\u169B\\u169C\\u16EB-\\u16ED\\u1735\\u1736\\u17D4-\\u17D6\\u17D8-\\u17DA\\u1800-\\u180A\\u1944\\u1945\\u1A1E\\u1A1F\\u1AA0-\\u1AA6\\u1AA8-\\u1AAD\\u1B5A-\\u1B60\\u1B7D\\u1B7E\\u1BFC-\\u1BFF\\u1C3B-\\u1C3F\\u1C7E\\u1C7F\\u1CC0-\\u1CC7\\u1CD3\\u2010-\\u2027\\u2030-\\u2043\\u2045-\\u2051\\u2053-\\u205E\\u207D\\u207E\\u208D\\u208E\\u2308-\\u230B\\u2329\\u232A\\u2768-\\u2775\\u27C5\\u27C6\\u27E6-\\u27EF\\u2983-\\u2998\\u29D8-\\u29DB\\u29FC\\u29FD\\u2CF9-\\u2CFC\\u2CFE\\u2CFF\\u2D70\\u2E00-\\u2E2E\\u2E30-\\u2E4F\\u2E52-\\u2E5D\\u3001-\\u3003\\u3008-\\u3011\\u3014-\\u301F\\u3030\\u303D\\u30A0\\u30FB\\uA4FE\\uA4FF\\uA60D-\\uA60F\\uA673\\uA67E\\uA6F2-\\uA6F7\\uA874-\\uA877\\uA8CE\\uA8CF\\uA8F8-\\uA8FA\\uA8FC\\uA92E\\uA92F\\uA95F\\uA9C1-\\uA9CD\\uA9DE\\uA9DF\\uAA5C-\\uAA5F\\uAADE\\uAADF\\uAAF0\\uAAF1\\uABEB\\uFD3E\\uFD3F\\uFE10-\\uFE19\\uFE30-\\uFE52\\uFE54-\\uFE61\\uFE63\\uFE68\\uFE6A\\uFE6B\\uFF01-\\uFF03\\uFF05-\\uFF0A\\uFF0C-\\uFF0F\\uFF1A\\uFF1B\\uFF1F\\uFF20\\uFF3B-\\uFF3D\\uFF3F\\uFF5B\\uFF5D\\uFF5F-\\uFF65]/\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/micromark-factory-whitespace/node_modules/micromark-util-character/dev/lib/unicode-punctuation-regex.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/micromark-factory-whitespace/node_modules/micromark-util-symbol/codes.js":
/*!***************************************************************************************************!*\
  !*** ../../node_modules/micromark-factory-whitespace/node_modules/micromark-util-symbol/codes.js ***!
  \***************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   codes: () => (/* binding */ codes)\n/* harmony export */ });\n/**\n * Character codes.\n *\n * This module is compiled away!\n *\n * micromark works based on character codes.\n * This module contains constants for the ASCII block and the replacement\n * character.\n * A couple of them are handled in a special way, such as the line endings\n * (CR, LF, and CR+LF, commonly known as end-of-line: EOLs), the tab (horizontal\n * tab) and its expansion based on what column it’s at (virtual space),\n * and the end-of-file (eof) character.\n * As values are preprocessed before handling them, the actual characters LF,\n * CR, HT, and NUL (which is present as the replacement character), are\n * guaranteed to not exist.\n *\n * Unicode basic latin block.\n */\nconst codes = /** @type {const} */ ({\n  carriageReturn: -5,\n  lineFeed: -4,\n  carriageReturnLineFeed: -3,\n  horizontalTab: -2,\n  virtualSpace: -1,\n  eof: null,\n  nul: 0,\n  soh: 1,\n  stx: 2,\n  etx: 3,\n  eot: 4,\n  enq: 5,\n  ack: 6,\n  bel: 7,\n  bs: 8,\n  ht: 9, // `\\t`\n  lf: 10, // `\\n`\n  vt: 11, // `\\v`\n  ff: 12, // `\\f`\n  cr: 13, // `\\r`\n  so: 14,\n  si: 15,\n  dle: 16,\n  dc1: 17,\n  dc2: 18,\n  dc3: 19,\n  dc4: 20,\n  nak: 21,\n  syn: 22,\n  etb: 23,\n  can: 24,\n  em: 25,\n  sub: 26,\n  esc: 27,\n  fs: 28,\n  gs: 29,\n  rs: 30,\n  us: 31,\n  space: 32,\n  exclamationMark: 33, // `!`\n  quotationMark: 34, // `\"`\n  numberSign: 35, // `#`\n  dollarSign: 36, // `$`\n  percentSign: 37, // `%`\n  ampersand: 38, // `&`\n  apostrophe: 39, // `'`\n  leftParenthesis: 40, // `(`\n  rightParenthesis: 41, // `)`\n  asterisk: 42, // `*`\n  plusSign: 43, // `+`\n  comma: 44, // `,`\n  dash: 45, // `-`\n  dot: 46, // `.`\n  slash: 47, // `/`\n  digit0: 48, // `0`\n  digit1: 49, // `1`\n  digit2: 50, // `2`\n  digit3: 51, // `3`\n  digit4: 52, // `4`\n  digit5: 53, // `5`\n  digit6: 54, // `6`\n  digit7: 55, // `7`\n  digit8: 56, // `8`\n  digit9: 57, // `9`\n  colon: 58, // `:`\n  semicolon: 59, // `;`\n  lessThan: 60, // `<`\n  equalsTo: 61, // `=`\n  greaterThan: 62, // `>`\n  questionMark: 63, // `?`\n  atSign: 64, // `@`\n  uppercaseA: 65, // `A`\n  uppercaseB: 66, // `B`\n  uppercaseC: 67, // `C`\n  uppercaseD: 68, // `D`\n  uppercaseE: 69, // `E`\n  uppercaseF: 70, // `F`\n  uppercaseG: 71, // `G`\n  uppercaseH: 72, // `H`\n  uppercaseI: 73, // `I`\n  uppercaseJ: 74, // `J`\n  uppercaseK: 75, // `K`\n  uppercaseL: 76, // `L`\n  uppercaseM: 77, // `M`\n  uppercaseN: 78, // `N`\n  uppercaseO: 79, // `O`\n  uppercaseP: 80, // `P`\n  uppercaseQ: 81, // `Q`\n  uppercaseR: 82, // `R`\n  uppercaseS: 83, // `S`\n  uppercaseT: 84, // `T`\n  uppercaseU: 85, // `U`\n  uppercaseV: 86, // `V`\n  uppercaseW: 87, // `W`\n  uppercaseX: 88, // `X`\n  uppercaseY: 89, // `Y`\n  uppercaseZ: 90, // `Z`\n  leftSquareBracket: 91, // `[`\n  backslash: 92, // `\\`\n  rightSquareBracket: 93, // `]`\n  caret: 94, // `^`\n  underscore: 95, // `_`\n  graveAccent: 96, // `` ` ``\n  lowercaseA: 97, // `a`\n  lowercaseB: 98, // `b`\n  lowercaseC: 99, // `c`\n  lowercaseD: 100, // `d`\n  lowercaseE: 101, // `e`\n  lowercaseF: 102, // `f`\n  lowercaseG: 103, // `g`\n  lowercaseH: 104, // `h`\n  lowercaseI: 105, // `i`\n  lowercaseJ: 106, // `j`\n  lowercaseK: 107, // `k`\n  lowercaseL: 108, // `l`\n  lowercaseM: 109, // `m`\n  lowercaseN: 110, // `n`\n  lowercaseO: 111, // `o`\n  lowercaseP: 112, // `p`\n  lowercaseQ: 113, // `q`\n  lowercaseR: 114, // `r`\n  lowercaseS: 115, // `s`\n  lowercaseT: 116, // `t`\n  lowercaseU: 117, // `u`\n  lowercaseV: 118, // `v`\n  lowercaseW: 119, // `w`\n  lowercaseX: 120, // `x`\n  lowercaseY: 121, // `y`\n  lowercaseZ: 122, // `z`\n  leftCurlyBrace: 123, // `{`\n  verticalBar: 124, // `|`\n  rightCurlyBrace: 125, // `}`\n  tilde: 126, // `~`\n  del: 127,\n  // Unicode Specials block.\n  byteOrderMarker: 65279,\n  // Unicode Specials block.\n  replacementCharacter: 65533 // `�`\n})\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/micromark-factory-whitespace/node_modules/micromark-util-symbol/codes.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/micromark-factory-whitespace/node_modules/micromark-util-symbol/types.js":
/*!***************************************************************************************************!*\
  !*** ../../node_modules/micromark-factory-whitespace/node_modules/micromark-util-symbol/types.js ***!
  \***************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   types: () => (/* binding */ types)\n/* harmony export */ });\n/**\n * This module is compiled away!\n *\n * Here is the list of all types of tokens exposed by micromark, with a short\n * explanation of what they include and where they are found.\n * In picking names, generally, the rule is to be as explicit as possible\n * instead of reusing names.\n * For example, there is a `definitionDestination` and a `resourceDestination`,\n * instead of one shared name.\n */\n\n// Note: when changing the next record, you must also change `TokenTypeMap`\n// in `micromark-util-types/index.d.ts`.\nconst types = /** @type {const} */ ({\n  // Generic type for data, such as in a title, a destination, etc.\n  data: 'data',\n\n  // Generic type for syntactic whitespace (tabs, virtual spaces, spaces).\n  // Such as, between a fenced code fence and an info string.\n  whitespace: 'whitespace',\n\n  // Generic type for line endings (line feed, carriage return, carriage return +\n  // line feed).\n  lineEnding: 'lineEnding',\n\n  // A line ending, but ending a blank line.\n  lineEndingBlank: 'lineEndingBlank',\n\n  // Generic type for whitespace (tabs, virtual spaces, spaces) at the start of a\n  // line.\n  linePrefix: 'linePrefix',\n\n  // Generic type for whitespace (tabs, virtual spaces, spaces) at the end of a\n  // line.\n  lineSuffix: 'lineSuffix',\n\n  // Whole ATX heading:\n  //\n  // ```markdown\n  // #\n  // ## Alpha\n  // ### Bravo ###\n  // ```\n  //\n  // Includes `atxHeadingSequence`, `whitespace`, `atxHeadingText`.\n  atxHeading: 'atxHeading',\n\n  // Sequence of number signs in an ATX heading (`###`).\n  atxHeadingSequence: 'atxHeadingSequence',\n\n  // Content in an ATX heading (`alpha`).\n  // Includes text.\n  atxHeadingText: 'atxHeadingText',\n\n  // Whole autolink (`<https://example.com>` or `<<EMAIL>>`)\n  // Includes `autolinkMarker` and `autolinkProtocol` or `autolinkEmail`.\n  autolink: 'autolink',\n\n  // Email autolink w/o markers (`<EMAIL>`)\n  autolinkEmail: 'autolinkEmail',\n\n  // Marker around an `autolinkProtocol` or `autolinkEmail` (`<` or `>`).\n  autolinkMarker: 'autolinkMarker',\n\n  // Protocol autolink w/o markers (`https://example.com`)\n  autolinkProtocol: 'autolinkProtocol',\n\n  // A whole character escape (`\\-`).\n  // Includes `escapeMarker` and `characterEscapeValue`.\n  characterEscape: 'characterEscape',\n\n  // The escaped character (`-`).\n  characterEscapeValue: 'characterEscapeValue',\n\n  // A whole character reference (`&amp;`, `&#8800;`, or `&#x1D306;`).\n  // Includes `characterReferenceMarker`, an optional\n  // `characterReferenceMarkerNumeric`, in which case an optional\n  // `characterReferenceMarkerHexadecimal`, and a `characterReferenceValue`.\n  characterReference: 'characterReference',\n\n  // The start or end marker (`&` or `;`).\n  characterReferenceMarker: 'characterReferenceMarker',\n\n  // Mark reference as numeric (`#`).\n  characterReferenceMarkerNumeric: 'characterReferenceMarkerNumeric',\n\n  // Mark reference as numeric (`x` or `X`).\n  characterReferenceMarkerHexadecimal: 'characterReferenceMarkerHexadecimal',\n\n  // Value of character reference w/o markers (`amp`, `8800`, or `1D306`).\n  characterReferenceValue: 'characterReferenceValue',\n\n  // Whole fenced code:\n  //\n  // ````markdown\n  // ```js\n  // alert(1)\n  // ```\n  // ````\n  codeFenced: 'codeFenced',\n\n  // A fenced code fence, including whitespace, sequence, info, and meta\n  // (` ```js `).\n  codeFencedFence: 'codeFencedFence',\n\n  // Sequence of grave accent or tilde characters (` ``` `) in a fence.\n  codeFencedFenceSequence: 'codeFencedFenceSequence',\n\n  // Info word (`js`) in a fence.\n  // Includes string.\n  codeFencedFenceInfo: 'codeFencedFenceInfo',\n\n  // Meta words (`highlight=\"1\"`) in a fence.\n  // Includes string.\n  codeFencedFenceMeta: 'codeFencedFenceMeta',\n\n  // A line of code.\n  codeFlowValue: 'codeFlowValue',\n\n  // Whole indented code:\n  //\n  // ```markdown\n  //     alert(1)\n  // ```\n  //\n  // Includes `lineEnding`, `linePrefix`, and `codeFlowValue`.\n  codeIndented: 'codeIndented',\n\n  // A text code (``` `alpha` ```).\n  // Includes `codeTextSequence`, `codeTextData`, `lineEnding`, and can include\n  // `codeTextPadding`.\n  codeText: 'codeText',\n\n  codeTextData: 'codeTextData',\n\n  // A space or line ending right after or before a tick.\n  codeTextPadding: 'codeTextPadding',\n\n  // A text code fence (` `` `).\n  codeTextSequence: 'codeTextSequence',\n\n  // Whole content:\n  //\n  // ```markdown\n  // [a]: b\n  // c\n  // =\n  // d\n  // ```\n  //\n  // Includes `paragraph` and `definition`.\n  content: 'content',\n  // Whole definition:\n  //\n  // ```markdown\n  // [micromark]: https://github.com/micromark/micromark\n  // ```\n  //\n  // Includes `definitionLabel`, `definitionMarker`, `whitespace`,\n  // `definitionDestination`, and optionally `lineEnding` and `definitionTitle`.\n  definition: 'definition',\n\n  // Destination of a definition (`https://github.com/micromark/micromark` or\n  // `<https://github.com/micromark/micromark>`).\n  // Includes `definitionDestinationLiteral` or `definitionDestinationRaw`.\n  definitionDestination: 'definitionDestination',\n\n  // Enclosed destination of a definition\n  // (`<https://github.com/micromark/micromark>`).\n  // Includes `definitionDestinationLiteralMarker` and optionally\n  // `definitionDestinationString`.\n  definitionDestinationLiteral: 'definitionDestinationLiteral',\n\n  // Markers of an enclosed definition destination (`<` or `>`).\n  definitionDestinationLiteralMarker: 'definitionDestinationLiteralMarker',\n\n  // Unenclosed destination of a definition\n  // (`https://github.com/micromark/micromark`).\n  // Includes `definitionDestinationString`.\n  definitionDestinationRaw: 'definitionDestinationRaw',\n\n  // Text in an destination (`https://github.com/micromark/micromark`).\n  // Includes string.\n  definitionDestinationString: 'definitionDestinationString',\n\n  // Label of a definition (`[micromark]`).\n  // Includes `definitionLabelMarker` and `definitionLabelString`.\n  definitionLabel: 'definitionLabel',\n\n  // Markers of a definition label (`[` or `]`).\n  definitionLabelMarker: 'definitionLabelMarker',\n\n  // Value of a definition label (`micromark`).\n  // Includes string.\n  definitionLabelString: 'definitionLabelString',\n\n  // Marker between a label and a destination (`:`).\n  definitionMarker: 'definitionMarker',\n\n  // Title of a definition (`\"x\"`, `'y'`, or `(z)`).\n  // Includes `definitionTitleMarker` and optionally `definitionTitleString`.\n  definitionTitle: 'definitionTitle',\n\n  // Marker around a title of a definition (`\"`, `'`, `(`, or `)`).\n  definitionTitleMarker: 'definitionTitleMarker',\n\n  // Data without markers in a title (`z`).\n  // Includes string.\n  definitionTitleString: 'definitionTitleString',\n\n  // Emphasis (`*alpha*`).\n  // Includes `emphasisSequence` and `emphasisText`.\n  emphasis: 'emphasis',\n\n  // Sequence of emphasis markers (`*` or `_`).\n  emphasisSequence: 'emphasisSequence',\n\n  // Emphasis text (`alpha`).\n  // Includes text.\n  emphasisText: 'emphasisText',\n\n  // The character escape marker (`\\`).\n  escapeMarker: 'escapeMarker',\n\n  // A hard break created with a backslash (`\\\\n`).\n  // Note: does not include the line ending.\n  hardBreakEscape: 'hardBreakEscape',\n\n  // A hard break created with trailing spaces (`  \\n`).\n  // Does not include the line ending.\n  hardBreakTrailing: 'hardBreakTrailing',\n\n  // Flow HTML:\n  //\n  // ```markdown\n  // <div\n  // ```\n  //\n  // Inlcudes `lineEnding`, `htmlFlowData`.\n  htmlFlow: 'htmlFlow',\n\n  htmlFlowData: 'htmlFlowData',\n\n  // HTML in text (the tag in `a <i> b`).\n  // Includes `lineEnding`, `htmlTextData`.\n  htmlText: 'htmlText',\n\n  htmlTextData: 'htmlTextData',\n\n  // Whole image (`![alpha](bravo)`, `![alpha][bravo]`, `![alpha][]`, or\n  // `![alpha]`).\n  // Includes `label` and an optional `resource` or `reference`.\n  image: 'image',\n\n  // Whole link label (`[*alpha*]`).\n  // Includes `labelLink` or `labelImage`, `labelText`, and `labelEnd`.\n  label: 'label',\n\n  // Text in an label (`*alpha*`).\n  // Includes text.\n  labelText: 'labelText',\n\n  // Start a link label (`[`).\n  // Includes a `labelMarker`.\n  labelLink: 'labelLink',\n\n  // Start an image label (`![`).\n  // Includes `labelImageMarker` and `labelMarker`.\n  labelImage: 'labelImage',\n\n  // Marker of a label (`[` or `]`).\n  labelMarker: 'labelMarker',\n\n  // Marker to start an image (`!`).\n  labelImageMarker: 'labelImageMarker',\n\n  // End a label (`]`).\n  // Includes `labelMarker`.\n  labelEnd: 'labelEnd',\n\n  // Whole link (`[alpha](bravo)`, `[alpha][bravo]`, `[alpha][]`, or `[alpha]`).\n  // Includes `label` and an optional `resource` or `reference`.\n  link: 'link',\n\n  // Whole paragraph:\n  //\n  // ```markdown\n  // alpha\n  // bravo.\n  // ```\n  //\n  // Includes text.\n  paragraph: 'paragraph',\n\n  // A reference (`[alpha]` or `[]`).\n  // Includes `referenceMarker` and an optional `referenceString`.\n  reference: 'reference',\n\n  // A reference marker (`[` or `]`).\n  referenceMarker: 'referenceMarker',\n\n  // Reference text (`alpha`).\n  // Includes string.\n  referenceString: 'referenceString',\n\n  // A resource (`(https://example.com \"alpha\")`).\n  // Includes `resourceMarker`, an optional `resourceDestination` with an optional\n  // `whitespace` and `resourceTitle`.\n  resource: 'resource',\n\n  // A resource destination (`https://example.com`).\n  // Includes `resourceDestinationLiteral` or `resourceDestinationRaw`.\n  resourceDestination: 'resourceDestination',\n\n  // A literal resource destination (`<https://example.com>`).\n  // Includes `resourceDestinationLiteralMarker` and optionally\n  // `resourceDestinationString`.\n  resourceDestinationLiteral: 'resourceDestinationLiteral',\n\n  // A resource destination marker (`<` or `>`).\n  resourceDestinationLiteralMarker: 'resourceDestinationLiteralMarker',\n\n  // A raw resource destination (`https://example.com`).\n  // Includes `resourceDestinationString`.\n  resourceDestinationRaw: 'resourceDestinationRaw',\n\n  // Resource destination text (`https://example.com`).\n  // Includes string.\n  resourceDestinationString: 'resourceDestinationString',\n\n  // A resource marker (`(` or `)`).\n  resourceMarker: 'resourceMarker',\n\n  // A resource title (`\"alpha\"`, `'alpha'`, or `(alpha)`).\n  // Includes `resourceTitleMarker` and optionally `resourceTitleString`.\n  resourceTitle: 'resourceTitle',\n\n  // A resource title marker (`\"`, `'`, `(`, or `)`).\n  resourceTitleMarker: 'resourceTitleMarker',\n\n  // Resource destination title (`alpha`).\n  // Includes string.\n  resourceTitleString: 'resourceTitleString',\n\n  // Whole setext heading:\n  //\n  // ```markdown\n  // alpha\n  // bravo\n  // =====\n  // ```\n  //\n  // Includes `setextHeadingText`, `lineEnding`, `linePrefix`, and\n  // `setextHeadingLine`.\n  setextHeading: 'setextHeading',\n\n  // Content in a setext heading (`alpha\\nbravo`).\n  // Includes text.\n  setextHeadingText: 'setextHeadingText',\n\n  // Underline in a setext heading, including whitespace suffix (`==`).\n  // Includes `setextHeadingLineSequence`.\n  setextHeadingLine: 'setextHeadingLine',\n\n  // Sequence of equals or dash characters in underline in a setext heading (`-`).\n  setextHeadingLineSequence: 'setextHeadingLineSequence',\n\n  // Strong (`**alpha**`).\n  // Includes `strongSequence` and `strongText`.\n  strong: 'strong',\n\n  // Sequence of strong markers (`**` or `__`).\n  strongSequence: 'strongSequence',\n\n  // Strong text (`alpha`).\n  // Includes text.\n  strongText: 'strongText',\n\n  // Whole thematic break:\n  //\n  // ```markdown\n  // * * *\n  // ```\n  //\n  // Includes `thematicBreakSequence` and `whitespace`.\n  thematicBreak: 'thematicBreak',\n\n  // A sequence of one or more thematic break markers (`***`).\n  thematicBreakSequence: 'thematicBreakSequence',\n\n  // Whole block quote:\n  //\n  // ```markdown\n  // > a\n  // >\n  // > b\n  // ```\n  //\n  // Includes `blockQuotePrefix` and flow.\n  blockQuote: 'blockQuote',\n  // The `>` or `> ` of a block quote.\n  blockQuotePrefix: 'blockQuotePrefix',\n  // The `>` of a block quote prefix.\n  blockQuoteMarker: 'blockQuoteMarker',\n  // The optional ` ` of a block quote prefix.\n  blockQuotePrefixWhitespace: 'blockQuotePrefixWhitespace',\n\n  // Whole unordered list:\n  //\n  // ```markdown\n  // - a\n  //   b\n  // ```\n  //\n  // Includes `listItemPrefix`, flow, and optionally  `listItemIndent` on further\n  // lines.\n  listOrdered: 'listOrdered',\n\n  // Whole ordered list:\n  //\n  // ```markdown\n  // 1. a\n  //    b\n  // ```\n  //\n  // Includes `listItemPrefix`, flow, and optionally  `listItemIndent` on further\n  // lines.\n  listUnordered: 'listUnordered',\n\n  // The indent of further list item lines.\n  listItemIndent: 'listItemIndent',\n\n  // A marker, as in, `*`, `+`, `-`, `.`, or `)`.\n  listItemMarker: 'listItemMarker',\n\n  // The thing that starts a list item, such as `1. `.\n  // Includes `listItemValue` if ordered, `listItemMarker`, and\n  // `listItemPrefixWhitespace` (unless followed by a line ending).\n  listItemPrefix: 'listItemPrefix',\n\n  // The whitespace after a marker.\n  listItemPrefixWhitespace: 'listItemPrefixWhitespace',\n\n  // The numerical value of an ordered item.\n  listItemValue: 'listItemValue',\n\n  // Internal types used for subtokenizers, compiled away\n  chunkDocument: 'chunkDocument',\n  chunkContent: 'chunkContent',\n  chunkFlow: 'chunkFlow',\n  chunkText: 'chunkText',\n  chunkString: 'chunkString'\n})\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/micromark-factory-whitespace/node_modules/micromark-util-symbol/types.js\n");

/***/ })

};
;