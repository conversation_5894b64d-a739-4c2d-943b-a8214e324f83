"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/hast-util-embedded";
exports.ids = ["vendor-chunks/hast-util-embedded"];
exports.modules = {

/***/ "(ssr)/../../node_modules/hast-util-embedded/lib/index.js":
/*!**********************************************************!*\
  !*** ../../node_modules/hast-util-embedded/lib/index.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   embedded: () => (/* binding */ embedded)\n/* harmony export */ });\n/* harmony import */ var hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! hast-util-is-element */ \"(ssr)/../../node_modules/hast-util-embedded/node_modules/hast-util-is-element/lib/index.js\");\n\n\n/**\n * Check if a node is a *embedded content*.\n *\n * @param value\n *   Thing to check (typically `Node`).\n * @returns\n *   Whether `value` is an element considered embedded content.\n *\n *   The elements `audio`, `canvas`, `embed`, `iframe`, `img`, `math`,\n *   `object`, `picture`, `svg`, and `video` are embedded content.\n */\nconst embedded = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)(\n  /**\n   * @param element\n   * @returns {element is {tagName: 'audio' | 'canvas' | 'embed' | 'iframe' | 'img' | 'math' | 'object' | 'picture' | 'svg' | 'video'}}\n   */\n  function (element) {\n    return (\n      element.tagName === 'audio' ||\n      element.tagName === 'canvas' ||\n      element.tagName === 'embed' ||\n      element.tagName === 'iframe' ||\n      element.tagName === 'img' ||\n      element.tagName === 'math' ||\n      element.tagName === 'object' ||\n      element.tagName === 'picture' ||\n      element.tagName === 'svg' ||\n      element.tagName === 'video'\n    )\n  }\n)\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2hhc3QtdXRpbC1lbWJlZGRlZC9saWIvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBbUQ7O0FBRW5EO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTyxpQkFBaUIsb0VBQWM7QUFDdEM7QUFDQTtBQUNBLGVBQWUsWUFBWTtBQUMzQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL0BvcGVuY2FudmFzL3dlYi8uLi8uLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLWVtYmVkZGVkL2xpYi9pbmRleC5qcz85ZTQ1Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7Y29udmVydEVsZW1lbnR9IGZyb20gJ2hhc3QtdXRpbC1pcy1lbGVtZW50J1xuXG4vKipcbiAqIENoZWNrIGlmIGEgbm9kZSBpcyBhICplbWJlZGRlZCBjb250ZW50Ki5cbiAqXG4gKiBAcGFyYW0gdmFsdWVcbiAqICAgVGhpbmcgdG8gY2hlY2sgKHR5cGljYWxseSBgTm9kZWApLlxuICogQHJldHVybnNcbiAqICAgV2hldGhlciBgdmFsdWVgIGlzIGFuIGVsZW1lbnQgY29uc2lkZXJlZCBlbWJlZGRlZCBjb250ZW50LlxuICpcbiAqICAgVGhlIGVsZW1lbnRzIGBhdWRpb2AsIGBjYW52YXNgLCBgZW1iZWRgLCBgaWZyYW1lYCwgYGltZ2AsIGBtYXRoYCxcbiAqICAgYG9iamVjdGAsIGBwaWN0dXJlYCwgYHN2Z2AsIGFuZCBgdmlkZW9gIGFyZSBlbWJlZGRlZCBjb250ZW50LlxuICovXG5leHBvcnQgY29uc3QgZW1iZWRkZWQgPSBjb252ZXJ0RWxlbWVudChcbiAgLyoqXG4gICAqIEBwYXJhbSBlbGVtZW50XG4gICAqIEByZXR1cm5zIHtlbGVtZW50IGlzIHt0YWdOYW1lOiAnYXVkaW8nIHwgJ2NhbnZhcycgfCAnZW1iZWQnIHwgJ2lmcmFtZScgfCAnaW1nJyB8ICdtYXRoJyB8ICdvYmplY3QnIHwgJ3BpY3R1cmUnIHwgJ3N2ZycgfCAndmlkZW8nfX1cbiAgICovXG4gIGZ1bmN0aW9uIChlbGVtZW50KSB7XG4gICAgcmV0dXJuIChcbiAgICAgIGVsZW1lbnQudGFnTmFtZSA9PT0gJ2F1ZGlvJyB8fFxuICAgICAgZWxlbWVudC50YWdOYW1lID09PSAnY2FudmFzJyB8fFxuICAgICAgZWxlbWVudC50YWdOYW1lID09PSAnZW1iZWQnIHx8XG4gICAgICBlbGVtZW50LnRhZ05hbWUgPT09ICdpZnJhbWUnIHx8XG4gICAgICBlbGVtZW50LnRhZ05hbWUgPT09ICdpbWcnIHx8XG4gICAgICBlbGVtZW50LnRhZ05hbWUgPT09ICdtYXRoJyB8fFxuICAgICAgZWxlbWVudC50YWdOYW1lID09PSAnb2JqZWN0JyB8fFxuICAgICAgZWxlbWVudC50YWdOYW1lID09PSAncGljdHVyZScgfHxcbiAgICAgIGVsZW1lbnQudGFnTmFtZSA9PT0gJ3N2ZycgfHxcbiAgICAgIGVsZW1lbnQudGFnTmFtZSA9PT0gJ3ZpZGVvJ1xuICAgIClcbiAgfVxuKVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/hast-util-embedded/lib/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/hast-util-embedded/node_modules/hast-util-is-element/lib/index.js":
/*!********************************************************************************************!*\
  !*** ../../node_modules/hast-util-embedded/node_modules/hast-util-is-element/lib/index.js ***!
  \********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   convertElement: () => (/* binding */ convertElement),\n/* harmony export */   isElement: () => (/* binding */ isElement)\n/* harmony export */ });\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Parents} Parents\n */\n\n/**\n * @template Fn\n * @template Fallback\n * @typedef {Fn extends (value: any) => value is infer Thing ? Thing : Fallback} Predicate\n */\n\n/**\n * @callback Check\n *   Check that an arbitrary value is an element.\n * @param {unknown} this\n *   Context object (`this`) to call `test` with\n * @param {unknown} [element]\n *   Anything (typically a node).\n * @param {number | null | undefined} [index]\n *   Position of `element` in its parent.\n * @param {Parents | null | undefined} [parent]\n *   Parent of `element`.\n * @returns {boolean}\n *   Whether this is an element and passes a test.\n *\n * @typedef {Array<TestFunction | string> | TestFunction | string | null | undefined} Test\n *   Check for an arbitrary element.\n *\n *   * when `string`, checks that the element has that tag name\n *   * when `function`, see `TestFunction`\n *   * when `Array`, checks if one of the subtests pass\n *\n * @callback TestFunction\n *   Check if an element passes a test.\n * @param {unknown} this\n *   The given context.\n * @param {Element} element\n *   An element.\n * @param {number | undefined} [index]\n *   Position of `element` in its parent.\n * @param {Parents | undefined} [parent]\n *   Parent of `element`.\n * @returns {boolean | undefined | void}\n *   Whether this element passes the test.\n *\n *   Note: `void` is included until TS sees no return as `undefined`.\n */\n\n/**\n * Check if `element` is an `Element` and whether it passes the given test.\n *\n * @param element\n *   Thing to check, typically `element`.\n * @param test\n *   Check for a specific element.\n * @param index\n *   Position of `element` in its parent.\n * @param parent\n *   Parent of `element`.\n * @param context\n *   Context object (`this`) to call `test` with.\n * @returns\n *   Whether `element` is an `Element` and passes a test.\n * @throws\n *   When an incorrect `test`, `index`, or `parent` is given; there is no error\n *   thrown when `element` is not a node or not an element.\n */\nconst isElement =\n  // Note: overloads in JSDoc can’t yet use different `@template`s.\n  /**\n   * @type {(\n   *   (<Condition extends TestFunction>(element: unknown, test: Condition, index?: number | null | undefined, parent?: Parents | null | undefined, context?: unknown) => element is Element & Predicate<Condition, Element>) &\n   *   (<Condition extends string>(element: unknown, test: Condition, index?: number | null | undefined, parent?: Parents | null | undefined, context?: unknown) => element is Element & {tagName: Condition}) &\n   *   ((element?: null | undefined) => false) &\n   *   ((element: unknown, test?: null | undefined, index?: number | null | undefined, parent?: Parents | null | undefined, context?: unknown) => element is Element) &\n   *   ((element: unknown, test?: Test, index?: number | null | undefined, parent?: Parents | null | undefined, context?: unknown) => boolean)\n   * )}\n   */\n  (\n    /**\n     * @param {unknown} [element]\n     * @param {Test | undefined} [test]\n     * @param {number | null | undefined} [index]\n     * @param {Parents | null | undefined} [parent]\n     * @param {unknown} [context]\n     * @returns {boolean}\n     */\n    // eslint-disable-next-line max-params\n    function (element, test, index, parent, context) {\n      const check = convertElement(test)\n\n      if (\n        index !== null &&\n        index !== undefined &&\n        (typeof index !== 'number' ||\n          index < 0 ||\n          index === Number.POSITIVE_INFINITY)\n      ) {\n        throw new Error('Expected positive finite `index`')\n      }\n\n      if (\n        parent !== null &&\n        parent !== undefined &&\n        (!parent.type || !parent.children)\n      ) {\n        throw new Error('Expected valid `parent`')\n      }\n\n      if (\n        (index === null || index === undefined) !==\n        (parent === null || parent === undefined)\n      ) {\n        throw new Error('Expected both `index` and `parent`')\n      }\n\n      return looksLikeAnElement(element)\n        ? check.call(context, element, index, parent)\n        : false\n    }\n  )\n\n/**\n * Generate a check from a test.\n *\n * Useful if you’re going to test many nodes, for example when creating a\n * utility where something else passes a compatible test.\n *\n * The created function is a bit faster because it expects valid input only:\n * an `element`, `index`, and `parent`.\n *\n * @param test\n *   A test for a specific element.\n * @returns\n *   A check.\n */\nconst convertElement =\n  // Note: overloads in JSDoc can’t yet use different `@template`s.\n  /**\n   * @type {(\n   *   (<Condition extends TestFunction>(test: Condition) => (element: unknown, index?: number | null | undefined, parent?: Parents | null | undefined, context?: unknown) => element is Element & Predicate<Condition, Element>) &\n   *   (<Condition extends string>(test: Condition) => (element: unknown, index?: number | null | undefined, parent?: Parents | null | undefined, context?: unknown) => element is Element & {tagName: Condition}) &\n   *   ((test?: null | undefined) => (element?: unknown, index?: number | null | undefined, parent?: Parents | null | undefined, context?: unknown) => element is Element) &\n   *   ((test?: Test) => Check)\n   * )}\n   */\n  (\n    /**\n     * @param {Test | null | undefined} [test]\n     * @returns {Check}\n     */\n    function (test) {\n      if (test === null || test === undefined) {\n        return element\n      }\n\n      if (typeof test === 'string') {\n        return tagNameFactory(test)\n      }\n\n      // Assume array.\n      if (typeof test === 'object') {\n        return anyFactory(test)\n      }\n\n      if (typeof test === 'function') {\n        return castFactory(test)\n      }\n\n      throw new Error('Expected function, string, or array as `test`')\n    }\n  )\n\n/**\n * Handle multiple tests.\n *\n * @param {Array<TestFunction | string>} tests\n * @returns {Check}\n */\nfunction anyFactory(tests) {\n  /** @type {Array<Check>} */\n  const checks = []\n  let index = -1\n\n  while (++index < tests.length) {\n    checks[index] = convertElement(tests[index])\n  }\n\n  return castFactory(any)\n\n  /**\n   * @this {unknown}\n   * @type {TestFunction}\n   */\n  function any(...parameters) {\n    let index = -1\n\n    while (++index < checks.length) {\n      if (checks[index].apply(this, parameters)) return true\n    }\n\n    return false\n  }\n}\n\n/**\n * Turn a string into a test for an element with a certain type.\n *\n * @param {string} check\n * @returns {Check}\n */\nfunction tagNameFactory(check) {\n  return castFactory(tagName)\n\n  /**\n   * @param {Element} element\n   * @returns {boolean}\n   */\n  function tagName(element) {\n    return element.tagName === check\n  }\n}\n\n/**\n * Turn a custom test into a test for an element that passes that test.\n *\n * @param {TestFunction} testFunction\n * @returns {Check}\n */\nfunction castFactory(testFunction) {\n  return check\n\n  /**\n   * @this {unknown}\n   * @type {Check}\n   */\n  function check(value, index, parent) {\n    return Boolean(\n      looksLikeAnElement(value) &&\n        testFunction.call(\n          this,\n          value,\n          typeof index === 'number' ? index : undefined,\n          parent || undefined\n        )\n    )\n  }\n}\n\n/**\n * Make sure something is an element.\n *\n * @param {unknown} element\n * @returns {element is Element}\n */\nfunction element(element) {\n  return Boolean(\n    element &&\n      typeof element === 'object' &&\n      'type' in element &&\n      element.type === 'element' &&\n      'tagName' in element &&\n      typeof element.tagName === 'string'\n  )\n}\n\n/**\n * @param {unknown} value\n * @returns {value is Element}\n */\nfunction looksLikeAnElement(value) {\n  return (\n    value !== null &&\n    typeof value === 'object' &&\n    'type' in value &&\n    'tagName' in value\n  )\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/hast-util-embedded/node_modules/hast-util-is-element/lib/index.js\n");

/***/ })

};
;