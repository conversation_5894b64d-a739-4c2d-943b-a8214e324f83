/*!*************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[2]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[3]!./src/app/globals.css ***!
  \*************************************************************************************************************************************************************************************************************************************************************************/
@import url("https://fonts.googleapis.com/css2?family=Fira+Code&family=Inter:wght@400;500;600;700&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap");
*, ::before, ::after{
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}
::backdrop{
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}
/*
! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com
*/
/*
1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
*/
*,
::before,
::after {
  box-sizing: border-box; /* 1 */
  border-width: 0; /* 2 */
  border-style: solid; /* 2 */
  border-color: #e5e7eb; /* 2 */
}
::before,
::after {
  --tw-content: '';
}
/*
1. Use a consistent sensible line-height in all browsers.
2. Prevent adjustments of font size after orientation changes in iOS.
3. Use a more readable tab size.
4. Use the user's configured `sans` font-family by default.
5. Use the user's configured `sans` font-feature-settings by default.
6. Use the user's configured `sans` font-variation-settings by default.
7. Disable tap highlights on iOS
*/
html,
:host {
  line-height: 1.5; /* 1 */
  -webkit-text-size-adjust: 100%; /* 2 */
  -moz-tab-size: 4; /* 3 */
  tab-size: 4; /* 3 */
  font-family: Inter, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica, Arial, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol; /* 4 */
  font-feature-settings: normal; /* 5 */
  font-variation-settings: normal; /* 6 */
  -webkit-tap-highlight-color: transparent; /* 7 */
}
/*
1. Remove the margin in all browsers.
2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.
*/
body {
  margin: 0; /* 1 */
  line-height: inherit; /* 2 */
}
/*
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
3. Ensure horizontal rules are visible by default.
*/
hr {
  height: 0; /* 1 */
  color: inherit; /* 2 */
  border-top-width: 1px; /* 3 */
}
/*
Add the correct text decoration in Chrome, Edge, and Safari.
*/
abbr:where([title]) {
  text-decoration: underline dotted;
}
/*
Remove the default font size and weight for headings.
*/
h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}
/*
Reset links to optimize for opt-in styling instead of opt-out.
*/
a {
  color: inherit;
  text-decoration: inherit;
}
/*
Add the correct font weight in Edge and Safari.
*/
b,
strong {
  font-weight: bolder;
}
/*
1. Use the user's configured `mono` font-family by default.
2. Use the user's configured `mono` font-feature-settings by default.
3. Use the user's configured `mono` font-variation-settings by default.
4. Correct the odd `em` font sizing in all browsers.
*/
code,
kbd,
samp,
pre {
  font-family: `"Fira Code"`, `ui-monospace`, `SFMono-Regular`, `Menlo`, `Monaco`, `Consolas`, `"Liberation Mono"`, `"Courier New"`, `monospace`; /* 1 */
  font-feature-settings: normal; /* 2 */
  font-variation-settings: normal; /* 3 */
  font-size: 1em; /* 4 */
}
/*
Add the correct font size in all browsers.
*/
small {
  font-size: 80%;
}
/*
Prevent `sub` and `sup` elements from affecting the line height in all browsers.
*/
sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}
sub {
  bottom: -0.25em;
}
sup {
  top: -0.5em;
}
/*
1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
3. Remove gaps between table borders by default.
*/
table {
  text-indent: 0; /* 1 */
  border-color: inherit; /* 2 */
  border-collapse: collapse; /* 3 */
}
/*
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
3. Remove default padding in all browsers.
*/
button,
input,
optgroup,
select,
textarea {
  font-family: inherit; /* 1 */
  font-feature-settings: inherit; /* 1 */
  font-variation-settings: inherit; /* 1 */
  font-size: 100%; /* 1 */
  font-weight: inherit; /* 1 */
  line-height: inherit; /* 1 */
  letter-spacing: inherit; /* 1 */
  color: inherit; /* 1 */
  margin: 0; /* 2 */
  padding: 0; /* 3 */
}
/*
Remove the inheritance of text transform in Edge and Firefox.
*/
button,
select {
  text-transform: none;
}
/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Remove default button styles.
*/
button,
input:where([type='button']),
input:where([type='reset']),
input:where([type='submit']) {
  -webkit-appearance: button; /* 1 */
  background-color: transparent; /* 2 */
  background-image: none; /* 2 */
}
/*
Use the modern Firefox focus style for all focusable elements.
*/
:-moz-focusring {
  outline: auto;
}
/*
Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)
*/
:-moz-ui-invalid {
  box-shadow: none;
}
/*
Add the correct vertical alignment in Chrome and Firefox.
*/
progress {
  vertical-align: baseline;
}
/*
Correct the cursor style of increment and decrement buttons in Safari.
*/
::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}
/*
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/
[type='search'] {
  -webkit-appearance: textfield; /* 1 */
  outline-offset: -2px; /* 2 */
}
/*
Remove the inner padding in Chrome and Safari on macOS.
*/
::-webkit-search-decoration {
  -webkit-appearance: none;
}
/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to `inherit` in Safari.
*/
::-webkit-file-upload-button {
  -webkit-appearance: button; /* 1 */
  font: inherit; /* 2 */
}
/*
Add the correct display in Chrome and Safari.
*/
summary {
  display: list-item;
}
/*
Removes the default spacing and border for appropriate elements.
*/
blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  margin: 0;
}
fieldset {
  margin: 0;
  padding: 0;
}
legend {
  padding: 0;
}
ol,
ul,
menu {
  list-style: none;
  margin: 0;
  padding: 0;
}
/*
Reset default styling for dialogs.
*/
dialog {
  padding: 0;
}
/*
Prevent resizing textareas horizontally by default.
*/
textarea {
  resize: vertical;
}
/*
1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)
2. Set the default placeholder color to the user's configured gray 400 color.
*/
input::placeholder,
textarea::placeholder {
  opacity: 1; /* 1 */
  color: #9ca3af; /* 2 */
}
/*
Set the default cursor for buttons.
*/
button,
[role="button"] {
  cursor: pointer;
}
/*
Make sure disabled buttons don't get the pointer cursor.
*/
:disabled {
  cursor: default;
}
/*
1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)
2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)
   This can trigger a poorly considered lint error in some tools but is included by design.
*/
img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block; /* 1 */
  vertical-align: middle; /* 2 */
}
/*
Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
*/
img,
video {
  max-width: 100%;
  height: auto;
}
/* Make elements with the HTML hidden attribute stay hidden by default */
[hidden]:where(:not([hidden="until-found"])) {
  display: none;
}
*{
  scrollbar-color: initial;
  scrollbar-width: initial;
}
:root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
  }
html {
    font-family:
      "Inter",
      -apple-system,
      BlinkMacSystemFont,
      "Segoe UI",
      Roboto,
      Helvetica,
      Arial,
      sans-serif,
      "Apple Color Emoji",
      "Segoe UI Emoji",
      "Segoe UI Symbol";
  }
*{
  border-color: hsl(var(--border));
}
body{
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
}
.container{
  width: 100%;
}
@media (min-width: 640px){
  .container{
    max-width: 640px;
  }
}
@media (min-width: 768px){
  .container{
    max-width: 768px;
  }
}
@media (min-width: 1024px){
  .container{
    max-width: 1024px;
  }
}
@media (min-width: 1280px){
  .container{
    max-width: 1280px;
  }
}
@media (min-width: 1536px){
  .container{
    max-width: 1536px;
  }
}
:root{
  --aui-background: 0 0% 100%;
  --aui-foreground: 240 10% 3.9%;
  --aui-card: 0 0% 100%;
  --aui-card-foreground: 240 10% 3.9%;
  --aui-popover: 0 0% 100%;
  --aui-popover-foreground: 240 10% 3.9%;
  --aui-primary: 240 5.9% 10%;
  --aui-primary-foreground: 0 0% 98%;
  --aui-secondary: 240 4.8% 95.9%;
  --aui-secondary-foreground: 240 5.9% 10%;
  --aui-muted: 240 4.8% 95.9%;
  --aui-muted-foreground: 240 3.8% 46.1%;
  --aui-accent: 240 4.8% 95.9%;
  --aui-accent-foreground: 240 5.9% 10%;
  --aui-destructive: 0 84.2% 60.2%;
  --aui-destructive-foreground: 0 0% 98%;
  --aui-border: 240 5.9% 90%;
  --aui-input: 240 5.9% 90%;
  --aui-ring: 240 10% 3.9%;
  --aui-radius: 0.5rem;
  --aui-thread-max-width: 42rem;
}
.aui-root{
  color: hsl(var(--aui-foreground));
}
.aui-root *{
  border-color: hsl(var(--aui-border));
}
.aui-button{
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  white-space: nowrap;
  border-radius: calc(var(--radius) - 2px);
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 500;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.aui-button:focus-visible{
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-color: hsl(var(--aui-ring));
}
.aui-button:disabled{
  pointer-events: none;
  opacity: 0.5;
}
.aui-button svg{
  pointer-events: none;
  width: 1rem;
  height: 1rem;
  flex-shrink: 0;
}
.aui-button-primary{
  background-color: hsl(var(--aui-primary));
  color: hsl(var(--aui-primary-foreground));
  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.aui-button-primary:hover{
  background-color: hsl(var(--aui-primary) / 0.9);
}
.aui-button-outline{
  border-width: 1px;
  border-color: hsl(var(--aui-input));
  background-color: hsl(var(--aui-background));
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.aui-button-outline:hover{
  background-color: hsl(var(--aui-accent));
  color: hsl(var(--aui-accent-foreground));
}
.aui-button-ghost:hover{
  background-color: hsl(var(--aui-accent));
  color: hsl(var(--aui-accent-foreground));
}
.aui-button-medium{
  height: 2.25rem;
  padding-left: 1rem;
  padding-right: 1rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
.aui-button-icon{
  width: 1.5rem;
  height: 1.5rem;
  padding: 0.25rem;
}
.aui-sr-only{
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}
.aui-avatar-root{
  position: relative;
  display: flex;
  height: 2.5rem;
  width: 2.5rem;
  flex-shrink: 0;
  overflow: hidden;
  border-radius: 9999px;
}
.aui-avatar-image{
  aspect-ratio: 1 / 1;
  height: 100%;
  width: 100%;
  object-fit: cover;
}
.aui-avatar-fallback{
  display: flex;
  height: 100%;
  width: 100%;
  align-items: center;
  justify-content: center;
  border-radius: 9999px;
  background-color: hsl(var(--aui-muted));
}
.aui-tooltip-content{
  z-index: 50;
  overflow: hidden;
  border-radius: calc(var(--radius) - 2px);
  background-color: hsl(var(--aui-primary));
  padding-left: 0.75rem;
  padding-right: 0.75rem;
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
  font-size: 0.75rem;
  line-height: 1rem;
  color: hsl(var(--aui-primary-foreground));
  animation-name: enter;
  animation-duration: 150ms;
  --tw-enter-opacity: initial;
  --tw-enter-scale: initial;
  --tw-enter-rotate: initial;
  --tw-enter-translate-x: initial;
  --tw-enter-translate-y: initial;
  --tw-enter-opacity: 0;
  --tw-enter-scale: .95;
}
.aui-tooltip-content[data-state="closed"]{
  animation-name: exit;
  animation-duration: 150ms;
  --tw-exit-opacity: initial;
  --tw-exit-scale: initial;
  --tw-exit-rotate: initial;
  --tw-exit-translate-x: initial;
  --tw-exit-translate-y: initial;
  --tw-exit-opacity: 0;
  --tw-exit-scale: .95;
}
.aui-tooltip-content[data-side="bottom"]{
  --tw-enter-translate-y: -0.5rem;
}
.aui-tooltip-content[data-side="left"]{
  --tw-enter-translate-x: 0.5rem;
}
.aui-tooltip-content[data-side="right"]{
  --tw-enter-translate-x: -0.5rem;
}
.aui-tooltip-content[data-side="top"]{
  --tw-enter-translate-y: 0.5rem;
}
.aui-dialog-overlay{
  position: fixed;
  inset: 0px;
  z-index: 50;
  background-color: rgb(0 0 0 / 0.8);
}
.aui-dialog-overlay[data-state="open"]{
  animation-name: enter;
  animation-duration: 150ms;
  --tw-enter-opacity: initial;
  --tw-enter-scale: initial;
  --tw-enter-rotate: initial;
  --tw-enter-translate-x: initial;
  --tw-enter-translate-y: initial;
}
.aui-dialog-overlay[data-state="closed"]{
  animation-name: exit;
  animation-duration: 150ms;
  --tw-exit-opacity: initial;
  --tw-exit-scale: initial;
  --tw-exit-rotate: initial;
  --tw-exit-translate-x: initial;
  --tw-exit-translate-y: initial;
  --tw-exit-opacity: 0;
}
.aui-dialog-overlay[data-state="open"]{
  --tw-enter-opacity: 0;
}
.aui-dialog-content{
  position: fixed;
  left: 50%;
  top: 50%;
  z-index: 50;
}
.aui-dialog-content[data-state="open"]{
  animation-name: enter;
  animation-duration: 150ms;
  --tw-enter-opacity: initial;
  --tw-enter-scale: initial;
  --tw-enter-rotate: initial;
  --tw-enter-translate-x: initial;
  --tw-enter-translate-y: initial;
}
.aui-dialog-content[data-state="closed"]{
  animation-name: exit;
  animation-duration: 150ms;
  --tw-exit-opacity: initial;
  --tw-exit-scale: initial;
  --tw-exit-rotate: initial;
  --tw-exit-translate-x: initial;
  --tw-exit-translate-y: initial;
  --tw-exit-opacity: 0;
}
.aui-dialog-content[data-state="open"]{
  --tw-enter-opacity: 0;
}
.aui-dialog-content[data-state="closed"]{
  --tw-exit-scale: .95;
}
.aui-dialog-content[data-state="open"]{
  --tw-enter-scale: .95;
}
.aui-dialog-content[data-state="closed"]{
  --tw-exit-translate-x: -50%;
  --tw-exit-translate-y: -48%;
}
.aui-dialog-content[data-state="open"]{
  --tw-enter-translate-x: -50%;
  --tw-enter-translate-y: -48%;
}
.aui-dialog-content{
  display: grid;
  --tw-translate-x: -50%;
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  transition-duration: 200ms;
  animation-duration: 200ms;
}
.aui-thread-root{
  box-sizing: border-box;
  height: 100%;
  background-color: hsl(var(--aui-background));
}
.aui-thread-root>.aui-thread-viewport{
  background-color: inherit;
}
.aui-thread-viewport{
  display: flex;
  height: 100%;
  flex-direction: column;
  align-items: center;
  overflow-y: scroll;
  scroll-behavior: smooth;
  background-color: hsl(var(--aui-background));
  padding-left: 1rem;
  padding-right: 1rem;
  padding-top: 2rem;
}
.aui-thread-viewport-footer{
  position: sticky;
  bottom: 0px;
  margin-top: 0.75rem;
  display: flex;
  width: 100%;
  max-width: var(--aui-thread-max-width);
  flex-direction: column;
  align-items: center;
  justify-content: flex-end;
  border-top-left-radius: var(--radius);
  border-top-right-radius: var(--radius);
  background-color: inherit;
  padding-bottom: 1rem;
}
.aui-thread-scroll-to-bottom{
  position: absolute;
  top: -2rem;
  border-radius: 9999px;
}
.aui-thread-scroll-to-bottom:disabled{
  visibility: hidden;
}
.aui-thread-followup-suggestions{
  display: flex;
  min-height: 2rem;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}
.aui-thread-followup-suggestion{
  border-radius: 9999px;
  border-width: 1px;
  background-color: hsl(var(--aui-background));
  padding-left: 0.75rem;
  padding-right: 0.75rem;
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
  transition-timing-function: cubic-bezier(0.4, 0, 1, 1);
  animation-timing-function: cubic-bezier(0.4, 0, 1, 1);
}
.aui-thread-followup-suggestion:hover{
  background-color: hsl(var(--aui-muted) / 0.8);
}
.aui-thread-welcome-root{
  display: flex;
  width: 100%;
  max-width: var(--aui-thread-max-width);
  flex-grow: 1;
  flex-direction: column;
}
.aui-thread-welcome-center{
  display: flex;
  width: 100%;
  flex-grow: 1;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.aui-thread-welcome-message{
  margin-top: 1rem;
  font-weight: 500;
}
.aui-thread-welcome-suggestions{
  margin-top: 0.75rem;
  display: flex;
  width: 100%;
  align-items: stretch;
  justify-content: center;
  gap: 1rem;
}
.aui-thread-welcome-suggestion{
  display: flex;
  max-width: 24rem;
  flex-grow: 1;
  flex-basis: 0px;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius);
  border-width: 1px;
  padding: 0.75rem;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
  transition-timing-function: cubic-bezier(0.4, 0, 1, 1);
  animation-timing-function: cubic-bezier(0.4, 0, 1, 1);
}
.aui-thread-welcome-suggestion:hover{
  background-color: hsl(var(--aui-muted) / 0.8);
}
.aui-thread-welcome-suggestion-text{
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  text-overflow: ellipsis;
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 600;
}
.aui-composer-root{
  display: flex;
  width: 100%;
  flex-wrap: wrap;
  align-items: flex-end;
  border-radius: var(--radius);
  border-width: 1px;
  background-color: inherit;
  padding-left: 0.625rem;
  padding-right: 0.625rem;
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
  transition-timing-function: cubic-bezier(0.4, 0, 1, 1);
  animation-timing-function: cubic-bezier(0.4, 0, 1, 1);
}
.aui-composer-root:focus-within{
  border-color: hsl(var(--aui-ring) / 0.2);
}
.aui-composer-input{
  max-height: 10rem;
  flex-grow: 1;
  resize: none;
  border-style: none;
  background-color: transparent;
  padding-left: 0.5rem;
  padding-right: 0.5rem;
  padding-top: 1rem;
  padding-bottom: 1rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.aui-composer-input::placeholder{
  color: hsl(var(--aui-muted-foreground));
}
.aui-composer-input:focus{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.aui-composer-input:disabled{
  cursor: not-allowed;
}
.aui-composer-send,
.aui-composer-cancel,
.aui-composer-attach{
  margin-top: 0.625rem;
  margin-bottom: 0.625rem;
  width: 2rem;
  height: 2rem;
  padding: 0.5rem;
  transition-property: opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
  transition-timing-function: cubic-bezier(0.4, 0, 1, 1);
  animation-timing-function: cubic-bezier(0.4, 0, 1, 1);
}
.aui-composer-attachments{
  display: flex;
  width: 100%;
  flex-direction: row;
  gap: 0.75rem;
  padding-left: 2.5rem;
  padding-right: 2.5rem;
}
.aui-attachment-root{
  position: relative;
  margin-top: 0.75rem;
}
.aui-attachment-content{
  display: flex;
  height: 3rem;
  width: 10rem;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  border-radius: var(--radius);
  border-width: 1px;
  padding: 0.25rem;
}
.aui-attachment-preview-trigger{
  cursor: pointer;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.aui-attachment-preview-trigger:hover{
  background-color: hsl(var(--aui-accent) / 0.5);
}
.aui-attachment-thumb{
  display: flex;
  width: 2.5rem;
  height: 2.5rem;
  align-items: center;
  justify-content: center;
  border-radius: 0.25rem;
  border-width: 1px;
  background-color: hsl(var(--aui-muted));
  font-size: 0.875rem;
  line-height: 1.25rem;
}
.aui-attachment-text{
  flex-grow: 1;
  flex-basis: 0px;
}
.aui-attachment-name{
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  text-overflow: ellipsis;
  word-break: break-all;
  font-size: 0.75rem;
  line-height: 1rem;
  font-weight: 700;
  color: hsl(var(--aui-muted-foreground));
}
.aui-attachment-type{
  font-size: 0.75rem;
  line-height: 1rem;
  color: hsl(var(--aui-muted-foreground));
}
.aui-attachment-remove{
  position: absolute;
  right: -0.75rem;
  top: -0.75rem;
  width: 1.5rem;
  height: 1.5rem;
  color: hsl(var(--aui-muted-foreground));
}
.aui-attachment-remove>svg{
  width: 1rem;
  height: 1rem;
  border-radius: 9999px;
  background-color: hsl(var(--aui-background));
}
.aui-user-message-root{
  display: grid;
  grid-auto-rows: auto;
  grid-template-columns: minmax(72px,1fr) auto;
  row-gap: 0.5rem;
}
.aui-user-message-root>*{
  grid-column-start: 2;
}
.aui-user-message-root{
  width: 100%;
  max-width: var(--aui-thread-max-width);
  padding-top: 1rem;
  padding-bottom: 1rem;
}
:where(.aui-user-message-root) > .aui-user-action-bar-root{
  grid-column-start: 1;
  grid-row-start: 2;
  margin-right: 0.75rem;
  margin-top: 0.625rem;
}
:where(.aui-user-message-root) > .aui-user-message-attachments{
  grid-column: 1 / -1;
  grid-column-start: 1;
  grid-row-start: 1;
  justify-content: flex-end;
}
:where(.aui-user-message-root) > .aui-user-message-content{
  grid-column-start: 2;
  grid-row-start: 2;
}
:where(.aui-user-message-root) > .aui-branch-picker-root{
  grid-column: 1 / -1;
  grid-column-start: 1;
  grid-row-start: 3;
  margin-right: -0.25rem;
  justify-content: flex-end;
}
.aui-user-message-content{
  max-width: calc(var(--aui-thread-max-width) * 0.8);
  overflow-wrap: break-word;
  border-radius: 1.5rem;
  background-color: hsl(var(--aui-muted));
  padding-left: 1.25rem;
  padding-right: 1.25rem;
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
  color: hsl(var(--aui-foreground));
}
.aui-user-message-attachments{
  display: flex;
  width: 100%;
  flex-direction: row;
  gap: 0.75rem;
}
.aui-user-action-bar-root{
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}
.aui-edit-composer-root{
  margin-top: 1rem;
  margin-bottom: 1rem;
  display: flex;
  width: 100%;
  max-width: var(--aui-thread-max-width);
  flex-direction: column;
  gap: 0.5rem;
  border-radius: 0.75rem;
  background-color: hsl(var(--aui-muted));
}
.aui-edit-composer-input{
  display: flex;
  height: 2rem;
  width: 100%;
  resize: none;
  background-color: transparent;
  padding: 1rem;
  padding-bottom: 0px;
  color: hsl(var(--aui-foreground));
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.aui-edit-composer-footer{
  margin-left: 0.75rem;
  margin-right: 0.75rem;
  margin-bottom: 0.75rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  align-self: flex-end;
}
.aui-assistant-message-root{
  display: grid;
  grid-template-columns: auto auto 1fr;
  grid-template-rows: auto 1fr;
  position: relative;
  width: 100%;
  max-width: var(--aui-thread-max-width);
  padding-top: 1rem;
  padding-bottom: 1rem;
}
:where(.aui-assistant-message-root) > .aui-avatar-root{
  grid-column-start: 1;
  grid-row: 1 / -1;
  grid-row-start: 1;
  margin-right: 1rem;
}
:where(.aui-assistant-message-root) > .aui-branch-picker-root{
  grid-column-start: 2;
  grid-row-start: 2;
  margin-left: -0.5rem;
  margin-right: 0.5rem;
}
:where(.aui-assistant-message-root) > .aui-assistant-action-bar-root{
  grid-column-start: 3;
  grid-row-start: 2;
  margin-left: -0.25rem;
}
:where(.aui-assistant-message-root) > .aui-assistant-message-content{
  grid-column: span 2 / span 2;
  grid-column-start: 2;
  grid-row-start: 1;
  margin-top: 0.375rem;
  margin-bottom: 0.375rem;
}
.aui-assistant-message-content{
  max-width: calc(var(--aui-thread-max-width) * 0.8);
  overflow-wrap: break-word;
  line-height: 1.75rem;
  color: hsl(var(--aui-foreground));
}
.aui-assistant-action-bar-root{
  display: flex;
  gap: 0.25rem;
  color: hsl(var(--aui-muted-foreground));
}
:where(.aui-assistant-action-bar-root)[data-floating]{
  position: absolute;
  border-radius: calc(var(--radius) - 2px);
  border-width: 1px;
  background-color: hsl(var(--aui-background));
  padding: 0.25rem;
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
:where(.aui-assistant-action-bar-feedback-positive)[data-submitted]{
  --tw-text-opacity: 1;
  color: rgb(22 163 74 / var(--tw-text-opacity, 1));
}
:where(.aui-assistant-action-bar-feedback-positive)[data-submitted]:hover{
  --tw-text-opacity: 1;
  color: rgb(21 128 61 / var(--tw-text-opacity, 1));
}
:where(.aui-assistant-action-bar-feedback-positive)[data-submitted]:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(34 197 94 / var(--tw-text-opacity, 1));
}
:where(.aui-assistant-action-bar-feedback-positive)[data-submitted]:hover:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(74 222 128 / var(--tw-text-opacity, 1));
}
:where(.aui-assistant-action-bar-feedback-negative)[data-submitted]{
  --tw-text-opacity: 1;
  color: rgb(220 38 38 / var(--tw-text-opacity, 1));
}
:where(.aui-assistant-action-bar-feedback-negative)[data-submitted]:hover{
  --tw-text-opacity: 1;
  color: rgb(185 28 28 / var(--tw-text-opacity, 1));
}
:where(.aui-assistant-action-bar-feedback-negative)[data-submitted]:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity, 1));
}
:where(.aui-assistant-action-bar-feedback-negative)[data-submitted]:hover:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(248 113 113 / var(--tw-text-opacity, 1));
}
.aui-branch-picker-root{
  display: inline-flex;
  align-items: center;
  font-size: 0.75rem;
  line-height: 1rem;
  color: hsl(var(--aui-muted-foreground));
}
.aui-branch-picker-state{
  font-weight: 500;
}
.aui-text{
  white-space: pre-line;
}
@keyframes pulse{
  50%{
    opacity: .5;
  }
}
.aui-text-running::after{
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  font-family: Inter, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica, Arial, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol;
  --tw-content: '\25CF';
  content: var(--tw-content);
}
.aui-text-running:where([dir="ltr"], [dir="ltr"] *)::after{
  margin-left: 0.25rem;
}
.aui-text-running:where([dir="rtl"], [dir="rtl"] *)::after{
  margin-right: 0.25rem;
}
.aui-thread-list-root{
  display: flex;
  flex-direction: column;
  align-items: stretch;
  gap: 0.375rem;
}
.aui-thread-list-item{
  display: flex;
  align-items: center;
  gap: 0.5rem;
  border-radius: var(--radius);
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.aui-thread-list-item[data-active]{
  background-color: hsl(var(--aui-muted));
}
.aui-thread-list-item:hover{
  background-color: hsl(var(--aui-muted));
}
.aui-thread-list-item:focus-visible{
  background-color: hsl(var(--aui-muted));
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-color: hsl(var(--aui-ring));
}
.aui-thread-list-new{
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 0.25rem;
  border-radius: var(--radius);
  padding-left: 0.625rem;
  padding-right: 0.625rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  text-align: start;
}
.aui-thread-list-new[data-active]{
  background-color: hsl(var(--aui-muted));
}
.aui-thread-list-new:hover{
  background-color: hsl(var(--aui-muted));
}
.aui-thread-list-new > .lucide-plus{
  width: 1.25rem;
  height: 1.25rem;
}
.aui-thread-list-item-trigger{
  flex-grow: 1;
  padding-left: 0.75rem;
  padding-right: 0.75rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  text-align: start;
}
.aui-thread-list-item-title{
  font-size: 0.875rem;
  line-height: 1.25rem;
}
.aui-thread-list-item-archive{
  margin-left: auto;
  margin-right: 0.75rem;
  width: 1rem;
  height: 1rem;
  padding: 0px;
  color: hsl(var(--aui-foreground));
}
.aui-thread-list-item-archive:hover{
  color: hsl(var(--aui-primary));
}
.sr-only{
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}
.pointer-events-none{
  pointer-events: none;
}
.pointer-events-auto{
  pointer-events: auto;
}
.visible{
  visibility: visible;
}
.invisible{
  visibility: hidden;
}
.fixed{
  position: fixed;
}
.absolute{
  position: absolute;
}
.relative{
  position: relative;
}
.sticky{
  position: sticky;
}
.inset-0{
  inset: 0px;
}
.inset-x-0{
  left: 0px;
  right: 0px;
}
.inset-y-0{
  top: 0px;
  bottom: 0px;
}
.-right-3{
  right: -0.75rem;
}
.-top-3{
  top: -0.75rem;
}
.-top-8{
  top: -2rem;
}
.bottom-0{
  bottom: 0px;
}
.bottom-4{
  bottom: 1rem;
}
.bottom-full{
  bottom: 100%;
}
.left-0{
  left: 0px;
}
.left-2{
  left: 0.5rem;
}
.left-\[50\%\]{
  left: 50%;
}
.right-0{
  right: 0px;
}
.right-1{
  right: 0.25rem;
}
.right-2{
  right: 0.5rem;
}
.right-20{
  right: 5rem;
}
.right-4{
  right: 1rem;
}
.top-0{
  top: 0px;
}
.top-1{
  top: 0.25rem;
}
.top-2{
  top: 0.5rem;
}
.top-4{
  top: 1rem;
}
.top-\[50\%\]{
  top: 50%;
}
.z-10{
  z-index: 10;
}
.z-20{
  z-index: 20;
}
.z-50{
  z-index: 50;
}
.z-\[100\]{
  z-index: 100;
}
.z-\[9999\]{
  z-index: 9999;
}
.col-span-2{
  grid-column: span 2 / span 2;
}
.col-span-full{
  grid-column: 1 / -1;
}
.col-start-1{
  grid-column-start: 1;
}
.col-start-2{
  grid-column-start: 2;
}
.row-span-full{
  grid-row: 1 / -1;
}
.row-start-1{
  grid-row-start: 1;
}
.row-start-2{
  grid-row-start: 2;
}
.m-0{
  margin: 0px;
}
.m-4{
  margin: 1rem;
}
.m-auto{
  margin: auto;
}
.-mx-1{
  margin-left: -0.25rem;
  margin-right: -0.25rem;
}
.mx-8{
  margin-left: 2rem;
  margin-right: 2rem;
}
.mx-auto{
  margin-left: auto;
  margin-right: auto;
}
.my-1{
  margin-top: 0.25rem;
  margin-bottom: 0.25rem;
}
.my-1\.5{
  margin-top: 0.375rem;
  margin-bottom: 0.375rem;
}
.my-2\.5{
  margin-top: 0.625rem;
  margin-bottom: 0.625rem;
}
.my-4{
  margin-top: 1rem;
  margin-bottom: 1rem;
}
.my-5{
  margin-top: 1.25rem;
  margin-bottom: 1.25rem;
}
.my-auto{
  margin-top: auto;
  margin-bottom: auto;
}
.mb-1{
  margin-bottom: 0.25rem;
}
.mb-2{
  margin-bottom: 0.5rem;
}
.mb-3{
  margin-bottom: 0.75rem;
}
.mb-4{
  margin-bottom: 1rem;
}
.mb-5{
  margin-bottom: 1.25rem;
}
.mb-6{
  margin-bottom: 1.5rem;
}
.mb-8{
  margin-bottom: 2rem;
}
.mb-\[-24px\]{
  margin-bottom: -24px;
}
.mb-\[-8px\]{
  margin-bottom: -8px;
}
.ml-1{
  margin-left: 0.25rem;
}
.ml-10{
  margin-left: 2.5rem;
}
.ml-2{
  margin-left: 0.5rem;
}
.ml-3{
  margin-left: 0.75rem;
}
.ml-4{
  margin-left: 1rem;
}
.ml-6{
  margin-left: 1.5rem;
}
.ml-\[6px\]{
  margin-left: 6px;
}
.ml-auto{
  margin-left: auto;
}
.mr-1{
  margin-right: 0.25rem;
}
.mr-2{
  margin-right: 0.5rem;
}
.mr-4{
  margin-right: 1rem;
}
.mr-\[6px\]{
  margin-right: 6px;
}
.mr-auto{
  margin-right: auto;
}
.mt-1{
  margin-top: 0.25rem;
}
.mt-16{
  margin-top: 4rem;
}
.mt-2{
  margin-top: 0.5rem;
}
.mt-3{
  margin-top: 0.75rem;
}
.mt-32{
  margin-top: 8rem;
}
.mt-4{
  margin-top: 1rem;
}
.mt-5{
  margin-top: 1.25rem;
}
.mt-6{
  margin-top: 1.5rem;
}
.mt-8{
  margin-top: 2rem;
}
.mt-\[10px\]{
  margin-top: 10px;
}
.mt-auto{
  margin-top: auto;
}
.line-clamp-1{
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}
.line-clamp-3{
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}
.block{
  display: block;
}
.inline-block{
  display: inline-block;
}
.flex{
  display: flex;
}
.inline-flex{
  display: inline-flex;
}
.table{
  display: table;
}
.grid{
  display: grid;
}
.contents{
  display: contents;
}
.hidden{
  display: none;
}
.aspect-square{
  aspect-ratio: 1 / 1;
}
.size-10{
  width: 2.5rem;
  height: 2.5rem;
}
.size-4{
  width: 1rem;
  height: 1rem;
}
.size-6{
  width: 1.5rem;
  height: 1.5rem;
}
.size-7{
  width: 1.75rem;
  height: 1.75rem;
}
.size-8{
  width: 2rem;
  height: 2rem;
}
.h-1\.5{
  height: 0.375rem;
}
.h-10{
  height: 2.5rem;
}
.h-12{
  height: 3rem;
}
.h-2{
  height: 0.5rem;
}
.h-2\.5{
  height: 0.625rem;
}
.h-3{
  height: 0.75rem;
}
.h-3\.5{
  height: 0.875rem;
}
.h-32{
  height: 8rem;
}
.h-4{
  height: 1rem;
}
.h-5{
  height: 1.25rem;
}
.h-6{
  height: 1.5rem;
}
.h-7{
  height: 1.75rem;
}
.h-8{
  height: 2rem;
}
.h-9{
  height: 2.25rem;
}
.h-\[10px\]{
  height: 10px;
}
.h-\[180px\]{
  height: 180px;
}
.h-\[200px\]{
  height: 200px;
}
.h-\[26px\]{
  height: 26px;
}
.h-\[350px\]{
  height: 350px;
}
.h-\[36px\]{
  height: 36px;
}
.h-\[400px\]{
  height: 400px;
}
.h-\[48px\]{
  height: 48px;
}
.h-\[64px\]{
  height: 64px;
}
.h-\[var\(--radix-select-trigger-height\)\]{
  height: var(--radix-select-trigger-height);
}
.h-fit{
  height: fit-content;
}
.h-full{
  height: 100%;
}
.h-px{
  height: 1px;
}
.h-screen{
  height: 100vh;
}
.max-h-0{
  max-height: 0px;
}
.max-h-20{
  max-height: 5rem;
}
.max-h-40{
  max-height: 10rem;
}
.max-h-96{
  max-height: 24rem;
}
.max-h-\[300px\]{
  max-height: 300px;
}
.max-h-\[420px\]{
  max-height: 420px;
}
.max-h-\[450px\]{
  max-height: 450px;
}
.max-h-\[600px\]{
  max-height: 600px;
}
.max-h-\[60vh\]{
  max-height: 60vh;
}
.max-h-\[90vh\]{
  max-height: 90vh;
}
.max-h-screen{
  max-height: 100vh;
}
.min-h-\[60px\]{
  min-height: 60px;
}
.min-h-\[64px\]{
  min-height: 64px;
}
.min-h-fit{
  min-height: fit-content;
}
.min-h-full{
  min-height: 100%;
}
.min-h-screen{
  min-height: 100vh;
}
.w-1\.5{
  width: 0.375rem;
}
.w-1\/2{
  width: 50%;
}
.w-1\/3{
  width: 33.333333%;
}
.w-1\/4{
  width: 25%;
}
.w-10{
  width: 2.5rem;
}
.w-11{
  width: 2.75rem;
}
.w-12{
  width: 3rem;
}
.w-14{
  width: 3.5rem;
}
.w-16{
  width: 4rem;
}
.w-2{
  width: 0.5rem;
}
.w-2\.5{
  width: 0.625rem;
}
.w-2\/3{
  width: 66.666667%;
}
.w-2\/5{
  width: 40%;
}
.w-3{
  width: 0.75rem;
}
.w-3\.5{
  width: 0.875rem;
}
.w-3\/4{
  width: 75%;
}
.w-3\/5{
  width: 60%;
}
.w-32{
  width: 8rem;
}
.w-4{
  width: 1rem;
}
.w-40{
  width: 10rem;
}
.w-5{
  width: 1.25rem;
}
.w-56{
  width: 14rem;
}
.w-6{
  width: 1.5rem;
}
.w-64{
  width: 16rem;
}
.w-72{
  width: 18rem;
}
.w-8{
  width: 2rem;
}
.w-80{
  width: 20rem;
}
.w-9{
  width: 2.25rem;
}
.w-\[10\%\]{
  width: 10%;
}
.w-\[10px\]{
  width: 10px;
}
.w-\[125px\]{
  width: 125px;
}
.w-\[175px\]{
  width: 175px;
}
.w-\[20\%\]{
  width: 20%;
}
.w-\[250px\]{
  width: 250px;
}
.w-\[26px\]{
  width: 26px;
}
.w-\[280px\]{
  width: 280px;
}
.w-\[300px\]{
  width: 300px;
}
.w-\[30px\]{
  width: 30px;
}
.w-\[36px\]{
  width: 36px;
}
.w-\[400px\]{
  width: 400px;
}
.w-\[40px\]{
  width: 40px;
}
.w-\[48px\]{
  width: 48px;
}
.w-\[500px\]{
  width: 500px;
}
.w-\[50px\]{
  width: 50px;
}
.w-\[58px\]{
  width: 58px;
}
.w-\[60px\]{
  width: 60px;
}
.w-\[650px\]{
  width: 650px;
}
.w-\[70\%\]{
  width: 70%;
}
.w-\[80px\]{
  width: 80px;
}
.w-\[85\%\]{
  width: 85%;
}
.w-\[90\%\]{
  width: 90%;
}
.w-fit{
  width: fit-content;
}
.w-full{
  width: 100%;
}
.w-px{
  width: 1px;
}
.min-w-\[180px\]{
  min-width: 180px;
}
.min-w-\[191px\]{
  min-width: 191px;
}
.min-w-\[70vw\]{
  min-width: 70vw;
}
.min-w-\[8rem\]{
  min-width: 8rem;
}
.min-w-\[var\(--radix-select-trigger-width\)\]{
  min-width: var(--radix-select-trigger-width);
}
.min-w-full{
  min-width: 100%;
}
.max-w-2xl{
  max-width: 42rem;
}
.max-w-3xl{
  max-width: 48rem;
}
.max-w-\[300px\]{
  max-width: 300px;
}
.max-w-lg{
  max-width: 32rem;
}
.max-w-md{
  max-width: 28rem;
}
.max-w-xl{
  max-width: 36rem;
}
.flex-1{
  flex: 1 1 0%;
}
.flex-shrink-0{
  flex-shrink: 0;
}
.shrink-0{
  flex-shrink: 0;
}
.flex-grow{
  flex-grow: 1;
}
.grow{
  flex-grow: 1;
}
.basis-0{
  flex-basis: 0px;
}
.border-separate{
  border-collapse: separate;
}
.border-spacing-0{
  --tw-border-spacing-x: 0px;
  --tw-border-spacing-y: 0px;
  border-spacing: var(--tw-border-spacing-x) var(--tw-border-spacing-y);
}
.translate-x-\[-50\%\]{
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-y-\[-50\%\]{
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-100{
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-95{
  --tw-scale-x: .95;
  --tw-scale-y: .95;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-x-\[-1\]{
  --tw-scale-x: -1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.transform{
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
@keyframes gradient-x{
  0%, 100%{
    background-size: 200% 200%;
    background-position: left center;
  }
  50%{
    background-size: 200% 200%;
    background-position: right center;
  }
}
.animate-gradient-x{
  animation: gradient-x 3s ease-in-out infinite;
}
@keyframes pulse{
  50%{
    opacity: .5;
  }
}
.animate-pulse{
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
@keyframes spin{
  to{
    transform: rotate(360deg);
  }
}
.animate-spin{
  animation: spin 1s linear infinite;
}
.cursor-default{
  cursor: default;
}
.cursor-pointer{
  cursor: pointer;
}
.touch-none{
  touch-action: none;
}
.select-none{
  user-select: none;
}
.resize-none{
  resize: none;
}
.scroll-m-20{
  scroll-margin: 5rem;
}
.list-inside{
  list-style-position: inside;
}
.list-decimal{
  list-style-type: decimal;
}
.list-disc{
  list-style-type: disc;
}
.auto-rows-auto{
  grid-auto-rows: auto;
}
.grid-cols-1{
  grid-template-columns: repeat(1, minmax(0, 1fr));
}
.grid-cols-\[auto_auto_1fr\]{
  grid-template-columns: auto auto 1fr;
}
.grid-cols-\[minmax\(72px\2c 1fr\)_auto\]{
  grid-template-columns: minmax(72px,1fr) auto;
}
.grid-rows-\[auto_1fr\]{
  grid-template-rows: auto 1fr;
}
.flex-row{
  flex-direction: row;
}
.flex-col{
  flex-direction: column;
}
.flex-col-reverse{
  flex-direction: column-reverse;
}
.flex-wrap{
  flex-wrap: wrap;
}
.items-start{
  align-items: flex-start;
}
.items-end{
  align-items: flex-end;
}
.items-center{
  align-items: center;
}
.items-baseline{
  align-items: baseline;
}
.justify-start{
  justify-content: flex-start;
}
.justify-end{
  justify-content: flex-end;
}
.justify-center{
  justify-content: center;
}
.justify-between{
  justify-content: space-between;
}
.gap-0{
  gap: 0px;
}
.gap-1{
  gap: 0.25rem;
}
.gap-2{
  gap: 0.5rem;
}
.gap-3{
  gap: 0.75rem;
}
.gap-4{
  gap: 1rem;
}
.gap-6{
  gap: 1.5rem;
}
.gap-8{
  gap: 2rem;
}
.gap-y-2{
  row-gap: 0.5rem;
}
.space-x-2 > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(0.5rem * var(--tw-space-x-reverse));
  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-y-1\.5 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(0.375rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.375rem * var(--tw-space-y-reverse));
}
.space-y-2 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));
}
.space-y-6 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));
}
.overflow-auto{
  overflow: auto;
}
.overflow-hidden{
  overflow: hidden;
}
.overflow-x-auto{
  overflow-x: auto;
}
.overflow-y-auto{
  overflow-y: auto;
}
.overflow-x-hidden{
  overflow-x: hidden;
}
.scroll-smooth{
  scroll-behavior: smooth;
}
.truncate{
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.text-ellipsis{
  text-overflow: ellipsis;
}
.whitespace-normal{
  white-space: normal;
}
.whitespace-nowrap{
  white-space: nowrap;
}
.whitespace-pre-wrap{
  white-space: pre-wrap;
}
.text-wrap{
  text-wrap: wrap;
}
.text-pretty{
  text-wrap: pretty;
}
.break-words{
  overflow-wrap: break-word;
}
.break-all{
  word-break: break-all;
}
.rounded{
  border-radius: 0.25rem;
}
.rounded-2xl{
  border-radius: 1rem;
}
.rounded-3xl{
  border-radius: 1.5rem;
}
.rounded-full{
  border-radius: 9999px;
}
.rounded-lg{
  border-radius: var(--radius);
}
.rounded-md{
  border-radius: calc(var(--radius) - 2px);
}
.rounded-none{
  border-radius: 0px;
}
.rounded-sm{
  border-radius: calc(var(--radius) - 4px);
}
.rounded-xl{
  border-radius: 0.75rem;
}
.rounded-b-lg{
  border-bottom-right-radius: var(--radius);
  border-bottom-left-radius: var(--radius);
}
.rounded-t-lg{
  border-top-left-radius: var(--radius);
  border-top-right-radius: var(--radius);
}
.border{
  border-width: 1px;
}
.border-0{
  border-width: 0px;
}
.border-\[1px\]{
  border-width: 1px;
}
.border-b{
  border-bottom-width: 1px;
}
.border-l{
  border-left-width: 1px;
}
.border-l-2{
  border-left-width: 2px;
}
.border-l-\[1px\]{
  border-left-width: 1px;
}
.border-r{
  border-right-width: 1px;
}
.border-t{
  border-top-width: 1px;
}
.border-t-\[1px\]{
  border-top-width: 1px;
}
.border-none{
  border-style: none;
}
.border-destructive{
  border-color: hsl(var(--destructive));
}
.border-destructive\/50{
  border-color: hsl(var(--destructive) / 0.5);
}
.border-gray-100{
  --tw-border-opacity: 1;
  border-color: rgb(243 244 246 / var(--tw-border-opacity, 1));
}
.border-gray-200{
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));
}
.border-gray-500{
  --tw-border-opacity: 1;
  border-color: rgb(107 114 128 / var(--tw-border-opacity, 1));
}
.border-gray-500\/50{
  border-color: rgb(107 114 128 / 0.5);
}
.border-input{
  border-color: hsl(var(--input));
}
.border-primary{
  border-color: hsl(var(--primary));
}
.border-transparent{
  border-color: transparent;
}
.bg-\[\#3597934d\]{
  background-color: #3597934d;
}
.bg-aui-muted{
  background-color: hsl(var(--aui-muted));
}
.bg-background{
  background-color: hsl(var(--background));
}
.bg-black{
  --tw-bg-opacity: 1;
  background-color: rgb(0 0 0 / var(--tw-bg-opacity, 1));
}
.bg-black\/80{
  background-color: rgb(0 0 0 / 0.8);
}
.bg-blue-100{
  --tw-bg-opacity: 1;
  background-color: rgb(219 234 254 / var(--tw-bg-opacity, 1));
}
.bg-blue-50{
  --tw-bg-opacity: 1;
  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));
}
.bg-border{
  background-color: hsl(var(--border));
}
.bg-card{
  background-color: hsl(var(--card));
}
.bg-destructive{
  background-color: hsl(var(--destructive));
}
.bg-gray-100{
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
}
.bg-gray-200{
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));
}
.bg-gray-200\/50{
  background-color: rgb(229 231 235 / 0.5);
}
.bg-gray-50{
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}
.bg-gray-50\/70{
  background-color: rgb(249 250 251 / 0.7);
}
.bg-gray-500{
  --tw-bg-opacity: 1;
  background-color: rgb(107 114 128 / var(--tw-bg-opacity, 1));
}
.bg-gray-500\/50{
  background-color: rgb(107 114 128 / 0.5);
}
.bg-green-100{
  --tw-bg-opacity: 1;
  background-color: rgb(220 252 231 / var(--tw-bg-opacity, 1));
}
.bg-inherit{
  background-color: inherit;
}
.bg-muted{
  background-color: hsl(var(--muted));
}
.bg-popover{
  background-color: hsl(var(--popover));
}
.bg-primary{
  background-color: hsl(var(--primary));
}
.bg-primary\/10{
  background-color: hsl(var(--primary) / 0.1);
}
.bg-primary\/20{
  background-color: hsl(var(--primary) / 0.2);
}
.bg-secondary{
  background-color: hsl(var(--secondary));
}
.bg-transparent{
  background-color: transparent;
}
.bg-white{
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}
.bg-zinc-900{
  --tw-bg-opacity: 1;
  background-color: rgb(24 24 27 / var(--tw-bg-opacity, 1));
}
.bg-gradient-to-r{
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
}
.from-pink-50{
  --tw-gradient-from: #fdf2f8 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(253 242 248 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-pink-500\/50{
  --tw-gradient-from: rgb(236 72 153 / 0.5) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(236 72 153 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.via-purple-50{
  --tw-gradient-to: rgb(250 245 255 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #faf5ff var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-purple-500\/50{
  --tw-gradient-to: rgb(168 85 247 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(168 85 247 / 0.5) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.to-pink-50{
  --tw-gradient-to: #fdf2f8 var(--tw-gradient-to-position);
}
.to-pink-500\/50{
  --tw-gradient-to: rgb(236 72 153 / 0.5) var(--tw-gradient-to-position);
}
.fill-current{
  fill: currentColor;
}
.p-0{
  padding: 0px;
}
.p-1{
  padding: 0.25rem;
}
.p-10{
  padding: 2.5rem;
}
.p-2{
  padding: 0.5rem;
}
.p-4{
  padding: 1rem;
}
.p-5{
  padding: 1.25rem;
}
.p-6{
  padding: 1.5rem;
}
.p-8{
  padding: 2rem;
}
.p-\[1px\]{
  padding: 1px;
}
.px-1{
  padding-left: 0.25rem;
  padding-right: 0.25rem;
}
.px-2{
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}
.px-2\.5{
  padding-left: 0.625rem;
  padding-right: 0.625rem;
}
.px-3{
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}
.px-4{
  padding-left: 1rem;
  padding-right: 1rem;
}
.px-5{
  padding-left: 1.25rem;
  padding-right: 1.25rem;
}
.px-8{
  padding-left: 2rem;
  padding-right: 2rem;
}
.px-\[54px\]{
  padding-left: 54px;
  padding-right: 54px;
}
.py-0\.5{
  padding-top: 0.125rem;
  padding-bottom: 0.125rem;
}
.py-1{
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}
.py-1\.5{
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
}
.py-2{
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
.py-2\.5{
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
}
.py-3{
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}
.py-4{
  padding-top: 1rem;
  padding-bottom: 1rem;
}
.py-5{
  padding-top: 1.25rem;
  padding-bottom: 1.25rem;
}
.py-6{
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}
.pb-1{
  padding-bottom: 0.25rem;
}
.pb-2{
  padding-bottom: 0.5rem;
}
.pb-4{
  padding-bottom: 1rem;
}
.pb-\[2px\]{
  padding-bottom: 2px;
}
.pl-2{
  padding-left: 0.5rem;
}
.pl-6{
  padding-left: 1.5rem;
}
.pl-8{
  padding-left: 2rem;
}
.pl-\[6px\]{
  padding-left: 6px;
}
.pr-0{
  padding-right: 0px;
}
.pr-10{
  padding-right: 2.5rem;
}
.pr-2{
  padding-right: 0.5rem;
}
.pr-3{
  padding-right: 0.75rem;
}
.pr-4{
  padding-right: 1rem;
}
.pr-6{
  padding-right: 1.5rem;
}
.pr-8{
  padding-right: 2rem;
}
.pt-0{
  padding-top: 0px;
}
.pt-1{
  padding-top: 0.25rem;
}
.pt-3{
  padding-top: 0.75rem;
}
.pt-4{
  padding-top: 1rem;
}
.pt-8{
  padding-top: 2rem;
}
.pt-\[10px\]{
  padding-top: 10px;
}
.pt-\[2px\]{
  padding-top: 2px;
}
.text-left{
  text-align: left;
}
.text-center{
  text-align: center;
}
.text-right{
  text-align: right;
}
.font-mono{
  font-family: `"Fira Code"`, `ui-monospace`, `SFMono-Regular`, `Menlo`, `Monaco`, `Consolas`, `"Liberation Mono"`, `"Courier New"`, `monospace`;
}
.text-2xl{
  font-size: 1.5rem;
  line-height: 2rem;
}
.text-3xl{
  font-size: 1.875rem;
  line-height: 2.25rem;
}
.text-4xl{
  font-size: 2.25rem;
  line-height: 2.5rem;
}
.text-base{
  font-size: 1rem;
  line-height: 1.5rem;
}
.text-lg{
  font-size: 1.125rem;
  line-height: 1.75rem;
}
.text-sm{
  font-size: 0.875rem;
  line-height: 1.25rem;
}
.text-xl{
  font-size: 1.25rem;
  line-height: 1.75rem;
}
.text-xs{
  font-size: 0.75rem;
  line-height: 1rem;
}
.font-bold{
  font-weight: 700;
}
.font-extrabold{
  font-weight: 800;
}
.font-light{
  font-weight: 300;
}
.font-medium{
  font-weight: 500;
}
.font-normal{
  font-weight: 400;
}
.font-semibold{
  font-weight: 600;
}
.uppercase{
  text-transform: uppercase;
}
.lowercase{
  text-transform: lowercase;
}
.italic{
  font-style: italic;
}
.leading-7{
  line-height: 1.75rem;
}
.leading-none{
  line-height: 1;
}
.tracking-tight{
  letter-spacing: -0.025em;
}
.tracking-tighter{
  letter-spacing: -0.04em;
}
.tracking-widest{
  letter-spacing: 0.1em;
}
.text-\[\#575757\]{
  --tw-text-opacity: 1;
  color: rgb(87 87 87 / var(--tw-text-opacity, 1));
}
.text-\[\#CA632B\]{
  --tw-text-opacity: 1;
  color: rgb(202 99 43 / var(--tw-text-opacity, 1));
}
.text-black{
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity, 1));
}
.text-blue-500{
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity, 1));
}
.text-card-foreground{
  color: hsl(var(--card-foreground));
}
.text-current{
  color: currentColor;
}
.text-destructive{
  color: hsl(var(--destructive));
}
.text-destructive-foreground{
  color: hsl(var(--destructive-foreground));
}
.text-foreground{
  color: hsl(var(--foreground));
}
.text-foreground\/50{
  color: hsl(var(--foreground) / 0.5);
}
.text-gray-300{
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity, 1));
}
.text-gray-400{
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}
.text-gray-500{
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}
.text-gray-600{
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity, 1));
}
.text-gray-700{
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity, 1));
}
.text-gray-800{
  --tw-text-opacity: 1;
  color: rgb(31 41 55 / var(--tw-text-opacity, 1));
}
.text-muted-foreground{
  color: hsl(var(--muted-foreground));
}
.text-popover-foreground{
  color: hsl(var(--popover-foreground));
}
.text-primary{
  color: hsl(var(--primary));
}
.text-primary-foreground{
  color: hsl(var(--primary-foreground));
}
.text-red-300{
  --tw-text-opacity: 1;
  color: rgb(252 165 165 / var(--tw-text-opacity, 1));
}
.text-red-500{
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity, 1));
}
.text-secondary-foreground{
  color: hsl(var(--secondary-foreground));
}
.text-white{
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}
.underline{
  text-decoration-line: underline;
}
.underline-offset-4{
  text-underline-offset: 4px;
}
.opacity-0{
  opacity: 0;
}
.opacity-100{
  opacity: 1;
}
.opacity-50{
  opacity: 0.5;
}
.opacity-60{
  opacity: 0.6;
}
.opacity-70{
  opacity: 0.7;
}
.opacity-90{
  opacity: 0.9;
}
.shadow{
  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-lg{
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-md{
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-none{
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-sm{
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-xl{
  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.outline-none{
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.outline{
  outline-style: solid;
}
.ring-0{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.ring-gray-400{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(156 163 175 / var(--tw-ring-opacity, 1));
}
.ring-offset-2{
  --tw-ring-offset-width: 2px;
}
.ring-offset-background{
  --tw-ring-offset-color: hsl(var(--background));
}
.filter{
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.transition{
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-all{
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-colors{
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-opacity{
  transition-property: opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-transform{
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.duration-200{
  transition-duration: 200ms;
}
.duration-300{
  transition-duration: 300ms;
}
.duration-700{
  transition-duration: 700ms;
}
.ease-in{
  transition-timing-function: cubic-bezier(0.4, 0, 1, 1);
}
.ease-in-out{
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
@keyframes enter{
  from{
    opacity: var(--tw-enter-opacity, 1);
    transform: translate3d(var(--tw-enter-translate-x, 0), var(--tw-enter-translate-y, 0), 0) scale3d(var(--tw-enter-scale, 1), var(--tw-enter-scale, 1), var(--tw-enter-scale, 1)) rotate(var(--tw-enter-rotate, 0));
  }
}
@keyframes exit{
  to{
    opacity: var(--tw-exit-opacity, 1);
    transform: translate3d(var(--tw-exit-translate-x, 0), var(--tw-exit-translate-y, 0), 0) scale3d(var(--tw-exit-scale, 1), var(--tw-exit-scale, 1), var(--tw-exit-scale, 1)) rotate(var(--tw-exit-rotate, 0));
  }
}
.animate-in{
  animation-name: enter;
  animation-duration: 150ms;
  --tw-enter-opacity: initial;
  --tw-enter-scale: initial;
  --tw-enter-rotate: initial;
  --tw-enter-translate-x: initial;
  --tw-enter-translate-y: initial;
}
.fade-in-0{
  --tw-enter-opacity: 0;
}
.zoom-in-95{
  --tw-enter-scale: .95;
}
.duration-200{
  animation-duration: 200ms;
}
.duration-300{
  animation-duration: 300ms;
}
.duration-700{
  animation-duration: 700ms;
}
.ease-in{
  animation-timing-function: cubic-bezier(0.4, 0, 1, 1);
}
.ease-in-out{
  animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
.running{
  animation-play-state: running;
}
.scrollbar-thin::-webkit-scrollbar-track{
  background-color: var(--scrollbar-track);
  border-radius: var(--scrollbar-track-radius);
}
.scrollbar-thin::-webkit-scrollbar-track:hover{
  background-color: var(--scrollbar-track-hover, var(--scrollbar-track));
}
.scrollbar-thin::-webkit-scrollbar-track:active{
  background-color: var(--scrollbar-track-active, var(--scrollbar-track-hover, var(--scrollbar-track)));
}
.scrollbar-thin::-webkit-scrollbar-thumb{
  background-color: var(--scrollbar-thumb);
  border-radius: var(--scrollbar-thumb-radius);
}
.scrollbar-thin::-webkit-scrollbar-thumb:hover{
  background-color: var(--scrollbar-thumb-hover, var(--scrollbar-thumb));
}
.scrollbar-thin::-webkit-scrollbar-thumb:active{
  background-color: var(--scrollbar-thumb-active, var(--scrollbar-thumb-hover, var(--scrollbar-thumb)));
}
.scrollbar-thin::-webkit-scrollbar-corner{
  background-color: var(--scrollbar-corner);
  border-radius: var(--scrollbar-corner-radius);
}
.scrollbar-thin::-webkit-scrollbar-corner:hover{
  background-color: var(--scrollbar-corner-hover, var(--scrollbar-corner));
}
.scrollbar-thin::-webkit-scrollbar-corner:active{
  background-color: var(--scrollbar-corner-active, var(--scrollbar-corner-hover, var(--scrollbar-corner)));
}
.scrollbar-thin{
  scrollbar-width: thin;
  scrollbar-color: var(--scrollbar-thumb, initial) var(--scrollbar-track, initial);
}
.scrollbar-thin::-webkit-scrollbar{
  display: block;
  width: 8px;
  height: 8px;
}
.scrollbar-track-gray-100{
  --scrollbar-track: #f3f4f6 !important;
}
.scrollbar-thumb-gray-300{
  --scrollbar-thumb: #d1d5db !important;
}
.shadow-inner-right {
    box-shadow: inset -9px 0 6px -1px rgb(0 0 0 / 0.02);
  }
.shadow-inner-left {
    box-shadow: inset 9px 0 6px -1px rgb(0 0 0 / 0.02);
  }

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}
*::-webkit-scrollbar {
  display: none;
}

a {
  color: rgb(33, 118, 246);
}

/* Change highlight color */
::selection {
  background-color: rgba(53, 151, 147, 0.3);
}

::-moz-selection {
  background-color: rgba(53, 151, 147, 0.3);
}

.artifact-content {
  line-height: calc(
    1.625em + 2px
  ); /* Adjust the base value (1.625em) if needed */
}

.inline-code {
  font-family: monospace;
  background-color: #f0f0f0;
  padding: 2px 4px;
  border-radius: 4px;
  font-size: 0.9em;
}

.custom-blocknote-theme {
  --bn-colors-editor-text: #3f3f3f;
}

.custom-blocknote-theme a {
  color: #2563eb !important; /* Tailwind blue-600 */
  text-decoration: none;
}

.custom-blocknote-theme a:hover {
  text-decoration: underline;
  text-underline-offset: 2px;
}

.file\:border-0::file-selector-button{
  border-width: 0px;
}

.file\:bg-transparent::file-selector-button{
  background-color: transparent;
}

.file\:text-sm::file-selector-button{
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.file\:font-medium::file-selector-button{
  font-weight: 500;
}

.file\:text-foreground::file-selector-button{
  color: hsl(var(--foreground));
}

.placeholder\:text-muted-foreground::placeholder{
  color: hsl(var(--muted-foreground));
}

.before\:absolute::before{
  content: var(--tw-content);
  position: absolute;
}

.before\:inset-0::before{
  content: var(--tw-content);
  inset: 0px;
}

.before\:-z-10::before{
  content: var(--tw-content);
  z-index: -10;
}

@keyframes gradient-xy-enhanced{
  0%, 100%{
    content: var(--tw-content);
    background-size: 400% 400%;
    background-position: left center;
    transform: rotate(-3deg);
  }
  50%{
    content: var(--tw-content);
    background-size: 200% 200%;
    background-position: right center;
    transform: rotate(3deg);
  }
}

.before\:animate-gradient-xy-enhanced::before{
  content: var(--tw-content);
  animation: gradient-xy-enhanced 15s ease infinite;
}

.before\:rounded-md::before{
  content: var(--tw-content);
  border-radius: calc(var(--radius) - 2px);
}

.before\:bg-gradient-to-r::before{
  content: var(--tw-content);
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
}

.before\:from-pink-500\/20::before{
  content: var(--tw-content);
  --tw-gradient-from: rgb(236 72 153 / 0.2) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(236 72 153 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.before\:via-purple-500\/20::before{
  content: var(--tw-content);
  --tw-gradient-to: rgb(168 85 247 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(168 85 247 / 0.2) var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.before\:to-pink-500\/20::before{
  content: var(--tw-content);
  --tw-gradient-to: rgb(236 72 153 / 0.2) var(--tw-gradient-to-position);
}

.before\:p-\[4px\]::before{
  content: var(--tw-content);
  padding: 4px;
}

.before\:shadow-\[0_0_4px_rgba\(236\2c 72\2c 153\2c 0\.2\)\]::before{
  content: var(--tw-content);
  --tw-shadow: 0 0 4px rgba(236,72,153,0.2);
  --tw-shadow-colored: 0 0 4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.before\:blur-sm::before{
  content: var(--tw-content);
  --tw-blur: blur(4px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.after\:absolute::after{
  content: var(--tw-content);
  position: absolute;
}

.after\:inset-y-0::after{
  content: var(--tw-content);
  top: 0px;
  bottom: 0px;
}

.after\:left-1\/2::after{
  content: var(--tw-content);
  left: 50%;
}

.after\:w-1::after{
  content: var(--tw-content);
  width: 0.25rem;
}

.after\:-translate-x-1\/2::after{
  content: var(--tw-content);
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.first\:mt-0:first-child{
  margin-top: 0px;
}

.first\:rounded-tl-lg:first-child{
  border-top-left-radius: var(--radius);
}

.first\:border-t:first-child{
  border-top-width: 1px;
}

.last\:mb-0:last-child{
  margin-bottom: 0px;
}

.last\:rounded-tr-lg:last-child{
  border-top-right-radius: var(--radius);
}

.last\:border-r:last-child{
  border-right-width: 1px;
}

.focus-within\:border-aui-ring\/20:focus-within{
  border-color: hsl(var(--aui-ring) / 0.2);
}

.group:hover .group-hover\:opacity-100{
  opacity: 1;
}

.group.destructive .group-\[\.destructive\]\:border-muted\/40{
  border-color: hsl(var(--muted) / 0.4);
}

.group.destructive .group-\[\.destructive\]\:text-red-300{
  --tw-text-opacity: 1;
  color: rgb(252 165 165 / var(--tw-text-opacity, 1));
}

.peer:disabled ~ .peer-disabled\:cursor-not-allowed{
  cursor: not-allowed;
}

.peer:disabled ~ .peer-disabled\:opacity-70{
  opacity: 0.7;
}

.data-\[disabled\=true\]\:pointer-events-none[data-disabled="true"]{
  pointer-events: none;
}

.data-\[disabled\]\:pointer-events-none[data-disabled]{
  pointer-events: none;
}

.data-\[panel-group-direction\=vertical\]\:h-px[data-panel-group-direction="vertical"]{
  height: 1px;
}

.data-\[panel-group-direction\=vertical\]\:w-full[data-panel-group-direction="vertical"]{
  width: 100%;
}

.data-\[side\=bottom\]\:translate-y-1[data-side="bottom"]{
  --tw-translate-y: 0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[side\=left\]\:-translate-x-1[data-side="left"]{
  --tw-translate-x: -0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[side\=right\]\:translate-x-1[data-side="right"]{
  --tw-translate-x: 0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[side\=top\]\:-translate-y-1[data-side="top"]{
  --tw-translate-y: -0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[swipe\=cancel\]\:translate-x-0[data-swipe="cancel"]{
  --tw-translate-x: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[swipe\=end\]\:translate-x-\[var\(--radix-toast-swipe-end-x\)\][data-swipe="end"]{
  --tw-translate-x: var(--radix-toast-swipe-end-x);
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[swipe\=move\]\:translate-x-\[var\(--radix-toast-swipe-move-x\)\][data-swipe="move"]{
  --tw-translate-x: var(--radix-toast-swipe-move-x);
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

@keyframes accordion-up{
  from{
    height: var(--radix-accordion-content-height);
  }
  to{
    height: 0;
  }
}

.data-\[state\=closed\]\:animate-accordion-up[data-state="closed"]{
  animation: accordion-up 0.2s ease-out;
}

@keyframes accordion-down{
  from{
    height: 0;
  }
  to{
    height: var(--radix-accordion-content-height);
  }
}

.data-\[state\=open\]\:animate-accordion-down[data-state="open"]{
  animation: accordion-down 0.2s ease-out;
}

.data-\[panel-group-direction\=vertical\]\:flex-col[data-panel-group-direction="vertical"]{
  flex-direction: column;
}

.data-\[selected\=true\]\:bg-accent[data-selected="true"]{
  background-color: hsl(var(--accent));
}

.data-\[state\=checked\]\:bg-primary[data-state="checked"]{
  background-color: hsl(var(--primary));
}

.data-\[state\=open\]\:bg-accent[data-state="open"]{
  background-color: hsl(var(--accent));
}

.data-\[state\=open\]\:bg-secondary[data-state="open"]{
  background-color: hsl(var(--secondary));
}

.data-\[selected\=true\]\:text-accent-foreground[data-selected="true"]{
  color: hsl(var(--accent-foreground));
}

.data-\[state\=checked\]\:text-primary-foreground[data-state="checked"]{
  color: hsl(var(--primary-foreground));
}

.data-\[state\=open\]\:text-muted-foreground[data-state="open"]{
  color: hsl(var(--muted-foreground));
}

.data-\[disabled\=true\]\:opacity-50[data-disabled="true"]{
  opacity: 0.5;
}

.data-\[disabled\]\:opacity-50[data-disabled]{
  opacity: 0.5;
}

.data-\[swipe\=move\]\:transition-none[data-swipe="move"]{
  transition-property: none;
}

.data-\[state\=closed\]\:duration-300[data-state="closed"]{
  transition-duration: 300ms;
}

.data-\[state\=open\]\:duration-500[data-state="open"]{
  transition-duration: 500ms;
}

.data-\[state\=open\]\:animate-in[data-state="open"]{
  animation-name: enter;
  animation-duration: 150ms;
  --tw-enter-opacity: initial;
  --tw-enter-scale: initial;
  --tw-enter-rotate: initial;
  --tw-enter-translate-x: initial;
  --tw-enter-translate-y: initial;
}

.data-\[state\=closed\]\:animate-out[data-state="closed"]{
  animation-name: exit;
  animation-duration: 150ms;
  --tw-exit-opacity: initial;
  --tw-exit-scale: initial;
  --tw-exit-rotate: initial;
  --tw-exit-translate-x: initial;
  --tw-exit-translate-y: initial;
}

.data-\[swipe\=end\]\:animate-out[data-swipe="end"]{
  animation-name: exit;
  animation-duration: 150ms;
  --tw-exit-opacity: initial;
  --tw-exit-scale: initial;
  --tw-exit-rotate: initial;
  --tw-exit-translate-x: initial;
  --tw-exit-translate-y: initial;
}

.data-\[state\=closed\]\:fade-out-0[data-state="closed"]{
  --tw-exit-opacity: 0;
}

.data-\[state\=closed\]\:fade-out-80[data-state="closed"]{
  --tw-exit-opacity: 0.8;
}

.data-\[state\=open\]\:fade-in-0[data-state="open"]{
  --tw-enter-opacity: 0;
}

.data-\[state\=closed\]\:zoom-out-95[data-state="closed"]{
  --tw-exit-scale: .95;
}

.data-\[state\=open\]\:zoom-in-95[data-state="open"]{
  --tw-enter-scale: .95;
}

.data-\[side\=bottom\]\:slide-in-from-top-2[data-side="bottom"]{
  --tw-enter-translate-y: -0.5rem;
}

.data-\[side\=left\]\:slide-in-from-right-2[data-side="left"]{
  --tw-enter-translate-x: 0.5rem;
}

.data-\[side\=right\]\:slide-in-from-left-2[data-side="right"]{
  --tw-enter-translate-x: -0.5rem;
}

.data-\[side\=top\]\:slide-in-from-bottom-2[data-side="top"]{
  --tw-enter-translate-y: 0.5rem;
}

.data-\[state\=closed\]\:slide-out-to-bottom[data-state="closed"]{
  --tw-exit-translate-y: 100%;
}

.data-\[state\=closed\]\:slide-out-to-left[data-state="closed"]{
  --tw-exit-translate-x: -100%;
}

.data-\[state\=closed\]\:slide-out-to-left-1\/2[data-state="closed"]{
  --tw-exit-translate-x: -50%;
}

.data-\[state\=closed\]\:slide-out-to-right[data-state="closed"]{
  --tw-exit-translate-x: 100%;
}

.data-\[state\=closed\]\:slide-out-to-right-full[data-state="closed"]{
  --tw-exit-translate-x: 100%;
}

.data-\[state\=closed\]\:slide-out-to-top[data-state="closed"]{
  --tw-exit-translate-y: -100%;
}

.data-\[state\=closed\]\:slide-out-to-top-\[48\%\][data-state="closed"]{
  --tw-exit-translate-y: -48%;
}

.data-\[state\=open\]\:slide-in-from-bottom[data-state="open"]{
  --tw-enter-translate-y: 100%;
}

.data-\[state\=open\]\:slide-in-from-left[data-state="open"]{
  --tw-enter-translate-x: -100%;
}

.data-\[state\=open\]\:slide-in-from-left-1\/2[data-state="open"]{
  --tw-enter-translate-x: -50%;
}

.data-\[state\=open\]\:slide-in-from-right[data-state="open"]{
  --tw-enter-translate-x: 100%;
}

.data-\[state\=open\]\:slide-in-from-top[data-state="open"]{
  --tw-enter-translate-y: -100%;
}

.data-\[state\=open\]\:slide-in-from-top-\[48\%\][data-state="open"]{
  --tw-enter-translate-y: -48%;
}

.data-\[state\=open\]\:slide-in-from-top-full[data-state="open"]{
  --tw-enter-translate-y: -100%;
}

.data-\[state\=closed\]\:duration-300[data-state="closed"]{
  animation-duration: 300ms;
}

.data-\[state\=open\]\:duration-500[data-state="open"]{
  animation-duration: 500ms;
}

.data-\[panel-group-direction\=vertical\]\:after\:left-0[data-panel-group-direction="vertical"]::after{
  content: var(--tw-content);
  left: 0px;
}

.data-\[panel-group-direction\=vertical\]\:after\:h-1[data-panel-group-direction="vertical"]::after{
  content: var(--tw-content);
  height: 0.25rem;
}

.data-\[panel-group-direction\=vertical\]\:after\:w-full[data-panel-group-direction="vertical"]::after{
  content: var(--tw-content);
  width: 100%;
}

.data-\[panel-group-direction\=vertical\]\:after\:-translate-y-1\/2[data-panel-group-direction="vertical"]::after{
  content: var(--tw-content);
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[panel-group-direction\=vertical\]\:after\:translate-x-0[data-panel-group-direction="vertical"]::after{
  content: var(--tw-content);
  --tw-translate-x: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:border-red-600:hover{
  --tw-border-opacity: 1;
  border-color: rgb(220 38 38 / var(--tw-border-opacity, 1));
}

.hover\:bg-accent:hover{
  background-color: hsl(var(--accent));
}

.hover\:bg-accent\/50:hover{
  background-color: hsl(var(--accent) / 0.5);
}

.hover\:bg-background:hover{
  background-color: hsl(var(--background));
}

.hover\:bg-blue-100:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(219 234 254 / var(--tw-bg-opacity, 1));
}

.hover\:bg-destructive\/90:hover{
  background-color: hsl(var(--destructive) / 0.9);
}

.hover\:bg-gray-100:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-800:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));
}

.hover\:bg-primary\/90:hover{
  background-color: hsl(var(--primary) / 0.9);
}

.hover\:bg-red-100:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(254 226 226 / var(--tw-bg-opacity, 1));
}

.hover\:bg-secondary:hover{
  background-color: hsl(var(--secondary));
}

.hover\:bg-secondary\/80:hover{
  background-color: hsl(var(--secondary) / 0.8);
}

.hover\:bg-transparent:hover{
  background-color: transparent;
}

.hover\:text-\[\#CA632B\]\/95:hover{
  color: rgb(202 99 43 / 0.95);
}

.hover\:text-accent-foreground:hover{
  color: hsl(var(--accent-foreground));
}

.hover\:text-black:hover{
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity, 1));
}

.hover\:text-foreground:hover{
  color: hsl(var(--foreground));
}

.hover\:text-gray-700:hover{
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity, 1));
}

.hover\:text-gray-800:hover{
  --tw-text-opacity: 1;
  color: rgb(31 41 55 / var(--tw-text-opacity, 1));
}

.hover\:text-gray-900:hover{
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity, 1));
}

.hover\:text-red-500:hover{
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity, 1));
}

.hover\:underline:hover{
  text-decoration-line: underline;
}

.hover\:opacity-100:hover{
  opacity: 1;
}

.hover\:ring-2:hover{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.group.destructive .group-\[\.destructive\]\:hover\:border-destructive\/30:hover{
  border-color: hsl(var(--destructive) / 0.3);
}

.group.destructive .group-\[\.destructive\]\:hover\:bg-destructive:hover{
  background-color: hsl(var(--destructive));
}

.group.destructive .group-\[\.destructive\]\:hover\:text-destructive-foreground:hover{
  color: hsl(var(--destructive-foreground));
}

.group.destructive .group-\[\.destructive\]\:hover\:text-red-50:hover{
  --tw-text-opacity: 1;
  color: rgb(254 242 242 / var(--tw-text-opacity, 1));
}

.focus\:bg-accent:focus{
  background-color: hsl(var(--accent));
}

.focus\:text-accent-foreground:focus{
  color: hsl(var(--accent-foreground));
}

.focus\:opacity-100:focus{
  opacity: 1;
}

.focus\:outline-none:focus{
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.focus\:ring-0:focus{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus\:ring-1:focus{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus\:ring-2:focus{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus\:ring-ring:focus{
  --tw-ring-color: hsl(var(--ring));
}

.focus\:ring-offset-0:focus{
  --tw-ring-offset-width: 0px;
}

.focus\:ring-offset-2:focus{
  --tw-ring-offset-width: 2px;
}

.group.destructive .group-\[\.destructive\]\:focus\:ring-destructive:focus{
  --tw-ring-color: hsl(var(--destructive));
}

.group.destructive .group-\[\.destructive\]\:focus\:ring-red-400:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(248 113 113 / var(--tw-ring-opacity, 1));
}

.group.destructive .group-\[\.destructive\]\:focus\:ring-offset-red-600:focus{
  --tw-ring-offset-color: #dc2626;
}

.focus-visible\:outline-none:focus-visible{
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.focus-visible\:ring-0:focus-visible{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus-visible\:ring-1:focus-visible{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus-visible\:ring-ring:focus-visible{
  --tw-ring-color: hsl(var(--ring));
}

.focus-visible\:ring-offset-0:focus-visible{
  --tw-ring-offset-width: 0px;
}

.focus-visible\:ring-offset-1:focus-visible{
  --tw-ring-offset-width: 1px;
}

.disabled\:pointer-events-none:disabled{
  pointer-events: none;
}

.disabled\:invisible:disabled{
  visibility: hidden;
}

.disabled\:cursor-not-allowed:disabled{
  cursor: not-allowed;
}

.disabled\:opacity-50:disabled{
  opacity: 0.5;
}

.dark\:border-r:is(.dark *){
  border-right-width: 1px;
}

.dark\:border-destructive:is(.dark *){
  border-color: hsl(var(--destructive));
}

.dark\:bg-gray-950:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(3 7 18 / var(--tw-bg-opacity, 1));
}

@media (min-width: 640px){
  .sm\:bottom-0{
    bottom: 0px;
  }
  .sm\:right-0{
    right: 0px;
  }
  .sm\:top-auto{
    top: auto;
  }
  .sm\:inline{
    display: inline;
  }
  .sm\:w-\[350px\]{
    width: 350px;
  }
  .sm\:max-w-sm{
    max-width: 24rem;
  }
  .sm\:flex-row{
    flex-direction: row;
  }
  .sm\:flex-col{
    flex-direction: column;
  }
  .sm\:justify-end{
    justify-content: flex-end;
  }
  .sm\:space-x-2 > :not([hidden]) ~ :not([hidden]){
    --tw-space-x-reverse: 0;
    margin-right: calc(0.5rem * var(--tw-space-x-reverse));
    margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
  }
  .sm\:rounded-lg{
    border-radius: var(--radius);
  }
  .sm\:text-left{
    text-align: left;
  }
  .data-\[state\=open\]\:sm\:slide-in-from-bottom-full[data-state="open"]{
    --tw-enter-translate-y: 100%;
  }
}

@media (min-width: 768px){
  .md\:right-8{
    right: 2rem;
  }
  .md\:top-8{
    top: 2rem;
  }
  .md\:flex{
    display: flex;
  }
  .md\:grid{
    display: grid;
  }
  .md\:hidden{
    display: none;
  }
  .md\:max-w-\[420px\]{
    max-width: 420px;
  }
}

@media (min-width: 1024px){
  .lg\:flex{
    display: flex;
  }
  .lg\:max-w-none{
    max-width: none;
  }
  .lg\:grid-cols-2{
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
  .lg\:p-8{
    padding: 2rem;
  }
  .lg\:px-0{
    padding-left: 0px;
    padding-right: 0px;
  }
}

.\[\&\+div\]\:text-xs+div{
  font-size: 0.75rem;
  line-height: 1rem;
}

.\[\&\:last-child\>td\:first-child\]\:rounded-bl-lg:last-child>td:first-child{
  border-bottom-left-radius: var(--radius);
}

.\[\&\:last-child\>td\:last-child\]\:rounded-br-lg:last-child>td:last-child{
  border-bottom-right-radius: var(--radius);
}

.\[\&\>a\]\:text-xs>a{
  font-size: 0.75rem;
  line-height: 1rem;
}

.\[\&\>a\]\:no-underline>a{
  text-decoration-line: none;
}

.\[\&\>li\]\:mt-2>li{
  margin-top: 0.5rem;
}

.\[\&\>span\]\:line-clamp-1>span{
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.\[\&\>span\]\:text-xs>span{
  font-size: 0.75rem;
  line-height: 1rem;
}

.\[\&\>svg\+div\]\:translate-y-\[-3px\]>svg+div{
  --tw-translate-y: -3px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.\[\&\>svg\]\:absolute>svg{
  position: absolute;
}

.\[\&\>svg\]\:left-4>svg{
  left: 1rem;
}

.\[\&\>svg\]\:top-4>svg{
  top: 1rem;
}

.\[\&\>svg\]\:size-4>svg{
  width: 1rem;
  height: 1rem;
}

.\[\&\>svg\]\:shrink-0>svg{
  flex-shrink: 0;
}

.\[\&\>svg\]\:rounded-full>svg{
  border-radius: 9999px;
}

.\[\&\>svg\]\:bg-background>svg{
  background-color: hsl(var(--background));
}

.\[\&\>svg\]\:text-destructive>svg{
  color: hsl(var(--destructive));
}

.\[\&\>svg\]\:text-foreground>svg{
  color: hsl(var(--foreground));
}

.\[\&\>svg\~\*\]\:pl-7>svg~*{
  padding-left: 1.75rem;
}

.\[\&\[align\=center\]\]\:text-center[align=center]{
  text-align: center;
}

.\[\&\[align\=right\]\]\:text-right[align=right]{
  text-align: right;
}

.\[\&\[data-panel-group-direction\=vertical\]\>div\]\:rotate-90[data-panel-group-direction=vertical]>div{
  --tw-rotate: 90deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.\[\&\[data-state\=open\]\>svg\]\:rotate-180[data-state=open]>svg{
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.\[\&_\[cmdk-group-heading\]\]\:px-2 [cmdk-group-heading]{
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}

.\[\&_\[cmdk-group-heading\]\]\:py-1\.5 [cmdk-group-heading]{
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
}

.\[\&_\[cmdk-group-heading\]\]\:text-xs [cmdk-group-heading]{
  font-size: 0.75rem;
  line-height: 1rem;
}

.\[\&_\[cmdk-group-heading\]\]\:font-medium [cmdk-group-heading]{
  font-weight: 500;
}

.\[\&_\[cmdk-group-heading\]\]\:text-muted-foreground [cmdk-group-heading]{
  color: hsl(var(--muted-foreground));
}

.\[\&_\[cmdk-group\]\:not\(\[hidden\]\)_\~\[cmdk-group\]\]\:pt-0 [cmdk-group]:not([hidden]) ~[cmdk-group]{
  padding-top: 0px;
}

.\[\&_\[cmdk-group\]\]\:px-2 [cmdk-group]{
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}

.\[\&_\[cmdk-input-wrapper\]_svg\]\:h-5 [cmdk-input-wrapper] svg{
  height: 1.25rem;
}

.\[\&_\[cmdk-input-wrapper\]_svg\]\:w-5 [cmdk-input-wrapper] svg{
  width: 1.25rem;
}

.\[\&_\[cmdk-input\]\]\:h-12 [cmdk-input]{
  height: 3rem;
}

.\[\&_\[cmdk-item\]\]\:px-2 [cmdk-item]{
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}

.\[\&_\[cmdk-item\]\]\:py-3 [cmdk-item]{
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}

.\[\&_\[cmdk-item\]_svg\]\:h-5 [cmdk-item] svg{
  height: 1.25rem;
}

.\[\&_\[cmdk-item\]_svg\]\:w-5 [cmdk-item] svg{
  width: 1.25rem;
}

.\[\&_p\]\:leading-relaxed p{
  line-height: 1.625;
}

.\[\&_svg\]\:pointer-events-none svg{
  pointer-events: none;
}

.\[\&_svg\]\:size-4 svg{
  width: 1rem;
  height: 1rem;
}

.\[\&_svg\]\:shrink-0 svg{
  flex-shrink: 0;
}

/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[2].use[1]!../../node_modules/next/dist/build/webpack/loaders/next-font-loader/index.js??ruleSet[1].rules[14].oneOf[2].use[2]!../../node_modules/next/font/google/target.css?{"path":"src\\app\\layout.tsx","import":"Inter","arguments":[{"subsets":["latin"]}],"variableName":"inter"} ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/* cyrillic-ext */
@font-face {
  font-family: '__Inter_e8ce0c';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/55c55f0601d81cf3-s.woff2) format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: '__Inter_e8ce0c';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/26a46d62cd723877-s.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* greek-ext */
@font-face {
  font-family: '__Inter_e8ce0c';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/97e0cb1ae144a2a9-s.woff2) format('woff2');
  unicode-range: U+1F00-1FFF;
}
/* greek */
@font-face {
  font-family: '__Inter_e8ce0c';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/581909926a08bbc8-s.woff2) format('woff2');
  unicode-range: U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;
}
/* vietnamese */
@font-face {
  font-family: '__Inter_e8ce0c';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/df0a9ae256c0569c-s.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: '__Inter_e8ce0c';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/8e9860b6e62d6359-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: '__Inter_e8ce0c';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/e4af272ccee01ff0-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}@font-face {font-family: '__Inter_Fallback_e8ce0c';src: local("Arial");ascent-override: 90.49%;descent-override: 22.56%;line-gap-override: 0.00%;size-adjust: 107.06%
}.__className_e8ce0c {font-family: '__Inter_e8ce0c', '__Inter_Fallback_e8ce0c';font-style: normal
}

