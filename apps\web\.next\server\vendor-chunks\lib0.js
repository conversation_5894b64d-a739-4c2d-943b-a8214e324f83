"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/lib0";
exports.ids = ["vendor-chunks/lib0"];
exports.modules = {

/***/ "(ssr)/../../node_modules/lib0/array.js":
/*!****************************************!*\
  !*** ../../node_modules/lib0/array.js ***!
  \****************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   appendTo: () => (/* binding */ appendTo),\n/* harmony export */   copy: () => (/* binding */ copy),\n/* harmony export */   create: () => (/* binding */ create),\n/* harmony export */   equalFlat: () => (/* binding */ equalFlat),\n/* harmony export */   every: () => (/* binding */ every),\n/* harmony export */   flatten: () => (/* binding */ flatten),\n/* harmony export */   fold: () => (/* binding */ fold),\n/* harmony export */   from: () => (/* binding */ from),\n/* harmony export */   isArray: () => (/* binding */ isArray),\n/* harmony export */   last: () => (/* binding */ last),\n/* harmony export */   map: () => (/* binding */ map),\n/* harmony export */   some: () => (/* binding */ some),\n/* harmony export */   unfold: () => (/* binding */ unfold),\n/* harmony export */   unique: () => (/* binding */ unique),\n/* harmony export */   uniqueBy: () => (/* binding */ uniqueBy)\n/* harmony export */ });\n/* harmony import */ var _set_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./set.js */ \"(ssr)/../../node_modules/lib0/set.js\");\n/**\n * Utility module to work with Arrays.\n *\n * @module array\n */\n\n\n\n/**\n * Return the last element of an array. The element must exist\n *\n * @template L\n * @param {ArrayLike<L>} arr\n * @return {L}\n */\nconst last = arr => arr[arr.length - 1]\n\n/**\n * @template C\n * @return {Array<C>}\n */\nconst create = () => /** @type {Array<C>} */ ([])\n\n/**\n * @template D\n * @param {Array<D>} a\n * @return {Array<D>}\n */\nconst copy = a => /** @type {Array<D>} */ (a.slice())\n\n/**\n * Append elements from src to dest\n *\n * @template M\n * @param {Array<M>} dest\n * @param {Array<M>} src\n */\nconst appendTo = (dest, src) => {\n  for (let i = 0; i < src.length; i++) {\n    dest.push(src[i])\n  }\n}\n\n/**\n * Transforms something array-like to an actual Array.\n *\n * @function\n * @template T\n * @param {ArrayLike<T>|Iterable<T>} arraylike\n * @return {T}\n */\nconst from = Array.from\n\n/**\n * True iff condition holds on every element in the Array.\n *\n * @function\n * @template ITEM\n * @template {ArrayLike<ITEM>} ARR\n *\n * @param {ARR} arr\n * @param {function(ITEM, number, ARR):boolean} f\n * @return {boolean}\n */\nconst every = (arr, f) => {\n  for (let i = 0; i < arr.length; i++) {\n    if (!f(arr[i], i, arr)) {\n      return false\n    }\n  }\n  return true\n}\n\n/**\n * True iff condition holds on some element in the Array.\n *\n * @function\n * @template S\n * @template {ArrayLike<S>} ARR\n * @param {ARR} arr\n * @param {function(S, number, ARR):boolean} f\n * @return {boolean}\n */\nconst some = (arr, f) => {\n  for (let i = 0; i < arr.length; i++) {\n    if (f(arr[i], i, arr)) {\n      return true\n    }\n  }\n  return false\n}\n\n/**\n * @template ELEM\n *\n * @param {ArrayLike<ELEM>} a\n * @param {ArrayLike<ELEM>} b\n * @return {boolean}\n */\nconst equalFlat = (a, b) => a.length === b.length && every(a, (item, index) => item === b[index])\n\n/**\n * @template ELEM\n * @param {Array<Array<ELEM>>} arr\n * @return {Array<ELEM>}\n */\nconst flatten = arr => fold(arr, /** @type {Array<ELEM>} */ ([]), (acc, val) => acc.concat(val))\n\n/**\n * @template T\n * @param {number} len\n * @param {function(number, Array<T>):T} f\n * @return {Array<T>}\n */\nconst unfold = (len, f) => {\n  const array = new Array(len)\n  for (let i = 0; i < len; i++) {\n    array[i] = f(i, array)\n  }\n  return array\n}\n\n/**\n * @template T\n * @template RESULT\n * @param {Array<T>} arr\n * @param {RESULT} seed\n * @param {function(RESULT, T, number):RESULT} folder\n */\nconst fold = (arr, seed, folder) => arr.reduce(folder, seed)\n\nconst isArray = Array.isArray\n\n/**\n * @template T\n * @param {Array<T>} arr\n * @return {Array<T>}\n */\nconst unique = arr => from(_set_js__WEBPACK_IMPORTED_MODULE_0__.from(arr))\n\n/**\n * @template T\n * @template M\n * @param {ArrayLike<T>} arr\n * @param {function(T):M} mapper\n * @return {Array<T>}\n */\nconst uniqueBy = (arr, mapper) => {\n  /**\n   * @type {Set<M>}\n   */\n  const happened = _set_js__WEBPACK_IMPORTED_MODULE_0__.create()\n  /**\n   * @type {Array<T>}\n   */\n  const result = []\n  for (let i = 0; i < arr.length; i++) {\n    const el = arr[i]\n    const mapped = mapper(el)\n    if (!happened.has(mapped)) {\n      happened.add(mapped)\n      result.push(el)\n    }\n  }\n  return result\n}\n\n/**\n * @template {ArrayLike<any>} ARR\n * @template {function(ARR extends ArrayLike<infer T> ? T : never, number, ARR):any} MAPPER\n * @param {ARR} arr\n * @param {MAPPER} mapper\n * @return {Array<MAPPER extends function(...any): infer M ? M : never>}\n */\nconst map = (arr, mapper) => {\n  /**\n   * @type {Array<any>}\n   */\n  const res = Array(arr.length)\n  for (let i = 0; i < arr.length; i++) {\n    res[i] = mapper(/** @type {any} */ (arr[i]), i, /** @type {any} */ (arr))\n  }\n  return /** @type {any} */ (res)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/lib0/array.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/lib0/binary.js":
/*!*****************************************!*\
  !*** ../../node_modules/lib0/binary.js ***!
  \*****************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BIT1: () => (/* binding */ BIT1),\n/* harmony export */   BIT10: () => (/* binding */ BIT10),\n/* harmony export */   BIT11: () => (/* binding */ BIT11),\n/* harmony export */   BIT12: () => (/* binding */ BIT12),\n/* harmony export */   BIT13: () => (/* binding */ BIT13),\n/* harmony export */   BIT14: () => (/* binding */ BIT14),\n/* harmony export */   BIT15: () => (/* binding */ BIT15),\n/* harmony export */   BIT16: () => (/* binding */ BIT16),\n/* harmony export */   BIT17: () => (/* binding */ BIT17),\n/* harmony export */   BIT18: () => (/* binding */ BIT18),\n/* harmony export */   BIT19: () => (/* binding */ BIT19),\n/* harmony export */   BIT2: () => (/* binding */ BIT2),\n/* harmony export */   BIT20: () => (/* binding */ BIT20),\n/* harmony export */   BIT21: () => (/* binding */ BIT21),\n/* harmony export */   BIT22: () => (/* binding */ BIT22),\n/* harmony export */   BIT23: () => (/* binding */ BIT23),\n/* harmony export */   BIT24: () => (/* binding */ BIT24),\n/* harmony export */   BIT25: () => (/* binding */ BIT25),\n/* harmony export */   BIT26: () => (/* binding */ BIT26),\n/* harmony export */   BIT27: () => (/* binding */ BIT27),\n/* harmony export */   BIT28: () => (/* binding */ BIT28),\n/* harmony export */   BIT29: () => (/* binding */ BIT29),\n/* harmony export */   BIT3: () => (/* binding */ BIT3),\n/* harmony export */   BIT30: () => (/* binding */ BIT30),\n/* harmony export */   BIT31: () => (/* binding */ BIT31),\n/* harmony export */   BIT32: () => (/* binding */ BIT32),\n/* harmony export */   BIT4: () => (/* binding */ BIT4),\n/* harmony export */   BIT5: () => (/* binding */ BIT5),\n/* harmony export */   BIT6: () => (/* binding */ BIT6),\n/* harmony export */   BIT7: () => (/* binding */ BIT7),\n/* harmony export */   BIT8: () => (/* binding */ BIT8),\n/* harmony export */   BIT9: () => (/* binding */ BIT9),\n/* harmony export */   BITS0: () => (/* binding */ BITS0),\n/* harmony export */   BITS1: () => (/* binding */ BITS1),\n/* harmony export */   BITS10: () => (/* binding */ BITS10),\n/* harmony export */   BITS11: () => (/* binding */ BITS11),\n/* harmony export */   BITS12: () => (/* binding */ BITS12),\n/* harmony export */   BITS13: () => (/* binding */ BITS13),\n/* harmony export */   BITS14: () => (/* binding */ BITS14),\n/* harmony export */   BITS15: () => (/* binding */ BITS15),\n/* harmony export */   BITS16: () => (/* binding */ BITS16),\n/* harmony export */   BITS17: () => (/* binding */ BITS17),\n/* harmony export */   BITS18: () => (/* binding */ BITS18),\n/* harmony export */   BITS19: () => (/* binding */ BITS19),\n/* harmony export */   BITS2: () => (/* binding */ BITS2),\n/* harmony export */   BITS20: () => (/* binding */ BITS20),\n/* harmony export */   BITS21: () => (/* binding */ BITS21),\n/* harmony export */   BITS22: () => (/* binding */ BITS22),\n/* harmony export */   BITS23: () => (/* binding */ BITS23),\n/* harmony export */   BITS24: () => (/* binding */ BITS24),\n/* harmony export */   BITS25: () => (/* binding */ BITS25),\n/* harmony export */   BITS26: () => (/* binding */ BITS26),\n/* harmony export */   BITS27: () => (/* binding */ BITS27),\n/* harmony export */   BITS28: () => (/* binding */ BITS28),\n/* harmony export */   BITS29: () => (/* binding */ BITS29),\n/* harmony export */   BITS3: () => (/* binding */ BITS3),\n/* harmony export */   BITS30: () => (/* binding */ BITS30),\n/* harmony export */   BITS31: () => (/* binding */ BITS31),\n/* harmony export */   BITS32: () => (/* binding */ BITS32),\n/* harmony export */   BITS4: () => (/* binding */ BITS4),\n/* harmony export */   BITS5: () => (/* binding */ BITS5),\n/* harmony export */   BITS6: () => (/* binding */ BITS6),\n/* harmony export */   BITS7: () => (/* binding */ BITS7),\n/* harmony export */   BITS8: () => (/* binding */ BITS8),\n/* harmony export */   BITS9: () => (/* binding */ BITS9)\n/* harmony export */ });\n/* eslint-env browser */\n\n/**\n * Binary data constants.\n *\n * @module binary\n */\n\n/**\n * n-th bit activated.\n *\n * @type {number}\n */\nconst BIT1 = 1\nconst BIT2 = 2\nconst BIT3 = 4\nconst BIT4 = 8\nconst BIT5 = 16\nconst BIT6 = 32\nconst BIT7 = 64\nconst BIT8 = 128\nconst BIT9 = 256\nconst BIT10 = 512\nconst BIT11 = 1024\nconst BIT12 = 2048\nconst BIT13 = 4096\nconst BIT14 = 8192\nconst BIT15 = 16384\nconst BIT16 = 32768\nconst BIT17 = 65536\nconst BIT18 = 1 << 17\nconst BIT19 = 1 << 18\nconst BIT20 = 1 << 19\nconst BIT21 = 1 << 20\nconst BIT22 = 1 << 21\nconst BIT23 = 1 << 22\nconst BIT24 = 1 << 23\nconst BIT25 = 1 << 24\nconst BIT26 = 1 << 25\nconst BIT27 = 1 << 26\nconst BIT28 = 1 << 27\nconst BIT29 = 1 << 28\nconst BIT30 = 1 << 29\nconst BIT31 = 1 << 30\nconst BIT32 = 1 << 31\n\n/**\n * First n bits activated.\n *\n * @type {number}\n */\nconst BITS0 = 0\nconst BITS1 = 1\nconst BITS2 = 3\nconst BITS3 = 7\nconst BITS4 = 15\nconst BITS5 = 31\nconst BITS6 = 63\nconst BITS7 = 127\nconst BITS8 = 255\nconst BITS9 = 511\nconst BITS10 = 1023\nconst BITS11 = 2047\nconst BITS12 = 4095\nconst BITS13 = 8191\nconst BITS14 = 16383\nconst BITS15 = 32767\nconst BITS16 = 65535\nconst BITS17 = BIT18 - 1\nconst BITS18 = BIT19 - 1\nconst BITS19 = BIT20 - 1\nconst BITS20 = BIT21 - 1\nconst BITS21 = BIT22 - 1\nconst BITS22 = BIT23 - 1\nconst BITS23 = BIT24 - 1\nconst BITS24 = BIT25 - 1\nconst BITS25 = BIT26 - 1\nconst BITS26 = BIT27 - 1\nconst BITS27 = BIT28 - 1\nconst BITS28 = BIT29 - 1\nconst BITS29 = BIT30 - 1\nconst BITS30 = BIT31 - 1\n/**\n * @type {number}\n */\nconst BITS31 = 0x7FFFFFFF\n/**\n * @type {number}\n */\nconst BITS32 = 0xFFFFFFFF\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/lib0/binary.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/lib0/buffer.js":
/*!*****************************************!*\
  !*** ../../node_modules/lib0/buffer.js ***!
  \*****************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   copyUint8Array: () => (/* binding */ copyUint8Array),\n/* harmony export */   createUint8ArrayFromArrayBuffer: () => (/* binding */ createUint8ArrayFromArrayBuffer),\n/* harmony export */   createUint8ArrayFromLen: () => (/* binding */ createUint8ArrayFromLen),\n/* harmony export */   createUint8ArrayViewFromArrayBuffer: () => (/* binding */ createUint8ArrayViewFromArrayBuffer),\n/* harmony export */   decodeAny: () => (/* binding */ decodeAny),\n/* harmony export */   encodeAny: () => (/* binding */ encodeAny),\n/* harmony export */   fromBase64: () => (/* binding */ fromBase64),\n/* harmony export */   fromBase64UrlEncoded: () => (/* binding */ fromBase64UrlEncoded),\n/* harmony export */   fromHexString: () => (/* binding */ fromHexString),\n/* harmony export */   shiftNBitsLeft: () => (/* binding */ shiftNBitsLeft),\n/* harmony export */   toBase64: () => (/* binding */ toBase64),\n/* harmony export */   toBase64UrlEncoded: () => (/* binding */ toBase64UrlEncoded),\n/* harmony export */   toHexString: () => (/* binding */ toHexString)\n/* harmony export */ });\n/* harmony import */ var _string_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./string.js */ \"(ssr)/../../node_modules/lib0/string.js\");\n/* harmony import */ var _environment_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./environment.js */ \"(ssr)/../../node_modules/lib0/environment.js\");\n/* harmony import */ var _array_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./array.js */ \"(ssr)/../../node_modules/lib0/array.js\");\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./math.js */ \"(ssr)/../../node_modules/lib0/math.js\");\n/* harmony import */ var _encoding_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./encoding.js */ \"(ssr)/../../node_modules/lib0/encoding.js\");\n/* harmony import */ var _decoding_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./decoding.js */ \"(ssr)/../../node_modules/lib0/decoding.js\");\n/**\n * Utility functions to work with buffers (Uint8Array).\n *\n * @module buffer\n */\n\n\n\n\n\n\n\n\n/**\n * @param {number} len\n */\nconst createUint8ArrayFromLen = len => new Uint8Array(len)\n\n/**\n * Create Uint8Array with initial content from buffer\n *\n * @param {ArrayBuffer} buffer\n * @param {number} byteOffset\n * @param {number} length\n */\nconst createUint8ArrayViewFromArrayBuffer = (buffer, byteOffset, length) => new Uint8Array(buffer, byteOffset, length)\n\n/**\n * Create Uint8Array with initial content from buffer\n *\n * @param {ArrayBuffer} buffer\n */\nconst createUint8ArrayFromArrayBuffer = buffer => new Uint8Array(buffer)\n\n/* c8 ignore start */\n/**\n * @param {Uint8Array} bytes\n * @return {string}\n */\nconst toBase64Browser = bytes => {\n  let s = ''\n  for (let i = 0; i < bytes.byteLength; i++) {\n    s += _string_js__WEBPACK_IMPORTED_MODULE_0__.fromCharCode(bytes[i])\n  }\n  // eslint-disable-next-line no-undef\n  return btoa(s)\n}\n/* c8 ignore stop */\n\n/**\n * @param {Uint8Array} bytes\n * @return {string}\n */\nconst toBase64Node = bytes => Buffer.from(bytes.buffer, bytes.byteOffset, bytes.byteLength).toString('base64')\n\n/* c8 ignore start */\n/**\n * @param {string} s\n * @return {Uint8Array}\n */\nconst fromBase64Browser = s => {\n  // eslint-disable-next-line no-undef\n  const a = atob(s)\n  const bytes = createUint8ArrayFromLen(a.length)\n  for (let i = 0; i < a.length; i++) {\n    bytes[i] = a.charCodeAt(i)\n  }\n  return bytes\n}\n/* c8 ignore stop */\n\n/**\n * @param {string} s\n */\nconst fromBase64Node = s => {\n  const buf = Buffer.from(s, 'base64')\n  return createUint8ArrayViewFromArrayBuffer(buf.buffer, buf.byteOffset, buf.byteLength)\n}\n\n/* c8 ignore next */\nconst toBase64 = _environment_js__WEBPACK_IMPORTED_MODULE_1__.isBrowser ? toBase64Browser : toBase64Node\n\n/* c8 ignore next */\nconst fromBase64 = _environment_js__WEBPACK_IMPORTED_MODULE_1__.isBrowser ? fromBase64Browser : fromBase64Node\n\n/**\n * Implements base64url - see https://datatracker.ietf.org/doc/html/rfc4648#section-5\n * @param {Uint8Array} buf\n */\nconst toBase64UrlEncoded = buf => toBase64(buf).replaceAll('+', '-').replaceAll('/', '_').replaceAll('=', '')\n\n/**\n * @param {string} base64\n */\nconst fromBase64UrlEncoded = base64 => fromBase64(base64.replaceAll('-', '+').replaceAll('_', '/'))\n\n/**\n * Base64 is always a more efficient choice. This exists for utility purposes only.\n *\n * @param {Uint8Array} buf\n */\nconst toHexString = buf => _array_js__WEBPACK_IMPORTED_MODULE_2__.map(buf, b => b.toString(16).padStart(2, '0')).join('')\n\n/**\n * Note: This function expects that the hex doesn't start with 0x..\n *\n * @param {string} hex\n */\nconst fromHexString = hex => {\n  const hlen = hex.length\n  const buf = new Uint8Array(_math_js__WEBPACK_IMPORTED_MODULE_3__.ceil(hlen / 2))\n  for (let i = 0; i < hlen; i += 2) {\n    buf[buf.length - i / 2 - 1] = Number.parseInt(hex.slice(hlen - i - 2, hlen - i), 16)\n  }\n  return buf\n}\n\n/**\n * Copy the content of an Uint8Array view to a new ArrayBuffer.\n *\n * @param {Uint8Array} uint8Array\n * @return {Uint8Array}\n */\nconst copyUint8Array = uint8Array => {\n  const newBuf = createUint8ArrayFromLen(uint8Array.byteLength)\n  newBuf.set(uint8Array)\n  return newBuf\n}\n\n/**\n * Encode anything as a UInt8Array. It's a pun on typescripts's `any` type.\n * See encoding.writeAny for more information.\n *\n * @param {any} data\n * @return {Uint8Array}\n */\nconst encodeAny = data =>\n  _encoding_js__WEBPACK_IMPORTED_MODULE_4__.encode(encoder => _encoding_js__WEBPACK_IMPORTED_MODULE_4__.writeAny(encoder, data))\n\n/**\n * Decode an any-encoded value.\n *\n * @param {Uint8Array} buf\n * @return {any}\n */\nconst decodeAny = buf => _decoding_js__WEBPACK_IMPORTED_MODULE_5__.readAny(_decoding_js__WEBPACK_IMPORTED_MODULE_5__.createDecoder(buf))\n\n/**\n * Shift Byte Array {N} bits to the left. Does not expand byte array.\n *\n * @param {Uint8Array} bs\n * @param {number} N should be in the range of [0-7]\n */\nconst shiftNBitsLeft = (bs, N) => {\n  if (N === 0) return bs\n  bs = new Uint8Array(bs)\n  bs[0] <<= N\n  for (let i = 1; i < bs.length; i++) {\n    bs[i - 1] |= bs[i] >>> (8 - N)\n    bs[i] <<= N\n  }\n  return bs\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/lib0/buffer.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/lib0/conditions.js":
/*!*********************************************!*\
  !*** ../../node_modules/lib0/conditions.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   undefinedToNull: () => (/* binding */ undefinedToNull)\n/* harmony export */ });\n/**\n * Often used conditions.\n *\n * @module conditions\n */\n\n/**\n * @template T\n * @param {T|null|undefined} v\n * @return {T|null}\n */\n/* c8 ignore next */\nconst undefinedToNull = v => v === undefined ? null : v\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2xpYjAvY29uZGl0aW9ucy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0EsV0FBVyxrQkFBa0I7QUFDN0IsWUFBWTtBQUNaO0FBQ0E7QUFDTyIsInNvdXJjZXMiOlsid2VicGFjazovL0BvcGVuY2FudmFzL3dlYi8uLi8uLi9ub2RlX21vZHVsZXMvbGliMC9jb25kaXRpb25zLmpzP2Y2YzQiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBPZnRlbiB1c2VkIGNvbmRpdGlvbnMuXG4gKlxuICogQG1vZHVsZSBjb25kaXRpb25zXG4gKi9cblxuLyoqXG4gKiBAdGVtcGxhdGUgVFxuICogQHBhcmFtIHtUfG51bGx8dW5kZWZpbmVkfSB2XG4gKiBAcmV0dXJuIHtUfG51bGx9XG4gKi9cbi8qIGM4IGlnbm9yZSBuZXh0ICovXG5leHBvcnQgY29uc3QgdW5kZWZpbmVkVG9OdWxsID0gdiA9PiB2ID09PSB1bmRlZmluZWQgPyBudWxsIDogdlxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/lib0/conditions.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/lib0/decoding.js":
/*!*******************************************!*\
  !*** ../../node_modules/lib0/decoding.js ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Decoder: () => (/* binding */ Decoder),\n/* harmony export */   IncUintOptRleDecoder: () => (/* binding */ IncUintOptRleDecoder),\n/* harmony export */   IntDiffDecoder: () => (/* binding */ IntDiffDecoder),\n/* harmony export */   IntDiffOptRleDecoder: () => (/* binding */ IntDiffOptRleDecoder),\n/* harmony export */   RleDecoder: () => (/* binding */ RleDecoder),\n/* harmony export */   RleIntDiffDecoder: () => (/* binding */ RleIntDiffDecoder),\n/* harmony export */   StringDecoder: () => (/* binding */ StringDecoder),\n/* harmony export */   UintOptRleDecoder: () => (/* binding */ UintOptRleDecoder),\n/* harmony export */   _readVarStringNative: () => (/* binding */ _readVarStringNative),\n/* harmony export */   _readVarStringPolyfill: () => (/* binding */ _readVarStringPolyfill),\n/* harmony export */   clone: () => (/* binding */ clone),\n/* harmony export */   createDecoder: () => (/* binding */ createDecoder),\n/* harmony export */   hasContent: () => (/* binding */ hasContent),\n/* harmony export */   peekUint16: () => (/* binding */ peekUint16),\n/* harmony export */   peekUint32: () => (/* binding */ peekUint32),\n/* harmony export */   peekUint8: () => (/* binding */ peekUint8),\n/* harmony export */   peekVarInt: () => (/* binding */ peekVarInt),\n/* harmony export */   peekVarString: () => (/* binding */ peekVarString),\n/* harmony export */   peekVarUint: () => (/* binding */ peekVarUint),\n/* harmony export */   readAny: () => (/* binding */ readAny),\n/* harmony export */   readBigInt64: () => (/* binding */ readBigInt64),\n/* harmony export */   readBigUint64: () => (/* binding */ readBigUint64),\n/* harmony export */   readFloat32: () => (/* binding */ readFloat32),\n/* harmony export */   readFloat64: () => (/* binding */ readFloat64),\n/* harmony export */   readFromDataView: () => (/* binding */ readFromDataView),\n/* harmony export */   readTailAsUint8Array: () => (/* binding */ readTailAsUint8Array),\n/* harmony export */   readTerminatedString: () => (/* binding */ readTerminatedString),\n/* harmony export */   readTerminatedUint8Array: () => (/* binding */ readTerminatedUint8Array),\n/* harmony export */   readUint16: () => (/* binding */ readUint16),\n/* harmony export */   readUint32: () => (/* binding */ readUint32),\n/* harmony export */   readUint32BigEndian: () => (/* binding */ readUint32BigEndian),\n/* harmony export */   readUint8: () => (/* binding */ readUint8),\n/* harmony export */   readUint8Array: () => (/* binding */ readUint8Array),\n/* harmony export */   readVarInt: () => (/* binding */ readVarInt),\n/* harmony export */   readVarString: () => (/* binding */ readVarString),\n/* harmony export */   readVarUint: () => (/* binding */ readVarUint),\n/* harmony export */   readVarUint8Array: () => (/* binding */ readVarUint8Array),\n/* harmony export */   skip8: () => (/* binding */ skip8)\n/* harmony export */ });\n/* harmony import */ var _binary_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./binary.js */ \"(ssr)/../../node_modules/lib0/binary.js\");\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./math.js */ \"(ssr)/../../node_modules/lib0/math.js\");\n/* harmony import */ var _number_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./number.js */ \"(ssr)/../../node_modules/lib0/number.js\");\n/* harmony import */ var _string_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./string.js */ \"(ssr)/../../node_modules/lib0/string.js\");\n/* harmony import */ var _error_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./error.js */ \"(ssr)/../../node_modules/lib0/error.js\");\n/* harmony import */ var _encoding_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./encoding.js */ \"(ssr)/../../node_modules/lib0/encoding.js\");\n/**\n * Efficient schema-less binary decoding with support for variable length encoding.\n *\n * Use [lib0/decoding] with [lib0/encoding]. Every encoding function has a corresponding decoding function.\n *\n * Encodes numbers in little-endian order (least to most significant byte order)\n * and is compatible with Golang's binary encoding (https://golang.org/pkg/encoding/binary/)\n * which is also used in Protocol Buffers.\n *\n * ```js\n * // encoding step\n * const encoder = encoding.createEncoder()\n * encoding.writeVarUint(encoder, 256)\n * encoding.writeVarString(encoder, 'Hello world!')\n * const buf = encoding.toUint8Array(encoder)\n * ```\n *\n * ```js\n * // decoding step\n * const decoder = decoding.createDecoder(buf)\n * decoding.readVarUint(decoder) // => 256\n * decoding.readVarString(decoder) // => 'Hello world!'\n * decoding.hasContent(decoder) // => false - all data is read\n * ```\n *\n * @module decoding\n */\n\n\n\n\n\n\n\n\nconst errorUnexpectedEndOfArray = _error_js__WEBPACK_IMPORTED_MODULE_0__.create('Unexpected end of array')\nconst errorIntegerOutOfRange = _error_js__WEBPACK_IMPORTED_MODULE_0__.create('Integer out of Range')\n\n/**\n * A Decoder handles the decoding of an Uint8Array.\n */\nclass Decoder {\n  /**\n   * @param {Uint8Array} uint8Array Binary data to decode\n   */\n  constructor (uint8Array) {\n    /**\n     * Decoding target.\n     *\n     * @type {Uint8Array}\n     */\n    this.arr = uint8Array\n    /**\n     * Current decoding position.\n     *\n     * @type {number}\n     */\n    this.pos = 0\n  }\n}\n\n/**\n * @function\n * @param {Uint8Array} uint8Array\n * @return {Decoder}\n */\nconst createDecoder = uint8Array => new Decoder(uint8Array)\n\n/**\n * @function\n * @param {Decoder} decoder\n * @return {boolean}\n */\nconst hasContent = decoder => decoder.pos !== decoder.arr.length\n\n/**\n * Clone a decoder instance.\n * Optionally set a new position parameter.\n *\n * @function\n * @param {Decoder} decoder The decoder instance\n * @param {number} [newPos] Defaults to current position\n * @return {Decoder} A clone of `decoder`\n */\nconst clone = (decoder, newPos = decoder.pos) => {\n  const _decoder = createDecoder(decoder.arr)\n  _decoder.pos = newPos\n  return _decoder\n}\n\n/**\n * Create an Uint8Array view of the next `len` bytes and advance the position by `len`.\n *\n * Important: The Uint8Array still points to the underlying ArrayBuffer. Make sure to discard the result as soon as possible to prevent any memory leaks.\n *            Use `buffer.copyUint8Array` to copy the result into a new Uint8Array.\n *\n * @function\n * @param {Decoder} decoder The decoder instance\n * @param {number} len The length of bytes to read\n * @return {Uint8Array}\n */\nconst readUint8Array = (decoder, len) => {\n  const view = new Uint8Array(decoder.arr.buffer, decoder.pos + decoder.arr.byteOffset, len)\n  decoder.pos += len\n  return view\n}\n\n/**\n * Read variable length Uint8Array.\n *\n * Important: The Uint8Array still points to the underlying ArrayBuffer. Make sure to discard the result as soon as possible to prevent any memory leaks.\n *            Use `buffer.copyUint8Array` to copy the result into a new Uint8Array.\n *\n * @function\n * @param {Decoder} decoder\n * @return {Uint8Array}\n */\nconst readVarUint8Array = decoder => readUint8Array(decoder, readVarUint(decoder))\n\n/**\n * Read the rest of the content as an ArrayBuffer\n * @function\n * @param {Decoder} decoder\n * @return {Uint8Array}\n */\nconst readTailAsUint8Array = decoder => readUint8Array(decoder, decoder.arr.length - decoder.pos)\n\n/**\n * Skip one byte, jump to the next position.\n * @function\n * @param {Decoder} decoder The decoder instance\n * @return {number} The next position\n */\nconst skip8 = decoder => decoder.pos++\n\n/**\n * Read one byte as unsigned integer.\n * @function\n * @param {Decoder} decoder The decoder instance\n * @return {number} Unsigned 8-bit integer\n */\nconst readUint8 = decoder => decoder.arr[decoder.pos++]\n\n/**\n * Read 2 bytes as unsigned integer.\n *\n * @function\n * @param {Decoder} decoder\n * @return {number} An unsigned integer.\n */\nconst readUint16 = decoder => {\n  const uint =\n    decoder.arr[decoder.pos] +\n    (decoder.arr[decoder.pos + 1] << 8)\n  decoder.pos += 2\n  return uint\n}\n\n/**\n * Read 4 bytes as unsigned integer.\n *\n * @function\n * @param {Decoder} decoder\n * @return {number} An unsigned integer.\n */\nconst readUint32 = decoder => {\n  const uint =\n    (decoder.arr[decoder.pos] +\n    (decoder.arr[decoder.pos + 1] << 8) +\n    (decoder.arr[decoder.pos + 2] << 16) +\n    (decoder.arr[decoder.pos + 3] << 24)) >>> 0\n  decoder.pos += 4\n  return uint\n}\n\n/**\n * Read 4 bytes as unsigned integer in big endian order.\n * (most significant byte first)\n *\n * @function\n * @param {Decoder} decoder\n * @return {number} An unsigned integer.\n */\nconst readUint32BigEndian = decoder => {\n  const uint =\n    (decoder.arr[decoder.pos + 3] +\n    (decoder.arr[decoder.pos + 2] << 8) +\n    (decoder.arr[decoder.pos + 1] << 16) +\n    (decoder.arr[decoder.pos] << 24)) >>> 0\n  decoder.pos += 4\n  return uint\n}\n\n/**\n * Look ahead without incrementing the position\n * to the next byte and read it as unsigned integer.\n *\n * @function\n * @param {Decoder} decoder\n * @return {number} An unsigned integer.\n */\nconst peekUint8 = decoder => decoder.arr[decoder.pos]\n\n/**\n * Look ahead without incrementing the position\n * to the next byte and read it as unsigned integer.\n *\n * @function\n * @param {Decoder} decoder\n * @return {number} An unsigned integer.\n */\nconst peekUint16 = decoder =>\n  decoder.arr[decoder.pos] +\n  (decoder.arr[decoder.pos + 1] << 8)\n\n/**\n * Look ahead without incrementing the position\n * to the next byte and read it as unsigned integer.\n *\n * @function\n * @param {Decoder} decoder\n * @return {number} An unsigned integer.\n */\nconst peekUint32 = decoder => (\n  decoder.arr[decoder.pos] +\n  (decoder.arr[decoder.pos + 1] << 8) +\n  (decoder.arr[decoder.pos + 2] << 16) +\n  (decoder.arr[decoder.pos + 3] << 24)\n) >>> 0\n\n/**\n * Read unsigned integer (32bit) with variable length.\n * 1/8th of the storage is used as encoding overhead.\n *  * numbers < 2^7 is stored in one bytlength\n *  * numbers < 2^14 is stored in two bylength\n *\n * @function\n * @param {Decoder} decoder\n * @return {number} An unsigned integer.length\n */\nconst readVarUint = decoder => {\n  let num = 0\n  let mult = 1\n  const len = decoder.arr.length\n  while (decoder.pos < len) {\n    const r = decoder.arr[decoder.pos++]\n    // num = num | ((r & binary.BITS7) << len)\n    num = num + (r & _binary_js__WEBPACK_IMPORTED_MODULE_1__.BITS7) * mult // shift $r << (7*#iterations) and add it to num\n    mult *= 128 // next iteration, shift 7 \"more\" to the left\n    if (r < _binary_js__WEBPACK_IMPORTED_MODULE_1__.BIT8) {\n      return num\n    }\n    /* c8 ignore start */\n    if (num > _number_js__WEBPACK_IMPORTED_MODULE_2__.MAX_SAFE_INTEGER) {\n      throw errorIntegerOutOfRange\n    }\n    /* c8 ignore stop */\n  }\n  throw errorUnexpectedEndOfArray\n}\n\n/**\n * Read signed integer (32bit) with variable length.\n * 1/8th of the storage is used as encoding overhead.\n *  * numbers < 2^7 is stored in one bytlength\n *  * numbers < 2^14 is stored in two bylength\n * @todo This should probably create the inverse ~num if number is negative - but this would be a breaking change.\n *\n * @function\n * @param {Decoder} decoder\n * @return {number} An unsigned integer.length\n */\nconst readVarInt = decoder => {\n  let r = decoder.arr[decoder.pos++]\n  let num = r & _binary_js__WEBPACK_IMPORTED_MODULE_1__.BITS6\n  let mult = 64\n  const sign = (r & _binary_js__WEBPACK_IMPORTED_MODULE_1__.BIT7) > 0 ? -1 : 1\n  if ((r & _binary_js__WEBPACK_IMPORTED_MODULE_1__.BIT8) === 0) {\n    // don't continue reading\n    return sign * num\n  }\n  const len = decoder.arr.length\n  while (decoder.pos < len) {\n    r = decoder.arr[decoder.pos++]\n    // num = num | ((r & binary.BITS7) << len)\n    num = num + (r & _binary_js__WEBPACK_IMPORTED_MODULE_1__.BITS7) * mult\n    mult *= 128\n    if (r < _binary_js__WEBPACK_IMPORTED_MODULE_1__.BIT8) {\n      return sign * num\n    }\n    /* c8 ignore start */\n    if (num > _number_js__WEBPACK_IMPORTED_MODULE_2__.MAX_SAFE_INTEGER) {\n      throw errorIntegerOutOfRange\n    }\n    /* c8 ignore stop */\n  }\n  throw errorUnexpectedEndOfArray\n}\n\n/**\n * Look ahead and read varUint without incrementing position\n *\n * @function\n * @param {Decoder} decoder\n * @return {number}\n */\nconst peekVarUint = decoder => {\n  const pos = decoder.pos\n  const s = readVarUint(decoder)\n  decoder.pos = pos\n  return s\n}\n\n/**\n * Look ahead and read varUint without incrementing position\n *\n * @function\n * @param {Decoder} decoder\n * @return {number}\n */\nconst peekVarInt = decoder => {\n  const pos = decoder.pos\n  const s = readVarInt(decoder)\n  decoder.pos = pos\n  return s\n}\n\n/**\n * We don't test this function anymore as we use native decoding/encoding by default now.\n * Better not modify this anymore..\n *\n * Transforming utf8 to a string is pretty expensive. The code performs 10x better\n * when String.fromCodePoint is fed with all characters as arguments.\n * But most environments have a maximum number of arguments per functions.\n * For effiency reasons we apply a maximum of 10000 characters at once.\n *\n * @function\n * @param {Decoder} decoder\n * @return {String} The read String.\n */\n/* c8 ignore start */\nconst _readVarStringPolyfill = decoder => {\n  let remainingLen = readVarUint(decoder)\n  if (remainingLen === 0) {\n    return ''\n  } else {\n    let encodedString = String.fromCodePoint(readUint8(decoder)) // remember to decrease remainingLen\n    if (--remainingLen < 100) { // do not create a Uint8Array for small strings\n      while (remainingLen--) {\n        encodedString += String.fromCodePoint(readUint8(decoder))\n      }\n    } else {\n      while (remainingLen > 0) {\n        const nextLen = remainingLen < 10000 ? remainingLen : 10000\n        // this is dangerous, we create a fresh array view from the existing buffer\n        const bytes = decoder.arr.subarray(decoder.pos, decoder.pos + nextLen)\n        decoder.pos += nextLen\n        // Starting with ES5.1 we can supply a generic array-like object as arguments\n        encodedString += String.fromCodePoint.apply(null, /** @type {any} */ (bytes))\n        remainingLen -= nextLen\n      }\n    }\n    return decodeURIComponent(escape(encodedString))\n  }\n}\n/* c8 ignore stop */\n\n/**\n * @function\n * @param {Decoder} decoder\n * @return {String} The read String\n */\nconst _readVarStringNative = decoder =>\n  /** @type any */ (_string_js__WEBPACK_IMPORTED_MODULE_3__.utf8TextDecoder).decode(readVarUint8Array(decoder))\n\n/**\n * Read string of variable length\n * * varUint is used to store the length of the string\n *\n * @function\n * @param {Decoder} decoder\n * @return {String} The read String\n *\n */\n/* c8 ignore next */\nconst readVarString = _string_js__WEBPACK_IMPORTED_MODULE_3__.utf8TextDecoder ? _readVarStringNative : _readVarStringPolyfill\n\n/**\n * @param {Decoder} decoder\n * @return {Uint8Array}\n */\nconst readTerminatedUint8Array = decoder => {\n  const encoder = _encoding_js__WEBPACK_IMPORTED_MODULE_4__.createEncoder()\n  let b\n  while (true) {\n    b = readUint8(decoder)\n    if (b === 0) {\n      return _encoding_js__WEBPACK_IMPORTED_MODULE_4__.toUint8Array(encoder)\n    }\n    if (b === 1) {\n      b = readUint8(decoder)\n    }\n    _encoding_js__WEBPACK_IMPORTED_MODULE_4__.write(encoder, b)\n  }\n}\n\n/**\n * @param {Decoder} decoder\n * @return {string}\n */\nconst readTerminatedString = decoder => _string_js__WEBPACK_IMPORTED_MODULE_3__.decodeUtf8(readTerminatedUint8Array(decoder))\n\n/**\n * Look ahead and read varString without incrementing position\n *\n * @function\n * @param {Decoder} decoder\n * @return {string}\n */\nconst peekVarString = decoder => {\n  const pos = decoder.pos\n  const s = readVarString(decoder)\n  decoder.pos = pos\n  return s\n}\n\n/**\n * @param {Decoder} decoder\n * @param {number} len\n * @return {DataView}\n */\nconst readFromDataView = (decoder, len) => {\n  const dv = new DataView(decoder.arr.buffer, decoder.arr.byteOffset + decoder.pos, len)\n  decoder.pos += len\n  return dv\n}\n\n/**\n * @param {Decoder} decoder\n */\nconst readFloat32 = decoder => readFromDataView(decoder, 4).getFloat32(0, false)\n\n/**\n * @param {Decoder} decoder\n */\nconst readFloat64 = decoder => readFromDataView(decoder, 8).getFloat64(0, false)\n\n/**\n * @param {Decoder} decoder\n */\nconst readBigInt64 = decoder => /** @type {any} */ (readFromDataView(decoder, 8)).getBigInt64(0, false)\n\n/**\n * @param {Decoder} decoder\n */\nconst readBigUint64 = decoder => /** @type {any} */ (readFromDataView(decoder, 8)).getBigUint64(0, false)\n\n/**\n * @type {Array<function(Decoder):any>}\n */\nconst readAnyLookupTable = [\n  decoder => undefined, // CASE 127: undefined\n  decoder => null, // CASE 126: null\n  readVarInt, // CASE 125: integer\n  readFloat32, // CASE 124: float32\n  readFloat64, // CASE 123: float64\n  readBigInt64, // CASE 122: bigint\n  decoder => false, // CASE 121: boolean (false)\n  decoder => true, // CASE 120: boolean (true)\n  readVarString, // CASE 119: string\n  decoder => { // CASE 118: object<string,any>\n    const len = readVarUint(decoder)\n    /**\n     * @type {Object<string,any>}\n     */\n    const obj = {}\n    for (let i = 0; i < len; i++) {\n      const key = readVarString(decoder)\n      obj[key] = readAny(decoder)\n    }\n    return obj\n  },\n  decoder => { // CASE 117: array<any>\n    const len = readVarUint(decoder)\n    const arr = []\n    for (let i = 0; i < len; i++) {\n      arr.push(readAny(decoder))\n    }\n    return arr\n  },\n  readVarUint8Array // CASE 116: Uint8Array\n]\n\n/**\n * @param {Decoder} decoder\n */\nconst readAny = decoder => readAnyLookupTable[127 - readUint8(decoder)](decoder)\n\n/**\n * T must not be null.\n *\n * @template T\n */\nclass RleDecoder extends Decoder {\n  /**\n   * @param {Uint8Array} uint8Array\n   * @param {function(Decoder):T} reader\n   */\n  constructor (uint8Array, reader) {\n    super(uint8Array)\n    /**\n     * The reader\n     */\n    this.reader = reader\n    /**\n     * Current state\n     * @type {T|null}\n     */\n    this.s = null\n    this.count = 0\n  }\n\n  read () {\n    if (this.count === 0) {\n      this.s = this.reader(this)\n      if (hasContent(this)) {\n        this.count = readVarUint(this) + 1 // see encoder implementation for the reason why this is incremented\n      } else {\n        this.count = -1 // read the current value forever\n      }\n    }\n    this.count--\n    return /** @type {T} */ (this.s)\n  }\n}\n\nclass IntDiffDecoder extends Decoder {\n  /**\n   * @param {Uint8Array} uint8Array\n   * @param {number} start\n   */\n  constructor (uint8Array, start) {\n    super(uint8Array)\n    /**\n     * Current state\n     * @type {number}\n     */\n    this.s = start\n  }\n\n  /**\n   * @return {number}\n   */\n  read () {\n    this.s += readVarInt(this)\n    return this.s\n  }\n}\n\nclass RleIntDiffDecoder extends Decoder {\n  /**\n   * @param {Uint8Array} uint8Array\n   * @param {number} start\n   */\n  constructor (uint8Array, start) {\n    super(uint8Array)\n    /**\n     * Current state\n     * @type {number}\n     */\n    this.s = start\n    this.count = 0\n  }\n\n  /**\n   * @return {number}\n   */\n  read () {\n    if (this.count === 0) {\n      this.s += readVarInt(this)\n      if (hasContent(this)) {\n        this.count = readVarUint(this) + 1 // see encoder implementation for the reason why this is incremented\n      } else {\n        this.count = -1 // read the current value forever\n      }\n    }\n    this.count--\n    return /** @type {number} */ (this.s)\n  }\n}\n\nclass UintOptRleDecoder extends Decoder {\n  /**\n   * @param {Uint8Array} uint8Array\n   */\n  constructor (uint8Array) {\n    super(uint8Array)\n    /**\n     * @type {number}\n     */\n    this.s = 0\n    this.count = 0\n  }\n\n  read () {\n    if (this.count === 0) {\n      this.s = readVarInt(this)\n      // if the sign is negative, we read the count too, otherwise count is 1\n      const isNegative = _math_js__WEBPACK_IMPORTED_MODULE_5__.isNegativeZero(this.s)\n      this.count = 1\n      if (isNegative) {\n        this.s = -this.s\n        this.count = readVarUint(this) + 2\n      }\n    }\n    this.count--\n    return /** @type {number} */ (this.s)\n  }\n}\n\nclass IncUintOptRleDecoder extends Decoder {\n  /**\n   * @param {Uint8Array} uint8Array\n   */\n  constructor (uint8Array) {\n    super(uint8Array)\n    /**\n     * @type {number}\n     */\n    this.s = 0\n    this.count = 0\n  }\n\n  read () {\n    if (this.count === 0) {\n      this.s = readVarInt(this)\n      // if the sign is negative, we read the count too, otherwise count is 1\n      const isNegative = _math_js__WEBPACK_IMPORTED_MODULE_5__.isNegativeZero(this.s)\n      this.count = 1\n      if (isNegative) {\n        this.s = -this.s\n        this.count = readVarUint(this) + 2\n      }\n    }\n    this.count--\n    return /** @type {number} */ (this.s++)\n  }\n}\n\nclass IntDiffOptRleDecoder extends Decoder {\n  /**\n   * @param {Uint8Array} uint8Array\n   */\n  constructor (uint8Array) {\n    super(uint8Array)\n    /**\n     * @type {number}\n     */\n    this.s = 0\n    this.count = 0\n    this.diff = 0\n  }\n\n  /**\n   * @return {number}\n   */\n  read () {\n    if (this.count === 0) {\n      const diff = readVarInt(this)\n      // if the first bit is set, we read more data\n      const hasCount = diff & 1\n      this.diff = _math_js__WEBPACK_IMPORTED_MODULE_5__.floor(diff / 2) // shift >> 1\n      this.count = 1\n      if (hasCount) {\n        this.count = readVarUint(this) + 2\n      }\n    }\n    this.s += this.diff\n    this.count--\n    return this.s\n  }\n}\n\nclass StringDecoder {\n  /**\n   * @param {Uint8Array} uint8Array\n   */\n  constructor (uint8Array) {\n    this.decoder = new UintOptRleDecoder(uint8Array)\n    this.str = readVarString(this.decoder)\n    /**\n     * @type {number}\n     */\n    this.spos = 0\n  }\n\n  /**\n   * @return {string}\n   */\n  read () {\n    const end = this.spos + this.decoder.read()\n    const res = this.str.slice(this.spos, end)\n    this.spos = end\n    return res\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/lib0/decoding.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/lib0/diff.js":
/*!***************************************!*\
  !*** ../../node_modules/lib0/diff.js ***!
  \***************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   simpleDiff: () => (/* binding */ simpleDiff),\n/* harmony export */   simpleDiffArray: () => (/* binding */ simpleDiffArray),\n/* harmony export */   simpleDiffString: () => (/* binding */ simpleDiffString),\n/* harmony export */   simpleDiffStringWithCursor: () => (/* binding */ simpleDiffStringWithCursor)\n/* harmony export */ });\n/* harmony import */ var _function_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./function.js */ \"(ssr)/../../node_modules/lib0/function.js\");\n/**\n * Efficient diffs.\n *\n * @module diff\n */\n\n\n\n/**\n * A SimpleDiff describes a change on a String.\n *\n * ```js\n * console.log(a) // the old value\n * console.log(b) // the updated value\n * // Apply changes of diff (pseudocode)\n * a.remove(diff.index, diff.remove) // Remove `diff.remove` characters\n * a.insert(diff.index, diff.insert) // Insert `diff.insert`\n * a === b // values match\n * ```\n *\n * @typedef {Object} SimpleDiff\n * @property {Number} index The index where changes were applied\n * @property {Number} remove The number of characters to delete starting\n *                                  at `index`.\n * @property {T} insert The new text to insert at `index` after applying\n *                           `delete`\n *\n * @template T\n */\n\nconst highSurrogateRegex = /[\\uD800-\\uDBFF]/\nconst lowSurrogateRegex = /[\\uDC00-\\uDFFF]/\n\n/**\n * Create a diff between two strings. This diff implementation is highly\n * efficient, but not very sophisticated.\n *\n * @function\n *\n * @param {string} a The old version of the string\n * @param {string} b The updated version of the string\n * @return {SimpleDiff<string>} The diff description.\n */\nconst simpleDiffString = (a, b) => {\n  let left = 0 // number of same characters counting from left\n  let right = 0 // number of same characters counting from right\n  while (left < a.length && left < b.length && a[left] === b[left]) {\n    left++\n  }\n  // If the last same character is a high surrogate, we need to rollback to the previous character\n  if (left > 0 && highSurrogateRegex.test(a[left - 1])) left--\n  while (right + left < a.length && right + left < b.length && a[a.length - right - 1] === b[b.length - right - 1]) {\n    right++\n  }\n  // If the last same character is a low surrogate, we need to rollback to the previous character\n  if (right > 0 && lowSurrogateRegex.test(a[a.length - right])) right--\n  return {\n    index: left,\n    remove: a.length - left - right,\n    insert: b.slice(left, b.length - right)\n  }\n}\n\n/**\n * @todo Remove in favor of simpleDiffString\n * @deprecated\n */\nconst simpleDiff = simpleDiffString\n\n/**\n * Create a diff between two arrays. This diff implementation is highly\n * efficient, but not very sophisticated.\n *\n * Note: This is basically the same function as above. Another function was created so that the runtime\n * can better optimize these function calls.\n *\n * @function\n * @template T\n *\n * @param {Array<T>} a The old version of the array\n * @param {Array<T>} b The updated version of the array\n * @param {function(T, T):boolean} [compare]\n * @return {SimpleDiff<Array<T>>} The diff description.\n */\nconst simpleDiffArray = (a, b, compare = _function_js__WEBPACK_IMPORTED_MODULE_0__.equalityStrict) => {\n  let left = 0 // number of same characters counting from left\n  let right = 0 // number of same characters counting from right\n  while (left < a.length && left < b.length && compare(a[left], b[left])) {\n    left++\n  }\n  while (right + left < a.length && right + left < b.length && compare(a[a.length - right - 1], b[b.length - right - 1])) {\n    right++\n  }\n  return {\n    index: left,\n    remove: a.length - left - right,\n    insert: b.slice(left, b.length - right)\n  }\n}\n\n/**\n * Diff text and try to diff at the current cursor position.\n *\n * @param {string} a\n * @param {string} b\n * @param {number} cursor This should refer to the current left cursor-range position\n */\nconst simpleDiffStringWithCursor = (a, b, cursor) => {\n  let left = 0 // number of same characters counting from left\n  let right = 0 // number of same characters counting from right\n  // Iterate left to the right until we find a changed character\n  // First iteration considers the current cursor position\n  while (\n    left < a.length &&\n    left < b.length &&\n    a[left] === b[left] &&\n    left < cursor\n  ) {\n    left++\n  }\n  // If the last same character is a high surrogate, we need to rollback to the previous character\n  if (left > 0 && highSurrogateRegex.test(a[left - 1])) left--\n  // Iterate right to the left until we find a changed character\n  while (\n    right + left < a.length &&\n    right + left < b.length &&\n    a[a.length - right - 1] === b[b.length - right - 1]\n  ) {\n    right++\n  }\n  // If the last same character is a low surrogate, we need to rollback to the previous character\n  if (right > 0 && lowSurrogateRegex.test(a[a.length - right])) right--\n  // Try to iterate left further to the right without caring about the current cursor position\n  while (\n    right + left < a.length &&\n    right + left < b.length &&\n    a[left] === b[left]\n  ) {\n    left++\n  }\n  if (left > 0 && highSurrogateRegex.test(a[left - 1])) left--\n  return {\n    index: left,\n    remove: a.length - left - right,\n    insert: b.slice(left, b.length - right)\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/lib0/diff.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/lib0/dom.js":
/*!**************************************!*\
  !*** ../../node_modules/lib0/dom.js ***!
  \**************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CDATA_SECTION_NODE: () => (/* binding */ CDATA_SECTION_NODE),\n/* harmony export */   COMMENT_NODE: () => (/* binding */ COMMENT_NODE),\n/* harmony export */   DOCUMENT_FRAGMENT_NODE: () => (/* binding */ DOCUMENT_FRAGMENT_NODE),\n/* harmony export */   DOCUMENT_NODE: () => (/* binding */ DOCUMENT_NODE),\n/* harmony export */   DOCUMENT_TYPE_NODE: () => (/* binding */ DOCUMENT_TYPE_NODE),\n/* harmony export */   ELEMENT_NODE: () => (/* binding */ ELEMENT_NODE),\n/* harmony export */   TEXT_NODE: () => (/* binding */ TEXT_NODE),\n/* harmony export */   addEventListener: () => (/* binding */ addEventListener),\n/* harmony export */   addEventListeners: () => (/* binding */ addEventListeners),\n/* harmony export */   append: () => (/* binding */ append),\n/* harmony export */   appendChild: () => (/* binding */ appendChild),\n/* harmony export */   canvas: () => (/* binding */ canvas),\n/* harmony export */   checkNodeType: () => (/* binding */ checkNodeType),\n/* harmony export */   createDocumentFragment: () => (/* binding */ createDocumentFragment),\n/* harmony export */   createElement: () => (/* binding */ createElement),\n/* harmony export */   createTextNode: () => (/* binding */ createTextNode),\n/* harmony export */   doc: () => (/* binding */ doc),\n/* harmony export */   domParser: () => (/* binding */ domParser),\n/* harmony export */   element: () => (/* binding */ element),\n/* harmony export */   emitCustomEvent: () => (/* binding */ emitCustomEvent),\n/* harmony export */   fragment: () => (/* binding */ fragment),\n/* harmony export */   getElementById: () => (/* binding */ getElementById),\n/* harmony export */   insertBefore: () => (/* binding */ insertBefore),\n/* harmony export */   isParentOf: () => (/* binding */ isParentOf),\n/* harmony export */   mapToStyleString: () => (/* binding */ mapToStyleString),\n/* harmony export */   pairToStyleString: () => (/* binding */ pairToStyleString),\n/* harmony export */   pairsToStyleString: () => (/* binding */ pairsToStyleString),\n/* harmony export */   parseElement: () => (/* binding */ parseElement),\n/* harmony export */   parseFragment: () => (/* binding */ parseFragment),\n/* harmony export */   querySelector: () => (/* binding */ querySelector),\n/* harmony export */   querySelectorAll: () => (/* binding */ querySelectorAll),\n/* harmony export */   remove: () => (/* binding */ remove),\n/* harmony export */   removeEventListener: () => (/* binding */ removeEventListener),\n/* harmony export */   removeEventListeners: () => (/* binding */ removeEventListeners),\n/* harmony export */   replaceWith: () => (/* binding */ replaceWith),\n/* harmony export */   setAttributes: () => (/* binding */ setAttributes),\n/* harmony export */   setAttributesMap: () => (/* binding */ setAttributesMap),\n/* harmony export */   text: () => (/* binding */ text)\n/* harmony export */ });\n/* harmony import */ var _pair_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./pair.js */ \"(ssr)/../../node_modules/lib0/pair.js\");\n/* harmony import */ var _map_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./map.js */ \"(ssr)/../../node_modules/lib0/map.js\");\n/* eslint-env browser */\n\n/**\n * Utility module to work with the DOM.\n *\n * @module dom\n */\n\n\n\n\n/* c8 ignore start */\n/**\n * @type {Document}\n */\nconst doc = /** @type {Document} */ (typeof document !== 'undefined' ? document : {})\n\n/**\n * @param {string} name\n * @return {HTMLElement}\n */\nconst createElement = name => doc.createElement(name)\n\n/**\n * @return {DocumentFragment}\n */\nconst createDocumentFragment = () => doc.createDocumentFragment()\n\n/**\n * @param {string} text\n * @return {Text}\n */\nconst createTextNode = text => doc.createTextNode(text)\n\nconst domParser = /** @type {DOMParser} */ (typeof DOMParser !== 'undefined' ? new DOMParser() : null)\n\n/**\n * @param {HTMLElement} el\n * @param {string} name\n * @param {Object} opts\n */\nconst emitCustomEvent = (el, name, opts) => el.dispatchEvent(new CustomEvent(name, opts))\n\n/**\n * @param {Element} el\n * @param {Array<pair.Pair<string,string|boolean>>} attrs Array of key-value pairs\n * @return {Element}\n */\nconst setAttributes = (el, attrs) => {\n  _pair_js__WEBPACK_IMPORTED_MODULE_0__.forEach(attrs, (key, value) => {\n    if (value === false) {\n      el.removeAttribute(key)\n    } else if (value === true) {\n      el.setAttribute(key, '')\n    } else {\n      // @ts-ignore\n      el.setAttribute(key, value)\n    }\n  })\n  return el\n}\n\n/**\n * @param {Element} el\n * @param {Map<string, string>} attrs Array of key-value pairs\n * @return {Element}\n */\nconst setAttributesMap = (el, attrs) => {\n  attrs.forEach((value, key) => { el.setAttribute(key, value) })\n  return el\n}\n\n/**\n * @param {Array<Node>|HTMLCollection} children\n * @return {DocumentFragment}\n */\nconst fragment = children => {\n  const fragment = createDocumentFragment()\n  for (let i = 0; i < children.length; i++) {\n    appendChild(fragment, children[i])\n  }\n  return fragment\n}\n\n/**\n * @param {Element} parent\n * @param {Array<Node>} nodes\n * @return {Element}\n */\nconst append = (parent, nodes) => {\n  appendChild(parent, fragment(nodes))\n  return parent\n}\n\n/**\n * @param {HTMLElement} el\n */\nconst remove = el => el.remove()\n\n/**\n * @param {EventTarget} el\n * @param {string} name\n * @param {EventListener} f\n */\nconst addEventListener = (el, name, f) => el.addEventListener(name, f)\n\n/**\n * @param {EventTarget} el\n * @param {string} name\n * @param {EventListener} f\n */\nconst removeEventListener = (el, name, f) => el.removeEventListener(name, f)\n\n/**\n * @param {Node} node\n * @param {Array<pair.Pair<string,EventListener>>} listeners\n * @return {Node}\n */\nconst addEventListeners = (node, listeners) => {\n  _pair_js__WEBPACK_IMPORTED_MODULE_0__.forEach(listeners, (name, f) => addEventListener(node, name, f))\n  return node\n}\n\n/**\n * @param {Node} node\n * @param {Array<pair.Pair<string,EventListener>>} listeners\n * @return {Node}\n */\nconst removeEventListeners = (node, listeners) => {\n  _pair_js__WEBPACK_IMPORTED_MODULE_0__.forEach(listeners, (name, f) => removeEventListener(node, name, f))\n  return node\n}\n\n/**\n * @param {string} name\n * @param {Array<pair.Pair<string,string>|pair.Pair<string,boolean>>} attrs Array of key-value pairs\n * @param {Array<Node>} children\n * @return {Element}\n */\nconst element = (name, attrs = [], children = []) =>\n  append(setAttributes(createElement(name), attrs), children)\n\n/**\n * @param {number} width\n * @param {number} height\n */\nconst canvas = (width, height) => {\n  const c = /** @type {HTMLCanvasElement} */ (createElement('canvas'))\n  c.height = height\n  c.width = width\n  return c\n}\n\n/**\n * @param {string} t\n * @return {Text}\n */\nconst text = createTextNode\n\n/**\n * @param {pair.Pair<string,string>} pair\n */\nconst pairToStyleString = pair => `${pair.left}:${pair.right};`\n\n/**\n * @param {Array<pair.Pair<string,string>>} pairs\n * @return {string}\n */\nconst pairsToStyleString = pairs => pairs.map(pairToStyleString).join('')\n\n/**\n * @param {Map<string,string>} m\n * @return {string}\n */\nconst mapToStyleString = m => _map_js__WEBPACK_IMPORTED_MODULE_1__.map(m, (value, key) => `${key}:${value};`).join('')\n\n/**\n * @todo should always query on a dom element\n *\n * @param {HTMLElement|ShadowRoot} el\n * @param {string} query\n * @return {HTMLElement | null}\n */\nconst querySelector = (el, query) => el.querySelector(query)\n\n/**\n * @param {HTMLElement|ShadowRoot} el\n * @param {string} query\n * @return {NodeListOf<HTMLElement>}\n */\nconst querySelectorAll = (el, query) => el.querySelectorAll(query)\n\n/**\n * @param {string} id\n * @return {HTMLElement}\n */\nconst getElementById = id => /** @type {HTMLElement} */ (doc.getElementById(id))\n\n/**\n * @param {string} html\n * @return {HTMLElement}\n */\nconst _parse = html => domParser.parseFromString(`<html><body>${html}</body></html>`, 'text/html').body\n\n/**\n * @param {string} html\n * @return {DocumentFragment}\n */\nconst parseFragment = html => fragment(/** @type {any} */ (_parse(html).childNodes))\n\n/**\n * @param {string} html\n * @return {HTMLElement}\n */\nconst parseElement = html => /** @type HTMLElement */ (_parse(html).firstElementChild)\n\n/**\n * @param {HTMLElement} oldEl\n * @param {HTMLElement|DocumentFragment} newEl\n */\nconst replaceWith = (oldEl, newEl) => oldEl.replaceWith(newEl)\n\n/**\n * @param {HTMLElement} parent\n * @param {HTMLElement} el\n * @param {Node|null} ref\n * @return {HTMLElement}\n */\nconst insertBefore = (parent, el, ref) => parent.insertBefore(el, ref)\n\n/**\n * @param {Node} parent\n * @param {Node} child\n * @return {Node}\n */\nconst appendChild = (parent, child) => parent.appendChild(child)\n\nconst ELEMENT_NODE = doc.ELEMENT_NODE\nconst TEXT_NODE = doc.TEXT_NODE\nconst CDATA_SECTION_NODE = doc.CDATA_SECTION_NODE\nconst COMMENT_NODE = doc.COMMENT_NODE\nconst DOCUMENT_NODE = doc.DOCUMENT_NODE\nconst DOCUMENT_TYPE_NODE = doc.DOCUMENT_TYPE_NODE\nconst DOCUMENT_FRAGMENT_NODE = doc.DOCUMENT_FRAGMENT_NODE\n\n/**\n * @param {any} node\n * @param {number} type\n */\nconst checkNodeType = (node, type) => node.nodeType === type\n\n/**\n * @param {Node} parent\n * @param {HTMLElement} child\n */\nconst isParentOf = (parent, child) => {\n  let p = child.parentNode\n  while (p && p !== parent) {\n    p = p.parentNode\n  }\n  return p === parent\n}\n/* c8 ignore stop */\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/lib0/dom.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/lib0/encoding.js":
/*!*******************************************!*\
  !*** ../../node_modules/lib0/encoding.js ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Encoder: () => (/* binding */ Encoder),\n/* harmony export */   IncUintOptRleEncoder: () => (/* binding */ IncUintOptRleEncoder),\n/* harmony export */   IntDiffEncoder: () => (/* binding */ IntDiffEncoder),\n/* harmony export */   IntDiffOptRleEncoder: () => (/* binding */ IntDiffOptRleEncoder),\n/* harmony export */   RleEncoder: () => (/* binding */ RleEncoder),\n/* harmony export */   RleIntDiffEncoder: () => (/* binding */ RleIntDiffEncoder),\n/* harmony export */   StringEncoder: () => (/* binding */ StringEncoder),\n/* harmony export */   UintOptRleEncoder: () => (/* binding */ UintOptRleEncoder),\n/* harmony export */   _writeVarStringNative: () => (/* binding */ _writeVarStringNative),\n/* harmony export */   _writeVarStringPolyfill: () => (/* binding */ _writeVarStringPolyfill),\n/* harmony export */   createEncoder: () => (/* binding */ createEncoder),\n/* harmony export */   encode: () => (/* binding */ encode),\n/* harmony export */   hasContent: () => (/* binding */ hasContent),\n/* harmony export */   length: () => (/* binding */ length),\n/* harmony export */   set: () => (/* binding */ set),\n/* harmony export */   setUint16: () => (/* binding */ setUint16),\n/* harmony export */   setUint32: () => (/* binding */ setUint32),\n/* harmony export */   setUint8: () => (/* binding */ setUint8),\n/* harmony export */   toUint8Array: () => (/* binding */ toUint8Array),\n/* harmony export */   verifyLen: () => (/* binding */ verifyLen),\n/* harmony export */   write: () => (/* binding */ write),\n/* harmony export */   writeAny: () => (/* binding */ writeAny),\n/* harmony export */   writeBigInt64: () => (/* binding */ writeBigInt64),\n/* harmony export */   writeBigUint64: () => (/* binding */ writeBigUint64),\n/* harmony export */   writeBinaryEncoder: () => (/* binding */ writeBinaryEncoder),\n/* harmony export */   writeFloat32: () => (/* binding */ writeFloat32),\n/* harmony export */   writeFloat64: () => (/* binding */ writeFloat64),\n/* harmony export */   writeOnDataView: () => (/* binding */ writeOnDataView),\n/* harmony export */   writeTerminatedString: () => (/* binding */ writeTerminatedString),\n/* harmony export */   writeTerminatedUint8Array: () => (/* binding */ writeTerminatedUint8Array),\n/* harmony export */   writeUint16: () => (/* binding */ writeUint16),\n/* harmony export */   writeUint32: () => (/* binding */ writeUint32),\n/* harmony export */   writeUint32BigEndian: () => (/* binding */ writeUint32BigEndian),\n/* harmony export */   writeUint8: () => (/* binding */ writeUint8),\n/* harmony export */   writeUint8Array: () => (/* binding */ writeUint8Array),\n/* harmony export */   writeVarInt: () => (/* binding */ writeVarInt),\n/* harmony export */   writeVarString: () => (/* binding */ writeVarString),\n/* harmony export */   writeVarUint: () => (/* binding */ writeVarUint),\n/* harmony export */   writeVarUint8Array: () => (/* binding */ writeVarUint8Array)\n/* harmony export */ });\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./math.js */ \"(ssr)/../../node_modules/lib0/math.js\");\n/* harmony import */ var _number_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./number.js */ \"(ssr)/../../node_modules/lib0/number.js\");\n/* harmony import */ var _binary_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./binary.js */ \"(ssr)/../../node_modules/lib0/binary.js\");\n/* harmony import */ var _string_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./string.js */ \"(ssr)/../../node_modules/lib0/string.js\");\n/* harmony import */ var _array_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./array.js */ \"(ssr)/../../node_modules/lib0/array.js\");\n/**\n * Efficient schema-less binary encoding with support for variable length encoding.\n *\n * Use [lib0/encoding] with [lib0/decoding]. Every encoding function has a corresponding decoding function.\n *\n * Encodes numbers in little-endian order (least to most significant byte order)\n * and is compatible with Golang's binary encoding (https://golang.org/pkg/encoding/binary/)\n * which is also used in Protocol Buffers.\n *\n * ```js\n * // encoding step\n * const encoder = encoding.createEncoder()\n * encoding.writeVarUint(encoder, 256)\n * encoding.writeVarString(encoder, 'Hello world!')\n * const buf = encoding.toUint8Array(encoder)\n * ```\n *\n * ```js\n * // decoding step\n * const decoder = decoding.createDecoder(buf)\n * decoding.readVarUint(decoder) // => 256\n * decoding.readVarString(decoder) // => 'Hello world!'\n * decoding.hasContent(decoder) // => false - all data is read\n * ```\n *\n * @module encoding\n */\n\n\n\n\n\n\n\n/**\n * A BinaryEncoder handles the encoding to an Uint8Array.\n */\nclass Encoder {\n  constructor () {\n    this.cpos = 0\n    this.cbuf = new Uint8Array(100)\n    /**\n     * @type {Array<Uint8Array>}\n     */\n    this.bufs = []\n  }\n}\n\n/**\n * @function\n * @return {Encoder}\n */\nconst createEncoder = () => new Encoder()\n\n/**\n * @param {function(Encoder):void} f\n */\nconst encode = (f) => {\n  const encoder = createEncoder()\n  f(encoder)\n  return toUint8Array(encoder)\n}\n\n/**\n * The current length of the encoded data.\n *\n * @function\n * @param {Encoder} encoder\n * @return {number}\n */\nconst length = encoder => {\n  let len = encoder.cpos\n  for (let i = 0; i < encoder.bufs.length; i++) {\n    len += encoder.bufs[i].length\n  }\n  return len\n}\n\n/**\n * Check whether encoder is empty.\n *\n * @function\n * @param {Encoder} encoder\n * @return {boolean}\n */\nconst hasContent = encoder => encoder.cpos > 0 || encoder.bufs.length > 0\n\n/**\n * Transform to Uint8Array.\n *\n * @function\n * @param {Encoder} encoder\n * @return {Uint8Array} The created ArrayBuffer.\n */\nconst toUint8Array = encoder => {\n  const uint8arr = new Uint8Array(length(encoder))\n  let curPos = 0\n  for (let i = 0; i < encoder.bufs.length; i++) {\n    const d = encoder.bufs[i]\n    uint8arr.set(d, curPos)\n    curPos += d.length\n  }\n  uint8arr.set(new Uint8Array(encoder.cbuf.buffer, 0, encoder.cpos), curPos)\n  return uint8arr\n}\n\n/**\n * Verify that it is possible to write `len` bytes wtihout checking. If\n * necessary, a new Buffer with the required length is attached.\n *\n * @param {Encoder} encoder\n * @param {number} len\n */\nconst verifyLen = (encoder, len) => {\n  const bufferLen = encoder.cbuf.length\n  if (bufferLen - encoder.cpos < len) {\n    encoder.bufs.push(new Uint8Array(encoder.cbuf.buffer, 0, encoder.cpos))\n    encoder.cbuf = new Uint8Array(_math_js__WEBPACK_IMPORTED_MODULE_0__.max(bufferLen, len) * 2)\n    encoder.cpos = 0\n  }\n}\n\n/**\n * Write one byte to the encoder.\n *\n * @function\n * @param {Encoder} encoder\n * @param {number} num The byte that is to be encoded.\n */\nconst write = (encoder, num) => {\n  const bufferLen = encoder.cbuf.length\n  if (encoder.cpos === bufferLen) {\n    encoder.bufs.push(encoder.cbuf)\n    encoder.cbuf = new Uint8Array(bufferLen * 2)\n    encoder.cpos = 0\n  }\n  encoder.cbuf[encoder.cpos++] = num\n}\n\n/**\n * Write one byte at a specific position.\n * Position must already be written (i.e. encoder.length > pos)\n *\n * @function\n * @param {Encoder} encoder\n * @param {number} pos Position to which to write data\n * @param {number} num Unsigned 8-bit integer\n */\nconst set = (encoder, pos, num) => {\n  let buffer = null\n  // iterate all buffers and adjust position\n  for (let i = 0; i < encoder.bufs.length && buffer === null; i++) {\n    const b = encoder.bufs[i]\n    if (pos < b.length) {\n      buffer = b // found buffer\n    } else {\n      pos -= b.length\n    }\n  }\n  if (buffer === null) {\n    // use current buffer\n    buffer = encoder.cbuf\n  }\n  buffer[pos] = num\n}\n\n/**\n * Write one byte as an unsigned integer.\n *\n * @function\n * @param {Encoder} encoder\n * @param {number} num The number that is to be encoded.\n */\nconst writeUint8 = write\n\n/**\n * Write one byte as an unsigned Integer at a specific location.\n *\n * @function\n * @param {Encoder} encoder\n * @param {number} pos The location where the data will be written.\n * @param {number} num The number that is to be encoded.\n */\nconst setUint8 = set\n\n/**\n * Write two bytes as an unsigned integer.\n *\n * @function\n * @param {Encoder} encoder\n * @param {number} num The number that is to be encoded.\n */\nconst writeUint16 = (encoder, num) => {\n  write(encoder, num & _binary_js__WEBPACK_IMPORTED_MODULE_1__.BITS8)\n  write(encoder, (num >>> 8) & _binary_js__WEBPACK_IMPORTED_MODULE_1__.BITS8)\n}\n/**\n * Write two bytes as an unsigned integer at a specific location.\n *\n * @function\n * @param {Encoder} encoder\n * @param {number} pos The location where the data will be written.\n * @param {number} num The number that is to be encoded.\n */\nconst setUint16 = (encoder, pos, num) => {\n  set(encoder, pos, num & _binary_js__WEBPACK_IMPORTED_MODULE_1__.BITS8)\n  set(encoder, pos + 1, (num >>> 8) & _binary_js__WEBPACK_IMPORTED_MODULE_1__.BITS8)\n}\n\n/**\n * Write two bytes as an unsigned integer\n *\n * @function\n * @param {Encoder} encoder\n * @param {number} num The number that is to be encoded.\n */\nconst writeUint32 = (encoder, num) => {\n  for (let i = 0; i < 4; i++) {\n    write(encoder, num & _binary_js__WEBPACK_IMPORTED_MODULE_1__.BITS8)\n    num >>>= 8\n  }\n}\n\n/**\n * Write two bytes as an unsigned integer in big endian order.\n * (most significant byte first)\n *\n * @function\n * @param {Encoder} encoder\n * @param {number} num The number that is to be encoded.\n */\nconst writeUint32BigEndian = (encoder, num) => {\n  for (let i = 3; i >= 0; i--) {\n    write(encoder, (num >>> (8 * i)) & _binary_js__WEBPACK_IMPORTED_MODULE_1__.BITS8)\n  }\n}\n\n/**\n * Write two bytes as an unsigned integer at a specific location.\n *\n * @function\n * @param {Encoder} encoder\n * @param {number} pos The location where the data will be written.\n * @param {number} num The number that is to be encoded.\n */\nconst setUint32 = (encoder, pos, num) => {\n  for (let i = 0; i < 4; i++) {\n    set(encoder, pos + i, num & _binary_js__WEBPACK_IMPORTED_MODULE_1__.BITS8)\n    num >>>= 8\n  }\n}\n\n/**\n * Write a variable length unsigned integer. Max encodable integer is 2^53.\n *\n * @function\n * @param {Encoder} encoder\n * @param {number} num The number that is to be encoded.\n */\nconst writeVarUint = (encoder, num) => {\n  while (num > _binary_js__WEBPACK_IMPORTED_MODULE_1__.BITS7) {\n    write(encoder, _binary_js__WEBPACK_IMPORTED_MODULE_1__.BIT8 | (_binary_js__WEBPACK_IMPORTED_MODULE_1__.BITS7 & num))\n    num = _math_js__WEBPACK_IMPORTED_MODULE_0__.floor(num / 128) // shift >>> 7\n  }\n  write(encoder, _binary_js__WEBPACK_IMPORTED_MODULE_1__.BITS7 & num)\n}\n\n/**\n * Write a variable length integer.\n *\n * We use the 7th bit instead for signaling that this is a negative number.\n *\n * @function\n * @param {Encoder} encoder\n * @param {number} num The number that is to be encoded.\n */\nconst writeVarInt = (encoder, num) => {\n  const isNegative = _math_js__WEBPACK_IMPORTED_MODULE_0__.isNegativeZero(num)\n  if (isNegative) {\n    num = -num\n  }\n  //             |- whether to continue reading         |- whether is negative     |- number\n  write(encoder, (num > _binary_js__WEBPACK_IMPORTED_MODULE_1__.BITS6 ? _binary_js__WEBPACK_IMPORTED_MODULE_1__.BIT8 : 0) | (isNegative ? _binary_js__WEBPACK_IMPORTED_MODULE_1__.BIT7 : 0) | (_binary_js__WEBPACK_IMPORTED_MODULE_1__.BITS6 & num))\n  num = _math_js__WEBPACK_IMPORTED_MODULE_0__.floor(num / 64) // shift >>> 6\n  // We don't need to consider the case of num === 0 so we can use a different\n  // pattern here than above.\n  while (num > 0) {\n    write(encoder, (num > _binary_js__WEBPACK_IMPORTED_MODULE_1__.BITS7 ? _binary_js__WEBPACK_IMPORTED_MODULE_1__.BIT8 : 0) | (_binary_js__WEBPACK_IMPORTED_MODULE_1__.BITS7 & num))\n    num = _math_js__WEBPACK_IMPORTED_MODULE_0__.floor(num / 128) // shift >>> 7\n  }\n}\n\n/**\n * A cache to store strings temporarily\n */\nconst _strBuffer = new Uint8Array(30000)\nconst _maxStrBSize = _strBuffer.length / 3\n\n/**\n * Write a variable length string.\n *\n * @function\n * @param {Encoder} encoder\n * @param {String} str The string that is to be encoded.\n */\nconst _writeVarStringNative = (encoder, str) => {\n  if (str.length < _maxStrBSize) {\n    // We can encode the string into the existing buffer\n    /* c8 ignore next */\n    const written = _string_js__WEBPACK_IMPORTED_MODULE_2__.utf8TextEncoder.encodeInto(str, _strBuffer).written || 0\n    writeVarUint(encoder, written)\n    for (let i = 0; i < written; i++) {\n      write(encoder, _strBuffer[i])\n    }\n  } else {\n    writeVarUint8Array(encoder, _string_js__WEBPACK_IMPORTED_MODULE_2__.encodeUtf8(str))\n  }\n}\n\n/**\n * Write a variable length string.\n *\n * @function\n * @param {Encoder} encoder\n * @param {String} str The string that is to be encoded.\n */\nconst _writeVarStringPolyfill = (encoder, str) => {\n  const encodedString = unescape(encodeURIComponent(str))\n  const len = encodedString.length\n  writeVarUint(encoder, len)\n  for (let i = 0; i < len; i++) {\n    write(encoder, /** @type {number} */ (encodedString.codePointAt(i)))\n  }\n}\n\n/**\n * Write a variable length string.\n *\n * @function\n * @param {Encoder} encoder\n * @param {String} str The string that is to be encoded.\n */\n/* c8 ignore next */\nconst writeVarString = (_string_js__WEBPACK_IMPORTED_MODULE_2__.utf8TextEncoder && /** @type {any} */ (_string_js__WEBPACK_IMPORTED_MODULE_2__.utf8TextEncoder).encodeInto) ? _writeVarStringNative : _writeVarStringPolyfill\n\n/**\n * Write a string terminated by a special byte sequence. This is not very performant and is\n * generally discouraged. However, the resulting byte arrays are lexiographically ordered which\n * makes this a nice feature for databases.\n *\n * The string will be encoded using utf8 and then terminated and escaped using writeTerminatingUint8Array.\n *\n * @function\n * @param {Encoder} encoder\n * @param {String} str The string that is to be encoded.\n */\nconst writeTerminatedString = (encoder, str) =>\n  writeTerminatedUint8Array(encoder, _string_js__WEBPACK_IMPORTED_MODULE_2__.encodeUtf8(str))\n\n/**\n * Write a terminating Uint8Array. Note that this is not performant and is generally\n * discouraged. There are few situations when this is needed.\n *\n * We use 0x0 as a terminating character. 0x1 serves as an escape character for 0x0 and 0x1.\n *\n * Example: [0,1,2] is encoded to [1,0,1,1,2,0]. 0x0, and 0x1 needed to be escaped using 0x1. Then\n * the result is terminated using the 0x0 character.\n *\n * This is basically how many systems implement null terminated strings. However, we use an escape\n * character 0x1 to avoid issues and potenial attacks on our database (if this is used as a key\n * encoder for NoSql databases).\n *\n * @function\n * @param {Encoder} encoder\n * @param {Uint8Array} buf The string that is to be encoded.\n */\nconst writeTerminatedUint8Array = (encoder, buf) => {\n  for (let i = 0; i < buf.length; i++) {\n    const b = buf[i]\n    if (b === 0 || b === 1) {\n      write(encoder, 1)\n    }\n    write(encoder, buf[i])\n  }\n  write(encoder, 0)\n}\n\n/**\n * Write the content of another Encoder.\n *\n * @TODO: can be improved!\n *        - Note: Should consider that when appending a lot of small Encoders, we should rather clone than referencing the old structure.\n *                Encoders start with a rather big initial buffer.\n *\n * @function\n * @param {Encoder} encoder The enUint8Arr\n * @param {Encoder} append The BinaryEncoder to be written.\n */\nconst writeBinaryEncoder = (encoder, append) => writeUint8Array(encoder, toUint8Array(append))\n\n/**\n * Append fixed-length Uint8Array to the encoder.\n *\n * @function\n * @param {Encoder} encoder\n * @param {Uint8Array} uint8Array\n */\nconst writeUint8Array = (encoder, uint8Array) => {\n  const bufferLen = encoder.cbuf.length\n  const cpos = encoder.cpos\n  const leftCopyLen = _math_js__WEBPACK_IMPORTED_MODULE_0__.min(bufferLen - cpos, uint8Array.length)\n  const rightCopyLen = uint8Array.length - leftCopyLen\n  encoder.cbuf.set(uint8Array.subarray(0, leftCopyLen), cpos)\n  encoder.cpos += leftCopyLen\n  if (rightCopyLen > 0) {\n    // Still something to write, write right half..\n    // Append new buffer\n    encoder.bufs.push(encoder.cbuf)\n    // must have at least size of remaining buffer\n    encoder.cbuf = new Uint8Array(_math_js__WEBPACK_IMPORTED_MODULE_0__.max(bufferLen * 2, rightCopyLen))\n    // copy array\n    encoder.cbuf.set(uint8Array.subarray(leftCopyLen))\n    encoder.cpos = rightCopyLen\n  }\n}\n\n/**\n * Append an Uint8Array to Encoder.\n *\n * @function\n * @param {Encoder} encoder\n * @param {Uint8Array} uint8Array\n */\nconst writeVarUint8Array = (encoder, uint8Array) => {\n  writeVarUint(encoder, uint8Array.byteLength)\n  writeUint8Array(encoder, uint8Array)\n}\n\n/**\n * Create an DataView of the next `len` bytes. Use it to write data after\n * calling this function.\n *\n * ```js\n * // write float32 using DataView\n * const dv = writeOnDataView(encoder, 4)\n * dv.setFloat32(0, 1.1)\n * // read float32 using DataView\n * const dv = readFromDataView(encoder, 4)\n * dv.getFloat32(0) // => 1.100000023841858 (leaving it to the reader to find out why this is the correct result)\n * ```\n *\n * @param {Encoder} encoder\n * @param {number} len\n * @return {DataView}\n */\nconst writeOnDataView = (encoder, len) => {\n  verifyLen(encoder, len)\n  const dview = new DataView(encoder.cbuf.buffer, encoder.cpos, len)\n  encoder.cpos += len\n  return dview\n}\n\n/**\n * @param {Encoder} encoder\n * @param {number} num\n */\nconst writeFloat32 = (encoder, num) => writeOnDataView(encoder, 4).setFloat32(0, num, false)\n\n/**\n * @param {Encoder} encoder\n * @param {number} num\n */\nconst writeFloat64 = (encoder, num) => writeOnDataView(encoder, 8).setFloat64(0, num, false)\n\n/**\n * @param {Encoder} encoder\n * @param {bigint} num\n */\nconst writeBigInt64 = (encoder, num) => /** @type {any} */ (writeOnDataView(encoder, 8)).setBigInt64(0, num, false)\n\n/**\n * @param {Encoder} encoder\n * @param {bigint} num\n */\nconst writeBigUint64 = (encoder, num) => /** @type {any} */ (writeOnDataView(encoder, 8)).setBigUint64(0, num, false)\n\nconst floatTestBed = new DataView(new ArrayBuffer(4))\n/**\n * Check if a number can be encoded as a 32 bit float.\n *\n * @param {number} num\n * @return {boolean}\n */\nconst isFloat32 = num => {\n  floatTestBed.setFloat32(0, num)\n  return floatTestBed.getFloat32(0) === num\n}\n\n/**\n * Encode data with efficient binary format.\n *\n * Differences to JSON:\n * • Transforms data to a binary format (not to a string)\n * • Encodes undefined, NaN, and ArrayBuffer (these can't be represented in JSON)\n * • Numbers are efficiently encoded either as a variable length integer, as a\n *   32 bit float, as a 64 bit float, or as a 64 bit bigint.\n *\n * Encoding table:\n *\n * | Data Type           | Prefix   | Encoding Method    | Comment |\n * | ------------------- | -------- | ------------------ | ------- |\n * | undefined           | 127      |                    | Functions, symbol, and everything that cannot be identified is encoded as undefined |\n * | null                | 126      |                    | |\n * | integer             | 125      | writeVarInt        | Only encodes 32 bit signed integers |\n * | float32             | 124      | writeFloat32       | |\n * | float64             | 123      | writeFloat64       | |\n * | bigint              | 122      | writeBigInt64      | |\n * | boolean (false)     | 121      |                    | True and false are different data types so we save the following byte |\n * | boolean (true)      | 120      |                    | - 0b01111000 so the last bit determines whether true or false |\n * | string              | 119      | writeVarString     | |\n * | object<string,any>  | 118      | custom             | Writes {length} then {length} key-value pairs |\n * | array<any>          | 117      | custom             | Writes {length} then {length} json values |\n * | Uint8Array          | 116      | writeVarUint8Array | We use Uint8Array for any kind of binary data |\n *\n * Reasons for the decreasing prefix:\n * We need the first bit for extendability (later we may want to encode the\n * prefix with writeVarUint). The remaining 7 bits are divided as follows:\n * [0-30]   the beginning of the data range is used for custom purposes\n *          (defined by the function that uses this library)\n * [31-127] the end of the data range is used for data encoding by\n *          lib0/encoding.js\n *\n * @param {Encoder} encoder\n * @param {undefined|null|number|bigint|boolean|string|Object<string,any>|Array<any>|Uint8Array} data\n */\nconst writeAny = (encoder, data) => {\n  switch (typeof data) {\n    case 'string':\n      // TYPE 119: STRING\n      write(encoder, 119)\n      writeVarString(encoder, data)\n      break\n    case 'number':\n      if (_number_js__WEBPACK_IMPORTED_MODULE_3__.isInteger(data) && _math_js__WEBPACK_IMPORTED_MODULE_0__.abs(data) <= _binary_js__WEBPACK_IMPORTED_MODULE_1__.BITS31) {\n        // TYPE 125: INTEGER\n        write(encoder, 125)\n        writeVarInt(encoder, data)\n      } else if (isFloat32(data)) {\n        // TYPE 124: FLOAT32\n        write(encoder, 124)\n        writeFloat32(encoder, data)\n      } else {\n        // TYPE 123: FLOAT64\n        write(encoder, 123)\n        writeFloat64(encoder, data)\n      }\n      break\n    case 'bigint':\n      // TYPE 122: BigInt\n      write(encoder, 122)\n      writeBigInt64(encoder, data)\n      break\n    case 'object':\n      if (data === null) {\n        // TYPE 126: null\n        write(encoder, 126)\n      } else if (_array_js__WEBPACK_IMPORTED_MODULE_4__.isArray(data)) {\n        // TYPE 117: Array\n        write(encoder, 117)\n        writeVarUint(encoder, data.length)\n        for (let i = 0; i < data.length; i++) {\n          writeAny(encoder, data[i])\n        }\n      } else if (data instanceof Uint8Array) {\n        // TYPE 116: ArrayBuffer\n        write(encoder, 116)\n        writeVarUint8Array(encoder, data)\n      } else {\n        // TYPE 118: Object\n        write(encoder, 118)\n        const keys = Object.keys(data)\n        writeVarUint(encoder, keys.length)\n        for (let i = 0; i < keys.length; i++) {\n          const key = keys[i]\n          writeVarString(encoder, key)\n          writeAny(encoder, data[key])\n        }\n      }\n      break\n    case 'boolean':\n      // TYPE 120/121: boolean (true/false)\n      write(encoder, data ? 120 : 121)\n      break\n    default:\n      // TYPE 127: undefined\n      write(encoder, 127)\n  }\n}\n\n/**\n * Now come a few stateful encoder that have their own classes.\n */\n\n/**\n * Basic Run Length Encoder - a basic compression implementation.\n *\n * Encodes [1,1,1,7] to [1,3,7,1] (3 times 1, 1 time 7). This encoder might do more harm than good if there are a lot of values that are not repeated.\n *\n * It was originally used for image compression. Cool .. article http://csbruce.com/cbm/transactor/pdfs/trans_v7_i06.pdf\n *\n * @note T must not be null!\n *\n * @template T\n */\nclass RleEncoder extends Encoder {\n  /**\n   * @param {function(Encoder, T):void} writer\n   */\n  constructor (writer) {\n    super()\n    /**\n     * The writer\n     */\n    this.w = writer\n    /**\n     * Current state\n     * @type {T|null}\n     */\n    this.s = null\n    this.count = 0\n  }\n\n  /**\n   * @param {T} v\n   */\n  write (v) {\n    if (this.s === v) {\n      this.count++\n    } else {\n      if (this.count > 0) {\n        // flush counter, unless this is the first value (count = 0)\n        writeVarUint(this, this.count - 1) // since count is always > 0, we can decrement by one. non-standard encoding ftw\n      }\n      this.count = 1\n      // write first value\n      this.w(this, v)\n      this.s = v\n    }\n  }\n}\n\n/**\n * Basic diff decoder using variable length encoding.\n *\n * Encodes the values [3, 1100, 1101, 1050, 0] to [3, 1097, 1, -51, -1050] using writeVarInt.\n */\nclass IntDiffEncoder extends Encoder {\n  /**\n   * @param {number} start\n   */\n  constructor (start) {\n    super()\n    /**\n     * Current state\n     * @type {number}\n     */\n    this.s = start\n  }\n\n  /**\n   * @param {number} v\n   */\n  write (v) {\n    writeVarInt(this, v - this.s)\n    this.s = v\n  }\n}\n\n/**\n * A combination of IntDiffEncoder and RleEncoder.\n *\n * Basically first writes the IntDiffEncoder and then counts duplicate diffs using RleEncoding.\n *\n * Encodes the values [1,1,1,2,3,4,5,6] as [1,1,0,2,1,5] (RLE([1,0,0,1,1,1,1,1]) ⇒ RleIntDiff[1,1,0,2,1,5])\n */\nclass RleIntDiffEncoder extends Encoder {\n  /**\n   * @param {number} start\n   */\n  constructor (start) {\n    super()\n    /**\n     * Current state\n     * @type {number}\n     */\n    this.s = start\n    this.count = 0\n  }\n\n  /**\n   * @param {number} v\n   */\n  write (v) {\n    if (this.s === v && this.count > 0) {\n      this.count++\n    } else {\n      if (this.count > 0) {\n        // flush counter, unless this is the first value (count = 0)\n        writeVarUint(this, this.count - 1) // since count is always > 0, we can decrement by one. non-standard encoding ftw\n      }\n      this.count = 1\n      // write first value\n      writeVarInt(this, v - this.s)\n      this.s = v\n    }\n  }\n}\n\n/**\n * @param {UintOptRleEncoder} encoder\n */\nconst flushUintOptRleEncoder = encoder => {\n  if (encoder.count > 0) {\n    // flush counter, unless this is the first value (count = 0)\n    // case 1: just a single value. set sign to positive\n    // case 2: write several values. set sign to negative to indicate that there is a length coming\n    writeVarInt(encoder.encoder, encoder.count === 1 ? encoder.s : -encoder.s)\n    if (encoder.count > 1) {\n      writeVarUint(encoder.encoder, encoder.count - 2) // since count is always > 1, we can decrement by one. non-standard encoding ftw\n    }\n  }\n}\n\n/**\n * Optimized Rle encoder that does not suffer from the mentioned problem of the basic Rle encoder.\n *\n * Internally uses VarInt encoder to write unsigned integers. If the input occurs multiple times, we write\n * write it as a negative number. The UintOptRleDecoder then understands that it needs to read a count.\n *\n * Encodes [1,2,3,3,3] as [1,2,-3,3] (once 1, once 2, three times 3)\n */\nclass UintOptRleEncoder {\n  constructor () {\n    this.encoder = new Encoder()\n    /**\n     * @type {number}\n     */\n    this.s = 0\n    this.count = 0\n  }\n\n  /**\n   * @param {number} v\n   */\n  write (v) {\n    if (this.s === v) {\n      this.count++\n    } else {\n      flushUintOptRleEncoder(this)\n      this.count = 1\n      this.s = v\n    }\n  }\n\n  /**\n   * Flush the encoded state and transform this to a Uint8Array.\n   *\n   * Note that this should only be called once.\n   */\n  toUint8Array () {\n    flushUintOptRleEncoder(this)\n    return toUint8Array(this.encoder)\n  }\n}\n\n/**\n * Increasing Uint Optimized RLE Encoder\n *\n * The RLE encoder counts the number of same occurences of the same value.\n * The IncUintOptRle encoder counts if the value increases.\n * I.e. 7, 8, 9, 10 will be encoded as [-7, 4]. 1, 3, 5 will be encoded\n * as [1, 3, 5].\n */\nclass IncUintOptRleEncoder {\n  constructor () {\n    this.encoder = new Encoder()\n    /**\n     * @type {number}\n     */\n    this.s = 0\n    this.count = 0\n  }\n\n  /**\n   * @param {number} v\n   */\n  write (v) {\n    if (this.s + this.count === v) {\n      this.count++\n    } else {\n      flushUintOptRleEncoder(this)\n      this.count = 1\n      this.s = v\n    }\n  }\n\n  /**\n   * Flush the encoded state and transform this to a Uint8Array.\n   *\n   * Note that this should only be called once.\n   */\n  toUint8Array () {\n    flushUintOptRleEncoder(this)\n    return toUint8Array(this.encoder)\n  }\n}\n\n/**\n * @param {IntDiffOptRleEncoder} encoder\n */\nconst flushIntDiffOptRleEncoder = encoder => {\n  if (encoder.count > 0) {\n    //          31 bit making up the diff | wether to write the counter\n    // const encodedDiff = encoder.diff << 1 | (encoder.count === 1 ? 0 : 1)\n    const encodedDiff = encoder.diff * 2 + (encoder.count === 1 ? 0 : 1)\n    // flush counter, unless this is the first value (count = 0)\n    // case 1: just a single value. set first bit to positive\n    // case 2: write several values. set first bit to negative to indicate that there is a length coming\n    writeVarInt(encoder.encoder, encodedDiff)\n    if (encoder.count > 1) {\n      writeVarUint(encoder.encoder, encoder.count - 2) // since count is always > 1, we can decrement by one. non-standard encoding ftw\n    }\n  }\n}\n\n/**\n * A combination of the IntDiffEncoder and the UintOptRleEncoder.\n *\n * The count approach is similar to the UintDiffOptRleEncoder, but instead of using the negative bitflag, it encodes\n * in the LSB whether a count is to be read. Therefore this Encoder only supports 31 bit integers!\n *\n * Encodes [1, 2, 3, 2] as [3, 1, 6, -1] (more specifically [(1 << 1) | 1, (3 << 0) | 0, -1])\n *\n * Internally uses variable length encoding. Contrary to normal UintVar encoding, the first byte contains:\n * * 1 bit that denotes whether the next value is a count (LSB)\n * * 1 bit that denotes whether this value is negative (MSB - 1)\n * * 1 bit that denotes whether to continue reading the variable length integer (MSB)\n *\n * Therefore, only five bits remain to encode diff ranges.\n *\n * Use this Encoder only when appropriate. In most cases, this is probably a bad idea.\n */\nclass IntDiffOptRleEncoder {\n  constructor () {\n    this.encoder = new Encoder()\n    /**\n     * @type {number}\n     */\n    this.s = 0\n    this.count = 0\n    this.diff = 0\n  }\n\n  /**\n   * @param {number} v\n   */\n  write (v) {\n    if (this.diff === v - this.s) {\n      this.s = v\n      this.count++\n    } else {\n      flushIntDiffOptRleEncoder(this)\n      this.count = 1\n      this.diff = v - this.s\n      this.s = v\n    }\n  }\n\n  /**\n   * Flush the encoded state and transform this to a Uint8Array.\n   *\n   * Note that this should only be called once.\n   */\n  toUint8Array () {\n    flushIntDiffOptRleEncoder(this)\n    return toUint8Array(this.encoder)\n  }\n}\n\n/**\n * Optimized String Encoder.\n *\n * Encoding many small strings in a simple Encoder is not very efficient. The function call to decode a string takes some time and creates references that must be eventually deleted.\n * In practice, when decoding several million small strings, the GC will kick in more and more often to collect orphaned string objects (or maybe there is another reason?).\n *\n * This string encoder solves the above problem. All strings are concatenated and written as a single string using a single encoding call.\n *\n * The lengths are encoded using a UintOptRleEncoder.\n */\nclass StringEncoder {\n  constructor () {\n    /**\n     * @type {Array<string>}\n     */\n    this.sarr = []\n    this.s = ''\n    this.lensE = new UintOptRleEncoder()\n  }\n\n  /**\n   * @param {string} string\n   */\n  write (string) {\n    this.s += string\n    if (this.s.length > 19) {\n      this.sarr.push(this.s)\n      this.s = ''\n    }\n    this.lensE.write(string.length)\n  }\n\n  toUint8Array () {\n    const encoder = new Encoder()\n    this.sarr.push(this.s)\n    this.s = ''\n    writeVarString(encoder, this.sarr.join(''))\n    writeUint8Array(encoder, this.lensE.toUint8Array())\n    return toUint8Array(encoder)\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/lib0/encoding.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/lib0/environment.js":
/*!**********************************************!*\
  !*** ../../node_modules/lib0/environment.js ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ensureConf: () => (/* binding */ ensureConf),\n/* harmony export */   getConf: () => (/* binding */ getConf),\n/* harmony export */   getParam: () => (/* binding */ getParam),\n/* harmony export */   getVariable: () => (/* binding */ getVariable),\n/* harmony export */   hasConf: () => (/* binding */ hasConf),\n/* harmony export */   hasParam: () => (/* binding */ hasParam),\n/* harmony export */   isBrowser: () => (/* binding */ isBrowser),\n/* harmony export */   isMac: () => (/* binding */ isMac),\n/* harmony export */   isNode: () => (/* binding */ isNode),\n/* harmony export */   production: () => (/* binding */ production),\n/* harmony export */   supportsColor: () => (/* binding */ supportsColor)\n/* harmony export */ });\n/* harmony import */ var _map_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./map.js */ \"(ssr)/../../node_modules/lib0/map.js\");\n/* harmony import */ var _string_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./string.js */ \"(ssr)/../../node_modules/lib0/string.js\");\n/* harmony import */ var _conditions_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./conditions.js */ \"(ssr)/../../node_modules/lib0/conditions.js\");\n/* harmony import */ var _storage_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./storage.js */ \"(ssr)/../../node_modules/lib0/storage.js\");\n/* harmony import */ var _function_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./function.js */ \"(ssr)/../../node_modules/lib0/function.js\");\n/**\n * Isomorphic module to work access the environment (query params, env variables).\n *\n * @module environment\n */\n\n\n\n\n\n\n\n/* c8 ignore next 2 */\n// @ts-ignore\nconst isNode = typeof process !== 'undefined' && process.release && /node|io\\.js/.test(process.release.name) && Object.prototype.toString.call(typeof process !== 'undefined' ? process : 0) === '[object process]'\n\n/* c8 ignore next */\nconst isBrowser = typeof window !== 'undefined' && typeof document !== 'undefined' && !isNode\n/* c8 ignore next 3 */\nconst isMac = typeof navigator !== 'undefined'\n  ? /Mac/.test(navigator.platform)\n  : false\n\n/**\n * @type {Map<string,string>}\n */\nlet params\nconst args = []\n\n/* c8 ignore start */\nconst computeParams = () => {\n  if (params === undefined) {\n    if (isNode) {\n      params = _map_js__WEBPACK_IMPORTED_MODULE_0__.create()\n      const pargs = process.argv\n      let currParamName = null\n      for (let i = 0; i < pargs.length; i++) {\n        const parg = pargs[i]\n        if (parg[0] === '-') {\n          if (currParamName !== null) {\n            params.set(currParamName, '')\n          }\n          currParamName = parg\n        } else {\n          if (currParamName !== null) {\n            params.set(currParamName, parg)\n            currParamName = null\n          } else {\n            args.push(parg)\n          }\n        }\n      }\n      if (currParamName !== null) {\n        params.set(currParamName, '')\n      }\n      // in ReactNative for example this would not be true (unless connected to the Remote Debugger)\n    } else if (typeof location === 'object') {\n      params = _map_js__WEBPACK_IMPORTED_MODULE_0__.create(); // eslint-disable-next-line no-undef\n      (location.search || '?').slice(1).split('&').forEach((kv) => {\n        if (kv.length !== 0) {\n          const [key, value] = kv.split('=')\n          params.set(`--${_string_js__WEBPACK_IMPORTED_MODULE_1__.fromCamelCase(key, '-')}`, value)\n          params.set(`-${_string_js__WEBPACK_IMPORTED_MODULE_1__.fromCamelCase(key, '-')}`, value)\n        }\n      })\n    } else {\n      params = _map_js__WEBPACK_IMPORTED_MODULE_0__.create()\n    }\n  }\n  return params\n}\n/* c8 ignore stop */\n\n/**\n * @param {string} name\n * @return {boolean}\n */\n/* c8 ignore next */\nconst hasParam = (name) => computeParams().has(name)\n\n/**\n * @param {string} name\n * @param {string} defaultVal\n * @return {string}\n */\n/* c8 ignore next 2 */\nconst getParam = (name, defaultVal) =>\n  computeParams().get(name) || defaultVal\n\n/**\n * @param {string} name\n * @return {string|null}\n */\n/* c8 ignore next 4 */\nconst getVariable = (name) =>\n  isNode\n    ? _conditions_js__WEBPACK_IMPORTED_MODULE_2__.undefinedToNull(process.env[name.toUpperCase().replaceAll('-', '_')])\n    : _conditions_js__WEBPACK_IMPORTED_MODULE_2__.undefinedToNull(_storage_js__WEBPACK_IMPORTED_MODULE_3__.varStorage.getItem(name))\n\n/**\n * @param {string} name\n * @return {string|null}\n */\n/* c8 ignore next 2 */\nconst getConf = (name) =>\n  computeParams().get('--' + name) || getVariable(name)\n\n/**\n * @param {string} name\n * @return {string}\n */\n/* c8 ignore next 5 */\nconst ensureConf = (name) => {\n  const c = getConf(name)\n  if (c == null) throw new Error(`Expected configuration \"${name.toUpperCase().replaceAll('-', '_')}\"`)\n  return c\n}\n\n/**\n * @param {string} name\n * @return {boolean}\n */\n/* c8 ignore next 2 */\nconst hasConf = (name) =>\n  hasParam('--' + name) || getVariable(name) !== null\n\n/* c8 ignore next */\nconst production = hasConf('production')\n\n/* c8 ignore next 2 */\nconst forceColor = isNode &&\n  _function_js__WEBPACK_IMPORTED_MODULE_4__.isOneOf(process.env.FORCE_COLOR, ['true', '1', '2'])\n\n/* c8 ignore start */\n/**\n * Color is enabled by default if the terminal supports it.\n *\n * Explicitly enable color using `--color` parameter\n * Disable color using `--no-color` parameter or using `NO_COLOR=1` environment variable.\n * `FORCE_COLOR=1` enables color and takes precedence over all.\n */\nconst supportsColor = forceColor || (\n  !hasParam('--no-colors') && // @todo deprecate --no-colors\n  !hasConf('no-color') &&\n  (!isNode || process.stdout.isTTY) && (\n    !isNode ||\n    hasParam('--color') ||\n    getVariable('COLORTERM') !== null ||\n    (getVariable('TERM') || '').includes('color')\n  )\n)\n/* c8 ignore stop */\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/lib0/environment.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/lib0/error.js":
/*!****************************************!*\
  !*** ../../node_modules/lib0/error.js ***!
  \****************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   create: () => (/* binding */ create),\n/* harmony export */   methodUnimplemented: () => (/* binding */ methodUnimplemented),\n/* harmony export */   unexpectedCase: () => (/* binding */ unexpectedCase)\n/* harmony export */ });\n/**\n * Error helpers.\n *\n * @module error\n */\n\n/**\n * @param {string} s\n * @return {Error}\n */\n/* c8 ignore next */\nconst create = s => new Error(s)\n\n/**\n * @throws {Error}\n * @return {never}\n */\n/* c8 ignore next 3 */\nconst methodUnimplemented = () => {\n  throw create('Method unimplemented')\n}\n\n/**\n * @throws {Error}\n * @return {never}\n */\n/* c8 ignore next 3 */\nconst unexpectedCase = () => {\n  throw create('Unexpected case')\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2xpYjAvZXJyb3IuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLFdBQVcsUUFBUTtBQUNuQixZQUFZO0FBQ1o7QUFDQTtBQUNPOztBQUVQO0FBQ0EsWUFBWTtBQUNaLFlBQVk7QUFDWjtBQUNBO0FBQ087QUFDUDtBQUNBOztBQUVBO0FBQ0EsWUFBWTtBQUNaLFlBQVk7QUFDWjtBQUNBO0FBQ087QUFDUDtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQG9wZW5jYW52YXMvd2ViLy4uLy4uL25vZGVfbW9kdWxlcy9saWIwL2Vycm9yLmpzPzE4NWEiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBFcnJvciBoZWxwZXJzLlxuICpcbiAqIEBtb2R1bGUgZXJyb3JcbiAqL1xuXG4vKipcbiAqIEBwYXJhbSB7c3RyaW5nfSBzXG4gKiBAcmV0dXJuIHtFcnJvcn1cbiAqL1xuLyogYzggaWdub3JlIG5leHQgKi9cbmV4cG9ydCBjb25zdCBjcmVhdGUgPSBzID0+IG5ldyBFcnJvcihzKVxuXG4vKipcbiAqIEB0aHJvd3Mge0Vycm9yfVxuICogQHJldHVybiB7bmV2ZXJ9XG4gKi9cbi8qIGM4IGlnbm9yZSBuZXh0IDMgKi9cbmV4cG9ydCBjb25zdCBtZXRob2RVbmltcGxlbWVudGVkID0gKCkgPT4ge1xuICB0aHJvdyBjcmVhdGUoJ01ldGhvZCB1bmltcGxlbWVudGVkJylcbn1cblxuLyoqXG4gKiBAdGhyb3dzIHtFcnJvcn1cbiAqIEByZXR1cm4ge25ldmVyfVxuICovXG4vKiBjOCBpZ25vcmUgbmV4dCAzICovXG5leHBvcnQgY29uc3QgdW5leHBlY3RlZENhc2UgPSAoKSA9PiB7XG4gIHRocm93IGNyZWF0ZSgnVW5leHBlY3RlZCBjYXNlJylcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/lib0/error.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/lib0/eventloop.js":
/*!********************************************!*\
  !*** ../../node_modules/lib0/eventloop.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Animation: () => (/* binding */ Animation),\n/* harmony export */   animationFrame: () => (/* binding */ animationFrame),\n/* harmony export */   createDebouncer: () => (/* binding */ createDebouncer),\n/* harmony export */   enqueue: () => (/* binding */ enqueue),\n/* harmony export */   idleCallback: () => (/* binding */ idleCallback),\n/* harmony export */   interval: () => (/* binding */ interval),\n/* harmony export */   timeout: () => (/* binding */ timeout)\n/* harmony export */ });\n/* global requestIdleCallback, requestAnimationFrame, cancelIdleCallback, cancelAnimationFrame */\n\n/**\n * Utility module to work with EcmaScript's event loop.\n *\n * @module eventloop\n */\n\n/**\n * @type {Array<function>}\n */\nlet queue = []\n\nconst _runQueue = () => {\n  for (let i = 0; i < queue.length; i++) {\n    queue[i]()\n  }\n  queue = []\n}\n\n/**\n * @param {function():void} f\n */\nconst enqueue = f => {\n  queue.push(f)\n  if (queue.length === 1) {\n    setTimeout(_runQueue, 0)\n  }\n}\n\n/**\n * @typedef {Object} TimeoutObject\n * @property {function} TimeoutObject.destroy\n */\n\n/**\n * @param {function(number):void} clearFunction\n */\nconst createTimeoutClass = clearFunction => class TT {\n  /**\n   * @param {number} timeoutId\n   */\n  constructor (timeoutId) {\n    this._ = timeoutId\n  }\n\n  destroy () {\n    clearFunction(this._)\n  }\n}\n\nconst Timeout = createTimeoutClass(clearTimeout)\n\n/**\n * @param {number} timeout\n * @param {function} callback\n * @return {TimeoutObject}\n */\nconst timeout = (timeout, callback) => new Timeout(setTimeout(callback, timeout))\n\nconst Interval = createTimeoutClass(clearInterval)\n\n/**\n * @param {number} timeout\n * @param {function} callback\n * @return {TimeoutObject}\n */\nconst interval = (timeout, callback) => new Interval(setInterval(callback, timeout))\n\n/* c8 ignore next */\nconst Animation = createTimeoutClass(arg => typeof requestAnimationFrame !== 'undefined' && cancelAnimationFrame(arg))\n\n/**\n * @param {function(number):void} cb\n * @return {TimeoutObject}\n */\n/* c8 ignore next */\nconst animationFrame = cb => typeof requestAnimationFrame === 'undefined' ? timeout(0, cb) : new Animation(requestAnimationFrame(cb))\n\n/* c8 ignore next */\n// @ts-ignore\nconst Idle = createTimeoutClass(arg => typeof cancelIdleCallback !== 'undefined' && cancelIdleCallback(arg))\n\n/**\n * Note: this is experimental and is probably only useful in browsers.\n *\n * @param {function} cb\n * @return {TimeoutObject}\n */\n/* c8 ignore next 2 */\n// @ts-ignore\nconst idleCallback = cb => typeof requestIdleCallback !== 'undefined' ? new Idle(requestIdleCallback(cb)) : timeout(1000, cb)\n\n/**\n * @param {number} timeout Timeout of the debounce action\n * @return {function(function():void):void}\n */\nconst createDebouncer = timeout => {\n  let timer = -1\n  return f => {\n    clearTimeout(timer)\n    if (f) {\n      timer = /** @type {any} */ (setTimeout(f, timeout))\n    }\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/lib0/eventloop.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/lib0/function.js":
/*!*******************************************!*\
  !*** ../../node_modules/lib0/function.js ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   apply: () => (/* binding */ apply),\n/* harmony export */   callAll: () => (/* binding */ callAll),\n/* harmony export */   equalityDeep: () => (/* binding */ equalityDeep),\n/* harmony export */   equalityFlat: () => (/* binding */ equalityFlat),\n/* harmony export */   equalityStrict: () => (/* binding */ equalityStrict),\n/* harmony export */   id: () => (/* binding */ id),\n/* harmony export */   is: () => (/* binding */ is),\n/* harmony export */   isArray: () => (/* binding */ isArray),\n/* harmony export */   isNumber: () => (/* binding */ isNumber),\n/* harmony export */   isOneOf: () => (/* binding */ isOneOf),\n/* harmony export */   isString: () => (/* binding */ isString),\n/* harmony export */   isTemplate: () => (/* binding */ isTemplate),\n/* harmony export */   nop: () => (/* binding */ nop)\n/* harmony export */ });\n/* harmony import */ var _array_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./array.js */ \"(ssr)/../../node_modules/lib0/array.js\");\n/* harmony import */ var _object_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./object.js */ \"(ssr)/../../node_modules/lib0/object.js\");\n/**\n * Common functions and function call helpers.\n *\n * @module function\n */\n\n\n\n\n/**\n * Calls all functions in `fs` with args. Only throws after all functions were called.\n *\n * @param {Array<function>} fs\n * @param {Array<any>} args\n */\nconst callAll = (fs, args, i = 0) => {\n  try {\n    for (; i < fs.length; i++) {\n      fs[i](...args)\n    }\n  } finally {\n    if (i < fs.length) {\n      callAll(fs, args, i + 1)\n    }\n  }\n}\n\nconst nop = () => {}\n\n/**\n * @template T\n * @param {function():T} f\n * @return {T}\n */\nconst apply = f => f()\n\n/**\n * @template A\n *\n * @param {A} a\n * @return {A}\n */\nconst id = a => a\n\n/**\n * @template T\n *\n * @param {T} a\n * @param {T} b\n * @return {boolean}\n */\nconst equalityStrict = (a, b) => a === b\n\n/**\n * @template T\n *\n * @param {Array<T>|object} a\n * @param {Array<T>|object} b\n * @return {boolean}\n */\nconst equalityFlat = (a, b) => a === b || (a != null && b != null && a.constructor === b.constructor && ((_array_js__WEBPACK_IMPORTED_MODULE_0__.isArray(a) && _array_js__WEBPACK_IMPORTED_MODULE_0__.equalFlat(a, /** @type {Array<T>} */ (b))) || (typeof a === 'object' && _object_js__WEBPACK_IMPORTED_MODULE_1__.equalFlat(a, b))))\n\n/* c8 ignore start */\n\n/**\n * @param {any} a\n * @param {any} b\n * @return {boolean}\n */\nconst equalityDeep = (a, b) => {\n  if (a == null || b == null) {\n    return equalityStrict(a, b)\n  }\n  if (a.constructor !== b.constructor) {\n    return false\n  }\n  if (a === b) {\n    return true\n  }\n  switch (a.constructor) {\n    case ArrayBuffer:\n      a = new Uint8Array(a)\n      b = new Uint8Array(b)\n    // eslint-disable-next-line no-fallthrough\n    case Uint8Array: {\n      if (a.byteLength !== b.byteLength) {\n        return false\n      }\n      for (let i = 0; i < a.length; i++) {\n        if (a[i] !== b[i]) {\n          return false\n        }\n      }\n      break\n    }\n    case Set: {\n      if (a.size !== b.size) {\n        return false\n      }\n      for (const value of a) {\n        if (!b.has(value)) {\n          return false\n        }\n      }\n      break\n    }\n    case Map: {\n      if (a.size !== b.size) {\n        return false\n      }\n      for (const key of a.keys()) {\n        if (!b.has(key) || !equalityDeep(a.get(key), b.get(key))) {\n          return false\n        }\n      }\n      break\n    }\n    case Object:\n      if (_object_js__WEBPACK_IMPORTED_MODULE_1__.length(a) !== _object_js__WEBPACK_IMPORTED_MODULE_1__.length(b)) {\n        return false\n      }\n      for (const key in a) {\n        if (!_object_js__WEBPACK_IMPORTED_MODULE_1__.hasProperty(a, key) || !equalityDeep(a[key], b[key])) {\n          return false\n        }\n      }\n      break\n    case Array:\n      if (a.length !== b.length) {\n        return false\n      }\n      for (let i = 0; i < a.length; i++) {\n        if (!equalityDeep(a[i], b[i])) {\n          return false\n        }\n      }\n      break\n    default:\n      return false\n  }\n  return true\n}\n\n/**\n * @template V\n * @template {V} OPTS\n *\n * @param {V} value\n * @param {Array<OPTS>} options\n */\n// @ts-ignore\nconst isOneOf = (value, options) => options.includes(value)\n/* c8 ignore stop */\n\nconst isArray = _array_js__WEBPACK_IMPORTED_MODULE_0__.isArray\n\n/**\n * @param {any} s\n * @return {s is String}\n */\nconst isString = (s) => s && s.constructor === String\n\n/**\n * @param {any} n\n * @return {n is Number}\n */\nconst isNumber = n => n != null && n.constructor === Number\n\n/**\n * @template {abstract new (...args: any) => any} TYPE\n * @param {any} n\n * @param {TYPE} T\n * @return {n is InstanceType<TYPE>}\n */\nconst is = (n, T) => n && n.constructor === T\n\n/**\n * @template {abstract new (...args: any) => any} TYPE\n * @param {TYPE} T\n */\nconst isTemplate = (T) =>\n  /**\n   * @param {any} n\n   * @return {n is InstanceType<TYPE>}\n   **/\n  n => n && n.constructor === T\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/lib0/function.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/lib0/iterator.js":
/*!*******************************************!*\
  !*** ../../node_modules/lib0/iterator.js ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createIterator: () => (/* binding */ createIterator),\n/* harmony export */   iteratorFilter: () => (/* binding */ iteratorFilter),\n/* harmony export */   iteratorMap: () => (/* binding */ iteratorMap),\n/* harmony export */   mapIterator: () => (/* binding */ mapIterator)\n/* harmony export */ });\n/**\n * Utility module to create and manipulate Iterators.\n *\n * @module iterator\n */\n\n/**\n * @template T,R\n * @param {Iterator<T>} iterator\n * @param {function(T):R} f\n * @return {IterableIterator<R>}\n */\nconst mapIterator = (iterator, f) => ({\n  [Symbol.iterator] () {\n    return this\n  },\n  // @ts-ignore\n  next () {\n    const r = iterator.next()\n    return { value: r.done ? undefined : f(r.value), done: r.done }\n  }\n})\n\n/**\n * @template T\n * @param {function():IteratorResult<T>} next\n * @return {IterableIterator<T>}\n */\nconst createIterator = next => ({\n  /**\n   * @return {IterableIterator<T>}\n   */\n  [Symbol.iterator] () {\n    return this\n  },\n  // @ts-ignore\n  next\n})\n\n/**\n * @template T\n * @param {Iterator<T>} iterator\n * @param {function(T):boolean} filter\n */\nconst iteratorFilter = (iterator, filter) => createIterator(() => {\n  let res\n  do {\n    res = iterator.next()\n  } while (!res.done && !filter(res.value))\n  return res\n})\n\n/**\n * @template T,M\n * @param {Iterator<T>} iterator\n * @param {function(T):M} fmap\n */\nconst iteratorMap = (iterator, fmap) => createIterator(() => {\n  const { done, value } = iterator.next()\n  return { done, value: done ? undefined : fmap(value) }\n})\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/lib0/iterator.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/lib0/json.js":
/*!***************************************!*\
  !*** ../../node_modules/lib0/json.js ***!
  \***************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   parse: () => (/* binding */ parse),\n/* harmony export */   stringify: () => (/* binding */ stringify)\n/* harmony export */ });\n/**\n * JSON utility functions.\n *\n * @module json\n */\n\n/**\n * Transform JavaScript object to JSON.\n *\n * @param {any} object\n * @return {string}\n */\nconst stringify = JSON.stringify\n\n/**\n * Parse JSON object.\n *\n * @param {string} json\n * @return {any}\n */\nconst parse = JSON.parse\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2xpYjAvanNvbi5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsV0FBVyxLQUFLO0FBQ2hCLFlBQVk7QUFDWjtBQUNPOztBQUVQO0FBQ0E7QUFDQTtBQUNBLFdBQVcsUUFBUTtBQUNuQixZQUFZO0FBQ1o7QUFDTyIsInNvdXJjZXMiOlsid2VicGFjazovL0BvcGVuY2FudmFzL3dlYi8uLi8uLi9ub2RlX21vZHVsZXMvbGliMC9qc29uLmpzP2E1ZmUiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBKU09OIHV0aWxpdHkgZnVuY3Rpb25zLlxuICpcbiAqIEBtb2R1bGUganNvblxuICovXG5cbi8qKlxuICogVHJhbnNmb3JtIEphdmFTY3JpcHQgb2JqZWN0IHRvIEpTT04uXG4gKlxuICogQHBhcmFtIHthbnl9IG9iamVjdFxuICogQHJldHVybiB7c3RyaW5nfVxuICovXG5leHBvcnQgY29uc3Qgc3RyaW5naWZ5ID0gSlNPTi5zdHJpbmdpZnlcblxuLyoqXG4gKiBQYXJzZSBKU09OIG9iamVjdC5cbiAqXG4gKiBAcGFyYW0ge3N0cmluZ30ganNvblxuICogQHJldHVybiB7YW55fVxuICovXG5leHBvcnQgY29uc3QgcGFyc2UgPSBKU09OLnBhcnNlXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/lib0/json.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/lib0/logging.common.js":
/*!*************************************************!*\
  !*** ../../node_modules/lib0/logging.common.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BLUE: () => (/* binding */ BLUE),\n/* harmony export */   BOLD: () => (/* binding */ BOLD),\n/* harmony export */   GREEN: () => (/* binding */ GREEN),\n/* harmony export */   GREY: () => (/* binding */ GREY),\n/* harmony export */   ORANGE: () => (/* binding */ ORANGE),\n/* harmony export */   PURPLE: () => (/* binding */ PURPLE),\n/* harmony export */   RED: () => (/* binding */ RED),\n/* harmony export */   UNBOLD: () => (/* binding */ UNBOLD),\n/* harmony export */   UNCOLOR: () => (/* binding */ UNCOLOR),\n/* harmony export */   computeNoColorLoggingArgs: () => (/* binding */ computeNoColorLoggingArgs),\n/* harmony export */   createModuleLogger: () => (/* binding */ createModuleLogger)\n/* harmony export */ });\n/* harmony import */ var _symbol_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./symbol.js */ \"(ssr)/../../node_modules/lib0/symbol.js\");\n/* harmony import */ var _time_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./time.js */ \"(ssr)/../../node_modules/lib0/time.js\");\n/* harmony import */ var _environment_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./environment.js */ \"(ssr)/../../node_modules/lib0/environment.js\");\n/* harmony import */ var _function_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./function.js */ \"(ssr)/../../node_modules/lib0/function.js\");\n/* harmony import */ var _json_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./json.js */ \"(ssr)/../../node_modules/lib0/json.js\");\n\n\n\n\n\n\nconst BOLD = _symbol_js__WEBPACK_IMPORTED_MODULE_0__.create()\nconst UNBOLD = _symbol_js__WEBPACK_IMPORTED_MODULE_0__.create()\nconst BLUE = _symbol_js__WEBPACK_IMPORTED_MODULE_0__.create()\nconst GREY = _symbol_js__WEBPACK_IMPORTED_MODULE_0__.create()\nconst GREEN = _symbol_js__WEBPACK_IMPORTED_MODULE_0__.create()\nconst RED = _symbol_js__WEBPACK_IMPORTED_MODULE_0__.create()\nconst PURPLE = _symbol_js__WEBPACK_IMPORTED_MODULE_0__.create()\nconst ORANGE = _symbol_js__WEBPACK_IMPORTED_MODULE_0__.create()\nconst UNCOLOR = _symbol_js__WEBPACK_IMPORTED_MODULE_0__.create()\n\n/* c8 ignore start */\n/**\n * @param {Array<undefined|string|Symbol|Object|number|function():any>} args\n * @return {Array<string|object|number|undefined>}\n */\nconst computeNoColorLoggingArgs = args => {\n  if (args.length === 1 && args[0]?.constructor === Function) {\n    args = /** @type {Array<string|Symbol|Object|number>} */ (/** @type {[function]} */ (args)[0]())\n  }\n  const strBuilder = []\n  const logArgs = []\n  // try with formatting until we find something unsupported\n  let i = 0\n  for (; i < args.length; i++) {\n    const arg = args[i]\n    if (arg === undefined) {\n      break\n    } else if (arg.constructor === String || arg.constructor === Number) {\n      strBuilder.push(arg)\n    } else if (arg.constructor === Object) {\n      break\n    }\n  }\n  if (i > 0) {\n    // create logArgs with what we have so far\n    logArgs.push(strBuilder.join(''))\n  }\n  // append the rest\n  for (; i < args.length; i++) {\n    const arg = args[i]\n    if (!(arg instanceof Symbol)) {\n      logArgs.push(arg)\n    }\n  }\n  return logArgs\n}\n/* c8 ignore stop */\n\nconst loggingColors = [GREEN, PURPLE, ORANGE, BLUE]\nlet nextColor = 0\nlet lastLoggingTime = _time_js__WEBPACK_IMPORTED_MODULE_1__.getUnixTime()\n\n/* c8 ignore start */\n/**\n * @param {function(...any):void} _print\n * @param {string} moduleName\n * @return {function(...any):void}\n */\nconst createModuleLogger = (_print, moduleName) => {\n  const color = loggingColors[nextColor]\n  const debugRegexVar = _environment_js__WEBPACK_IMPORTED_MODULE_2__.getVariable('log')\n  const doLogging = debugRegexVar !== null &&\n    (debugRegexVar === '*' || debugRegexVar === 'true' ||\n      new RegExp(debugRegexVar, 'gi').test(moduleName))\n  nextColor = (nextColor + 1) % loggingColors.length\n  moduleName += ': '\n  return !doLogging\n    ? _function_js__WEBPACK_IMPORTED_MODULE_3__.nop\n    : (...args) => {\n        if (args.length === 1 && args[0]?.constructor === Function) {\n          args = args[0]()\n        }\n        const timeNow = _time_js__WEBPACK_IMPORTED_MODULE_1__.getUnixTime()\n        const timeDiff = timeNow - lastLoggingTime\n        lastLoggingTime = timeNow\n        _print(\n          color,\n          moduleName,\n          UNCOLOR,\n          ...args.map((arg) => {\n            if (arg != null && arg.constructor === Uint8Array) {\n              arg = Array.from(arg)\n            }\n            const t = typeof arg\n            switch (t) {\n              case 'string':\n              case 'symbol':\n                return arg\n              default: {\n                return _json_js__WEBPACK_IMPORTED_MODULE_4__.stringify(arg)\n              }\n            }\n          }),\n          color,\n          ' +' + timeDiff + 'ms'\n        )\n      }\n}\n/* c8 ignore stop */\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2xpYjAvbG9nZ2luZy5jb21tb24uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFxQztBQUNKO0FBQ007QUFDRjtBQUNKOztBQUUxQixhQUFhLDhDQUFhO0FBQzFCLGVBQWUsOENBQWE7QUFDNUIsYUFBYSw4Q0FBYTtBQUMxQixhQUFhLDhDQUFhO0FBQzFCLGNBQWMsOENBQWE7QUFDM0IsWUFBWSw4Q0FBYTtBQUN6QixlQUFlLDhDQUFhO0FBQzVCLGVBQWUsOENBQWE7QUFDNUIsZ0JBQWdCLDhDQUFhOztBQUVwQztBQUNBO0FBQ0EsV0FBVyw2REFBNkQ7QUFDeEUsWUFBWTtBQUNaO0FBQ087QUFDUDtBQUNBLHNCQUFzQixvQ0FBb0MsZUFBZSxZQUFZO0FBQ3JGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTLGlCQUFpQjtBQUMxQjtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVMsaUJBQWlCO0FBQzFCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLHNCQUFzQixpREFBZ0I7O0FBRXRDO0FBQ0E7QUFDQSxXQUFXLHVCQUF1QjtBQUNsQyxXQUFXLFFBQVE7QUFDbkIsWUFBWTtBQUNaO0FBQ087QUFDUDtBQUNBLHdCQUF3Qix3REFBZTtBQUN2QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNLDZDQUFRO0FBQ2Q7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3QkFBd0IsaURBQWdCO0FBQ3hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsdUJBQXVCLCtDQUFjO0FBQ3JDO0FBQ0E7QUFDQSxXQUFXO0FBQ1g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQG9wZW5jYW52YXMvd2ViLy4uLy4uL25vZGVfbW9kdWxlcy9saWIwL2xvZ2dpbmcuY29tbW9uLmpzP2U5ZDUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICogYXMgc3ltYm9sIGZyb20gJy4vc3ltYm9sLmpzJ1xuaW1wb3J0ICogYXMgdGltZSBmcm9tICcuL3RpbWUuanMnXG5pbXBvcnQgKiBhcyBlbnYgZnJvbSAnLi9lbnZpcm9ubWVudC5qcydcbmltcG9ydCAqIGFzIGZ1bmMgZnJvbSAnLi9mdW5jdGlvbi5qcydcbmltcG9ydCAqIGFzIGpzb24gZnJvbSAnLi9qc29uLmpzJ1xuXG5leHBvcnQgY29uc3QgQk9MRCA9IHN5bWJvbC5jcmVhdGUoKVxuZXhwb3J0IGNvbnN0IFVOQk9MRCA9IHN5bWJvbC5jcmVhdGUoKVxuZXhwb3J0IGNvbnN0IEJMVUUgPSBzeW1ib2wuY3JlYXRlKClcbmV4cG9ydCBjb25zdCBHUkVZID0gc3ltYm9sLmNyZWF0ZSgpXG5leHBvcnQgY29uc3QgR1JFRU4gPSBzeW1ib2wuY3JlYXRlKClcbmV4cG9ydCBjb25zdCBSRUQgPSBzeW1ib2wuY3JlYXRlKClcbmV4cG9ydCBjb25zdCBQVVJQTEUgPSBzeW1ib2wuY3JlYXRlKClcbmV4cG9ydCBjb25zdCBPUkFOR0UgPSBzeW1ib2wuY3JlYXRlKClcbmV4cG9ydCBjb25zdCBVTkNPTE9SID0gc3ltYm9sLmNyZWF0ZSgpXG5cbi8qIGM4IGlnbm9yZSBzdGFydCAqL1xuLyoqXG4gKiBAcGFyYW0ge0FycmF5PHVuZGVmaW5lZHxzdHJpbmd8U3ltYm9sfE9iamVjdHxudW1iZXJ8ZnVuY3Rpb24oKTphbnk+fSBhcmdzXG4gKiBAcmV0dXJuIHtBcnJheTxzdHJpbmd8b2JqZWN0fG51bWJlcnx1bmRlZmluZWQ+fVxuICovXG5leHBvcnQgY29uc3QgY29tcHV0ZU5vQ29sb3JMb2dnaW5nQXJncyA9IGFyZ3MgPT4ge1xuICBpZiAoYXJncy5sZW5ndGggPT09IDEgJiYgYXJnc1swXT8uY29uc3RydWN0b3IgPT09IEZ1bmN0aW9uKSB7XG4gICAgYXJncyA9IC8qKiBAdHlwZSB7QXJyYXk8c3RyaW5nfFN5bWJvbHxPYmplY3R8bnVtYmVyPn0gKi8gKC8qKiBAdHlwZSB7W2Z1bmN0aW9uXX0gKi8gKGFyZ3MpWzBdKCkpXG4gIH1cbiAgY29uc3Qgc3RyQnVpbGRlciA9IFtdXG4gIGNvbnN0IGxvZ0FyZ3MgPSBbXVxuICAvLyB0cnkgd2l0aCBmb3JtYXR0aW5nIHVudGlsIHdlIGZpbmQgc29tZXRoaW5nIHVuc3VwcG9ydGVkXG4gIGxldCBpID0gMFxuICBmb3IgKDsgaSA8IGFyZ3MubGVuZ3RoOyBpKyspIHtcbiAgICBjb25zdCBhcmcgPSBhcmdzW2ldXG4gICAgaWYgKGFyZyA9PT0gdW5kZWZpbmVkKSB7XG4gICAgICBicmVha1xuICAgIH0gZWxzZSBpZiAoYXJnLmNvbnN0cnVjdG9yID09PSBTdHJpbmcgfHwgYXJnLmNvbnN0cnVjdG9yID09PSBOdW1iZXIpIHtcbiAgICAgIHN0ckJ1aWxkZXIucHVzaChhcmcpXG4gICAgfSBlbHNlIGlmIChhcmcuY29uc3RydWN0b3IgPT09IE9iamVjdCkge1xuICAgICAgYnJlYWtcbiAgICB9XG4gIH1cbiAgaWYgKGkgPiAwKSB7XG4gICAgLy8gY3JlYXRlIGxvZ0FyZ3Mgd2l0aCB3aGF0IHdlIGhhdmUgc28gZmFyXG4gICAgbG9nQXJncy5wdXNoKHN0ckJ1aWxkZXIuam9pbignJykpXG4gIH1cbiAgLy8gYXBwZW5kIHRoZSByZXN0XG4gIGZvciAoOyBpIDwgYXJncy5sZW5ndGg7IGkrKykge1xuICAgIGNvbnN0IGFyZyA9IGFyZ3NbaV1cbiAgICBpZiAoIShhcmcgaW5zdGFuY2VvZiBTeW1ib2wpKSB7XG4gICAgICBsb2dBcmdzLnB1c2goYXJnKVxuICAgIH1cbiAgfVxuICByZXR1cm4gbG9nQXJnc1xufVxuLyogYzggaWdub3JlIHN0b3AgKi9cblxuY29uc3QgbG9nZ2luZ0NvbG9ycyA9IFtHUkVFTiwgUFVSUExFLCBPUkFOR0UsIEJMVUVdXG5sZXQgbmV4dENvbG9yID0gMFxubGV0IGxhc3RMb2dnaW5nVGltZSA9IHRpbWUuZ2V0VW5peFRpbWUoKVxuXG4vKiBjOCBpZ25vcmUgc3RhcnQgKi9cbi8qKlxuICogQHBhcmFtIHtmdW5jdGlvbiguLi5hbnkpOnZvaWR9IF9wcmludFxuICogQHBhcmFtIHtzdHJpbmd9IG1vZHVsZU5hbWVcbiAqIEByZXR1cm4ge2Z1bmN0aW9uKC4uLmFueSk6dm9pZH1cbiAqL1xuZXhwb3J0IGNvbnN0IGNyZWF0ZU1vZHVsZUxvZ2dlciA9IChfcHJpbnQsIG1vZHVsZU5hbWUpID0+IHtcbiAgY29uc3QgY29sb3IgPSBsb2dnaW5nQ29sb3JzW25leHRDb2xvcl1cbiAgY29uc3QgZGVidWdSZWdleFZhciA9IGVudi5nZXRWYXJpYWJsZSgnbG9nJylcbiAgY29uc3QgZG9Mb2dnaW5nID0gZGVidWdSZWdleFZhciAhPT0gbnVsbCAmJlxuICAgIChkZWJ1Z1JlZ2V4VmFyID09PSAnKicgfHwgZGVidWdSZWdleFZhciA9PT0gJ3RydWUnIHx8XG4gICAgICBuZXcgUmVnRXhwKGRlYnVnUmVnZXhWYXIsICdnaScpLnRlc3QobW9kdWxlTmFtZSkpXG4gIG5leHRDb2xvciA9IChuZXh0Q29sb3IgKyAxKSAlIGxvZ2dpbmdDb2xvcnMubGVuZ3RoXG4gIG1vZHVsZU5hbWUgKz0gJzogJ1xuICByZXR1cm4gIWRvTG9nZ2luZ1xuICAgID8gZnVuYy5ub3BcbiAgICA6ICguLi5hcmdzKSA9PiB7XG4gICAgICAgIGlmIChhcmdzLmxlbmd0aCA9PT0gMSAmJiBhcmdzWzBdPy5jb25zdHJ1Y3RvciA9PT0gRnVuY3Rpb24pIHtcbiAgICAgICAgICBhcmdzID0gYXJnc1swXSgpXG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgdGltZU5vdyA9IHRpbWUuZ2V0VW5peFRpbWUoKVxuICAgICAgICBjb25zdCB0aW1lRGlmZiA9IHRpbWVOb3cgLSBsYXN0TG9nZ2luZ1RpbWVcbiAgICAgICAgbGFzdExvZ2dpbmdUaW1lID0gdGltZU5vd1xuICAgICAgICBfcHJpbnQoXG4gICAgICAgICAgY29sb3IsXG4gICAgICAgICAgbW9kdWxlTmFtZSxcbiAgICAgICAgICBVTkNPTE9SLFxuICAgICAgICAgIC4uLmFyZ3MubWFwKChhcmcpID0+IHtcbiAgICAgICAgICAgIGlmIChhcmcgIT0gbnVsbCAmJiBhcmcuY29uc3RydWN0b3IgPT09IFVpbnQ4QXJyYXkpIHtcbiAgICAgICAgICAgICAgYXJnID0gQXJyYXkuZnJvbShhcmcpXG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBjb25zdCB0ID0gdHlwZW9mIGFyZ1xuICAgICAgICAgICAgc3dpdGNoICh0KSB7XG4gICAgICAgICAgICAgIGNhc2UgJ3N0cmluZyc6XG4gICAgICAgICAgICAgIGNhc2UgJ3N5bWJvbCc6XG4gICAgICAgICAgICAgICAgcmV0dXJuIGFyZ1xuICAgICAgICAgICAgICBkZWZhdWx0OiB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIGpzb24uc3RyaW5naWZ5KGFyZylcbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICAgIH0pLFxuICAgICAgICAgIGNvbG9yLFxuICAgICAgICAgICcgKycgKyB0aW1lRGlmZiArICdtcydcbiAgICAgICAgKVxuICAgICAgfVxufVxuLyogYzggaWdub3JlIHN0b3AgKi9cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/lib0/logging.common.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/lib0/logging.node.js":
/*!***********************************************!*\
  !*** ../../node_modules/lib0/logging.node.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BLUE: () => (/* reexport safe */ _logging_common_js__WEBPACK_IMPORTED_MODULE_0__.BLUE),\n/* harmony export */   BOLD: () => (/* reexport safe */ _logging_common_js__WEBPACK_IMPORTED_MODULE_0__.BOLD),\n/* harmony export */   GREEN: () => (/* reexport safe */ _logging_common_js__WEBPACK_IMPORTED_MODULE_0__.GREEN),\n/* harmony export */   GREY: () => (/* reexport safe */ _logging_common_js__WEBPACK_IMPORTED_MODULE_0__.GREY),\n/* harmony export */   ORANGE: () => (/* reexport safe */ _logging_common_js__WEBPACK_IMPORTED_MODULE_0__.ORANGE),\n/* harmony export */   PURPLE: () => (/* reexport safe */ _logging_common_js__WEBPACK_IMPORTED_MODULE_0__.PURPLE),\n/* harmony export */   RED: () => (/* reexport safe */ _logging_common_js__WEBPACK_IMPORTED_MODULE_0__.RED),\n/* harmony export */   UNBOLD: () => (/* reexport safe */ _logging_common_js__WEBPACK_IMPORTED_MODULE_0__.UNBOLD),\n/* harmony export */   UNCOLOR: () => (/* reexport safe */ _logging_common_js__WEBPACK_IMPORTED_MODULE_0__.UNCOLOR),\n/* harmony export */   createModuleLogger: () => (/* binding */ createModuleLogger),\n/* harmony export */   createVConsole: () => (/* binding */ createVConsole),\n/* harmony export */   group: () => (/* binding */ group),\n/* harmony export */   groupCollapsed: () => (/* binding */ groupCollapsed),\n/* harmony export */   groupEnd: () => (/* binding */ groupEnd),\n/* harmony export */   print: () => (/* binding */ print),\n/* harmony export */   printCanvas: () => (/* binding */ printCanvas),\n/* harmony export */   printDom: () => (/* binding */ printDom),\n/* harmony export */   printError: () => (/* binding */ printError),\n/* harmony export */   printImg: () => (/* binding */ printImg),\n/* harmony export */   printImgBase64: () => (/* binding */ printImgBase64),\n/* harmony export */   warn: () => (/* binding */ warn)\n/* harmony export */ });\n/* harmony import */ var _environment_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./environment.js */ \"(ssr)/../../node_modules/lib0/environment.js\");\n/* harmony import */ var _logging_common_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./logging.common.js */ \"(ssr)/../../node_modules/lib0/logging.common.js\");\n/**\n * Isomorphic logging module with support for colors!\n *\n * @module logging\n */\n\n\n\n\n\n\nconst _nodeStyleMap = {\n  [_logging_common_js__WEBPACK_IMPORTED_MODULE_0__.BOLD]: '\\u001b[1m',\n  [_logging_common_js__WEBPACK_IMPORTED_MODULE_0__.UNBOLD]: '\\u001b[2m',\n  [_logging_common_js__WEBPACK_IMPORTED_MODULE_0__.BLUE]: '\\x1b[34m',\n  [_logging_common_js__WEBPACK_IMPORTED_MODULE_0__.GREEN]: '\\x1b[32m',\n  [_logging_common_js__WEBPACK_IMPORTED_MODULE_0__.GREY]: '\\u001b[37m',\n  [_logging_common_js__WEBPACK_IMPORTED_MODULE_0__.RED]: '\\x1b[31m',\n  [_logging_common_js__WEBPACK_IMPORTED_MODULE_0__.PURPLE]: '\\x1b[35m',\n  [_logging_common_js__WEBPACK_IMPORTED_MODULE_0__.ORANGE]: '\\x1b[38;5;208m',\n  [_logging_common_js__WEBPACK_IMPORTED_MODULE_0__.UNCOLOR]: '\\x1b[0m'\n}\n\n/* c8 ignore start */\n/**\n * @param {Array<string|undefined|Symbol|Object|number|function():Array<any>>} args\n * @return {Array<string|object|number|undefined>}\n */\nconst computeNodeLoggingArgs = (args) => {\n  if (args.length === 1 && args[0]?.constructor === Function) {\n    args = /** @type {Array<string|Symbol|Object|number>} */ (/** @type {[function]} */ (args)[0]())\n  }\n  const strBuilder = []\n  const logArgs = []\n  // try with formatting until we find something unsupported\n  let i = 0\n  for (; i < args.length; i++) {\n    const arg = args[i]\n    // @ts-ignore\n    const style = _nodeStyleMap[arg]\n    if (style !== undefined) {\n      strBuilder.push(style)\n    } else {\n      if (arg === undefined) {\n        break\n      } else if (arg.constructor === String || arg.constructor === Number) {\n        strBuilder.push(arg)\n      } else {\n        break\n      }\n    }\n  }\n  if (i > 0) {\n    // create logArgs with what we have so far\n    strBuilder.push('\\x1b[0m')\n    logArgs.push(strBuilder.join(''))\n  }\n  // append the rest\n  for (; i < args.length; i++) {\n    const arg = args[i]\n    if (!(arg instanceof Symbol)) {\n      logArgs.push(arg)\n    }\n  }\n  return logArgs\n}\n/* c8 ignore stop */\n\n/* c8 ignore start */\nconst computeLoggingArgs = _environment_js__WEBPACK_IMPORTED_MODULE_1__.supportsColor\n  ? computeNodeLoggingArgs\n  : _logging_common_js__WEBPACK_IMPORTED_MODULE_0__.computeNoColorLoggingArgs\n/* c8 ignore stop */\n\n/**\n * @param {Array<string|Symbol|Object|number|undefined>} args\n */\nconst print = (...args) => {\n  console.log(...computeLoggingArgs(args))\n}\n\n/* c8 ignore start */\n/**\n * @param {Array<string|Symbol|Object|number>} args\n */\nconst warn = (...args) => {\n  console.warn(...computeLoggingArgs(args))\n}\n/* c8 ignore stop */\n\n/**\n * @param {Error} err\n */\n/* c8 ignore start */\nconst printError = (err) => {\n  console.error(err)\n}\n/* c8 ignore stop */\n\n/**\n * @param {string} _url image location\n * @param {number} _height height of the image in pixel\n */\n/* c8 ignore start */\nconst printImg = (_url, _height) => {\n  // console.log('%c                ', `font-size: ${height}x; background: url(${url}) no-repeat;`)\n}\n/* c8 ignore stop */\n\n/**\n * @param {string} base64\n * @param {number} height\n */\n/* c8 ignore next 2 */\nconst printImgBase64 = (base64, height) =>\n  printImg(`data:image/gif;base64,${base64}`, height)\n\n/**\n * @param {Array<string|Symbol|Object|number>} args\n */\n/* c8 ignore next 3 */\nconst group = (...args) => {\n  console.group(...computeLoggingArgs(args))\n}\n\n/**\n * @param {Array<string|Symbol|Object|number>} args\n */\n/* c8 ignore next 3 */\nconst groupCollapsed = (...args) => {\n  console.groupCollapsed(...computeLoggingArgs(args))\n}\n\n/* c8 ignore next 3 */\nconst groupEnd = () => {\n  console.groupEnd()\n}\n\n/**\n * @param {function():Node} _createNode\n */\n/* c8 ignore next 2 */\nconst printDom = (_createNode) => {}\n\n/**\n * @param {HTMLCanvasElement} canvas\n * @param {number} height\n */\n/* c8 ignore next 2 */\nconst printCanvas = (canvas, height) =>\n  printImg(canvas.toDataURL(), height)\n\n/**\n * @param {Element} _dom\n */\n/* c8 ignore next */\nconst createVConsole = (_dom) => {}\n\n/**\n * @param {string} moduleName\n * @return {function(...any):void}\n */\n/* c8 ignore next */\nconst createModuleLogger = (moduleName) => _logging_common_js__WEBPACK_IMPORTED_MODULE_0__.createModuleLogger(print, moduleName)\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/lib0/logging.node.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/lib0/map.js":
/*!**************************************!*\
  !*** ../../node_modules/lib0/map.js ***!
  \**************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   all: () => (/* binding */ all),\n/* harmony export */   any: () => (/* binding */ any),\n/* harmony export */   copy: () => (/* binding */ copy),\n/* harmony export */   create: () => (/* binding */ create),\n/* harmony export */   map: () => (/* binding */ map),\n/* harmony export */   setIfUndefined: () => (/* binding */ setIfUndefined)\n/* harmony export */ });\n/**\n * Utility module to work with key-value stores.\n *\n * @module map\n */\n\n/**\n * Creates a new Map instance.\n *\n * @function\n * @return {Map<any, any>}\n *\n * @function\n */\nconst create = () => new Map()\n\n/**\n * Copy a Map object into a fresh Map object.\n *\n * @function\n * @template K,V\n * @param {Map<K,V>} m\n * @return {Map<K,V>}\n */\nconst copy = m => {\n  const r = create()\n  m.forEach((v, k) => { r.set(k, v) })\n  return r\n}\n\n/**\n * Get map property. Create T if property is undefined and set T on map.\n *\n * ```js\n * const listeners = map.setIfUndefined(events, 'eventName', set.create)\n * listeners.add(listener)\n * ```\n *\n * @function\n * @template {Map<any, any>} MAP\n * @template {MAP extends Map<any,infer V> ? function():V : unknown} CF\n * @param {MAP} map\n * @param {MAP extends Map<infer K,any> ? K : unknown} key\n * @param {CF} createT\n * @return {ReturnType<CF>}\n */\nconst setIfUndefined = (map, key, createT) => {\n  let set = map.get(key)\n  if (set === undefined) {\n    map.set(key, set = createT())\n  }\n  return set\n}\n\n/**\n * Creates an Array and populates it with the content of all key-value pairs using the `f(value, key)` function.\n *\n * @function\n * @template K\n * @template V\n * @template R\n * @param {Map<K,V>} m\n * @param {function(V,K):R} f\n * @return {Array<R>}\n */\nconst map = (m, f) => {\n  const res = []\n  for (const [key, value] of m) {\n    res.push(f(value, key))\n  }\n  return res\n}\n\n/**\n * Tests whether any key-value pairs pass the test implemented by `f(value, key)`.\n *\n * @todo should rename to some - similarly to Array.some\n *\n * @function\n * @template K\n * @template V\n * @param {Map<K,V>} m\n * @param {function(V,K):boolean} f\n * @return {boolean}\n */\nconst any = (m, f) => {\n  for (const [key, value] of m) {\n    if (f(value, key)) {\n      return true\n    }\n  }\n  return false\n}\n\n/**\n * Tests whether all key-value pairs pass the test implemented by `f(value, key)`.\n *\n * @function\n * @template K\n * @template V\n * @param {Map<K,V>} m\n * @param {function(V,K):boolean} f\n * @return {boolean}\n */\nconst all = (m, f) => {\n  for (const [key, value] of m) {\n    if (!f(value, key)) {\n      return false\n    }\n  }\n  return true\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/lib0/map.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/lib0/math.js":
/*!***************************************!*\
  !*** ../../node_modules/lib0/math.js ***!
  \***************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   abs: () => (/* binding */ abs),\n/* harmony export */   add: () => (/* binding */ add),\n/* harmony export */   ceil: () => (/* binding */ ceil),\n/* harmony export */   exp10: () => (/* binding */ exp10),\n/* harmony export */   floor: () => (/* binding */ floor),\n/* harmony export */   imul: () => (/* binding */ imul),\n/* harmony export */   isNaN: () => (/* binding */ isNaN),\n/* harmony export */   isNegativeZero: () => (/* binding */ isNegativeZero),\n/* harmony export */   log: () => (/* binding */ log),\n/* harmony export */   log10: () => (/* binding */ log10),\n/* harmony export */   log2: () => (/* binding */ log2),\n/* harmony export */   max: () => (/* binding */ max),\n/* harmony export */   min: () => (/* binding */ min),\n/* harmony export */   pow: () => (/* binding */ pow),\n/* harmony export */   round: () => (/* binding */ round),\n/* harmony export */   sign: () => (/* binding */ sign),\n/* harmony export */   sqrt: () => (/* binding */ sqrt)\n/* harmony export */ });\n/**\n * Common Math expressions.\n *\n * @module math\n */\n\nconst floor = Math.floor\nconst ceil = Math.ceil\nconst abs = Math.abs\nconst imul = Math.imul\nconst round = Math.round\nconst log10 = Math.log10\nconst log2 = Math.log2\nconst log = Math.log\nconst sqrt = Math.sqrt\n\n/**\n * @function\n * @param {number} a\n * @param {number} b\n * @return {number} The sum of a and b\n */\nconst add = (a, b) => a + b\n\n/**\n * @function\n * @param {number} a\n * @param {number} b\n * @return {number} The smaller element of a and b\n */\nconst min = (a, b) => a < b ? a : b\n\n/**\n * @function\n * @param {number} a\n * @param {number} b\n * @return {number} The bigger element of a and b\n */\nconst max = (a, b) => a > b ? a : b\n\nconst isNaN = Number.isNaN\n\nconst pow = Math.pow\n/**\n * Base 10 exponential function. Returns the value of 10 raised to the power of pow.\n *\n * @param {number} exp\n * @return {number}\n */\nconst exp10 = exp => Math.pow(10, exp)\n\nconst sign = Math.sign\n\n/**\n * @param {number} n\n * @return {boolean} Wether n is negative. This function also differentiates between -0 and +0\n */\nconst isNegativeZero = n => n !== 0 ? n < 0 : 1 / n < 0\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2xpYjAvbWF0aC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRU87QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVQO0FBQ0E7QUFDQSxXQUFXLFFBQVE7QUFDbkIsV0FBVyxRQUFRO0FBQ25CLFlBQVksUUFBUTtBQUNwQjtBQUNPOztBQUVQO0FBQ0E7QUFDQSxXQUFXLFFBQVE7QUFDbkIsV0FBVyxRQUFRO0FBQ25CLFlBQVksUUFBUTtBQUNwQjtBQUNPOztBQUVQO0FBQ0E7QUFDQSxXQUFXLFFBQVE7QUFDbkIsV0FBVyxRQUFRO0FBQ25CLFlBQVksUUFBUTtBQUNwQjtBQUNPOztBQUVBOztBQUVBO0FBQ1A7QUFDQTtBQUNBO0FBQ0EsV0FBVyxRQUFRO0FBQ25CLFlBQVk7QUFDWjtBQUNPOztBQUVBOztBQUVQO0FBQ0EsV0FBVyxRQUFRO0FBQ25CLFlBQVksU0FBUztBQUNyQjtBQUNPIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQG9wZW5jYW52YXMvd2ViLy4uLy4uL25vZGVfbW9kdWxlcy9saWIwL21hdGguanM/ODU5ZiJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIENvbW1vbiBNYXRoIGV4cHJlc3Npb25zLlxuICpcbiAqIEBtb2R1bGUgbWF0aFxuICovXG5cbmV4cG9ydCBjb25zdCBmbG9vciA9IE1hdGguZmxvb3JcbmV4cG9ydCBjb25zdCBjZWlsID0gTWF0aC5jZWlsXG5leHBvcnQgY29uc3QgYWJzID0gTWF0aC5hYnNcbmV4cG9ydCBjb25zdCBpbXVsID0gTWF0aC5pbXVsXG5leHBvcnQgY29uc3Qgcm91bmQgPSBNYXRoLnJvdW5kXG5leHBvcnQgY29uc3QgbG9nMTAgPSBNYXRoLmxvZzEwXG5leHBvcnQgY29uc3QgbG9nMiA9IE1hdGgubG9nMlxuZXhwb3J0IGNvbnN0IGxvZyA9IE1hdGgubG9nXG5leHBvcnQgY29uc3Qgc3FydCA9IE1hdGguc3FydFxuXG4vKipcbiAqIEBmdW5jdGlvblxuICogQHBhcmFtIHtudW1iZXJ9IGFcbiAqIEBwYXJhbSB7bnVtYmVyfSBiXG4gKiBAcmV0dXJuIHtudW1iZXJ9IFRoZSBzdW0gb2YgYSBhbmQgYlxuICovXG5leHBvcnQgY29uc3QgYWRkID0gKGEsIGIpID0+IGEgKyBiXG5cbi8qKlxuICogQGZ1bmN0aW9uXG4gKiBAcGFyYW0ge251bWJlcn0gYVxuICogQHBhcmFtIHtudW1iZXJ9IGJcbiAqIEByZXR1cm4ge251bWJlcn0gVGhlIHNtYWxsZXIgZWxlbWVudCBvZiBhIGFuZCBiXG4gKi9cbmV4cG9ydCBjb25zdCBtaW4gPSAoYSwgYikgPT4gYSA8IGIgPyBhIDogYlxuXG4vKipcbiAqIEBmdW5jdGlvblxuICogQHBhcmFtIHtudW1iZXJ9IGFcbiAqIEBwYXJhbSB7bnVtYmVyfSBiXG4gKiBAcmV0dXJuIHtudW1iZXJ9IFRoZSBiaWdnZXIgZWxlbWVudCBvZiBhIGFuZCBiXG4gKi9cbmV4cG9ydCBjb25zdCBtYXggPSAoYSwgYikgPT4gYSA+IGIgPyBhIDogYlxuXG5leHBvcnQgY29uc3QgaXNOYU4gPSBOdW1iZXIuaXNOYU5cblxuZXhwb3J0IGNvbnN0IHBvdyA9IE1hdGgucG93XG4vKipcbiAqIEJhc2UgMTAgZXhwb25lbnRpYWwgZnVuY3Rpb24uIFJldHVybnMgdGhlIHZhbHVlIG9mIDEwIHJhaXNlZCB0byB0aGUgcG93ZXIgb2YgcG93LlxuICpcbiAqIEBwYXJhbSB7bnVtYmVyfSBleHBcbiAqIEByZXR1cm4ge251bWJlcn1cbiAqL1xuZXhwb3J0IGNvbnN0IGV4cDEwID0gZXhwID0+IE1hdGgucG93KDEwLCBleHApXG5cbmV4cG9ydCBjb25zdCBzaWduID0gTWF0aC5zaWduXG5cbi8qKlxuICogQHBhcmFtIHtudW1iZXJ9IG5cbiAqIEByZXR1cm4ge2Jvb2xlYW59IFdldGhlciBuIGlzIG5lZ2F0aXZlLiBUaGlzIGZ1bmN0aW9uIGFsc28gZGlmZmVyZW50aWF0ZXMgYmV0d2VlbiAtMCBhbmQgKzBcbiAqL1xuZXhwb3J0IGNvbnN0IGlzTmVnYXRpdmVaZXJvID0gbiA9PiBuICE9PSAwID8gbiA8IDAgOiAxIC8gbiA8IDBcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/lib0/math.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/lib0/metric.js":
/*!*****************************************!*\
  !*** ../../node_modules/lib0/metric.js ***!
  \*****************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   atto: () => (/* binding */ atto),\n/* harmony export */   centi: () => (/* binding */ centi),\n/* harmony export */   deca: () => (/* binding */ deca),\n/* harmony export */   deci: () => (/* binding */ deci),\n/* harmony export */   exa: () => (/* binding */ exa),\n/* harmony export */   femto: () => (/* binding */ femto),\n/* harmony export */   giga: () => (/* binding */ giga),\n/* harmony export */   hecto: () => (/* binding */ hecto),\n/* harmony export */   kilo: () => (/* binding */ kilo),\n/* harmony export */   mega: () => (/* binding */ mega),\n/* harmony export */   micro: () => (/* binding */ micro),\n/* harmony export */   milli: () => (/* binding */ milli),\n/* harmony export */   nano: () => (/* binding */ nano),\n/* harmony export */   peta: () => (/* binding */ peta),\n/* harmony export */   pico: () => (/* binding */ pico),\n/* harmony export */   prefix: () => (/* binding */ prefix),\n/* harmony export */   tera: () => (/* binding */ tera),\n/* harmony export */   yocto: () => (/* binding */ yocto),\n/* harmony export */   yotta: () => (/* binding */ yotta),\n/* harmony export */   zepto: () => (/* binding */ zepto),\n/* harmony export */   zetta: () => (/* binding */ zetta)\n/* harmony export */ });\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./math.js */ \"(ssr)/../../node_modules/lib0/math.js\");\n/**\n * Utility module to convert metric values.\n *\n * @module metric\n */\n\n\n\nconst yotta = 1e24\nconst zetta = 1e21\nconst exa = 1e18\nconst peta = 1e15\nconst tera = 1e12\nconst giga = 1e9\nconst mega = 1e6\nconst kilo = 1e3\nconst hecto = 1e2\nconst deca = 10\nconst deci = 0.1\nconst centi = 0.01\nconst milli = 1e-3\nconst micro = 1e-6\nconst nano = 1e-9\nconst pico = 1e-12\nconst femto = 1e-15\nconst atto = 1e-18\nconst zepto = 1e-21\nconst yocto = 1e-24\n\nconst prefixUp = ['', 'k', 'M', 'G', 'T', 'P', 'E', 'Z', 'Y']\nconst prefixDown = ['', 'm', 'μ', 'n', 'p', 'f', 'a', 'z', 'y']\n\n/**\n * Calculate the metric prefix for a number. Assumes E.g. `prefix(1000) = { n: 1, prefix: 'k' }`\n *\n * @param {number} n\n * @param {number} [baseMultiplier] Multiplier of the base (10^(3*baseMultiplier)). E.g. `convert(time, -3)` if time is already in milli seconds\n * @return {{n:number,prefix:string}}\n */\nconst prefix = (n, baseMultiplier = 0) => {\n  const nPow = n === 0 ? 0 : _math_js__WEBPACK_IMPORTED_MODULE_0__.log10(n)\n  let mult = 0\n  while (nPow < mult * 3 && baseMultiplier > -8) {\n    baseMultiplier--\n    mult--\n  }\n  while (nPow >= 3 + mult * 3 && baseMultiplier < 8) {\n    baseMultiplier++\n    mult++\n  }\n  const prefix = baseMultiplier < 0 ? prefixDown[-baseMultiplier] : prefixUp[baseMultiplier]\n  return {\n    n: _math_js__WEBPACK_IMPORTED_MODULE_0__.round((mult > 0 ? n / _math_js__WEBPACK_IMPORTED_MODULE_0__.exp10(mult * 3) : n * _math_js__WEBPACK_IMPORTED_MODULE_0__.exp10(mult * -3)) * 1e12) / 1e12,\n    prefix\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/lib0/metric.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/lib0/mutex.js":
/*!****************************************!*\
  !*** ../../node_modules/lib0/mutex.js ***!
  \****************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createMutex: () => (/* binding */ createMutex)\n/* harmony export */ });\n/**\n * Mutual exclude for JavaScript.\n *\n * @module mutex\n */\n\n/**\n * @callback mutex\n * @param {function():void} cb Only executed when this mutex is not in the current stack\n * @param {function():void} [elseCb] Executed when this mutex is in the current stack\n */\n\n/**\n * Creates a mutual exclude function with the following property:\n *\n * ```js\n * const mutex = createMutex()\n * mutex(() => {\n *   // This function is immediately executed\n *   mutex(() => {\n *     // This function is not executed, as the mutex is already active.\n *   })\n * })\n * ```\n *\n * @return {mutex} A mutual exclude function\n * @public\n */\nconst createMutex = () => {\n  let token = true\n  return (f, g) => {\n    if (token) {\n      token = false\n      try {\n        f()\n      } finally {\n        token = true\n      }\n    } else if (g !== undefined) {\n      g()\n    }\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2xpYjAvbXV0ZXguanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLFdBQVcsaUJBQWlCO0FBQzVCLFdBQVcsaUJBQWlCO0FBQzVCOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTixJQUFJO0FBQ0o7QUFDQTtBQUNBLFlBQVksT0FBTztBQUNuQjtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL0BvcGVuY2FudmFzL3dlYi8uLi8uLi9ub2RlX21vZHVsZXMvbGliMC9tdXRleC5qcz81OGMwIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogTXV0dWFsIGV4Y2x1ZGUgZm9yIEphdmFTY3JpcHQuXG4gKlxuICogQG1vZHVsZSBtdXRleFxuICovXG5cbi8qKlxuICogQGNhbGxiYWNrIG11dGV4XG4gKiBAcGFyYW0ge2Z1bmN0aW9uKCk6dm9pZH0gY2IgT25seSBleGVjdXRlZCB3aGVuIHRoaXMgbXV0ZXggaXMgbm90IGluIHRoZSBjdXJyZW50IHN0YWNrXG4gKiBAcGFyYW0ge2Z1bmN0aW9uKCk6dm9pZH0gW2Vsc2VDYl0gRXhlY3V0ZWQgd2hlbiB0aGlzIG11dGV4IGlzIGluIHRoZSBjdXJyZW50IHN0YWNrXG4gKi9cblxuLyoqXG4gKiBDcmVhdGVzIGEgbXV0dWFsIGV4Y2x1ZGUgZnVuY3Rpb24gd2l0aCB0aGUgZm9sbG93aW5nIHByb3BlcnR5OlxuICpcbiAqIGBgYGpzXG4gKiBjb25zdCBtdXRleCA9IGNyZWF0ZU11dGV4KClcbiAqIG11dGV4KCgpID0+IHtcbiAqICAgLy8gVGhpcyBmdW5jdGlvbiBpcyBpbW1lZGlhdGVseSBleGVjdXRlZFxuICogICBtdXRleCgoKSA9PiB7XG4gKiAgICAgLy8gVGhpcyBmdW5jdGlvbiBpcyBub3QgZXhlY3V0ZWQsIGFzIHRoZSBtdXRleCBpcyBhbHJlYWR5IGFjdGl2ZS5cbiAqICAgfSlcbiAqIH0pXG4gKiBgYGBcbiAqXG4gKiBAcmV0dXJuIHttdXRleH0gQSBtdXR1YWwgZXhjbHVkZSBmdW5jdGlvblxuICogQHB1YmxpY1xuICovXG5leHBvcnQgY29uc3QgY3JlYXRlTXV0ZXggPSAoKSA9PiB7XG4gIGxldCB0b2tlbiA9IHRydWVcbiAgcmV0dXJuIChmLCBnKSA9PiB7XG4gICAgaWYgKHRva2VuKSB7XG4gICAgICB0b2tlbiA9IGZhbHNlXG4gICAgICB0cnkge1xuICAgICAgICBmKClcbiAgICAgIH0gZmluYWxseSB7XG4gICAgICAgIHRva2VuID0gdHJ1ZVxuICAgICAgfVxuICAgIH0gZWxzZSBpZiAoZyAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICBnKClcbiAgICB9XG4gIH1cbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/lib0/mutex.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/lib0/number.js":
/*!*****************************************!*\
  !*** ../../node_modules/lib0/number.js ***!
  \*****************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HIGHEST_INT32: () => (/* binding */ HIGHEST_INT32),\n/* harmony export */   HIGHEST_UINT32: () => (/* binding */ HIGHEST_UINT32),\n/* harmony export */   LOWEST_INT32: () => (/* binding */ LOWEST_INT32),\n/* harmony export */   MAX_SAFE_INTEGER: () => (/* binding */ MAX_SAFE_INTEGER),\n/* harmony export */   MIN_SAFE_INTEGER: () => (/* binding */ MIN_SAFE_INTEGER),\n/* harmony export */   countBits: () => (/* binding */ countBits),\n/* harmony export */   isInteger: () => (/* binding */ isInteger),\n/* harmony export */   isNaN: () => (/* binding */ isNaN),\n/* harmony export */   parseInt: () => (/* binding */ parseInt)\n/* harmony export */ });\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./math.js */ \"(ssr)/../../node_modules/lib0/math.js\");\n/* harmony import */ var _binary_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./binary.js */ \"(ssr)/../../node_modules/lib0/binary.js\");\n/**\n * Utility helpers for working with numbers.\n *\n * @module number\n */\n\n\n\n\nconst MAX_SAFE_INTEGER = Number.MAX_SAFE_INTEGER\nconst MIN_SAFE_INTEGER = Number.MIN_SAFE_INTEGER\n\nconst LOWEST_INT32 = 1 << 31\nconst HIGHEST_INT32 = _binary_js__WEBPACK_IMPORTED_MODULE_0__.BITS31\nconst HIGHEST_UINT32 = _binary_js__WEBPACK_IMPORTED_MODULE_0__.BITS32\n\n/* c8 ignore next */\nconst isInteger = Number.isInteger || (num => typeof num === 'number' && isFinite(num) && _math_js__WEBPACK_IMPORTED_MODULE_1__.floor(num) === num)\nconst isNaN = Number.isNaN\nconst parseInt = Number.parseInt\n\n/**\n * Count the number of \"1\" bits in an unsigned 32bit number.\n *\n * Super fun bitcount algorithm by Brian Kernighan.\n *\n * @param {number} n\n */\nconst countBits = n => {\n  n &= _binary_js__WEBPACK_IMPORTED_MODULE_0__.BITS32\n  let count = 0\n  while (n) {\n    n &= (n - 1)\n    count++\n  }\n  return count\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2xpYjAvbnVtYmVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFaUM7QUFDSTs7QUFFOUI7QUFDQTs7QUFFQTtBQUNBLHNCQUFzQiw4Q0FBYTtBQUNuQyx1QkFBdUIsOENBQWE7O0FBRTNDO0FBQ08sMEZBQTBGLDJDQUFVO0FBQ3BHO0FBQ0E7O0FBRVA7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsUUFBUTtBQUNuQjtBQUNPO0FBQ1AsT0FBTyw4Q0FBYTtBQUNwQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL0BvcGVuY2FudmFzL3dlYi8uLi8uLi9ub2RlX21vZHVsZXMvbGliMC9udW1iZXIuanM/OTM4ZCJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIFV0aWxpdHkgaGVscGVycyBmb3Igd29ya2luZyB3aXRoIG51bWJlcnMuXG4gKlxuICogQG1vZHVsZSBudW1iZXJcbiAqL1xuXG5pbXBvcnQgKiBhcyBtYXRoIGZyb20gJy4vbWF0aC5qcydcbmltcG9ydCAqIGFzIGJpbmFyeSBmcm9tICcuL2JpbmFyeS5qcydcblxuZXhwb3J0IGNvbnN0IE1BWF9TQUZFX0lOVEVHRVIgPSBOdW1iZXIuTUFYX1NBRkVfSU5URUdFUlxuZXhwb3J0IGNvbnN0IE1JTl9TQUZFX0lOVEVHRVIgPSBOdW1iZXIuTUlOX1NBRkVfSU5URUdFUlxuXG5leHBvcnQgY29uc3QgTE9XRVNUX0lOVDMyID0gMSA8PCAzMVxuZXhwb3J0IGNvbnN0IEhJR0hFU1RfSU5UMzIgPSBiaW5hcnkuQklUUzMxXG5leHBvcnQgY29uc3QgSElHSEVTVF9VSU5UMzIgPSBiaW5hcnkuQklUUzMyXG5cbi8qIGM4IGlnbm9yZSBuZXh0ICovXG5leHBvcnQgY29uc3QgaXNJbnRlZ2VyID0gTnVtYmVyLmlzSW50ZWdlciB8fCAobnVtID0+IHR5cGVvZiBudW0gPT09ICdudW1iZXInICYmIGlzRmluaXRlKG51bSkgJiYgbWF0aC5mbG9vcihudW0pID09PSBudW0pXG5leHBvcnQgY29uc3QgaXNOYU4gPSBOdW1iZXIuaXNOYU5cbmV4cG9ydCBjb25zdCBwYXJzZUludCA9IE51bWJlci5wYXJzZUludFxuXG4vKipcbiAqIENvdW50IHRoZSBudW1iZXIgb2YgXCIxXCIgYml0cyBpbiBhbiB1bnNpZ25lZCAzMmJpdCBudW1iZXIuXG4gKlxuICogU3VwZXIgZnVuIGJpdGNvdW50IGFsZ29yaXRobSBieSBCcmlhbiBLZXJuaWdoYW4uXG4gKlxuICogQHBhcmFtIHtudW1iZXJ9IG5cbiAqL1xuZXhwb3J0IGNvbnN0IGNvdW50Qml0cyA9IG4gPT4ge1xuICBuICY9IGJpbmFyeS5CSVRTMzJcbiAgbGV0IGNvdW50ID0gMFxuICB3aGlsZSAobikge1xuICAgIG4gJj0gKG4gLSAxKVxuICAgIGNvdW50KytcbiAgfVxuICByZXR1cm4gY291bnRcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/lib0/number.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/lib0/object.js":
/*!*****************************************!*\
  !*** ../../node_modules/lib0/object.js ***!
  \*****************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   assign: () => (/* binding */ assign),\n/* harmony export */   create: () => (/* binding */ create),\n/* harmony export */   deepFreeze: () => (/* binding */ deepFreeze),\n/* harmony export */   equalFlat: () => (/* binding */ equalFlat),\n/* harmony export */   every: () => (/* binding */ every),\n/* harmony export */   forEach: () => (/* binding */ forEach),\n/* harmony export */   freeze: () => (/* binding */ freeze),\n/* harmony export */   hasProperty: () => (/* binding */ hasProperty),\n/* harmony export */   isEmpty: () => (/* binding */ isEmpty),\n/* harmony export */   keys: () => (/* binding */ keys),\n/* harmony export */   length: () => (/* binding */ length),\n/* harmony export */   map: () => (/* binding */ map),\n/* harmony export */   size: () => (/* binding */ size),\n/* harmony export */   some: () => (/* binding */ some)\n/* harmony export */ });\n/**\n * Utility functions for working with EcmaScript objects.\n *\n * @module object\n */\n\n/**\n * @return {Object<string,any>} obj\n */\nconst create = () => Object.create(null)\n\n/**\n * Object.assign\n */\nconst assign = Object.assign\n\n/**\n * @param {Object<string,any>} obj\n */\nconst keys = Object.keys\n\n/**\n * @template V\n * @param {{[k:string]:V}} obj\n * @param {function(V,string):any} f\n */\nconst forEach = (obj, f) => {\n  for (const key in obj) {\n    f(obj[key], key)\n  }\n}\n\n/**\n * @todo implement mapToArray & map\n *\n * @template R\n * @param {Object<string,any>} obj\n * @param {function(any,string):R} f\n * @return {Array<R>}\n */\nconst map = (obj, f) => {\n  const results = []\n  for (const key in obj) {\n    results.push(f(obj[key], key))\n  }\n  return results\n}\n\n/**\n * @deprecated use object.size instead\n * @param {Object<string,any>} obj\n * @return {number}\n */\nconst length = obj => keys(obj).length\n\n/**\n * @param {Object<string,any>} obj\n * @return {number}\n */\nconst size = obj => keys(obj).length\n\n/**\n * @param {Object<string,any>} obj\n * @param {function(any,string):boolean} f\n * @return {boolean}\n */\nconst some = (obj, f) => {\n  for (const key in obj) {\n    if (f(obj[key], key)) {\n      return true\n    }\n  }\n  return false\n}\n\n/**\n * @param {Object|undefined} obj\n */\nconst isEmpty = obj => {\n  // eslint-disable-next-line\n  for (const _k in obj) {\n    return false\n  }\n  return true\n}\n\n/**\n * @param {Object<string,any>} obj\n * @param {function(any,string):boolean} f\n * @return {boolean}\n */\nconst every = (obj, f) => {\n  for (const key in obj) {\n    if (!f(obj[key], key)) {\n      return false\n    }\n  }\n  return true\n}\n\n/**\n * Calls `Object.prototype.hasOwnProperty`.\n *\n * @param {any} obj\n * @param {string|symbol} key\n * @return {boolean}\n */\nconst hasProperty = (obj, key) => Object.prototype.hasOwnProperty.call(obj, key)\n\n/**\n * @param {Object<string,any>} a\n * @param {Object<string,any>} b\n * @return {boolean}\n */\nconst equalFlat = (a, b) => a === b || (size(a) === size(b) && every(a, (val, key) => (val !== undefined || hasProperty(b, key)) && b[key] === val))\n\n/**\n * Make an object immutable. This hurts performance and is usually not needed if you perform good\n * coding practices.\n */\nconst freeze = Object.freeze\n\n/**\n * Make an object and all its children immutable.\n * This *really* hurts performance and is usually not needed if you perform good coding practices.\n *\n * @template {any} T\n * @param {T} o\n * @return {Readonly<T>}\n */\nconst deepFreeze = (o) => {\n  for (const key in o) {\n    const c = o[key]\n    if (typeof c === 'object' || typeof c === 'function') {\n      deepFreeze(o[key])\n    }\n  }\n  return freeze(o)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/lib0/object.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/lib0/observable.js":
/*!*********************************************!*\
  !*** ../../node_modules/lib0/observable.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Observable: () => (/* binding */ Observable),\n/* harmony export */   ObservableV2: () => (/* binding */ ObservableV2)\n/* harmony export */ });\n/* harmony import */ var _map_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./map.js */ \"(ssr)/../../node_modules/lib0/map.js\");\n/* harmony import */ var _set_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./set.js */ \"(ssr)/../../node_modules/lib0/set.js\");\n/* harmony import */ var _array_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./array.js */ \"(ssr)/../../node_modules/lib0/array.js\");\n/**\n * Observable class prototype.\n *\n * @module observable\n */\n\n\n\n\n\n/**\n * Handles named events.\n * @experimental\n *\n * This is basically a (better typed) duplicate of Observable, which will replace Observable in the\n * next release.\n *\n * @template {{[key in keyof EVENTS]: function(...any):void}} EVENTS\n */\nclass ObservableV2 {\n  constructor () {\n    /**\n     * Some desc.\n     * @type {Map<string, Set<any>>}\n     */\n    this._observers = _map_js__WEBPACK_IMPORTED_MODULE_0__.create()\n  }\n\n  /**\n   * @template {keyof EVENTS & string} NAME\n   * @param {NAME} name\n   * @param {EVENTS[NAME]} f\n   */\n  on (name, f) {\n    _map_js__WEBPACK_IMPORTED_MODULE_0__.setIfUndefined(this._observers, /** @type {string} */ (name), _set_js__WEBPACK_IMPORTED_MODULE_1__.create).add(f)\n    return f\n  }\n\n  /**\n   * @template {keyof EVENTS & string} NAME\n   * @param {NAME} name\n   * @param {EVENTS[NAME]} f\n   */\n  once (name, f) {\n    /**\n     * @param  {...any} args\n     */\n    const _f = (...args) => {\n      this.off(name, /** @type {any} */ (_f))\n      f(...args)\n    }\n    this.on(name, /** @type {any} */ (_f))\n  }\n\n  /**\n   * @template {keyof EVENTS & string} NAME\n   * @param {NAME} name\n   * @param {EVENTS[NAME]} f\n   */\n  off (name, f) {\n    const observers = this._observers.get(name)\n    if (observers !== undefined) {\n      observers.delete(f)\n      if (observers.size === 0) {\n        this._observers.delete(name)\n      }\n    }\n  }\n\n  /**\n   * Emit a named event. All registered event listeners that listen to the\n   * specified name will receive the event.\n   *\n   * @todo This should catch exceptions\n   *\n   * @template {keyof EVENTS & string} NAME\n   * @param {NAME} name The event name.\n   * @param {Parameters<EVENTS[NAME]>} args The arguments that are applied to the event listener.\n   */\n  emit (name, args) {\n    // copy all listeners to an array first to make sure that no event is emitted to listeners that are subscribed while the event handler is called.\n    return _array_js__WEBPACK_IMPORTED_MODULE_2__.from((this._observers.get(name) || _map_js__WEBPACK_IMPORTED_MODULE_0__.create()).values()).forEach(f => f(...args))\n  }\n\n  destroy () {\n    this._observers = _map_js__WEBPACK_IMPORTED_MODULE_0__.create()\n  }\n}\n\n/* c8 ignore start */\n/**\n * Handles named events.\n *\n * @deprecated\n * @template N\n */\nclass Observable {\n  constructor () {\n    /**\n     * Some desc.\n     * @type {Map<N, any>}\n     */\n    this._observers = _map_js__WEBPACK_IMPORTED_MODULE_0__.create()\n  }\n\n  /**\n   * @param {N} name\n   * @param {function} f\n   */\n  on (name, f) {\n    _map_js__WEBPACK_IMPORTED_MODULE_0__.setIfUndefined(this._observers, name, _set_js__WEBPACK_IMPORTED_MODULE_1__.create).add(f)\n  }\n\n  /**\n   * @param {N} name\n   * @param {function} f\n   */\n  once (name, f) {\n    /**\n     * @param  {...any} args\n     */\n    const _f = (...args) => {\n      this.off(name, _f)\n      f(...args)\n    }\n    this.on(name, _f)\n  }\n\n  /**\n   * @param {N} name\n   * @param {function} f\n   */\n  off (name, f) {\n    const observers = this._observers.get(name)\n    if (observers !== undefined) {\n      observers.delete(f)\n      if (observers.size === 0) {\n        this._observers.delete(name)\n      }\n    }\n  }\n\n  /**\n   * Emit a named event. All registered event listeners that listen to the\n   * specified name will receive the event.\n   *\n   * @todo This should catch exceptions\n   *\n   * @param {N} name The event name.\n   * @param {Array<any>} args The arguments that are applied to the event listener.\n   */\n  emit (name, args) {\n    // copy all listeners to an array first to make sure that no event is emitted to listeners that are subscribed while the event handler is called.\n    return _array_js__WEBPACK_IMPORTED_MODULE_2__.from((this._observers.get(name) || _map_js__WEBPACK_IMPORTED_MODULE_0__.create()).values()).forEach(f => f(...args))\n  }\n\n  destroy () {\n    this._observers = _map_js__WEBPACK_IMPORTED_MODULE_0__.create()\n  }\n}\n/* c8 ignore end */\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/lib0/observable.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/lib0/pair.js":
/*!***************************************!*\
  !*** ../../node_modules/lib0/pair.js ***!
  \***************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Pair: () => (/* binding */ Pair),\n/* harmony export */   create: () => (/* binding */ create),\n/* harmony export */   createReversed: () => (/* binding */ createReversed),\n/* harmony export */   forEach: () => (/* binding */ forEach),\n/* harmony export */   map: () => (/* binding */ map)\n/* harmony export */ });\n/**\n * Working with value pairs.\n *\n * @module pair\n */\n\n/**\n * @template L,R\n */\nclass Pair {\n  /**\n   * @param {L} left\n   * @param {R} right\n   */\n  constructor (left, right) {\n    this.left = left\n    this.right = right\n  }\n}\n\n/**\n * @template L,R\n * @param {L} left\n * @param {R} right\n * @return {Pair<L,R>}\n */\nconst create = (left, right) => new Pair(left, right)\n\n/**\n * @template L,R\n * @param {R} right\n * @param {L} left\n * @return {Pair<L,R>}\n */\nconst createReversed = (right, left) => new Pair(left, right)\n\n/**\n * @template L,R\n * @param {Array<Pair<L,R>>} arr\n * @param {function(L, R):any} f\n */\nconst forEach = (arr, f) => arr.forEach(p => f(p.left, p.right))\n\n/**\n * @template L,R,X\n * @param {Array<Pair<L,R>>} arr\n * @param {function(L, R):X} f\n * @return {Array<X>}\n */\nconst map = (arr, f) => arr.map(p => f(p.left, p.right))\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/lib0/pair.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/lib0/promise.js":
/*!******************************************!*\
  !*** ../../node_modules/lib0/promise.js ***!
  \******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   all: () => (/* binding */ all),\n/* harmony export */   create: () => (/* binding */ create),\n/* harmony export */   createEmpty: () => (/* binding */ createEmpty),\n/* harmony export */   isPromise: () => (/* binding */ isPromise),\n/* harmony export */   reject: () => (/* binding */ reject),\n/* harmony export */   resolve: () => (/* binding */ resolve),\n/* harmony export */   resolveWith: () => (/* binding */ resolveWith),\n/* harmony export */   until: () => (/* binding */ until),\n/* harmony export */   untilAsync: () => (/* binding */ untilAsync),\n/* harmony export */   wait: () => (/* binding */ wait)\n/* harmony export */ });\n/* harmony import */ var _time_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./time.js */ \"(ssr)/../../node_modules/lib0/time.js\");\n/**\n * Utility helpers to work with promises.\n *\n * @module promise\n */\n\n\n\n/**\n * @template T\n * @callback PromiseResolve\n * @param {T|PromiseLike<T>} [result]\n */\n\n/**\n * @template T\n * @param {function(PromiseResolve<T>,function(Error):void):any} f\n * @return {Promise<T>}\n */\nconst create = f => /** @type {Promise<T>} */ (new Promise(f))\n\n/**\n * @param {function(function():void,function(Error):void):void} f\n * @return {Promise<void>}\n */\nconst createEmpty = f => new Promise(f)\n\n/**\n * `Promise.all` wait for all promises in the array to resolve and return the result\n * @template {unknown[] | []} PS\n *\n * @param {PS} ps\n * @return {Promise<{ -readonly [P in keyof PS]: Awaited<PS[P]> }>}\n */\nconst all = Promise.all.bind(Promise)\n\n/**\n * @param {Error} [reason]\n * @return {Promise<never>}\n */\nconst reject = reason => Promise.reject(reason)\n\n/**\n * @template T\n * @param {T|void} res\n * @return {Promise<T|void>}\n */\nconst resolve = res => Promise.resolve(res)\n\n/**\n * @template T\n * @param {T} res\n * @return {Promise<T>}\n */\nconst resolveWith = res => Promise.resolve(res)\n\n/**\n * @todo Next version, reorder parameters: check, [timeout, [intervalResolution]]\n * @deprecated use untilAsync instead\n *\n * @param {number} timeout\n * @param {function():boolean} check\n * @param {number} [intervalResolution]\n * @return {Promise<void>}\n */\nconst until = (timeout, check, intervalResolution = 10) => create((resolve, reject) => {\n  const startTime = _time_js__WEBPACK_IMPORTED_MODULE_0__.getUnixTime()\n  const hasTimeout = timeout > 0\n  const untilInterval = () => {\n    if (check()) {\n      clearInterval(intervalHandle)\n      resolve()\n    } else if (hasTimeout) {\n      /* c8 ignore else */\n      if (_time_js__WEBPACK_IMPORTED_MODULE_0__.getUnixTime() - startTime > timeout) {\n        clearInterval(intervalHandle)\n        reject(new Error('Timeout'))\n      }\n    }\n  }\n  const intervalHandle = setInterval(untilInterval, intervalResolution)\n})\n\n/**\n * @param {()=>Promise<boolean>|boolean} check\n * @param {number} timeout\n * @param {number} intervalResolution\n * @return {Promise<void>}\n */\nconst untilAsync = async (check, timeout = 0, intervalResolution = 10) => {\n  const startTime = _time_js__WEBPACK_IMPORTED_MODULE_0__.getUnixTime()\n  const noTimeout = timeout <= 0\n  // eslint-disable-next-line no-unmodified-loop-condition\n  while (noTimeout || _time_js__WEBPACK_IMPORTED_MODULE_0__.getUnixTime() - startTime <= timeout) {\n    if (await check()) return\n    await wait(intervalResolution)\n  }\n  throw new Error('Timeout')\n}\n\n/**\n * @param {number} timeout\n * @return {Promise<undefined>}\n */\nconst wait = timeout => create((resolve, _reject) => setTimeout(resolve, timeout))\n\n/**\n * Checks if an object is a promise using ducktyping.\n *\n * Promises are often polyfilled, so it makes sense to add some additional guarantees if the user of this\n * library has some insane environment where global Promise objects are overwritten.\n *\n * @param {any} p\n * @return {boolean}\n */\nconst isPromise = p => p instanceof Promise || (p && p.then && p.catch && p.finally)\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/lib0/promise.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/lib0/random.js":
/*!*****************************************!*\
  !*** ../../node_modules/lib0/random.js ***!
  \*****************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   oneOf: () => (/* binding */ oneOf),\n/* harmony export */   rand: () => (/* binding */ rand),\n/* harmony export */   uint32: () => (/* binding */ uint32),\n/* harmony export */   uint53: () => (/* binding */ uint53),\n/* harmony export */   uuidv4: () => (/* binding */ uuidv4)\n/* harmony export */ });\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./math.js */ \"(ssr)/../../node_modules/lib0/math.js\");\n/* harmony import */ var _binary_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./binary.js */ \"(ssr)/../../node_modules/lib0/binary.js\");\n/* harmony import */ var lib0_webcrypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lib0/webcrypto */ \"(ssr)/../../node_modules/lib0/webcrypto.node.js\");\n/**\n * Isomorphic module for true random numbers / buffers / uuids.\n *\n * Attention: falls back to Math.random if the browser does not support crypto.\n *\n * @module random\n */\n\n\n\n\n\nconst rand = Math.random\n\nconst uint32 = () => (0,lib0_webcrypto__WEBPACK_IMPORTED_MODULE_0__.getRandomValues)(new Uint32Array(1))[0]\n\nconst uint53 = () => {\n  const arr = (0,lib0_webcrypto__WEBPACK_IMPORTED_MODULE_0__.getRandomValues)(new Uint32Array(8))\n  return (arr[0] & _binary_js__WEBPACK_IMPORTED_MODULE_1__.BITS21) * (_binary_js__WEBPACK_IMPORTED_MODULE_1__.BITS32 + 1) + (arr[1] >>> 0)\n}\n\n/**\n * @template T\n * @param {Array<T>} arr\n * @return {T}\n */\nconst oneOf = arr => arr[_math_js__WEBPACK_IMPORTED_MODULE_2__.floor(rand() * arr.length)]\n\n// @ts-ignore\nconst uuidv4Template = [1e7] + -1e3 + -4e3 + -8e3 + -1e11\n\n/**\n * @return {string}\n */\nconst uuidv4 = () => uuidv4Template.replace(/[018]/g, /** @param {number} c */ c =>\n  (c ^ uint32() & 15 >> c / 4).toString(16)\n)\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2xpYjAvcmFuZG9tLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRWlDO0FBQ0k7QUFDVzs7QUFFekM7O0FBRUEscUJBQXFCLCtEQUFlOztBQUVwQztBQUNQLGNBQWMsK0RBQWU7QUFDN0IsbUJBQW1CLDhDQUFhLEtBQUssOENBQWE7QUFDbEQ7O0FBRUE7QUFDQTtBQUNBLFdBQVcsVUFBVTtBQUNyQixZQUFZO0FBQ1o7QUFDTyx5QkFBeUIsMkNBQVU7O0FBRTFDO0FBQ0E7O0FBRUE7QUFDQSxZQUFZO0FBQ1o7QUFDTyxrRUFBa0UsUUFBUTtBQUNqRjtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQG9wZW5jYW52YXMvd2ViLy4uLy4uL25vZGVfbW9kdWxlcy9saWIwL3JhbmRvbS5qcz9kOTkyIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogSXNvbW9ycGhpYyBtb2R1bGUgZm9yIHRydWUgcmFuZG9tIG51bWJlcnMgLyBidWZmZXJzIC8gdXVpZHMuXG4gKlxuICogQXR0ZW50aW9uOiBmYWxscyBiYWNrIHRvIE1hdGgucmFuZG9tIGlmIHRoZSBicm93c2VyIGRvZXMgbm90IHN1cHBvcnQgY3J5cHRvLlxuICpcbiAqIEBtb2R1bGUgcmFuZG9tXG4gKi9cblxuaW1wb3J0ICogYXMgbWF0aCBmcm9tICcuL21hdGguanMnXG5pbXBvcnQgKiBhcyBiaW5hcnkgZnJvbSAnLi9iaW5hcnkuanMnXG5pbXBvcnQgeyBnZXRSYW5kb21WYWx1ZXMgfSBmcm9tICdsaWIwL3dlYmNyeXB0bydcblxuZXhwb3J0IGNvbnN0IHJhbmQgPSBNYXRoLnJhbmRvbVxuXG5leHBvcnQgY29uc3QgdWludDMyID0gKCkgPT4gZ2V0UmFuZG9tVmFsdWVzKG5ldyBVaW50MzJBcnJheSgxKSlbMF1cblxuZXhwb3J0IGNvbnN0IHVpbnQ1MyA9ICgpID0+IHtcbiAgY29uc3QgYXJyID0gZ2V0UmFuZG9tVmFsdWVzKG5ldyBVaW50MzJBcnJheSg4KSlcbiAgcmV0dXJuIChhcnJbMF0gJiBiaW5hcnkuQklUUzIxKSAqIChiaW5hcnkuQklUUzMyICsgMSkgKyAoYXJyWzFdID4+PiAwKVxufVxuXG4vKipcbiAqIEB0ZW1wbGF0ZSBUXG4gKiBAcGFyYW0ge0FycmF5PFQ+fSBhcnJcbiAqIEByZXR1cm4ge1R9XG4gKi9cbmV4cG9ydCBjb25zdCBvbmVPZiA9IGFyciA9PiBhcnJbbWF0aC5mbG9vcihyYW5kKCkgKiBhcnIubGVuZ3RoKV1cblxuLy8gQHRzLWlnbm9yZVxuY29uc3QgdXVpZHY0VGVtcGxhdGUgPSBbMWU3XSArIC0xZTMgKyAtNGUzICsgLThlMyArIC0xZTExXG5cbi8qKlxuICogQHJldHVybiB7c3RyaW5nfVxuICovXG5leHBvcnQgY29uc3QgdXVpZHY0ID0gKCkgPT4gdXVpZHY0VGVtcGxhdGUucmVwbGFjZSgvWzAxOF0vZywgLyoqIEBwYXJhbSB7bnVtYmVyfSBjICovIGMgPT5cbiAgKGMgXiB1aW50MzIoKSAmIDE1ID4+IGMgLyA0KS50b1N0cmluZygxNilcbilcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/lib0/random.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/lib0/set.js":
/*!**************************************!*\
  !*** ../../node_modules/lib0/set.js ***!
  \**************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   create: () => (/* binding */ create),\n/* harmony export */   first: () => (/* binding */ first),\n/* harmony export */   from: () => (/* binding */ from),\n/* harmony export */   toArray: () => (/* binding */ toArray)\n/* harmony export */ });\n/**\n * Utility module to work with sets.\n *\n * @module set\n */\n\nconst create = () => new Set()\n\n/**\n * @template T\n * @param {Set<T>} set\n * @return {Array<T>}\n */\nconst toArray = set => Array.from(set)\n\n/**\n * @template T\n * @param {Set<T>} set\n * @return {T}\n */\nconst first = set =>\n  set.values().next().value ?? undefined\n\n/**\n * @template T\n * @param {Iterable<T>} entries\n * @return {Set<T>}\n */\nconst from = entries => new Set(entries)\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2xpYjAvc2V0LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVPOztBQUVQO0FBQ0E7QUFDQSxXQUFXLFFBQVE7QUFDbkIsWUFBWTtBQUNaO0FBQ087O0FBRVA7QUFDQTtBQUNBLFdBQVcsUUFBUTtBQUNuQixZQUFZO0FBQ1o7QUFDTztBQUNQOztBQUVBO0FBQ0E7QUFDQSxXQUFXLGFBQWE7QUFDeEIsWUFBWTtBQUNaO0FBQ08iLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9Ab3BlbmNhbnZhcy93ZWIvLi4vLi4vbm9kZV9tb2R1bGVzL2xpYjAvc2V0LmpzPzNhY2IiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBVdGlsaXR5IG1vZHVsZSB0byB3b3JrIHdpdGggc2V0cy5cbiAqXG4gKiBAbW9kdWxlIHNldFxuICovXG5cbmV4cG9ydCBjb25zdCBjcmVhdGUgPSAoKSA9PiBuZXcgU2V0KClcblxuLyoqXG4gKiBAdGVtcGxhdGUgVFxuICogQHBhcmFtIHtTZXQ8VD59IHNldFxuICogQHJldHVybiB7QXJyYXk8VD59XG4gKi9cbmV4cG9ydCBjb25zdCB0b0FycmF5ID0gc2V0ID0+IEFycmF5LmZyb20oc2V0KVxuXG4vKipcbiAqIEB0ZW1wbGF0ZSBUXG4gKiBAcGFyYW0ge1NldDxUPn0gc2V0XG4gKiBAcmV0dXJuIHtUfVxuICovXG5leHBvcnQgY29uc3QgZmlyc3QgPSBzZXQgPT5cbiAgc2V0LnZhbHVlcygpLm5leHQoKS52YWx1ZSA/PyB1bmRlZmluZWRcblxuLyoqXG4gKiBAdGVtcGxhdGUgVFxuICogQHBhcmFtIHtJdGVyYWJsZTxUPn0gZW50cmllc1xuICogQHJldHVybiB7U2V0PFQ+fVxuICovXG5leHBvcnQgY29uc3QgZnJvbSA9IGVudHJpZXMgPT4gbmV3IFNldChlbnRyaWVzKVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/lib0/set.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/lib0/storage.js":
/*!******************************************!*\
  !*** ../../node_modules/lib0/storage.js ***!
  \******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   offChange: () => (/* binding */ offChange),\n/* harmony export */   onChange: () => (/* binding */ onChange),\n/* harmony export */   varStorage: () => (/* binding */ varStorage)\n/* harmony export */ });\n/* eslint-env browser */\n\n/**\n * Isomorphic variable storage.\n *\n * Uses LocalStorage in the browser and falls back to in-memory storage.\n *\n * @module storage\n */\n\n/* c8 ignore start */\nclass VarStoragePolyfill {\n  constructor () {\n    this.map = new Map()\n  }\n\n  /**\n   * @param {string} key\n   * @param {any} newValue\n   */\n  setItem (key, newValue) {\n    this.map.set(key, newValue)\n  }\n\n  /**\n   * @param {string} key\n   */\n  getItem (key) {\n    return this.map.get(key)\n  }\n}\n/* c8 ignore stop */\n\n/**\n * @type {any}\n */\nlet _localStorage = new VarStoragePolyfill()\nlet usePolyfill = true\n\n/* c8 ignore start */\ntry {\n  // if the same-origin rule is violated, accessing localStorage might thrown an error\n  if (typeof localStorage !== 'undefined' && localStorage) {\n    _localStorage = localStorage\n    usePolyfill = false\n  }\n} catch (e) { }\n/* c8 ignore stop */\n\n/**\n * This is basically localStorage in browser, or a polyfill in nodejs\n */\n/* c8 ignore next */\nconst varStorage = _localStorage\n\n/**\n * A polyfill for `addEventListener('storage', event => {..})` that does nothing if the polyfill is being used.\n *\n * @param {function({ key: string, newValue: string, oldValue: string }): void} eventHandler\n * @function\n */\n/* c8 ignore next */\nconst onChange = eventHandler => usePolyfill || addEventListener('storage', /** @type {any} */ (eventHandler))\n\n/**\n * A polyfill for `removeEventListener('storage', event => {..})` that does nothing if the polyfill is being used.\n *\n * @param {function({ key: string, newValue: string, oldValue: string }): void} eventHandler\n * @function\n */\n/* c8 ignore next */\nconst offChange = eventHandler => usePolyfill || removeEventListener('storage', /** @type {any} */ (eventHandler))\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/lib0/storage.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/lib0/string.js":
/*!*****************************************!*\
  !*** ../../node_modules/lib0/string.js ***!
  \*****************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MAX_UTF16_CHARACTER: () => (/* binding */ MAX_UTF16_CHARACTER),\n/* harmony export */   _decodeUtf8Native: () => (/* binding */ _decodeUtf8Native),\n/* harmony export */   _decodeUtf8Polyfill: () => (/* binding */ _decodeUtf8Polyfill),\n/* harmony export */   _encodeUtf8Native: () => (/* binding */ _encodeUtf8Native),\n/* harmony export */   _encodeUtf8Polyfill: () => (/* binding */ _encodeUtf8Polyfill),\n/* harmony export */   decodeUtf8: () => (/* binding */ decodeUtf8),\n/* harmony export */   encodeUtf8: () => (/* binding */ encodeUtf8),\n/* harmony export */   fromCamelCase: () => (/* binding */ fromCamelCase),\n/* harmony export */   fromCharCode: () => (/* binding */ fromCharCode),\n/* harmony export */   fromCodePoint: () => (/* binding */ fromCodePoint),\n/* harmony export */   repeat: () => (/* binding */ repeat),\n/* harmony export */   splice: () => (/* binding */ splice),\n/* harmony export */   trimLeft: () => (/* binding */ trimLeft),\n/* harmony export */   utf8ByteLength: () => (/* binding */ utf8ByteLength),\n/* harmony export */   utf8TextDecoder: () => (/* binding */ utf8TextDecoder),\n/* harmony export */   utf8TextEncoder: () => (/* binding */ utf8TextEncoder)\n/* harmony export */ });\n/* harmony import */ var _array_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./array.js */ \"(ssr)/../../node_modules/lib0/array.js\");\n\n\n/**\n * Utility module to work with strings.\n *\n * @module string\n */\n\nconst fromCharCode = String.fromCharCode\nconst fromCodePoint = String.fromCodePoint\n\n/**\n * The largest utf16 character.\n * Corresponds to Uint8Array([255, 255]) or charcodeof(2x2^8)\n */\nconst MAX_UTF16_CHARACTER = fromCharCode(65535)\n\n/**\n * @param {string} s\n * @return {string}\n */\nconst toLowerCase = s => s.toLowerCase()\n\nconst trimLeftRegex = /^\\s*/g\n\n/**\n * @param {string} s\n * @return {string}\n */\nconst trimLeft = s => s.replace(trimLeftRegex, '')\n\nconst fromCamelCaseRegex = /([A-Z])/g\n\n/**\n * @param {string} s\n * @param {string} separator\n * @return {string}\n */\nconst fromCamelCase = (s, separator) => trimLeft(s.replace(fromCamelCaseRegex, match => `${separator}${toLowerCase(match)}`))\n\n/**\n * Compute the utf8ByteLength\n * @param {string} str\n * @return {number}\n */\nconst utf8ByteLength = str => unescape(encodeURIComponent(str)).length\n\n/**\n * @param {string} str\n * @return {Uint8Array}\n */\nconst _encodeUtf8Polyfill = str => {\n  const encodedString = unescape(encodeURIComponent(str))\n  const len = encodedString.length\n  const buf = new Uint8Array(len)\n  for (let i = 0; i < len; i++) {\n    buf[i] = /** @type {number} */ (encodedString.codePointAt(i))\n  }\n  return buf\n}\n\n/* c8 ignore next */\nconst utf8TextEncoder = /** @type {TextEncoder} */ (typeof TextEncoder !== 'undefined' ? new TextEncoder() : null)\n\n/**\n * @param {string} str\n * @return {Uint8Array}\n */\nconst _encodeUtf8Native = str => utf8TextEncoder.encode(str)\n\n/**\n * @param {string} str\n * @return {Uint8Array}\n */\n/* c8 ignore next */\nconst encodeUtf8 = utf8TextEncoder ? _encodeUtf8Native : _encodeUtf8Polyfill\n\n/**\n * @param {Uint8Array} buf\n * @return {string}\n */\nconst _decodeUtf8Polyfill = buf => {\n  let remainingLen = buf.length\n  let encodedString = ''\n  let bufPos = 0\n  while (remainingLen > 0) {\n    const nextLen = remainingLen < 10000 ? remainingLen : 10000\n    const bytes = buf.subarray(bufPos, bufPos + nextLen)\n    bufPos += nextLen\n    // Starting with ES5.1 we can supply a generic array-like object as arguments\n    encodedString += String.fromCodePoint.apply(null, /** @type {any} */ (bytes))\n    remainingLen -= nextLen\n  }\n  return decodeURIComponent(escape(encodedString))\n}\n\n/* c8 ignore next */\nlet utf8TextDecoder = typeof TextDecoder === 'undefined' ? null : new TextDecoder('utf-8', { fatal: true, ignoreBOM: true })\n\n/* c8 ignore start */\nif (utf8TextDecoder && utf8TextDecoder.decode(new Uint8Array()).length === 1) {\n  // Safari doesn't handle BOM correctly.\n  // This fixes a bug in Safari 13.0.5 where it produces a BOM the first time it is called.\n  // utf8TextDecoder.decode(new Uint8Array()).length === 1 on the first call and\n  // utf8TextDecoder.decode(new Uint8Array()).length === 1 on the second call\n  // Another issue is that from then on no BOM chars are recognized anymore\n  /* c8 ignore next */\n  utf8TextDecoder = null\n}\n/* c8 ignore stop */\n\n/**\n * @param {Uint8Array} buf\n * @return {string}\n */\nconst _decodeUtf8Native = buf => /** @type {TextDecoder} */ (utf8TextDecoder).decode(buf)\n\n/**\n * @param {Uint8Array} buf\n * @return {string}\n */\n/* c8 ignore next */\nconst decodeUtf8 = utf8TextDecoder ? _decodeUtf8Native : _decodeUtf8Polyfill\n\n/**\n * @param {string} str The initial string\n * @param {number} index Starting position\n * @param {number} remove Number of characters to remove\n * @param {string} insert New content to insert\n */\nconst splice = (str, index, remove, insert = '') => str.slice(0, index) + insert + str.slice(index + remove)\n\n/**\n * @param {string} source\n * @param {number} n\n */\nconst repeat = (source, n) => _array_js__WEBPACK_IMPORTED_MODULE_0__.unfold(n, () => source).join('')\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2xpYjAvc3RyaW5nLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQW1DOztBQUVuQztBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVPO0FBQ0E7O0FBRVA7QUFDQTtBQUNBO0FBQ0E7QUFDTzs7QUFFUDtBQUNBLFdBQVcsUUFBUTtBQUNuQixZQUFZO0FBQ1o7QUFDQTs7QUFFQTs7QUFFQTtBQUNBLFdBQVcsUUFBUTtBQUNuQixZQUFZO0FBQ1o7QUFDTzs7QUFFUDs7QUFFQTtBQUNBLFdBQVcsUUFBUTtBQUNuQixXQUFXLFFBQVE7QUFDbkIsWUFBWTtBQUNaO0FBQ08sMkZBQTJGLFVBQVUsRUFBRSxtQkFBbUI7O0FBRWpJO0FBQ0E7QUFDQSxXQUFXLFFBQVE7QUFDbkIsWUFBWTtBQUNaO0FBQ087O0FBRVA7QUFDQSxXQUFXLFFBQVE7QUFDbkIsWUFBWTtBQUNaO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQSxrQkFBa0IsU0FBUztBQUMzQix3QkFBd0IsUUFBUTtBQUNoQztBQUNBO0FBQ0E7O0FBRUE7QUFDTyxtQ0FBbUMsYUFBYTs7QUFFdkQ7QUFDQSxXQUFXLFFBQVE7QUFDbkIsWUFBWTtBQUNaO0FBQ087O0FBRVA7QUFDQSxXQUFXLFFBQVE7QUFDbkIsWUFBWTtBQUNaO0FBQ0E7QUFDTzs7QUFFUDtBQUNBLFdBQVcsWUFBWTtBQUN2QixZQUFZO0FBQ1o7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpRUFBaUUsS0FBSztBQUN0RTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNPLDZGQUE2Riw4QkFBOEI7O0FBRWxJO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxXQUFXLFlBQVk7QUFDdkIsWUFBWTtBQUNaO0FBQ08sNENBQTRDLGFBQWE7O0FBRWhFO0FBQ0EsV0FBVyxZQUFZO0FBQ3ZCLFlBQVk7QUFDWjtBQUNBO0FBQ087O0FBRVA7QUFDQSxXQUFXLFFBQVE7QUFDbkIsV0FBVyxRQUFRO0FBQ25CLFdBQVcsUUFBUTtBQUNuQixXQUFXLFFBQVE7QUFDbkI7QUFDTzs7QUFFUDtBQUNBLFdBQVcsUUFBUTtBQUNuQixXQUFXLFFBQVE7QUFDbkI7QUFDTyw4QkFBOEIsNkNBQVkiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9Ab3BlbmNhbnZhcy93ZWIvLi4vLi4vbm9kZV9tb2R1bGVzL2xpYjAvc3RyaW5nLmpzP2YyMzgiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICogYXMgYXJyYXkgZnJvbSAnLi9hcnJheS5qcydcblxuLyoqXG4gKiBVdGlsaXR5IG1vZHVsZSB0byB3b3JrIHdpdGggc3RyaW5ncy5cbiAqXG4gKiBAbW9kdWxlIHN0cmluZ1xuICovXG5cbmV4cG9ydCBjb25zdCBmcm9tQ2hhckNvZGUgPSBTdHJpbmcuZnJvbUNoYXJDb2RlXG5leHBvcnQgY29uc3QgZnJvbUNvZGVQb2ludCA9IFN0cmluZy5mcm9tQ29kZVBvaW50XG5cbi8qKlxuICogVGhlIGxhcmdlc3QgdXRmMTYgY2hhcmFjdGVyLlxuICogQ29ycmVzcG9uZHMgdG8gVWludDhBcnJheShbMjU1LCAyNTVdKSBvciBjaGFyY29kZW9mKDJ4Ml44KVxuICovXG5leHBvcnQgY29uc3QgTUFYX1VURjE2X0NIQVJBQ1RFUiA9IGZyb21DaGFyQ29kZSg2NTUzNSlcblxuLyoqXG4gKiBAcGFyYW0ge3N0cmluZ30gc1xuICogQHJldHVybiB7c3RyaW5nfVxuICovXG5jb25zdCB0b0xvd2VyQ2FzZSA9IHMgPT4gcy50b0xvd2VyQ2FzZSgpXG5cbmNvbnN0IHRyaW1MZWZ0UmVnZXggPSAvXlxccyovZ1xuXG4vKipcbiAqIEBwYXJhbSB7c3RyaW5nfSBzXG4gKiBAcmV0dXJuIHtzdHJpbmd9XG4gKi9cbmV4cG9ydCBjb25zdCB0cmltTGVmdCA9IHMgPT4gcy5yZXBsYWNlKHRyaW1MZWZ0UmVnZXgsICcnKVxuXG5jb25zdCBmcm9tQ2FtZWxDYXNlUmVnZXggPSAvKFtBLVpdKS9nXG5cbi8qKlxuICogQHBhcmFtIHtzdHJpbmd9IHNcbiAqIEBwYXJhbSB7c3RyaW5nfSBzZXBhcmF0b3JcbiAqIEByZXR1cm4ge3N0cmluZ31cbiAqL1xuZXhwb3J0IGNvbnN0IGZyb21DYW1lbENhc2UgPSAocywgc2VwYXJhdG9yKSA9PiB0cmltTGVmdChzLnJlcGxhY2UoZnJvbUNhbWVsQ2FzZVJlZ2V4LCBtYXRjaCA9PiBgJHtzZXBhcmF0b3J9JHt0b0xvd2VyQ2FzZShtYXRjaCl9YCkpXG5cbi8qKlxuICogQ29tcHV0ZSB0aGUgdXRmOEJ5dGVMZW5ndGhcbiAqIEBwYXJhbSB7c3RyaW5nfSBzdHJcbiAqIEByZXR1cm4ge251bWJlcn1cbiAqL1xuZXhwb3J0IGNvbnN0IHV0ZjhCeXRlTGVuZ3RoID0gc3RyID0+IHVuZXNjYXBlKGVuY29kZVVSSUNvbXBvbmVudChzdHIpKS5sZW5ndGhcblxuLyoqXG4gKiBAcGFyYW0ge3N0cmluZ30gc3RyXG4gKiBAcmV0dXJuIHtVaW50OEFycmF5fVxuICovXG5leHBvcnQgY29uc3QgX2VuY29kZVV0ZjhQb2x5ZmlsbCA9IHN0ciA9PiB7XG4gIGNvbnN0IGVuY29kZWRTdHJpbmcgPSB1bmVzY2FwZShlbmNvZGVVUklDb21wb25lbnQoc3RyKSlcbiAgY29uc3QgbGVuID0gZW5jb2RlZFN0cmluZy5sZW5ndGhcbiAgY29uc3QgYnVmID0gbmV3IFVpbnQ4QXJyYXkobGVuKVxuICBmb3IgKGxldCBpID0gMDsgaSA8IGxlbjsgaSsrKSB7XG4gICAgYnVmW2ldID0gLyoqIEB0eXBlIHtudW1iZXJ9ICovIChlbmNvZGVkU3RyaW5nLmNvZGVQb2ludEF0KGkpKVxuICB9XG4gIHJldHVybiBidWZcbn1cblxuLyogYzggaWdub3JlIG5leHQgKi9cbmV4cG9ydCBjb25zdCB1dGY4VGV4dEVuY29kZXIgPSAvKiogQHR5cGUge1RleHRFbmNvZGVyfSAqLyAodHlwZW9mIFRleHRFbmNvZGVyICE9PSAndW5kZWZpbmVkJyA/IG5ldyBUZXh0RW5jb2RlcigpIDogbnVsbClcblxuLyoqXG4gKiBAcGFyYW0ge3N0cmluZ30gc3RyXG4gKiBAcmV0dXJuIHtVaW50OEFycmF5fVxuICovXG5leHBvcnQgY29uc3QgX2VuY29kZVV0ZjhOYXRpdmUgPSBzdHIgPT4gdXRmOFRleHRFbmNvZGVyLmVuY29kZShzdHIpXG5cbi8qKlxuICogQHBhcmFtIHtzdHJpbmd9IHN0clxuICogQHJldHVybiB7VWludDhBcnJheX1cbiAqL1xuLyogYzggaWdub3JlIG5leHQgKi9cbmV4cG9ydCBjb25zdCBlbmNvZGVVdGY4ID0gdXRmOFRleHRFbmNvZGVyID8gX2VuY29kZVV0ZjhOYXRpdmUgOiBfZW5jb2RlVXRmOFBvbHlmaWxsXG5cbi8qKlxuICogQHBhcmFtIHtVaW50OEFycmF5fSBidWZcbiAqIEByZXR1cm4ge3N0cmluZ31cbiAqL1xuZXhwb3J0IGNvbnN0IF9kZWNvZGVVdGY4UG9seWZpbGwgPSBidWYgPT4ge1xuICBsZXQgcmVtYWluaW5nTGVuID0gYnVmLmxlbmd0aFxuICBsZXQgZW5jb2RlZFN0cmluZyA9ICcnXG4gIGxldCBidWZQb3MgPSAwXG4gIHdoaWxlIChyZW1haW5pbmdMZW4gPiAwKSB7XG4gICAgY29uc3QgbmV4dExlbiA9IHJlbWFpbmluZ0xlbiA8IDEwMDAwID8gcmVtYWluaW5nTGVuIDogMTAwMDBcbiAgICBjb25zdCBieXRlcyA9IGJ1Zi5zdWJhcnJheShidWZQb3MsIGJ1ZlBvcyArIG5leHRMZW4pXG4gICAgYnVmUG9zICs9IG5leHRMZW5cbiAgICAvLyBTdGFydGluZyB3aXRoIEVTNS4xIHdlIGNhbiBzdXBwbHkgYSBnZW5lcmljIGFycmF5LWxpa2Ugb2JqZWN0IGFzIGFyZ3VtZW50c1xuICAgIGVuY29kZWRTdHJpbmcgKz0gU3RyaW5nLmZyb21Db2RlUG9pbnQuYXBwbHkobnVsbCwgLyoqIEB0eXBlIHthbnl9ICovIChieXRlcykpXG4gICAgcmVtYWluaW5nTGVuIC09IG5leHRMZW5cbiAgfVxuICByZXR1cm4gZGVjb2RlVVJJQ29tcG9uZW50KGVzY2FwZShlbmNvZGVkU3RyaW5nKSlcbn1cblxuLyogYzggaWdub3JlIG5leHQgKi9cbmV4cG9ydCBsZXQgdXRmOFRleHREZWNvZGVyID0gdHlwZW9mIFRleHREZWNvZGVyID09PSAndW5kZWZpbmVkJyA/IG51bGwgOiBuZXcgVGV4dERlY29kZXIoJ3V0Zi04JywgeyBmYXRhbDogdHJ1ZSwgaWdub3JlQk9NOiB0cnVlIH0pXG5cbi8qIGM4IGlnbm9yZSBzdGFydCAqL1xuaWYgKHV0ZjhUZXh0RGVjb2RlciAmJiB1dGY4VGV4dERlY29kZXIuZGVjb2RlKG5ldyBVaW50OEFycmF5KCkpLmxlbmd0aCA9PT0gMSkge1xuICAvLyBTYWZhcmkgZG9lc24ndCBoYW5kbGUgQk9NIGNvcnJlY3RseS5cbiAgLy8gVGhpcyBmaXhlcyBhIGJ1ZyBpbiBTYWZhcmkgMTMuMC41IHdoZXJlIGl0IHByb2R1Y2VzIGEgQk9NIHRoZSBmaXJzdCB0aW1lIGl0IGlzIGNhbGxlZC5cbiAgLy8gdXRmOFRleHREZWNvZGVyLmRlY29kZShuZXcgVWludDhBcnJheSgpKS5sZW5ndGggPT09IDEgb24gdGhlIGZpcnN0IGNhbGwgYW5kXG4gIC8vIHV0ZjhUZXh0RGVjb2Rlci5kZWNvZGUobmV3IFVpbnQ4QXJyYXkoKSkubGVuZ3RoID09PSAxIG9uIHRoZSBzZWNvbmQgY2FsbFxuICAvLyBBbm90aGVyIGlzc3VlIGlzIHRoYXQgZnJvbSB0aGVuIG9uIG5vIEJPTSBjaGFycyBhcmUgcmVjb2duaXplZCBhbnltb3JlXG4gIC8qIGM4IGlnbm9yZSBuZXh0ICovXG4gIHV0ZjhUZXh0RGVjb2RlciA9IG51bGxcbn1cbi8qIGM4IGlnbm9yZSBzdG9wICovXG5cbi8qKlxuICogQHBhcmFtIHtVaW50OEFycmF5fSBidWZcbiAqIEByZXR1cm4ge3N0cmluZ31cbiAqL1xuZXhwb3J0IGNvbnN0IF9kZWNvZGVVdGY4TmF0aXZlID0gYnVmID0+IC8qKiBAdHlwZSB7VGV4dERlY29kZXJ9ICovICh1dGY4VGV4dERlY29kZXIpLmRlY29kZShidWYpXG5cbi8qKlxuICogQHBhcmFtIHtVaW50OEFycmF5fSBidWZcbiAqIEByZXR1cm4ge3N0cmluZ31cbiAqL1xuLyogYzggaWdub3JlIG5leHQgKi9cbmV4cG9ydCBjb25zdCBkZWNvZGVVdGY4ID0gdXRmOFRleHREZWNvZGVyID8gX2RlY29kZVV0ZjhOYXRpdmUgOiBfZGVjb2RlVXRmOFBvbHlmaWxsXG5cbi8qKlxuICogQHBhcmFtIHtzdHJpbmd9IHN0ciBUaGUgaW5pdGlhbCBzdHJpbmdcbiAqIEBwYXJhbSB7bnVtYmVyfSBpbmRleCBTdGFydGluZyBwb3NpdGlvblxuICogQHBhcmFtIHtudW1iZXJ9IHJlbW92ZSBOdW1iZXIgb2YgY2hhcmFjdGVycyB0byByZW1vdmVcbiAqIEBwYXJhbSB7c3RyaW5nfSBpbnNlcnQgTmV3IGNvbnRlbnQgdG8gaW5zZXJ0XG4gKi9cbmV4cG9ydCBjb25zdCBzcGxpY2UgPSAoc3RyLCBpbmRleCwgcmVtb3ZlLCBpbnNlcnQgPSAnJykgPT4gc3RyLnNsaWNlKDAsIGluZGV4KSArIGluc2VydCArIHN0ci5zbGljZShpbmRleCArIHJlbW92ZSlcblxuLyoqXG4gKiBAcGFyYW0ge3N0cmluZ30gc291cmNlXG4gKiBAcGFyYW0ge251bWJlcn0gblxuICovXG5leHBvcnQgY29uc3QgcmVwZWF0ID0gKHNvdXJjZSwgbikgPT4gYXJyYXkudW5mb2xkKG4sICgpID0+IHNvdXJjZSkuam9pbignJylcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/lib0/string.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/lib0/symbol.js":
/*!*****************************************!*\
  !*** ../../node_modules/lib0/symbol.js ***!
  \*****************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   create: () => (/* binding */ create),\n/* harmony export */   isSymbol: () => (/* binding */ isSymbol)\n/* harmony export */ });\n/**\n * Utility module to work with EcmaScript Symbols.\n *\n * @module symbol\n */\n\n/**\n * Return fresh symbol.\n *\n * @return {Symbol}\n */\nconst create = Symbol\n\n/**\n * @param {any} s\n * @return {boolean}\n */\nconst isSymbol = s => typeof s === 'symbol'\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2xpYjAvc3ltYm9sLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxZQUFZO0FBQ1o7QUFDTzs7QUFFUDtBQUNBLFdBQVcsS0FBSztBQUNoQixZQUFZO0FBQ1o7QUFDTyIsInNvdXJjZXMiOlsid2VicGFjazovL0BvcGVuY2FudmFzL3dlYi8uLi8uLi9ub2RlX21vZHVsZXMvbGliMC9zeW1ib2wuanM/MGE5MiJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIFV0aWxpdHkgbW9kdWxlIHRvIHdvcmsgd2l0aCBFY21hU2NyaXB0IFN5bWJvbHMuXG4gKlxuICogQG1vZHVsZSBzeW1ib2xcbiAqL1xuXG4vKipcbiAqIFJldHVybiBmcmVzaCBzeW1ib2wuXG4gKlxuICogQHJldHVybiB7U3ltYm9sfVxuICovXG5leHBvcnQgY29uc3QgY3JlYXRlID0gU3ltYm9sXG5cbi8qKlxuICogQHBhcmFtIHthbnl9IHNcbiAqIEByZXR1cm4ge2Jvb2xlYW59XG4gKi9cbmV4cG9ydCBjb25zdCBpc1N5bWJvbCA9IHMgPT4gdHlwZW9mIHMgPT09ICdzeW1ib2wnXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/lib0/symbol.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/lib0/time.js":
/*!***************************************!*\
  !*** ../../node_modules/lib0/time.js ***!
  \***************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getDate: () => (/* binding */ getDate),\n/* harmony export */   getUnixTime: () => (/* binding */ getUnixTime),\n/* harmony export */   humanizeDuration: () => (/* binding */ humanizeDuration)\n/* harmony export */ });\n/* harmony import */ var _metric_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./metric.js */ \"(ssr)/../../node_modules/lib0/metric.js\");\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./math.js */ \"(ssr)/../../node_modules/lib0/math.js\");\n/**\n * Utility module to work with time.\n *\n * @module time\n */\n\n\n\n\n/**\n * Return current time.\n *\n * @return {Date}\n */\nconst getDate = () => new Date()\n\n/**\n * Return current unix time.\n *\n * @return {number}\n */\nconst getUnixTime = Date.now\n\n/**\n * Transform time (in ms) to a human readable format. E.g. 1100 => 1.1s. 60s => 1min. .001 => 10μs.\n *\n * @param {number} d duration in milliseconds\n * @return {string} humanized approximation of time\n */\nconst humanizeDuration = d => {\n  if (d < 60000) {\n    const p = _metric_js__WEBPACK_IMPORTED_MODULE_0__.prefix(d, -1)\n    return _math_js__WEBPACK_IMPORTED_MODULE_1__.round(p.n * 100) / 100 + p.prefix + 's'\n  }\n  d = _math_js__WEBPACK_IMPORTED_MODULE_1__.floor(d / 1000)\n  const seconds = d % 60\n  const minutes = _math_js__WEBPACK_IMPORTED_MODULE_1__.floor(d / 60) % 60\n  const hours = _math_js__WEBPACK_IMPORTED_MODULE_1__.floor(d / 3600) % 24\n  const days = _math_js__WEBPACK_IMPORTED_MODULE_1__.floor(d / 86400)\n  if (days > 0) {\n    return days + 'd' + ((hours > 0 || minutes > 30) ? ' ' + (minutes > 30 ? hours + 1 : hours) + 'h' : '')\n  }\n  if (hours > 0) {\n    /* c8 ignore next */\n    return hours + 'h' + ((minutes > 0 || seconds > 30) ? ' ' + (seconds > 30 ? minutes + 1 : minutes) + 'min' : '')\n  }\n  return minutes + 'min' + (seconds > 0 ? ' ' + seconds + 's' : '')\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/lib0/time.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/lib0/webcrypto.node.js":
/*!*************************************************!*\
  !*** ../../node_modules/lib0/webcrypto.node.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getRandomValues: () => (/* binding */ getRandomValues),\n/* harmony export */   subtle: () => (/* binding */ subtle)\n/* harmony export */ });\n/* harmony import */ var node_crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! node:crypto */ \"node:crypto\");\n\n\nconst subtle = /** @type {any} */ (node_crypto__WEBPACK_IMPORTED_MODULE_0__.webcrypto).subtle\nconst getRandomValues = /** @type {any} */ (node_crypto__WEBPACK_IMPORTED_MODULE_0__.webcrypto).getRandomValues.bind(node_crypto__WEBPACK_IMPORTED_MODULE_0__.webcrypto)\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2xpYjAvd2ViY3J5cHRvLm5vZGUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQXVDOztBQUVoQywwQkFBMEIsS0FBSyxJQUFJLGtEQUFTO0FBQzVDLG1DQUFtQyxLQUFLLElBQUksa0RBQVMsdUJBQXVCLGtEQUFTIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQG9wZW5jYW52YXMvd2ViLy4uLy4uL25vZGVfbW9kdWxlcy9saWIwL3dlYmNyeXB0by5ub2RlLmpzP2JkMjciXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgd2ViY3J5cHRvIH0gZnJvbSAnbm9kZTpjcnlwdG8nXG5cbmV4cG9ydCBjb25zdCBzdWJ0bGUgPSAvKiogQHR5cGUge2FueX0gKi8gKHdlYmNyeXB0bykuc3VidGxlXG5leHBvcnQgY29uc3QgZ2V0UmFuZG9tVmFsdWVzID0gLyoqIEB0eXBlIHthbnl9ICovICh3ZWJjcnlwdG8pLmdldFJhbmRvbVZhbHVlcy5iaW5kKHdlYmNyeXB0bylcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/lib0/webcrypto.node.js\n");

/***/ })

};
;