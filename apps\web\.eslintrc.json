{"extends": ["next/core-web-vitals", "plugin:@typescript-eslint/recommended"], "parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint", "unused-imports", "@typescript-eslint/eslint-plugin"], "rules": {"@typescript-eslint/no-unused-vars": ["error", {"argsIgnorePattern": "^_", "varsIgnorePattern": "^_|^UNUSED_", "caughtErrorsIgnorePattern": "^_", "destructuredArrayIgnorePattern": "^_"}], "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-empty-object-type": "off", "unused-imports/no-unused-imports": "error"}}