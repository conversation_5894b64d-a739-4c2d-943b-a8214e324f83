/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
/******/ (() => { // webpackBootstrap
/******/ 	"use strict";
/******/ 	var __webpack_modules__ = ({

/***/ "(ssr)/./src/hooks/utils.ts":
/*!****************************!*\
  !*** ./src/hooks/utils.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createClient: () => (/* binding */ createClient)\n/* harmony export */ });\n/* harmony import */ var _langchain_langgraph_sdk__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @langchain/langgraph-sdk */ \"(ssr)/../../node_modules/@langchain/langgraph-sdk/index.js\");\n\nconst createClient = ()=>{\n    const apiUrl = process.env.NEXT_PUBLIC_API_URL ?? \"http://localhost:3000/api\";\n    return new _langchain_langgraph_sdk__WEBPACK_IMPORTED_MODULE_0__.Client({\n        apiUrl\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvaG9va3MvdXRpbHMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBa0Q7QUFFM0MsTUFBTUMsZUFBZTtJQUMxQixNQUFNQyxTQUFTQyxRQUFRQyxHQUFHLENBQUNDLG1CQUFtQixJQUFJO0lBQ2xELE9BQU8sSUFBSUwsNERBQU1BLENBQUM7UUFDaEJFO0lBQ0Y7QUFDRixFQUFFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQG9wZW5jYW52YXMvd2ViLy4vc3JjL2hvb2tzL3V0aWxzLnRzPzkwNTciXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQ2xpZW50IH0gZnJvbSBcIkBsYW5nY2hhaW4vbGFuZ2dyYXBoLXNka1wiO1xuXG5leHBvcnQgY29uc3QgY3JlYXRlQ2xpZW50ID0gKCkgPT4ge1xuICBjb25zdCBhcGlVcmwgPSBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19BUElfVVJMID8/IFwiaHR0cDovL2xvY2FsaG9zdDozMDAwL2FwaVwiO1xuICByZXR1cm4gbmV3IENsaWVudCh7XG4gICAgYXBpVXJsLFxuICB9KTtcbn07XG4iXSwibmFtZXMiOlsiQ2xpZW50IiwiY3JlYXRlQ2xpZW50IiwiYXBpVXJsIiwicHJvY2VzcyIsImVudiIsIk5FWFRfUFVCTElDX0FQSV9VUkwiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/utils.ts\n");

/***/ }),

/***/ "(ssr)/./src/workers/graph-stream/stream.worker.ts":
/*!***************************************************!*\
  !*** ./src/workers/graph-stream/stream.worker.ts ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _hooks_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/hooks/utils */ \"(ssr)/./src/hooks/utils.ts\");\n\n// Since workers can't directly access the client SDK, you'll need to recreate/import necessary parts\nconst ctx = self;\nctx.addEventListener(\"message\", async (event)=>{\n    try {\n        const { threadId, assistantId, input, modelName, modelConfigs } = event.data;\n        const client = (0,_hooks_utils__WEBPACK_IMPORTED_MODULE_0__.createClient)();\n        const stream = client.runs.stream(threadId, assistantId, {\n            input: input,\n            streamMode: \"events\",\n            config: {\n                configurable: {\n                    customModelName: modelName,\n                    modelConfig: modelConfigs[modelName]\n                }\n            }\n        });\n        for await (const chunk of stream){\n            // Serialize the chunk and post it back to the main thread\n            ctx.postMessage({\n                type: \"chunk\",\n                data: JSON.stringify(chunk)\n            });\n        }\n        ctx.postMessage({\n            type: \"done\"\n        });\n    } catch (error) {\n        ctx.postMessage({\n            type: \"error\",\n            error: error.message\n        });\n    }\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvd29ya2Vycy9ncmFwaC1zdHJlYW0vc3RyZWFtLndvcmtlci50cyIsIm1hcHBpbmdzIjoiOztBQUE2QztBQUc3QyxxR0FBcUc7QUFDckcsTUFBTUMsTUFBY0M7QUFFcEJELElBQUlFLGdCQUFnQixDQUFDLFdBQVcsT0FBT0M7SUFDckMsSUFBSTtRQUNGLE1BQU0sRUFBRUMsUUFBUSxFQUFFQyxXQUFXLEVBQUVDLEtBQUssRUFBRUMsU0FBUyxFQUFFQyxZQUFZLEVBQUUsR0FDN0RMLE1BQU1NLElBQUk7UUFFWixNQUFNQyxTQUFTWCwwREFBWUE7UUFFM0IsTUFBTVksU0FBU0QsT0FBT0UsSUFBSSxDQUFDRCxNQUFNLENBQUNQLFVBQVVDLGFBQWE7WUFDdkRDLE9BQU9BO1lBQ1BPLFlBQVk7WUFDWkMsUUFBUTtnQkFDTkMsY0FBYztvQkFDWkMsaUJBQWlCVDtvQkFDakJVLGFBQWFULFlBQVksQ0FBQ0QsVUFBdUM7Z0JBQ25FO1lBQ0Y7UUFDRjtRQUVBLFdBQVcsTUFBTVcsU0FBU1AsT0FBUTtZQUNoQywwREFBMEQ7WUFDMURYLElBQUltQixXQUFXLENBQUM7Z0JBQ2RDLE1BQU07Z0JBQ05YLE1BQU1ZLEtBQUtDLFNBQVMsQ0FBQ0o7WUFDdkI7UUFDRjtRQUVBbEIsSUFBSW1CLFdBQVcsQ0FBQztZQUFFQyxNQUFNO1FBQU87SUFDakMsRUFBRSxPQUFPRyxPQUFZO1FBQ25CdkIsSUFBSW1CLFdBQVcsQ0FBQztZQUNkQyxNQUFNO1lBQ05HLE9BQU9BLE1BQU1DLE9BQU87UUFDdEI7SUFDRjtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQG9wZW5jYW52YXMvd2ViLy4vc3JjL3dvcmtlcnMvZ3JhcGgtc3RyZWFtL3N0cmVhbS53b3JrZXIudHM/ODMyNCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjcmVhdGVDbGllbnQgfSBmcm9tIFwiQC9ob29rcy91dGlsc1wiO1xuaW1wb3J0IHsgU3RyZWFtQ29uZmlnIH0gZnJvbSBcIi4vc3RyZWFtV29ya2VyLnR5cGVzXCI7XG5cbi8vIFNpbmNlIHdvcmtlcnMgY2FuJ3QgZGlyZWN0bHkgYWNjZXNzIHRoZSBjbGllbnQgU0RLLCB5b3UnbGwgbmVlZCB0byByZWNyZWF0ZS9pbXBvcnQgbmVjZXNzYXJ5IHBhcnRzXG5jb25zdCBjdHg6IFdvcmtlciA9IHNlbGYgYXMgYW55O1xuXG5jdHguYWRkRXZlbnRMaXN0ZW5lcihcIm1lc3NhZ2VcIiwgYXN5bmMgKGV2ZW50OiBNZXNzYWdlRXZlbnQ8U3RyZWFtQ29uZmlnPikgPT4ge1xuICB0cnkge1xuICAgIGNvbnN0IHsgdGhyZWFkSWQsIGFzc2lzdGFudElkLCBpbnB1dCwgbW9kZWxOYW1lLCBtb2RlbENvbmZpZ3MgfSA9XG4gICAgICBldmVudC5kYXRhO1xuXG4gICAgY29uc3QgY2xpZW50ID0gY3JlYXRlQ2xpZW50KCk7XG5cbiAgICBjb25zdCBzdHJlYW0gPSBjbGllbnQucnVucy5zdHJlYW0odGhyZWFkSWQsIGFzc2lzdGFudElkLCB7XG4gICAgICBpbnB1dDogaW5wdXQgYXMgUmVjb3JkPHN0cmluZywgdW5rbm93bj4sXG4gICAgICBzdHJlYW1Nb2RlOiBcImV2ZW50c1wiLFxuICAgICAgY29uZmlnOiB7XG4gICAgICAgIGNvbmZpZ3VyYWJsZToge1xuICAgICAgICAgIGN1c3RvbU1vZGVsTmFtZTogbW9kZWxOYW1lLFxuICAgICAgICAgIG1vZGVsQ29uZmlnOiBtb2RlbENvbmZpZ3NbbW9kZWxOYW1lIGFzIGtleW9mIHR5cGVvZiBtb2RlbENvbmZpZ3NdLFxuICAgICAgICB9LFxuICAgICAgfSxcbiAgICB9KTtcblxuICAgIGZvciBhd2FpdCAoY29uc3QgY2h1bmsgb2Ygc3RyZWFtKSB7XG4gICAgICAvLyBTZXJpYWxpemUgdGhlIGNodW5rIGFuZCBwb3N0IGl0IGJhY2sgdG8gdGhlIG1haW4gdGhyZWFkXG4gICAgICBjdHgucG9zdE1lc3NhZ2Uoe1xuICAgICAgICB0eXBlOiBcImNodW5rXCIsXG4gICAgICAgIGRhdGE6IEpTT04uc3RyaW5naWZ5KGNodW5rKSxcbiAgICAgIH0pO1xuICAgIH1cblxuICAgIGN0eC5wb3N0TWVzc2FnZSh7IHR5cGU6IFwiZG9uZVwiIH0pO1xuICB9IGNhdGNoIChlcnJvcjogYW55KSB7XG4gICAgY3R4LnBvc3RNZXNzYWdlKHtcbiAgICAgIHR5cGU6IFwiZXJyb3JcIixcbiAgICAgIGVycm9yOiBlcnJvci5tZXNzYWdlLFxuICAgIH0pO1xuICB9XG59KTtcbiJdLCJuYW1lcyI6WyJjcmVhdGVDbGllbnQiLCJjdHgiLCJzZWxmIiwiYWRkRXZlbnRMaXN0ZW5lciIsImV2ZW50IiwidGhyZWFkSWQiLCJhc3Npc3RhbnRJZCIsImlucHV0IiwibW9kZWxOYW1lIiwibW9kZWxDb25maWdzIiwiZGF0YSIsImNsaWVudCIsInN0cmVhbSIsInJ1bnMiLCJzdHJlYW1Nb2RlIiwiY29uZmlnIiwiY29uZmlndXJhYmxlIiwiY3VzdG9tTW9kZWxOYW1lIiwibW9kZWxDb25maWciLCJjaHVuayIsInBvc3RNZXNzYWdlIiwidHlwZSIsIkpTT04iLCJzdHJpbmdpZnkiLCJlcnJvciIsIm1lc3NhZ2UiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/workers/graph-stream/stream.worker.ts\n");

/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			id: moduleId,
/******/ 			loaded: false,
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		var threw = true;
/******/ 		try {
/******/ 			__webpack_modules__[moduleId].call(module.exports, module, module.exports, __webpack_require__);
/******/ 			threw = false;
/******/ 		} finally {
/******/ 			if(threw) delete __webpack_module_cache__[moduleId];
/******/ 		}
/******/ 	
/******/ 		// Flag the module as loaded
/******/ 		module.loaded = true;
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/******/ 	// expose the modules object (__webpack_modules__)
/******/ 	__webpack_require__.m = __webpack_modules__;
/******/ 	
/******/ 	// the startup function
/******/ 	__webpack_require__.x = () => {
/******/ 		// Load entry module and return exports
/******/ 		// This entry module depends on other loaded chunks and execution need to be delayed
/******/ 		var __webpack_exports__ = __webpack_require__.O(undefined, ["vendor-chunks/p-queue","vendor-chunks/eventemitter3","vendor-chunks/retry","vendor-chunks/p-retry","vendor-chunks/p-timeout","vendor-chunks/p-finally","vendor-chunks/@langchain"], () => (__webpack_require__("(ssr)/./src/workers/graph-stream/stream.worker.ts")))
/******/ 		__webpack_exports__ = __webpack_require__.O(__webpack_exports__);
/******/ 		return __webpack_exports__;
/******/ 	};
/******/ 	
/************************************************************************/
/******/ 	/* webpack/runtime/chunk loaded */
/******/ 	(() => {
/******/ 		var deferred = [];
/******/ 		__webpack_require__.O = (result, chunkIds, fn, priority) => {
/******/ 			if(chunkIds) {
/******/ 				priority = priority || 0;
/******/ 				for(var i = deferred.length; i > 0 && deferred[i - 1][2] > priority; i--) deferred[i] = deferred[i - 1];
/******/ 				deferred[i] = [chunkIds, fn, priority];
/******/ 				return;
/******/ 			}
/******/ 			var notFulfilled = Infinity;
/******/ 			for (var i = 0; i < deferred.length; i++) {
/******/ 				var [chunkIds, fn, priority] = deferred[i];
/******/ 				var fulfilled = true;
/******/ 				for (var j = 0; j < chunkIds.length; j++) {
/******/ 					if ((priority & 1 === 0 || notFulfilled >= priority) && Object.keys(__webpack_require__.O).every((key) => (__webpack_require__.O[key](chunkIds[j])))) {
/******/ 						chunkIds.splice(j--, 1);
/******/ 					} else {
/******/ 						fulfilled = false;
/******/ 						if(priority < notFulfilled) notFulfilled = priority;
/******/ 					}
/******/ 				}
/******/ 				if(fulfilled) {
/******/ 					deferred.splice(i--, 1)
/******/ 					var r = fn();
/******/ 					if (r !== undefined) result = r;
/******/ 				}
/******/ 			}
/******/ 			return result;
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/define property getters */
/******/ 	(() => {
/******/ 		// define getter functions for harmony exports
/******/ 		__webpack_require__.d = (exports, definition) => {
/******/ 			for(var key in definition) {
/******/ 				if(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
/******/ 					Object.defineProperty(exports, key, { enumerable: true, get: definition[key] });
/******/ 				}
/******/ 			}
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/ensure chunk */
/******/ 	(() => {
/******/ 		__webpack_require__.f = {};
/******/ 		// This file contains only the entry chunk.
/******/ 		// The chunk loading function for additional chunks
/******/ 		__webpack_require__.e = (chunkId) => {
/******/ 			return Promise.all(Object.keys(__webpack_require__.f).reduce((promises, key) => {
/******/ 				__webpack_require__.f[key](chunkId, promises);
/******/ 				return promises;
/******/ 			}, []));
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/get javascript chunk filename */
/******/ 	(() => {
/******/ 		// This function allow to reference async chunks and sibling chunks for the entrypoint
/******/ 		__webpack_require__.u = (chunkId) => {
/******/ 			// return url for filenames based on template
/******/ 			return "" + chunkId + ".js";
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/hasOwnProperty shorthand */
/******/ 	(() => {
/******/ 		__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/make namespace object */
/******/ 	(() => {
/******/ 		// define __esModule on exports
/******/ 		__webpack_require__.r = (exports) => {
/******/ 			if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 				Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 			}
/******/ 			Object.defineProperty(exports, '__esModule', { value: true });
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/node module decorator */
/******/ 	(() => {
/******/ 		__webpack_require__.nmd = (module) => {
/******/ 			module.paths = [];
/******/ 			if (!module.children) module.children = [];
/******/ 			return module;
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/require chunk loading */
/******/ 	(() => {
/******/ 		// no baseURI
/******/ 		
/******/ 		// object to store loaded chunks
/******/ 		// "1" means "loaded", otherwise not loaded yet
/******/ 		var installedChunks = {
/******/ 			"_ssr_src_workers_graph-stream_stream_worker_ts": 1
/******/ 		};
/******/ 		
/******/ 		__webpack_require__.O.require = (chunkId) => (installedChunks[chunkId]);
/******/ 		
/******/ 		var installChunk = (chunk) => {
/******/ 			var moreModules = chunk.modules, chunkIds = chunk.ids, runtime = chunk.runtime;
/******/ 			for(var moduleId in moreModules) {
/******/ 				if(__webpack_require__.o(moreModules, moduleId)) {
/******/ 					__webpack_require__.m[moduleId] = moreModules[moduleId];
/******/ 				}
/******/ 			}
/******/ 			if(runtime) runtime(__webpack_require__);
/******/ 			for(var i = 0; i < chunkIds.length; i++)
/******/ 				installedChunks[chunkIds[i]] = 1;
/******/ 			__webpack_require__.O();
/******/ 		};
/******/ 		
/******/ 		// require() chunk loading for javascript
/******/ 		__webpack_require__.f.require = (chunkId, promises) => {
/******/ 			// "1" is the signal for "already loaded"
/******/ 			if(!installedChunks[chunkId]) {
/******/ 				if(true) { // all chunks have JS
/******/ 					installChunk(require("./" + __webpack_require__.u(chunkId)));
/******/ 				} else installedChunks[chunkId] = 1;
/******/ 			}
/******/ 		};
/******/ 		
/******/ 		// no external install chunk
/******/ 		
/******/ 		// no HMR
/******/ 		
/******/ 		// no HMR manifest
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/startup chunk dependencies */
/******/ 	(() => {
/******/ 		var next = __webpack_require__.x;
/******/ 		__webpack_require__.x = () => {
/******/ 			__webpack_require__.e("vendor-chunks/p-queue");
/******/ 			__webpack_require__.e("vendor-chunks/eventemitter3");
/******/ 			__webpack_require__.e("vendor-chunks/retry");
/******/ 			__webpack_require__.e("vendor-chunks/p-retry");
/******/ 			__webpack_require__.e("vendor-chunks/p-timeout");
/******/ 			__webpack_require__.e("vendor-chunks/p-finally");
/******/ 			__webpack_require__.e("vendor-chunks/@langchain");
/******/ 			return next();
/******/ 		};
/******/ 	})();
/******/ 	
/************************************************************************/
/******/ 	
/******/ 	// run startup
/******/ 	var __webpack_exports__ = __webpack_require__.x();
/******/ 	module.exports = __webpack_exports__;
/******/ 	
/******/ })()
;