"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_emoji-mart_dist_module_js"],{

/***/ "(app-pages-browser)/../../node_modules/emoji-mart/dist/module.js":
/*!****************************************************!*\
  !*** ../../node_modules/emoji-mart/dist/module.js ***!
  \****************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Data: function() { return /* binding */ $7adb23b0109cc36a$export$2d0294657ab35f1b; },\n/* harmony export */   Emoji: function() { return /* binding */ $331b4160623139bf$export$2e2bcd8739ae039; },\n/* harmony export */   FrequentlyUsed: function() { return /* binding */ $b22cfd0a55410b4f$export$2e2bcd8739ae039; },\n/* harmony export */   I18n: function() { return /* binding */ $7adb23b0109cc36a$export$dbe3113d60765c1a; },\n/* harmony export */   Picker: function() { return /* binding */ $efa000751917694d$export$2e2bcd8739ae039; },\n/* harmony export */   SafeFlags: function() { return /* binding */ $e6eae5155b87f591$export$bcb25aa587e9cb13; },\n/* harmony export */   SearchIndex: function() { return /* binding */ $c4d155af13ad4d4b$export$2e2bcd8739ae039; },\n/* harmony export */   Store: function() { return /* binding */ $f72b75cf796873c7$export$2e2bcd8739ae039; },\n/* harmony export */   getEmojiDataFromNative: function() { return /* binding */ $693b183b0a78708f$export$5ef5574deca44bc0; },\n/* harmony export */   init: function() { return /* binding */ $7adb23b0109cc36a$export$2cd8252107eb640b; }\n/* harmony export */ });\nfunction $parcel$interopDefault(a) {\n  return a && a.__esModule ? a.default : a;\n}\nfunction $c770c458706daa72$export$2e2bcd8739ae039(obj, key, value) {\n    if (key in obj) Object.defineProperty(obj, key, {\n        value: value,\n        enumerable: true,\n        configurable: true,\n        writable: true\n    });\n    else obj[key] = value;\n    return obj;\n}\n\n\nvar $fb96b826c0c5f37a$var$n, $fb96b826c0c5f37a$export$41c562ebe57d11e2, $fb96b826c0c5f37a$var$u, $fb96b826c0c5f37a$export$a8257692ac88316c, $fb96b826c0c5f37a$var$t, $fb96b826c0c5f37a$var$r, $fb96b826c0c5f37a$var$o, $fb96b826c0c5f37a$var$f, $fb96b826c0c5f37a$var$e = {}, $fb96b826c0c5f37a$var$c = [], $fb96b826c0c5f37a$var$s = /acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i;\nfunction $fb96b826c0c5f37a$var$a(n1, l1) {\n    for(var u1 in l1)n1[u1] = l1[u1];\n    return n1;\n}\nfunction $fb96b826c0c5f37a$var$h(n2) {\n    var l2 = n2.parentNode;\n    l2 && l2.removeChild(n2);\n}\nfunction $fb96b826c0c5f37a$export$c8a8987d4410bf2d(l3, u2, i1) {\n    var t1, r1, o1, f1 = {};\n    for(o1 in u2)\"key\" == o1 ? t1 = u2[o1] : \"ref\" == o1 ? r1 = u2[o1] : f1[o1] = u2[o1];\n    if (arguments.length > 2 && (f1.children = arguments.length > 3 ? $fb96b826c0c5f37a$var$n.call(arguments, 2) : i1), \"function\" == typeof l3 && null != l3.defaultProps) for(o1 in l3.defaultProps)void 0 === f1[o1] && (f1[o1] = l3.defaultProps[o1]);\n    return $fb96b826c0c5f37a$var$y(l3, f1, t1, r1, null);\n}\nfunction $fb96b826c0c5f37a$var$y(n3, i2, t2, r2, o2) {\n    var f2 = {\n        type: n3,\n        props: i2,\n        key: t2,\n        ref: r2,\n        __k: null,\n        __: null,\n        __b: 0,\n        __e: null,\n        __d: void 0,\n        __c: null,\n        __h: null,\n        constructor: void 0,\n        __v: null == o2 ? ++$fb96b826c0c5f37a$var$u : o2\n    };\n    return null == o2 && null != $fb96b826c0c5f37a$export$41c562ebe57d11e2.vnode && $fb96b826c0c5f37a$export$41c562ebe57d11e2.vnode(f2), f2;\n}\nfunction $fb96b826c0c5f37a$export$7d1e3a5e95ceca43() {\n    return {\n        current: null\n    };\n}\nfunction $fb96b826c0c5f37a$export$ffb0004e005737fa(n4) {\n    return n4.children;\n}\nfunction $fb96b826c0c5f37a$export$16fa2f45be04daa8(n5, l4) {\n    this.props = n5, this.context = l4;\n}\nfunction $fb96b826c0c5f37a$var$k(n6, l5) {\n    if (null == l5) return n6.__ ? $fb96b826c0c5f37a$var$k(n6.__, n6.__.__k.indexOf(n6) + 1) : null;\n    for(var u3; l5 < n6.__k.length; l5++)if (null != (u3 = n6.__k[l5]) && null != u3.__e) return u3.__e;\n    return \"function\" == typeof n6.type ? $fb96b826c0c5f37a$var$k(n6) : null;\n}\nfunction $fb96b826c0c5f37a$var$b(n7) {\n    var l6, u4;\n    if (null != (n7 = n7.__) && null != n7.__c) {\n        for(n7.__e = n7.__c.base = null, l6 = 0; l6 < n7.__k.length; l6++)if (null != (u4 = n7.__k[l6]) && null != u4.__e) {\n            n7.__e = n7.__c.base = u4.__e;\n            break;\n        }\n        return $fb96b826c0c5f37a$var$b(n7);\n    }\n}\nfunction $fb96b826c0c5f37a$var$m(n8) {\n    (!n8.__d && (n8.__d = !0) && $fb96b826c0c5f37a$var$t.push(n8) && !$fb96b826c0c5f37a$var$g.__r++ || $fb96b826c0c5f37a$var$o !== $fb96b826c0c5f37a$export$41c562ebe57d11e2.debounceRendering) && (($fb96b826c0c5f37a$var$o = $fb96b826c0c5f37a$export$41c562ebe57d11e2.debounceRendering) || $fb96b826c0c5f37a$var$r)($fb96b826c0c5f37a$var$g);\n}\nfunction $fb96b826c0c5f37a$var$g() {\n    for(var n9; $fb96b826c0c5f37a$var$g.__r = $fb96b826c0c5f37a$var$t.length;)n9 = $fb96b826c0c5f37a$var$t.sort(function(n10, l7) {\n        return n10.__v.__b - l7.__v.__b;\n    }), $fb96b826c0c5f37a$var$t = [], n9.some(function(n11) {\n        var l8, u5, i3, t3, r3, o3;\n        n11.__d && (r3 = (t3 = (l8 = n11).__v).__e, (o3 = l8.__P) && (u5 = [], (i3 = $fb96b826c0c5f37a$var$a({}, t3)).__v = t3.__v + 1, $fb96b826c0c5f37a$var$j(o3, t3, i3, l8.__n, void 0 !== o3.ownerSVGElement, null != t3.__h ? [\n            r3\n        ] : null, u5, null == r3 ? $fb96b826c0c5f37a$var$k(t3) : r3, t3.__h), $fb96b826c0c5f37a$var$z(u5, t3), t3.__e != r3 && $fb96b826c0c5f37a$var$b(t3)));\n    });\n}\nfunction $fb96b826c0c5f37a$var$w(n12, l9, u6, i4, t4, r4, o4, f3, s1, a1) {\n    var h1, v1, p1, _1, b1, m1, g1, w1 = i4 && i4.__k || $fb96b826c0c5f37a$var$c, A1 = w1.length;\n    for(u6.__k = [], h1 = 0; h1 < l9.length; h1++)if (null != (_1 = u6.__k[h1] = null == (_1 = l9[h1]) || \"boolean\" == typeof _1 ? null : \"string\" == typeof _1 || \"number\" == typeof _1 || \"bigint\" == typeof _1 ? $fb96b826c0c5f37a$var$y(null, _1, null, null, _1) : Array.isArray(_1) ? $fb96b826c0c5f37a$var$y($fb96b826c0c5f37a$export$ffb0004e005737fa, {\n        children: _1\n    }, null, null, null) : _1.__b > 0 ? $fb96b826c0c5f37a$var$y(_1.type, _1.props, _1.key, null, _1.__v) : _1)) {\n        if (_1.__ = u6, _1.__b = u6.__b + 1, null === (p1 = w1[h1]) || p1 && _1.key == p1.key && _1.type === p1.type) w1[h1] = void 0;\n        else for(v1 = 0; v1 < A1; v1++){\n            if ((p1 = w1[v1]) && _1.key == p1.key && _1.type === p1.type) {\n                w1[v1] = void 0;\n                break;\n            }\n            p1 = null;\n        }\n        $fb96b826c0c5f37a$var$j(n12, _1, p1 = p1 || $fb96b826c0c5f37a$var$e, t4, r4, o4, f3, s1, a1), b1 = _1.__e, (v1 = _1.ref) && p1.ref != v1 && (g1 || (g1 = []), p1.ref && g1.push(p1.ref, null, _1), g1.push(v1, _1.__c || b1, _1)), null != b1 ? (null == m1 && (m1 = b1), \"function\" == typeof _1.type && _1.__k === p1.__k ? _1.__d = s1 = $fb96b826c0c5f37a$var$x(_1, s1, n12) : s1 = $fb96b826c0c5f37a$var$P(n12, _1, p1, w1, b1, s1), \"function\" == typeof u6.type && (u6.__d = s1)) : s1 && p1.__e == s1 && s1.parentNode != n12 && (s1 = $fb96b826c0c5f37a$var$k(p1));\n    }\n    for(u6.__e = m1, h1 = A1; h1--;)null != w1[h1] && (\"function\" == typeof u6.type && null != w1[h1].__e && w1[h1].__e == u6.__d && (u6.__d = $fb96b826c0c5f37a$var$k(i4, h1 + 1)), $fb96b826c0c5f37a$var$N(w1[h1], w1[h1]));\n    if (g1) for(h1 = 0; h1 < g1.length; h1++)$fb96b826c0c5f37a$var$M(g1[h1], g1[++h1], g1[++h1]);\n}\nfunction $fb96b826c0c5f37a$var$x(n13, l10, u7) {\n    for(var i5, t5 = n13.__k, r5 = 0; t5 && r5 < t5.length; r5++)(i5 = t5[r5]) && (i5.__ = n13, l10 = \"function\" == typeof i5.type ? $fb96b826c0c5f37a$var$x(i5, l10, u7) : $fb96b826c0c5f37a$var$P(u7, i5, i5, t5, i5.__e, l10));\n    return l10;\n}\nfunction $fb96b826c0c5f37a$export$47e4c5b300681277(n14, l11) {\n    return l11 = l11 || [], null == n14 || \"boolean\" == typeof n14 || (Array.isArray(n14) ? n14.some(function(n15) {\n        $fb96b826c0c5f37a$export$47e4c5b300681277(n15, l11);\n    }) : l11.push(n14)), l11;\n}\nfunction $fb96b826c0c5f37a$var$P(n16, l12, u8, i6, t6, r6) {\n    var o5, f4, e1;\n    if (void 0 !== l12.__d) o5 = l12.__d, l12.__d = void 0;\n    else if (null == u8 || t6 != r6 || null == t6.parentNode) n: if (null == r6 || r6.parentNode !== n16) n16.appendChild(t6), o5 = null;\n    else {\n        for(f4 = r6, e1 = 0; (f4 = f4.nextSibling) && e1 < i6.length; e1 += 2)if (f4 == t6) break n;\n        n16.insertBefore(t6, r6), o5 = r6;\n    }\n    return void 0 !== o5 ? o5 : t6.nextSibling;\n}\nfunction $fb96b826c0c5f37a$var$C(n17, l13, u9, i7, t7) {\n    var r7;\n    for(r7 in u9)\"children\" === r7 || \"key\" === r7 || r7 in l13 || $fb96b826c0c5f37a$var$H(n17, r7, null, u9[r7], i7);\n    for(r7 in l13)t7 && \"function\" != typeof l13[r7] || \"children\" === r7 || \"key\" === r7 || \"value\" === r7 || \"checked\" === r7 || u9[r7] === l13[r7] || $fb96b826c0c5f37a$var$H(n17, r7, l13[r7], u9[r7], i7);\n}\nfunction $fb96b826c0c5f37a$var$$(n18, l14, u10) {\n    \"-\" === l14[0] ? n18.setProperty(l14, u10) : n18[l14] = null == u10 ? \"\" : \"number\" != typeof u10 || $fb96b826c0c5f37a$var$s.test(l14) ? u10 : u10 + \"px\";\n}\nfunction $fb96b826c0c5f37a$var$H(n19, l15, u11, i8, t8) {\n    var r8;\n    n: if (\"style\" === l15) {\n        if (\"string\" == typeof u11) n19.style.cssText = u11;\n        else {\n            if (\"string\" == typeof i8 && (n19.style.cssText = i8 = \"\"), i8) for(l15 in i8)u11 && l15 in u11 || $fb96b826c0c5f37a$var$$(n19.style, l15, \"\");\n            if (u11) for(l15 in u11)i8 && u11[l15] === i8[l15] || $fb96b826c0c5f37a$var$$(n19.style, l15, u11[l15]);\n        }\n    } else if (\"o\" === l15[0] && \"n\" === l15[1]) r8 = l15 !== (l15 = l15.replace(/Capture$/, \"\")), l15 = l15.toLowerCase() in n19 ? l15.toLowerCase().slice(2) : l15.slice(2), n19.l || (n19.l = {}), n19.l[l15 + r8] = u11, u11 ? i8 || n19.addEventListener(l15, r8 ? $fb96b826c0c5f37a$var$T : $fb96b826c0c5f37a$var$I, r8) : n19.removeEventListener(l15, r8 ? $fb96b826c0c5f37a$var$T : $fb96b826c0c5f37a$var$I, r8);\n    else if (\"dangerouslySetInnerHTML\" !== l15) {\n        if (t8) l15 = l15.replace(/xlink[H:h]/, \"h\").replace(/sName$/, \"s\");\n        else if (\"href\" !== l15 && \"list\" !== l15 && \"form\" !== l15 && \"tabIndex\" !== l15 && \"download\" !== l15 && l15 in n19) try {\n            n19[l15] = null == u11 ? \"\" : u11;\n            break n;\n        } catch (n) {}\n        \"function\" == typeof u11 || (null != u11 && (!1 !== u11 || \"a\" === l15[0] && \"r\" === l15[1]) ? n19.setAttribute(l15, u11) : n19.removeAttribute(l15));\n    }\n}\nfunction $fb96b826c0c5f37a$var$I(n20) {\n    this.l[n20.type + !1]($fb96b826c0c5f37a$export$41c562ebe57d11e2.event ? $fb96b826c0c5f37a$export$41c562ebe57d11e2.event(n20) : n20);\n}\nfunction $fb96b826c0c5f37a$var$T(n21) {\n    this.l[n21.type + !0]($fb96b826c0c5f37a$export$41c562ebe57d11e2.event ? $fb96b826c0c5f37a$export$41c562ebe57d11e2.event(n21) : n21);\n}\nfunction $fb96b826c0c5f37a$var$j(n22, u12, i9, t9, r9, o6, f5, e2, c1) {\n    var s2, h2, v2, y1, p2, k1, b2, m2, g2, x1, A2, P1 = u12.type;\n    if (void 0 !== u12.constructor) return null;\n    null != i9.__h && (c1 = i9.__h, e2 = u12.__e = i9.__e, u12.__h = null, o6 = [\n        e2\n    ]), (s2 = $fb96b826c0c5f37a$export$41c562ebe57d11e2.__b) && s2(u12);\n    try {\n        n: if (\"function\" == typeof P1) {\n            if (m2 = u12.props, g2 = (s2 = P1.contextType) && t9[s2.__c], x1 = s2 ? g2 ? g2.props.value : s2.__ : t9, i9.__c ? b2 = (h2 = u12.__c = i9.__c).__ = h2.__E : (\"prototype\" in P1 && P1.prototype.render ? u12.__c = h2 = new P1(m2, x1) : (u12.__c = h2 = new $fb96b826c0c5f37a$export$16fa2f45be04daa8(m2, x1), h2.constructor = P1, h2.render = $fb96b826c0c5f37a$var$O), g2 && g2.sub(h2), h2.props = m2, h2.state || (h2.state = {}), h2.context = x1, h2.__n = t9, v2 = h2.__d = !0, h2.__h = []), null == h2.__s && (h2.__s = h2.state), null != P1.getDerivedStateFromProps && (h2.__s == h2.state && (h2.__s = $fb96b826c0c5f37a$var$a({}, h2.__s)), $fb96b826c0c5f37a$var$a(h2.__s, P1.getDerivedStateFromProps(m2, h2.__s))), y1 = h2.props, p2 = h2.state, v2) null == P1.getDerivedStateFromProps && null != h2.componentWillMount && h2.componentWillMount(), null != h2.componentDidMount && h2.__h.push(h2.componentDidMount);\n            else {\n                if (null == P1.getDerivedStateFromProps && m2 !== y1 && null != h2.componentWillReceiveProps && h2.componentWillReceiveProps(m2, x1), !h2.__e && null != h2.shouldComponentUpdate && !1 === h2.shouldComponentUpdate(m2, h2.__s, x1) || u12.__v === i9.__v) {\n                    h2.props = m2, h2.state = h2.__s, u12.__v !== i9.__v && (h2.__d = !1), h2.__v = u12, u12.__e = i9.__e, u12.__k = i9.__k, u12.__k.forEach(function(n23) {\n                        n23 && (n23.__ = u12);\n                    }), h2.__h.length && f5.push(h2);\n                    break n;\n                }\n                null != h2.componentWillUpdate && h2.componentWillUpdate(m2, h2.__s, x1), null != h2.componentDidUpdate && h2.__h.push(function() {\n                    h2.componentDidUpdate(y1, p2, k1);\n                });\n            }\n            h2.context = x1, h2.props = m2, h2.state = h2.__s, (s2 = $fb96b826c0c5f37a$export$41c562ebe57d11e2.__r) && s2(u12), h2.__d = !1, h2.__v = u12, h2.__P = n22, s2 = h2.render(h2.props, h2.state, h2.context), h2.state = h2.__s, null != h2.getChildContext && (t9 = $fb96b826c0c5f37a$var$a($fb96b826c0c5f37a$var$a({}, t9), h2.getChildContext())), v2 || null == h2.getSnapshotBeforeUpdate || (k1 = h2.getSnapshotBeforeUpdate(y1, p2)), A2 = null != s2 && s2.type === $fb96b826c0c5f37a$export$ffb0004e005737fa && null == s2.key ? s2.props.children : s2, $fb96b826c0c5f37a$var$w(n22, Array.isArray(A2) ? A2 : [\n                A2\n            ], u12, i9, t9, r9, o6, f5, e2, c1), h2.base = u12.__e, u12.__h = null, h2.__h.length && f5.push(h2), b2 && (h2.__E = h2.__ = null), h2.__e = !1;\n        } else null == o6 && u12.__v === i9.__v ? (u12.__k = i9.__k, u12.__e = i9.__e) : u12.__e = $fb96b826c0c5f37a$var$L(i9.__e, u12, i9, t9, r9, o6, f5, c1);\n        (s2 = $fb96b826c0c5f37a$export$41c562ebe57d11e2.diffed) && s2(u12);\n    } catch (n24) {\n        u12.__v = null, (c1 || null != o6) && (u12.__e = e2, u12.__h = !!c1, o6[o6.indexOf(e2)] = null), $fb96b826c0c5f37a$export$41c562ebe57d11e2.__e(n24, u12, i9);\n    }\n}\nfunction $fb96b826c0c5f37a$var$z(n25, u13) {\n    $fb96b826c0c5f37a$export$41c562ebe57d11e2.__c && $fb96b826c0c5f37a$export$41c562ebe57d11e2.__c(u13, n25), n25.some(function(u14) {\n        try {\n            n25 = u14.__h, u14.__h = [], n25.some(function(n26) {\n                n26.call(u14);\n            });\n        } catch (n27) {\n            $fb96b826c0c5f37a$export$41c562ebe57d11e2.__e(n27, u14.__v);\n        }\n    });\n}\nfunction $fb96b826c0c5f37a$var$L(l16, u15, i10, t10, r10, o7, f6, c2) {\n    var s3, a2, v3, y2 = i10.props, p3 = u15.props, d1 = u15.type, _2 = 0;\n    if (\"svg\" === d1 && (r10 = !0), null != o7) {\n        for(; _2 < o7.length; _2++)if ((s3 = o7[_2]) && \"setAttribute\" in s3 == !!d1 && (d1 ? s3.localName === d1 : 3 === s3.nodeType)) {\n            l16 = s3, o7[_2] = null;\n            break;\n        }\n    }\n    if (null == l16) {\n        if (null === d1) return document.createTextNode(p3);\n        l16 = r10 ? document.createElementNS(\"http://www.w3.org/2000/svg\", d1) : document.createElement(d1, p3.is && p3), o7 = null, c2 = !1;\n    }\n    if (null === d1) y2 === p3 || c2 && l16.data === p3 || (l16.data = p3);\n    else {\n        if (o7 = o7 && $fb96b826c0c5f37a$var$n.call(l16.childNodes), a2 = (y2 = i10.props || $fb96b826c0c5f37a$var$e).dangerouslySetInnerHTML, v3 = p3.dangerouslySetInnerHTML, !c2) {\n            if (null != o7) for(y2 = {}, _2 = 0; _2 < l16.attributes.length; _2++)y2[l16.attributes[_2].name] = l16.attributes[_2].value;\n            (v3 || a2) && (v3 && (a2 && v3.__html == a2.__html || v3.__html === l16.innerHTML) || (l16.innerHTML = v3 && v3.__html || \"\"));\n        }\n        if ($fb96b826c0c5f37a$var$C(l16, p3, y2, r10, c2), v3) u15.__k = [];\n        else if (_2 = u15.props.children, $fb96b826c0c5f37a$var$w(l16, Array.isArray(_2) ? _2 : [\n            _2\n        ], u15, i10, t10, r10 && \"foreignObject\" !== d1, o7, f6, o7 ? o7[0] : i10.__k && $fb96b826c0c5f37a$var$k(i10, 0), c2), null != o7) for(_2 = o7.length; _2--;)null != o7[_2] && $fb96b826c0c5f37a$var$h(o7[_2]);\n        c2 || (\"value\" in p3 && void 0 !== (_2 = p3.value) && (_2 !== y2.value || _2 !== l16.value || \"progress\" === d1 && !_2) && $fb96b826c0c5f37a$var$H(l16, \"value\", _2, y2.value, !1), \"checked\" in p3 && void 0 !== (_2 = p3.checked) && _2 !== l16.checked && $fb96b826c0c5f37a$var$H(l16, \"checked\", _2, y2.checked, !1));\n    }\n    return l16;\n}\nfunction $fb96b826c0c5f37a$var$M(n28, u16, i11) {\n    try {\n        \"function\" == typeof n28 ? n28(u16) : n28.current = u16;\n    } catch (n29) {\n        $fb96b826c0c5f37a$export$41c562ebe57d11e2.__e(n29, i11);\n    }\n}\nfunction $fb96b826c0c5f37a$var$N(n30, u17, i12) {\n    var t11, r11;\n    if ($fb96b826c0c5f37a$export$41c562ebe57d11e2.unmount && $fb96b826c0c5f37a$export$41c562ebe57d11e2.unmount(n30), (t11 = n30.ref) && (t11.current && t11.current !== n30.__e || $fb96b826c0c5f37a$var$M(t11, null, u17)), null != (t11 = n30.__c)) {\n        if (t11.componentWillUnmount) try {\n            t11.componentWillUnmount();\n        } catch (n31) {\n            $fb96b826c0c5f37a$export$41c562ebe57d11e2.__e(n31, u17);\n        }\n        t11.base = t11.__P = null;\n    }\n    if (t11 = n30.__k) for(r11 = 0; r11 < t11.length; r11++)t11[r11] && $fb96b826c0c5f37a$var$N(t11[r11], u17, \"function\" != typeof n30.type);\n    i12 || null == n30.__e || $fb96b826c0c5f37a$var$h(n30.__e), n30.__e = n30.__d = void 0;\n}\nfunction $fb96b826c0c5f37a$var$O(n32, l, u18) {\n    return this.constructor(n32, u18);\n}\nfunction $fb96b826c0c5f37a$export$b3890eb0ae9dca99(u19, i13, t12) {\n    var r12, o8, f7;\n    $fb96b826c0c5f37a$export$41c562ebe57d11e2.__ && $fb96b826c0c5f37a$export$41c562ebe57d11e2.__(u19, i13), o8 = (r12 = \"function\" == typeof t12) ? null : t12 && t12.__k || i13.__k, f7 = [], $fb96b826c0c5f37a$var$j(i13, u19 = (!r12 && t12 || i13).__k = $fb96b826c0c5f37a$export$c8a8987d4410bf2d($fb96b826c0c5f37a$export$ffb0004e005737fa, null, [\n        u19\n    ]), o8 || $fb96b826c0c5f37a$var$e, $fb96b826c0c5f37a$var$e, void 0 !== i13.ownerSVGElement, !r12 && t12 ? [\n        t12\n    ] : o8 ? null : i13.firstChild ? $fb96b826c0c5f37a$var$n.call(i13.childNodes) : null, f7, !r12 && t12 ? t12 : o8 ? o8.__e : i13.firstChild, r12), $fb96b826c0c5f37a$var$z(f7, u19);\n}\nfunction $fb96b826c0c5f37a$export$fa8d919ba61d84db(n33, l17) {\n    $fb96b826c0c5f37a$export$b3890eb0ae9dca99(n33, l17, $fb96b826c0c5f37a$export$fa8d919ba61d84db);\n}\nfunction $fb96b826c0c5f37a$export$e530037191fcd5d7(l18, u20, i14) {\n    var t13, r13, o9, f8 = $fb96b826c0c5f37a$var$a({}, l18.props);\n    for(o9 in u20)\"key\" == o9 ? t13 = u20[o9] : \"ref\" == o9 ? r13 = u20[o9] : f8[o9] = u20[o9];\n    return arguments.length > 2 && (f8.children = arguments.length > 3 ? $fb96b826c0c5f37a$var$n.call(arguments, 2) : i14), $fb96b826c0c5f37a$var$y(l18.type, f8, t13 || l18.key, r13 || l18.ref, null);\n}\nfunction $fb96b826c0c5f37a$export$fd42f52fd3ae1109(n34, l19) {\n    var u21 = {\n        __c: l19 = \"__cC\" + $fb96b826c0c5f37a$var$f++,\n        __: n34,\n        Consumer: function(n35, l20) {\n            return n35.children(l20);\n        },\n        Provider: function(n36) {\n            var u22, i15;\n            return this.getChildContext || (u22 = [], (i15 = {})[l19] = this, this.getChildContext = function() {\n                return i15;\n            }, this.shouldComponentUpdate = function(n37) {\n                this.props.value !== n37.value && u22.some($fb96b826c0c5f37a$var$m);\n            }, this.sub = function(n38) {\n                u22.push(n38);\n                var l21 = n38.componentWillUnmount;\n                n38.componentWillUnmount = function() {\n                    u22.splice(u22.indexOf(n38), 1), l21 && l21.call(n38);\n                };\n            }), n36.children;\n        }\n    };\n    return u21.Provider.__ = u21.Consumer.contextType = u21;\n}\n$fb96b826c0c5f37a$var$n = $fb96b826c0c5f37a$var$c.slice, $fb96b826c0c5f37a$export$41c562ebe57d11e2 = {\n    __e: function(n39, l22) {\n        for(var u23, i16, t14; l22 = l22.__;)if ((u23 = l22.__c) && !u23.__) try {\n            if ((i16 = u23.constructor) && null != i16.getDerivedStateFromError && (u23.setState(i16.getDerivedStateFromError(n39)), t14 = u23.__d), null != u23.componentDidCatch && (u23.componentDidCatch(n39), t14 = u23.__d), t14) return u23.__E = u23;\n        } catch (l23) {\n            n39 = l23;\n        }\n        throw n39;\n    }\n}, $fb96b826c0c5f37a$var$u = 0, $fb96b826c0c5f37a$export$a8257692ac88316c = function(n40) {\n    return null != n40 && void 0 === n40.constructor;\n}, $fb96b826c0c5f37a$export$16fa2f45be04daa8.prototype.setState = function(n41, l24) {\n    var u24;\n    u24 = null != this.__s && this.__s !== this.state ? this.__s : this.__s = $fb96b826c0c5f37a$var$a({}, this.state), \"function\" == typeof n41 && (n41 = n41($fb96b826c0c5f37a$var$a({}, u24), this.props)), n41 && $fb96b826c0c5f37a$var$a(u24, n41), null != n41 && this.__v && (l24 && this.__h.push(l24), $fb96b826c0c5f37a$var$m(this));\n}, $fb96b826c0c5f37a$export$16fa2f45be04daa8.prototype.forceUpdate = function(n42) {\n    this.__v && (this.__e = !0, n42 && this.__h.push(n42), $fb96b826c0c5f37a$var$m(this));\n}, $fb96b826c0c5f37a$export$16fa2f45be04daa8.prototype.render = $fb96b826c0c5f37a$export$ffb0004e005737fa, $fb96b826c0c5f37a$var$t = [], $fb96b826c0c5f37a$var$r = \"function\" == typeof Promise ? Promise.prototype.then.bind(Promise.resolve()) : setTimeout, $fb96b826c0c5f37a$var$g.__r = 0, $fb96b826c0c5f37a$var$f = 0;\n\n\n\nvar $bd9dd35321b03dd4$var$o = 0;\nfunction $bd9dd35321b03dd4$export$34b9dba7ce09269b(_1, e1, n, t, f) {\n    var l, s, u = {};\n    for(s in e1)\"ref\" == s ? l = e1[s] : u[s] = e1[s];\n    var a = {\n        type: _1,\n        props: u,\n        key: n,\n        ref: l,\n        __k: null,\n        __: null,\n        __b: 0,\n        __e: null,\n        __d: void 0,\n        __c: null,\n        __h: null,\n        constructor: void 0,\n        __v: --$bd9dd35321b03dd4$var$o,\n        __source: t,\n        __self: f\n    };\n    if (\"function\" == typeof _1 && (l = _1.defaultProps)) for(s in l)void 0 === u[s] && (u[s] = l[s]);\n    return (0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).vnode && (0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).vnode(a), a;\n}\n\n\n\nfunction $f72b75cf796873c7$var$set(key, value) {\n    try {\n        window.localStorage[`emoji-mart.${key}`] = JSON.stringify(value);\n    } catch (error) {}\n}\nfunction $f72b75cf796873c7$var$get(key) {\n    try {\n        const value = window.localStorage[`emoji-mart.${key}`];\n        if (value) return JSON.parse(value);\n    } catch (error) {}\n}\nvar $f72b75cf796873c7$export$2e2bcd8739ae039 = {\n    set: $f72b75cf796873c7$var$set,\n    get: $f72b75cf796873c7$var$get\n};\n\n\nconst $c84d045dcc34faf5$var$CACHE = new Map();\nconst $c84d045dcc34faf5$var$VERSIONS = [\n    {\n        v: 15,\n        emoji: \"\\uD83E\\uDEE8\"\n    },\n    {\n        v: 14,\n        emoji: \"\\uD83E\\uDEE0\"\n    },\n    {\n        v: 13.1,\n        emoji: \"\\uD83D\\uDE36\\u200D\\uD83C\\uDF2B\\uFE0F\"\n    },\n    {\n        v: 13,\n        emoji: \"\\uD83E\\uDD78\"\n    },\n    {\n        v: 12.1,\n        emoji: \"\\uD83E\\uDDD1\\u200D\\uD83E\\uDDB0\"\n    },\n    {\n        v: 12,\n        emoji: \"\\uD83E\\uDD71\"\n    },\n    {\n        v: 11,\n        emoji: \"\\uD83E\\uDD70\"\n    },\n    {\n        v: 5,\n        emoji: \"\\uD83E\\uDD29\"\n    },\n    {\n        v: 4,\n        emoji: \"\\uD83D\\uDC71\\u200D\\u2640\\uFE0F\"\n    },\n    {\n        v: 3,\n        emoji: \"\\uD83E\\uDD23\"\n    },\n    {\n        v: 2,\n        emoji: \"\\uD83D\\uDC4B\\uD83C\\uDFFB\"\n    },\n    {\n        v: 1,\n        emoji: \"\\uD83D\\uDE43\"\n    }, \n];\nfunction $c84d045dcc34faf5$var$latestVersion() {\n    for (const { v: v , emoji: emoji  } of $c84d045dcc34faf5$var$VERSIONS){\n        if ($c84d045dcc34faf5$var$isSupported(emoji)) return v;\n    }\n}\nfunction $c84d045dcc34faf5$var$noCountryFlags() {\n    if ($c84d045dcc34faf5$var$isSupported(\"\\uD83C\\uDDE8\\uD83C\\uDDE6\")) return false;\n    return true;\n}\nfunction $c84d045dcc34faf5$var$isSupported(emoji) {\n    if ($c84d045dcc34faf5$var$CACHE.has(emoji)) return $c84d045dcc34faf5$var$CACHE.get(emoji);\n    const supported = $c84d045dcc34faf5$var$isEmojiSupported(emoji);\n    $c84d045dcc34faf5$var$CACHE.set(emoji, supported);\n    return supported;\n}\n// https://github.com/koala-interactive/is-emoji-supported\nconst $c84d045dcc34faf5$var$isEmojiSupported = (()=>{\n    let ctx = null;\n    try {\n        if (!navigator.userAgent.includes(\"jsdom\")) ctx = document.createElement(\"canvas\").getContext(\"2d\", {\n            willReadFrequently: true\n        });\n    } catch  {}\n    // Not in browser env\n    if (!ctx) return ()=>false;\n    const CANVAS_HEIGHT = 25;\n    const CANVAS_WIDTH = 20;\n    const textSize = Math.floor(CANVAS_HEIGHT / 2);\n    // Initialize convas context\n    ctx.font = textSize + \"px Arial, Sans-Serif\";\n    ctx.textBaseline = \"top\";\n    ctx.canvas.width = CANVAS_WIDTH * 2;\n    ctx.canvas.height = CANVAS_HEIGHT;\n    return (unicode)=>{\n        ctx.clearRect(0, 0, CANVAS_WIDTH * 2, CANVAS_HEIGHT);\n        // Draw in red on the left\n        ctx.fillStyle = \"#FF0000\";\n        ctx.fillText(unicode, 0, 22);\n        // Draw in blue on right\n        ctx.fillStyle = \"#0000FF\";\n        ctx.fillText(unicode, CANVAS_WIDTH, 22);\n        const a = ctx.getImageData(0, 0, CANVAS_WIDTH, CANVAS_HEIGHT).data;\n        const count = a.length;\n        let i = 0;\n        // Search the first visible pixel\n        for(; i < count && !a[i + 3]; i += 4);\n        // No visible pixel\n        if (i >= count) return false;\n        // Emoji has immutable color, so we check the color of the emoji in two different colors\n        // the result show be the same.\n        const x = CANVAS_WIDTH + i / 4 % CANVAS_WIDTH;\n        const y = Math.floor(i / 4 / CANVAS_WIDTH);\n        const b = ctx.getImageData(x, y, 1, 1).data;\n        if (a[i] !== b[0] || a[i + 2] !== b[2]) return false;\n        // Some emojis are a contraction of different ones, so if it's not\n        // supported, it will show multiple characters\n        if (ctx.measureText(unicode).width >= CANVAS_WIDTH) return false;\n        // Supported\n        return true;\n    };\n})();\nvar $c84d045dcc34faf5$export$2e2bcd8739ae039 = {\n    latestVersion: $c84d045dcc34faf5$var$latestVersion,\n    noCountryFlags: $c84d045dcc34faf5$var$noCountryFlags\n};\n\n\n\nconst $b22cfd0a55410b4f$var$DEFAULTS = [\n    \"+1\",\n    \"grinning\",\n    \"kissing_heart\",\n    \"heart_eyes\",\n    \"laughing\",\n    \"stuck_out_tongue_winking_eye\",\n    \"sweat_smile\",\n    \"joy\",\n    \"scream\",\n    \"disappointed\",\n    \"unamused\",\n    \"weary\",\n    \"sob\",\n    \"sunglasses\",\n    \"heart\", \n];\nlet $b22cfd0a55410b4f$var$Index = null;\nfunction $b22cfd0a55410b4f$var$add(emoji) {\n    $b22cfd0a55410b4f$var$Index || ($b22cfd0a55410b4f$var$Index = (0, $f72b75cf796873c7$export$2e2bcd8739ae039).get(\"frequently\") || {});\n    const emojiId = emoji.id || emoji;\n    if (!emojiId) return;\n    $b22cfd0a55410b4f$var$Index[emojiId] || ($b22cfd0a55410b4f$var$Index[emojiId] = 0);\n    $b22cfd0a55410b4f$var$Index[emojiId] += 1;\n    (0, $f72b75cf796873c7$export$2e2bcd8739ae039).set(\"last\", emojiId);\n    (0, $f72b75cf796873c7$export$2e2bcd8739ae039).set(\"frequently\", $b22cfd0a55410b4f$var$Index);\n}\nfunction $b22cfd0a55410b4f$var$get({ maxFrequentRows: maxFrequentRows , perLine: perLine  }) {\n    if (!maxFrequentRows) return [];\n    $b22cfd0a55410b4f$var$Index || ($b22cfd0a55410b4f$var$Index = (0, $f72b75cf796873c7$export$2e2bcd8739ae039).get(\"frequently\"));\n    let emojiIds = [];\n    if (!$b22cfd0a55410b4f$var$Index) {\n        $b22cfd0a55410b4f$var$Index = {};\n        for(let i in $b22cfd0a55410b4f$var$DEFAULTS.slice(0, perLine)){\n            const emojiId = $b22cfd0a55410b4f$var$DEFAULTS[i];\n            $b22cfd0a55410b4f$var$Index[emojiId] = perLine - i;\n            emojiIds.push(emojiId);\n        }\n        return emojiIds;\n    }\n    const max = maxFrequentRows * perLine;\n    const last = (0, $f72b75cf796873c7$export$2e2bcd8739ae039).get(\"last\");\n    for(let emojiId in $b22cfd0a55410b4f$var$Index)emojiIds.push(emojiId);\n    emojiIds.sort((a, b)=>{\n        const aScore = $b22cfd0a55410b4f$var$Index[b];\n        const bScore = $b22cfd0a55410b4f$var$Index[a];\n        if (aScore == bScore) return a.localeCompare(b);\n        return aScore - bScore;\n    });\n    if (emojiIds.length > max) {\n        const removedIds = emojiIds.slice(max);\n        emojiIds = emojiIds.slice(0, max);\n        for (let removedId of removedIds){\n            if (removedId == last) continue;\n            delete $b22cfd0a55410b4f$var$Index[removedId];\n        }\n        if (last && emojiIds.indexOf(last) == -1) {\n            delete $b22cfd0a55410b4f$var$Index[emojiIds[emojiIds.length - 1]];\n            emojiIds.splice(-1, 1, last);\n        }\n        (0, $f72b75cf796873c7$export$2e2bcd8739ae039).set(\"frequently\", $b22cfd0a55410b4f$var$Index);\n    }\n    return emojiIds;\n}\nvar $b22cfd0a55410b4f$export$2e2bcd8739ae039 = {\n    add: $b22cfd0a55410b4f$var$add,\n    get: $b22cfd0a55410b4f$var$get,\n    DEFAULTS: $b22cfd0a55410b4f$var$DEFAULTS\n};\n\n\nvar $8d50d93417ef682a$exports = {};\n$8d50d93417ef682a$exports = JSON.parse('{\"search\":\"Search\",\"search_no_results_1\":\"Oh no!\",\"search_no_results_2\":\"That emoji couldn\\u2019t be found\",\"pick\":\"Pick an emoji\\u2026\",\"add_custom\":\"Add custom emoji\",\"categories\":{\"activity\":\"Activity\",\"custom\":\"Custom\",\"flags\":\"Flags\",\"foods\":\"Food & Drink\",\"frequent\":\"Frequently used\",\"nature\":\"Animals & Nature\",\"objects\":\"Objects\",\"people\":\"Smileys & People\",\"places\":\"Travel & Places\",\"search\":\"Search Results\",\"symbols\":\"Symbols\"},\"skins\":{\"1\":\"Default\",\"2\":\"Light\",\"3\":\"Medium-Light\",\"4\":\"Medium\",\"5\":\"Medium-Dark\",\"6\":\"Dark\",\"choose\":\"Choose default skin tone\"}}');\n\n\nvar $b247ea80b67298d5$export$2e2bcd8739ae039 = {\n    autoFocus: {\n        value: false\n    },\n    dynamicWidth: {\n        value: false\n    },\n    emojiButtonColors: {\n        value: null\n    },\n    emojiButtonRadius: {\n        value: \"100%\"\n    },\n    emojiButtonSize: {\n        value: 36\n    },\n    emojiSize: {\n        value: 24\n    },\n    emojiVersion: {\n        value: 15,\n        choices: [\n            1,\n            2,\n            3,\n            4,\n            5,\n            11,\n            12,\n            12.1,\n            13,\n            13.1,\n            14,\n            15\n        ]\n    },\n    exceptEmojis: {\n        value: []\n    },\n    icons: {\n        value: \"auto\",\n        choices: [\n            \"auto\",\n            \"outline\",\n            \"solid\"\n        ]\n    },\n    locale: {\n        value: \"en\",\n        choices: [\n            \"en\",\n            \"ar\",\n            \"be\",\n            \"cs\",\n            \"de\",\n            \"es\",\n            \"fa\",\n            \"fi\",\n            \"fr\",\n            \"hi\",\n            \"it\",\n            \"ja\",\n            \"ko\",\n            \"nl\",\n            \"pl\",\n            \"pt\",\n            \"ru\",\n            \"sa\",\n            \"tr\",\n            \"uk\",\n            \"vi\",\n            \"zh\", \n        ]\n    },\n    maxFrequentRows: {\n        value: 4\n    },\n    navPosition: {\n        value: \"top\",\n        choices: [\n            \"top\",\n            \"bottom\",\n            \"none\"\n        ]\n    },\n    noCountryFlags: {\n        value: false\n    },\n    noResultsEmoji: {\n        value: null\n    },\n    perLine: {\n        value: 9\n    },\n    previewEmoji: {\n        value: null\n    },\n    previewPosition: {\n        value: \"bottom\",\n        choices: [\n            \"top\",\n            \"bottom\",\n            \"none\"\n        ]\n    },\n    searchPosition: {\n        value: \"sticky\",\n        choices: [\n            \"sticky\",\n            \"static\",\n            \"none\"\n        ]\n    },\n    set: {\n        value: \"native\",\n        choices: [\n            \"native\",\n            \"apple\",\n            \"facebook\",\n            \"google\",\n            \"twitter\"\n        ]\n    },\n    skin: {\n        value: 1,\n        choices: [\n            1,\n            2,\n            3,\n            4,\n            5,\n            6\n        ]\n    },\n    skinTonePosition: {\n        value: \"preview\",\n        choices: [\n            \"preview\",\n            \"search\",\n            \"none\"\n        ]\n    },\n    theme: {\n        value: \"auto\",\n        choices: [\n            \"auto\",\n            \"light\",\n            \"dark\"\n        ]\n    },\n    // Data\n    categories: null,\n    categoryIcons: null,\n    custom: null,\n    data: null,\n    i18n: null,\n    // Callbacks\n    getImageURL: null,\n    getSpritesheetURL: null,\n    onAddCustomEmoji: null,\n    onClickOutside: null,\n    onEmojiSelect: null,\n    // Deprecated\n    stickySearch: {\n        deprecated: true,\n        value: true\n    }\n};\n\n\n\nlet $7adb23b0109cc36a$export$dbe3113d60765c1a = null;\nlet $7adb23b0109cc36a$export$2d0294657ab35f1b = null;\nconst $7adb23b0109cc36a$var$fetchCache = {};\nasync function $7adb23b0109cc36a$var$fetchJSON(src) {\n    if ($7adb23b0109cc36a$var$fetchCache[src]) return $7adb23b0109cc36a$var$fetchCache[src];\n    const response = await fetch(src);\n    const json = await response.json();\n    $7adb23b0109cc36a$var$fetchCache[src] = json;\n    return json;\n}\nlet $7adb23b0109cc36a$var$promise = null;\nlet $7adb23b0109cc36a$var$initiated = false;\nlet $7adb23b0109cc36a$var$initCallback = null;\nlet $7adb23b0109cc36a$var$initialized = false;\nfunction $7adb23b0109cc36a$export$2cd8252107eb640b(options, { caller: caller  } = {}) {\n    $7adb23b0109cc36a$var$promise || ($7adb23b0109cc36a$var$promise = new Promise((resolve)=>{\n        $7adb23b0109cc36a$var$initCallback = resolve;\n    }));\n    if (options) $7adb23b0109cc36a$var$_init(options);\n    else if (caller && !$7adb23b0109cc36a$var$initialized) console.warn(`\\`${caller}\\` requires data to be initialized first. Promise will be pending until \\`init\\` is called.`);\n    return $7adb23b0109cc36a$var$promise;\n}\nasync function $7adb23b0109cc36a$var$_init(props) {\n    $7adb23b0109cc36a$var$initialized = true;\n    let { emojiVersion: emojiVersion , set: set , locale: locale  } = props;\n    emojiVersion || (emojiVersion = (0, $b247ea80b67298d5$export$2e2bcd8739ae039).emojiVersion.value);\n    set || (set = (0, $b247ea80b67298d5$export$2e2bcd8739ae039).set.value);\n    locale || (locale = (0, $b247ea80b67298d5$export$2e2bcd8739ae039).locale.value);\n    if (!$7adb23b0109cc36a$export$2d0294657ab35f1b) {\n        $7adb23b0109cc36a$export$2d0294657ab35f1b = (typeof props.data === \"function\" ? await props.data() : props.data) || await $7adb23b0109cc36a$var$fetchJSON(`https://cdn.jsdelivr.net/npm/@emoji-mart/data@latest/sets/${emojiVersion}/${set}.json`);\n        $7adb23b0109cc36a$export$2d0294657ab35f1b.emoticons = {};\n        $7adb23b0109cc36a$export$2d0294657ab35f1b.natives = {};\n        $7adb23b0109cc36a$export$2d0294657ab35f1b.categories.unshift({\n            id: \"frequent\",\n            emojis: []\n        });\n        for(const alias in $7adb23b0109cc36a$export$2d0294657ab35f1b.aliases){\n            const emojiId = $7adb23b0109cc36a$export$2d0294657ab35f1b.aliases[alias];\n            const emoji = $7adb23b0109cc36a$export$2d0294657ab35f1b.emojis[emojiId];\n            if (!emoji) continue;\n            emoji.aliases || (emoji.aliases = []);\n            emoji.aliases.push(alias);\n        }\n        $7adb23b0109cc36a$export$2d0294657ab35f1b.originalCategories = $7adb23b0109cc36a$export$2d0294657ab35f1b.categories;\n    } else $7adb23b0109cc36a$export$2d0294657ab35f1b.categories = $7adb23b0109cc36a$export$2d0294657ab35f1b.categories.filter((c)=>{\n        const isCustom = !!c.name;\n        if (!isCustom) return true;\n        return false;\n    });\n    $7adb23b0109cc36a$export$dbe3113d60765c1a = (typeof props.i18n === \"function\" ? await props.i18n() : props.i18n) || (locale == \"en\" ? (0, (/*@__PURE__*/$parcel$interopDefault($8d50d93417ef682a$exports))) : await $7adb23b0109cc36a$var$fetchJSON(`https://cdn.jsdelivr.net/npm/@emoji-mart/data@latest/i18n/${locale}.json`));\n    if (props.custom) for(let i in props.custom){\n        i = parseInt(i);\n        const category = props.custom[i];\n        const prevCategory = props.custom[i - 1];\n        if (!category.emojis || !category.emojis.length) continue;\n        category.id || (category.id = `custom_${i + 1}`);\n        category.name || (category.name = $7adb23b0109cc36a$export$dbe3113d60765c1a.categories.custom);\n        if (prevCategory && !category.icon) category.target = prevCategory.target || prevCategory;\n        $7adb23b0109cc36a$export$2d0294657ab35f1b.categories.push(category);\n        for (const emoji of category.emojis)$7adb23b0109cc36a$export$2d0294657ab35f1b.emojis[emoji.id] = emoji;\n    }\n    if (props.categories) $7adb23b0109cc36a$export$2d0294657ab35f1b.categories = $7adb23b0109cc36a$export$2d0294657ab35f1b.originalCategories.filter((c)=>{\n        return props.categories.indexOf(c.id) != -1;\n    }).sort((c1, c2)=>{\n        const i1 = props.categories.indexOf(c1.id);\n        const i2 = props.categories.indexOf(c2.id);\n        return i1 - i2;\n    });\n    let latestVersionSupport = null;\n    let noCountryFlags = null;\n    if (set == \"native\") {\n        latestVersionSupport = (0, $c84d045dcc34faf5$export$2e2bcd8739ae039).latestVersion();\n        noCountryFlags = props.noCountryFlags || (0, $c84d045dcc34faf5$export$2e2bcd8739ae039).noCountryFlags();\n    }\n    let categoryIndex = $7adb23b0109cc36a$export$2d0294657ab35f1b.categories.length;\n    let resetSearchIndex = false;\n    while(categoryIndex--){\n        const category = $7adb23b0109cc36a$export$2d0294657ab35f1b.categories[categoryIndex];\n        if (category.id == \"frequent\") {\n            let { maxFrequentRows: maxFrequentRows , perLine: perLine  } = props;\n            maxFrequentRows = maxFrequentRows >= 0 ? maxFrequentRows : (0, $b247ea80b67298d5$export$2e2bcd8739ae039).maxFrequentRows.value;\n            perLine || (perLine = (0, $b247ea80b67298d5$export$2e2bcd8739ae039).perLine.value);\n            category.emojis = (0, $b22cfd0a55410b4f$export$2e2bcd8739ae039).get({\n                maxFrequentRows: maxFrequentRows,\n                perLine: perLine\n            });\n        }\n        if (!category.emojis || !category.emojis.length) {\n            $7adb23b0109cc36a$export$2d0294657ab35f1b.categories.splice(categoryIndex, 1);\n            continue;\n        }\n        const { categoryIcons: categoryIcons  } = props;\n        if (categoryIcons) {\n            const icon = categoryIcons[category.id];\n            if (icon && !category.icon) category.icon = icon;\n        }\n        let emojiIndex = category.emojis.length;\n        while(emojiIndex--){\n            const emojiId = category.emojis[emojiIndex];\n            const emoji = emojiId.id ? emojiId : $7adb23b0109cc36a$export$2d0294657ab35f1b.emojis[emojiId];\n            const ignore = ()=>{\n                category.emojis.splice(emojiIndex, 1);\n            };\n            if (!emoji || props.exceptEmojis && props.exceptEmojis.includes(emoji.id)) {\n                ignore();\n                continue;\n            }\n            if (latestVersionSupport && emoji.version > latestVersionSupport) {\n                ignore();\n                continue;\n            }\n            if (noCountryFlags && category.id == \"flags\") {\n                if (!(0, $e6eae5155b87f591$export$bcb25aa587e9cb13).includes(emoji.id)) {\n                    ignore();\n                    continue;\n                }\n            }\n            if (!emoji.search) {\n                resetSearchIndex = true;\n                emoji.search = \",\" + [\n                    [\n                        emoji.id,\n                        false\n                    ],\n                    [\n                        emoji.name,\n                        true\n                    ],\n                    [\n                        emoji.keywords,\n                        false\n                    ],\n                    [\n                        emoji.emoticons,\n                        false\n                    ], \n                ].map(([strings, split])=>{\n                    if (!strings) return;\n                    return (Array.isArray(strings) ? strings : [\n                        strings\n                    ]).map((string)=>{\n                        return (split ? string.split(/[-|_|\\s]+/) : [\n                            string\n                        ]).map((s)=>s.toLowerCase());\n                    }).flat();\n                }).flat().filter((a)=>a && a.trim()).join(\",\");\n                if (emoji.emoticons) for (const emoticon of emoji.emoticons){\n                    if ($7adb23b0109cc36a$export$2d0294657ab35f1b.emoticons[emoticon]) continue;\n                    $7adb23b0109cc36a$export$2d0294657ab35f1b.emoticons[emoticon] = emoji.id;\n                }\n                let skinIndex = 0;\n                for (const skin of emoji.skins){\n                    if (!skin) continue;\n                    skinIndex++;\n                    const { native: native  } = skin;\n                    if (native) {\n                        $7adb23b0109cc36a$export$2d0294657ab35f1b.natives[native] = emoji.id;\n                        emoji.search += `,${native}`;\n                    }\n                    const skinShortcodes = skinIndex == 1 ? \"\" : `:skin-tone-${skinIndex}:`;\n                    skin.shortcodes = `:${emoji.id}:${skinShortcodes}`;\n                }\n            }\n        }\n    }\n    if (resetSearchIndex) (0, $c4d155af13ad4d4b$export$2e2bcd8739ae039).reset();\n    $7adb23b0109cc36a$var$initCallback();\n}\nfunction $7adb23b0109cc36a$export$75fe5f91d452f94b(props, defaultProps, element) {\n    props || (props = {});\n    const _props = {};\n    for(let k in defaultProps)_props[k] = $7adb23b0109cc36a$export$88c9ddb45cea7241(k, props, defaultProps, element);\n    return _props;\n}\nfunction $7adb23b0109cc36a$export$88c9ddb45cea7241(propName, props, defaultProps, element) {\n    const defaults = defaultProps[propName];\n    let value = element && element.getAttribute(propName) || (props[propName] != null && props[propName] != undefined ? props[propName] : null);\n    if (!defaults) return value;\n    if (value != null && defaults.value && typeof defaults.value != typeof value) {\n        if (typeof defaults.value == \"boolean\") value = value == \"false\" ? false : true;\n        else value = defaults.value.constructor(value);\n    }\n    if (defaults.transform && value) value = defaults.transform(value);\n    if (value == null || defaults.choices && defaults.choices.indexOf(value) == -1) value = defaults.value;\n    return value;\n}\n\n\nconst $c4d155af13ad4d4b$var$SHORTCODES_REGEX = /^(?:\\:([^\\:]+)\\:)(?:\\:skin-tone-(\\d)\\:)?$/;\nlet $c4d155af13ad4d4b$var$Pool = null;\nfunction $c4d155af13ad4d4b$var$get(emojiId) {\n    if (emojiId.id) return emojiId;\n    return (0, $7adb23b0109cc36a$export$2d0294657ab35f1b).emojis[emojiId] || (0, $7adb23b0109cc36a$export$2d0294657ab35f1b).emojis[(0, $7adb23b0109cc36a$export$2d0294657ab35f1b).aliases[emojiId]] || (0, $7adb23b0109cc36a$export$2d0294657ab35f1b).emojis[(0, $7adb23b0109cc36a$export$2d0294657ab35f1b).natives[emojiId]];\n}\nfunction $c4d155af13ad4d4b$var$reset() {\n    $c4d155af13ad4d4b$var$Pool = null;\n}\nasync function $c4d155af13ad4d4b$var$search(value, { maxResults: maxResults , caller: caller  } = {}) {\n    if (!value || !value.trim().length) return null;\n    maxResults || (maxResults = 90);\n    await (0, $7adb23b0109cc36a$export$2cd8252107eb640b)(null, {\n        caller: caller || \"SearchIndex.search\"\n    });\n    const values = value.toLowerCase().replace(/(\\w)-/, \"$1 \").split(/[\\s|,]+/).filter((word, i, words)=>{\n        return word.trim() && words.indexOf(word) == i;\n    });\n    if (!values.length) return;\n    let pool = $c4d155af13ad4d4b$var$Pool || ($c4d155af13ad4d4b$var$Pool = Object.values((0, $7adb23b0109cc36a$export$2d0294657ab35f1b).emojis));\n    let results, scores;\n    for (const value1 of values){\n        if (!pool.length) break;\n        results = [];\n        scores = {};\n        for (const emoji of pool){\n            if (!emoji.search) continue;\n            const score = emoji.search.indexOf(`,${value1}`);\n            if (score == -1) continue;\n            results.push(emoji);\n            scores[emoji.id] || (scores[emoji.id] = 0);\n            scores[emoji.id] += emoji.id == value1 ? 0 : score + 1;\n        }\n        pool = results;\n    }\n    if (results.length < 2) return results;\n    results.sort((a, b)=>{\n        const aScore = scores[a.id];\n        const bScore = scores[b.id];\n        if (aScore == bScore) return a.id.localeCompare(b.id);\n        return aScore - bScore;\n    });\n    if (results.length > maxResults) results = results.slice(0, maxResults);\n    return results;\n}\nvar $c4d155af13ad4d4b$export$2e2bcd8739ae039 = {\n    search: $c4d155af13ad4d4b$var$search,\n    get: $c4d155af13ad4d4b$var$get,\n    reset: $c4d155af13ad4d4b$var$reset,\n    SHORTCODES_REGEX: $c4d155af13ad4d4b$var$SHORTCODES_REGEX\n};\n\n\nconst $e6eae5155b87f591$export$bcb25aa587e9cb13 = [\n    \"checkered_flag\",\n    \"crossed_flags\",\n    \"pirate_flag\",\n    \"rainbow-flag\",\n    \"transgender_flag\",\n    \"triangular_flag_on_post\",\n    \"waving_black_flag\",\n    \"waving_white_flag\", \n];\n\n\nfunction $693b183b0a78708f$export$9cb4719e2e525b7a(a, b) {\n    return Array.isArray(a) && Array.isArray(b) && a.length === b.length && a.every((val, index)=>val == b[index]);\n}\nasync function $693b183b0a78708f$export$e772c8ff12451969(frames = 1) {\n    for(let _ in [\n        ...Array(frames).keys()\n    ])await new Promise(requestAnimationFrame);\n}\nfunction $693b183b0a78708f$export$d10ac59fbe52a745(emoji, { skinIndex: skinIndex = 0  } = {}) {\n    const skin = emoji.skins[skinIndex] || (()=>{\n        skinIndex = 0;\n        return emoji.skins[skinIndex];\n    })();\n    const emojiData = {\n        id: emoji.id,\n        name: emoji.name,\n        native: skin.native,\n        unified: skin.unified,\n        keywords: emoji.keywords,\n        shortcodes: skin.shortcodes || emoji.shortcodes\n    };\n    if (emoji.skins.length > 1) emojiData.skin = skinIndex + 1;\n    if (skin.src) emojiData.src = skin.src;\n    if (emoji.aliases && emoji.aliases.length) emojiData.aliases = emoji.aliases;\n    if (emoji.emoticons && emoji.emoticons.length) emojiData.emoticons = emoji.emoticons;\n    return emojiData;\n}\nasync function $693b183b0a78708f$export$5ef5574deca44bc0(nativeString) {\n    const results = await (0, $c4d155af13ad4d4b$export$2e2bcd8739ae039).search(nativeString, {\n        maxResults: 1,\n        caller: \"getEmojiDataFromNative\"\n    });\n    if (!results || !results.length) return null;\n    const emoji = results[0];\n    let skinIndex = 0;\n    for (let skin of emoji.skins){\n        if (skin.native == nativeString) break;\n        skinIndex++;\n    }\n    return $693b183b0a78708f$export$d10ac59fbe52a745(emoji, {\n        skinIndex: skinIndex\n    });\n}\n\n\n\n\n\nconst $fcccfb36ed0cde68$var$categories = {\n    activity: {\n        outline: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"path\", {\n                d: \"M12 0C5.373 0 0 5.372 0 12c0 6.627 5.373 12 12 12 6.628 0 12-5.373 12-12 0-6.628-5.372-12-12-12m9.949 11H17.05c.224-2.527 1.232-4.773 1.968-6.113A9.966 9.966 0 0 1 21.949 11M13 11V2.051a9.945 9.945 0 0 1 4.432 1.564c-.858 1.491-2.156 4.22-2.392 7.385H13zm-2 0H8.961c-.238-3.165-1.536-5.894-2.393-7.385A9.95 9.95 0 0 1 11 2.051V11zm0 2v8.949a9.937 9.937 0 0 1-4.432-1.564c.857-1.492 2.155-4.221 2.393-7.385H11zm4.04 0c.236 3.164 1.534 5.893 2.392 7.385A9.92 9.92 0 0 1 13 21.949V13h2.04zM4.982 4.887C5.718 6.227 6.726 8.473 6.951 11h-4.9a9.977 9.977 0 0 1 2.931-6.113M2.051 13h4.9c-.226 2.527-1.233 4.771-1.969 6.113A9.972 9.972 0 0 1 2.051 13m16.967 6.113c-.735-1.342-1.744-3.586-1.968-6.113h4.899a9.961 9.961 0 0 1-2.931 6.113\"\n            })\n        }),\n        solid: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            viewBox: \"0 0 512 512\",\n            children: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"path\", {\n                d: \"M16.17 337.5c0 44.98 7.565 83.54 13.98 107.9C35.22 464.3 50.46 496 174.9 496c9.566 0 19.59-.4707 29.84-1.271L17.33 307.3C16.53 317.6 16.17 327.7 16.17 337.5zM495.8 174.5c0-44.98-7.565-83.53-13.98-107.9c-4.688-17.54-18.34-31.23-36.04-35.95C435.5 27.91 392.9 16 337 16c-9.564 0-19.59 .4707-29.84 1.271l187.5 187.5C495.5 194.4 495.8 184.3 495.8 174.5zM26.77 248.8l236.3 236.3c142-36.1 203.9-150.4 222.2-221.1L248.9 26.87C106.9 62.96 45.07 177.2 26.77 248.8zM256 335.1c0 9.141-7.474 16-16 16c-4.094 0-8.188-1.564-11.31-4.689L164.7 283.3C161.6 280.2 160 276.1 160 271.1c0-8.529 6.865-16 16-16c4.095 0 8.189 1.562 11.31 4.688l64.01 64C254.4 327.8 256 331.9 256 335.1zM304 287.1c0 9.141-7.474 16-16 16c-4.094 0-8.188-1.564-11.31-4.689L212.7 235.3C209.6 232.2 208 228.1 208 223.1c0-9.141 7.473-16 16-16c4.094 0 8.188 1.562 11.31 4.688l64.01 64.01C302.5 279.8 304 283.9 304 287.1zM256 175.1c0-9.141 7.473-16 16-16c4.094 0 8.188 1.562 11.31 4.688l64.01 64.01c3.125 3.125 4.688 7.219 4.688 11.31c0 9.133-7.468 16-16 16c-4.094 0-8.189-1.562-11.31-4.688l-64.01-64.01C257.6 184.2 256 180.1 256 175.1z\"\n            })\n        })\n    },\n    custom: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        viewBox: \"0 0 448 512\",\n        children: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"path\", {\n            d: \"M417.1 368c-5.937 10.27-16.69 16-27.75 16c-5.422 0-10.92-1.375-15.97-4.281L256 311.4V448c0 17.67-14.33 32-31.1 32S192 465.7 192 448V311.4l-118.3 68.29C68.67 382.6 63.17 384 57.75 384c-11.06 0-21.81-5.734-27.75-16c-8.828-15.31-3.594-34.88 11.72-43.72L159.1 256L41.72 187.7C26.41 178.9 21.17 159.3 29.1 144C36.63 132.5 49.26 126.7 61.65 128.2C65.78 128.7 69.88 130.1 73.72 132.3L192 200.6V64c0-17.67 14.33-32 32-32S256 46.33 256 64v136.6l118.3-68.29c3.838-2.213 7.939-3.539 12.07-4.051C398.7 126.7 411.4 132.5 417.1 144c8.828 15.31 3.594 34.88-11.72 43.72L288 256l118.3 68.28C421.6 333.1 426.8 352.7 417.1 368z\"\n        })\n    }),\n    flags: {\n        outline: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"path\", {\n                d: \"M0 0l6.084 24H8L1.916 0zM21 5h-4l-1-4H4l3 12h3l1 4h13L21 5zM6.563 3h7.875l2 8H8.563l-2-8zm8.832 10l-2.856 1.904L12.063 13h3.332zM19 13l-1.5-6h1.938l2 8H16l3-2z\"\n            })\n        }),\n        solid: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            viewBox: \"0 0 512 512\",\n            children: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"path\", {\n                d: \"M64 496C64 504.8 56.75 512 48 512h-32C7.25 512 0 504.8 0 496V32c0-17.75 14.25-32 32-32s32 14.25 32 32V496zM476.3 0c-6.365 0-13.01 1.35-19.34 4.233c-45.69 20.86-79.56 27.94-107.8 27.94c-59.96 0-94.81-31.86-163.9-31.87C160.9 .3055 131.6 4.867 96 15.75v350.5c32-9.984 59.87-14.1 84.85-14.1c73.63 0 124.9 31.78 198.6 31.78c31.91 0 68.02-5.971 111.1-23.09C504.1 355.9 512 344.4 512 332.1V30.73C512 11.1 495.3 0 476.3 0z\"\n            })\n        })\n    },\n    foods: {\n        outline: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"path\", {\n                d: \"M17 4.978c-1.838 0-2.876.396-3.68.934.513-1.172 1.768-2.934 4.68-2.934a1 1 0 0 0 0-2c-2.921 0-4.629 1.365-5.547 2.512-.064.078-.119.162-.18.244C11.73 1.838 10.798.023 9.207.023 8.579.022 7.85.306 7 .978 5.027 2.54 5.329 3.902 6.492 4.999 3.609 5.222 0 7.352 0 12.969c0 4.582 4.961 11.009 9 11.009 1.975 0 2.371-.486 3-1 .629.514 1.025 1 3 1 4.039 0 9-6.418 9-11 0-5.953-4.055-8-7-8M8.242 2.546c.641-.508.943-.523.965-.523.426.169.975 1.405 1.357 3.055-1.527-.629-2.741-1.352-2.98-1.846.059-.112.241-.356.658-.686M15 21.978c-1.08 0-1.21-.109-1.559-.402l-.176-.146c-.367-.302-.816-.452-1.266-.452s-.898.15-1.266.452l-.176.146c-.347.292-.477.402-1.557.402-2.813 0-7-5.389-7-9.009 0-5.823 4.488-5.991 5-5.991 1.939 0 2.484.471 3.387 1.251l.323.276a1.995 1.995 0 0 0 2.58 0l.323-.276c.902-.78 1.447-1.251 3.387-1.251.512 0 5 .168 5 6 0 3.617-4.187 9-7 9\"\n            })\n        }),\n        solid: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            viewBox: \"0 0 512 512\",\n            children: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"path\", {\n                d: \"M481.9 270.1C490.9 279.1 496 291.3 496 304C496 316.7 490.9 328.9 481.9 337.9C472.9 346.9 460.7 352 448 352H64C51.27 352 39.06 346.9 30.06 337.9C21.06 328.9 16 316.7 16 304C16 291.3 21.06 279.1 30.06 270.1C39.06 261.1 51.27 256 64 256H448C460.7 256 472.9 261.1 481.9 270.1zM475.3 388.7C478.3 391.7 480 395.8 480 400V416C480 432.1 473.3 449.3 461.3 461.3C449.3 473.3 432.1 480 416 480H96C79.03 480 62.75 473.3 50.75 461.3C38.74 449.3 32 432.1 32 416V400C32 395.8 33.69 391.7 36.69 388.7C39.69 385.7 43.76 384 48 384H464C468.2 384 472.3 385.7 475.3 388.7zM50.39 220.8C45.93 218.6 42.03 215.5 38.97 211.6C35.91 207.7 33.79 203.2 32.75 198.4C31.71 193.5 31.8 188.5 32.99 183.7C54.98 97.02 146.5 32 256 32C365.5 32 457 97.02 479 183.7C480.2 188.5 480.3 193.5 479.2 198.4C478.2 203.2 476.1 207.7 473 211.6C469.1 215.5 466.1 218.6 461.6 220.8C457.2 222.9 452.3 224 447.3 224H64.67C59.73 224 54.84 222.9 50.39 220.8zM372.7 116.7C369.7 119.7 368 123.8 368 128C368 131.2 368.9 134.3 370.7 136.9C372.5 139.5 374.1 141.6 377.9 142.8C380.8 143.1 384 144.3 387.1 143.7C390.2 143.1 393.1 141.6 395.3 139.3C397.6 137.1 399.1 134.2 399.7 131.1C400.3 128 399.1 124.8 398.8 121.9C397.6 118.1 395.5 116.5 392.9 114.7C390.3 112.9 387.2 111.1 384 111.1C379.8 111.1 375.7 113.7 372.7 116.7V116.7zM244.7 84.69C241.7 87.69 240 91.76 240 96C240 99.16 240.9 102.3 242.7 104.9C244.5 107.5 246.1 109.6 249.9 110.8C252.8 111.1 256 112.3 259.1 111.7C262.2 111.1 265.1 109.6 267.3 107.3C269.6 105.1 271.1 102.2 271.7 99.12C272.3 96.02 271.1 92.8 270.8 89.88C269.6 86.95 267.5 84.45 264.9 82.7C262.3 80.94 259.2 79.1 256 79.1C251.8 79.1 247.7 81.69 244.7 84.69V84.69zM116.7 116.7C113.7 119.7 112 123.8 112 128C112 131.2 112.9 134.3 114.7 136.9C116.5 139.5 118.1 141.6 121.9 142.8C124.8 143.1 128 144.3 131.1 143.7C134.2 143.1 137.1 141.6 139.3 139.3C141.6 137.1 143.1 134.2 143.7 131.1C144.3 128 143.1 124.8 142.8 121.9C141.6 118.1 139.5 116.5 136.9 114.7C134.3 112.9 131.2 111.1 128 111.1C123.8 111.1 119.7 113.7 116.7 116.7L116.7 116.7z\"\n            })\n        })\n    },\n    frequent: {\n        outline: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            viewBox: \"0 0 24 24\",\n            children: [\n                /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"path\", {\n                    d: \"M13 4h-2l-.001 7H9v2h2v2h2v-2h4v-2h-4z\"\n                }),\n                /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"path\", {\n                    d: \"M12 0C5.373 0 0 5.373 0 12s5.373 12 12 12 12-5.373 12-12S18.627 0 12 0m0 22C6.486 22 2 17.514 2 12S6.486 2 12 2s10 4.486 10 10-4.486 10-10 10\"\n                })\n            ]\n        }),\n        solid: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            viewBox: \"0 0 512 512\",\n            children: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"path\", {\n                d: \"M256 512C114.6 512 0 397.4 0 256C0 114.6 114.6 0 256 0C397.4 0 512 114.6 512 256C512 397.4 397.4 512 256 512zM232 256C232 264 236 271.5 242.7 275.1L338.7 339.1C349.7 347.3 364.6 344.3 371.1 333.3C379.3 322.3 376.3 307.4 365.3 300L280 243.2V120C280 106.7 269.3 96 255.1 96C242.7 96 231.1 106.7 231.1 120L232 256z\"\n            })\n        })\n    },\n    nature: {\n        outline: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            viewBox: \"0 0 24 24\",\n            children: [\n                /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"path\", {\n                    d: \"M15.5 8a1.5 1.5 0 1 0 .001 3.001A1.5 1.5 0 0 0 15.5 8M8.5 8a1.5 1.5 0 1 0 .001 3.001A1.5 1.5 0 0 0 8.5 8\"\n                }),\n                /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"path\", {\n                    d: \"M18.933 0h-.027c-.97 0-2.138.787-3.018 1.497-1.274-.374-2.612-.51-3.887-.51-1.285 0-2.616.133-3.874.517C7.245.79 6.069 0 5.093 0h-.027C3.352 0 .07 2.67.002 7.026c-.039 2.479.276 4.238 1.04 5.013.254.258.882.677 1.295.882.191 3.177.922 5.238 2.536 6.38.897.637 2.187.949 3.2 1.102C8.04 20.6 8 20.795 8 21c0 1.773 2.35 3 4 3 1.648 0 4-1.227 4-3 0-.201-.038-.393-.072-.586 2.573-.385 5.435-1.877 5.925-7.587.396-.22.887-.568 1.104-.788.763-.774 1.079-2.534 1.04-5.013C23.929 2.67 20.646 0 18.933 0M3.223 9.135c-.237.281-.837 1.155-.884 1.238-.15-.41-.368-1.349-.337-3.291.051-3.281 2.478-4.972 3.091-5.031.256.015.731.27 1.265.646-1.11 1.171-2.275 2.915-2.352 5.125-.133.546-.398.858-.783 1.313M12 22c-.901 0-1.954-.693-2-1 0-.654.475-1.236 1-1.602V20a1 1 0 1 0 2 0v-.602c.524.365 1 .947 1 1.602-.046.307-1.099 1-2 1m3-3.48v.02a4.752 4.752 0 0 0-1.262-1.02c1.092-.516 2.239-1.334 2.239-2.217 0-1.842-1.781-2.195-3.977-2.195-2.196 0-3.978.354-3.978 2.195 0 .883 1.148 1.701 2.238 2.217A4.8 4.8 0 0 0 9 18.539v-.025c-1-.076-2.182-.281-2.973-.842-1.301-.92-1.838-3.045-1.853-6.478l.023-.041c.496-.826 1.49-1.45 1.804-3.102 0-2.047 1.357-3.631 2.362-4.522C9.37 3.178 10.555 3 11.948 3c1.447 0 2.685.192 3.733.57 1 .9 2.316 2.465 2.316 4.48.313 1.651 1.307 2.275 1.803 3.102.035.058.068.117.102.178-.059 5.967-1.949 7.01-4.902 7.19m6.628-8.202c-.037-.065-.074-.13-.113-.195a7.587 7.587 0 0 0-.739-.987c-.385-.455-.648-.768-.782-1.313-.076-2.209-1.241-3.954-2.353-5.124.531-.376 1.004-.63 1.261-.647.636.071 3.044 1.764 3.096 5.031.027 1.81-.347 3.218-.37 3.235\"\n                })\n            ]\n        }),\n        solid: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            viewBox: \"0 0 576 512\",\n            children: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"path\", {\n                d: \"M332.7 19.85C334.6 8.395 344.5 0 356.1 0C363.6 0 370.6 3.52 375.1 9.502L392 32H444.1C456.8 32 469.1 37.06 478.1 46.06L496 64H552C565.3 64 576 74.75 576 88V112C576 156.2 540.2 192 496 192H426.7L421.6 222.5L309.6 158.5L332.7 19.85zM448 64C439.2 64 432 71.16 432 80C432 88.84 439.2 96 448 96C456.8 96 464 88.84 464 80C464 71.16 456.8 64 448 64zM416 256.1V480C416 497.7 401.7 512 384 512H352C334.3 512 320 497.7 320 480V364.8C295.1 377.1 268.8 384 240 384C211.2 384 184 377.1 160 364.8V480C160 497.7 145.7 512 128 512H96C78.33 512 64 497.7 64 480V249.8C35.23 238.9 12.64 214.5 4.836 183.3L.9558 167.8C-3.331 150.6 7.094 133.2 24.24 128.1C41.38 124.7 58.76 135.1 63.05 152.2L66.93 167.8C70.49 182 83.29 191.1 97.97 191.1H303.8L416 256.1z\"\n            })\n        })\n    },\n    objects: {\n        outline: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            viewBox: \"0 0 24 24\",\n            children: [\n                /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"path\", {\n                    d: \"M12 0a9 9 0 0 0-5 16.482V21s2.035 3 5 3 5-3 5-3v-4.518A9 9 0 0 0 12 0zm0 2c3.86 0 7 3.141 7 7s-3.14 7-7 7-7-3.141-7-7 3.14-7 7-7zM9 17.477c.94.332 1.946.523 3 .523s2.06-.19 3-.523v.834c-.91.436-1.925.689-3 .689a6.924 6.924 0 0 1-3-.69v-.833zm.236 3.07A8.854 8.854 0 0 0 12 21c.965 0 1.888-.167 2.758-.451C14.155 21.173 13.153 22 12 22c-1.102 0-2.117-.789-2.764-1.453z\"\n                }),\n                /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"path\", {\n                    d: \"M14.745 12.449h-.004c-.852-.024-1.188-.858-1.577-1.824-.421-1.061-.703-1.561-1.182-1.566h-.009c-.481 0-.783.497-1.235 1.537-.436.982-.801 1.811-1.636 1.791l-.276-.043c-.565-.171-.853-.691-1.284-1.794-.125-.313-.202-.632-.27-.913-.051-.213-.127-.53-.195-.634C7.067 9.004 7.039 9 6.99 9A1 1 0 0 1 7 7h.01c1.662.017 2.015 1.373 2.198 2.134.486-.981 1.304-2.058 2.797-2.075 1.531.018 2.28 1.153 2.731 2.141l.002-.008C14.944 8.424 15.327 7 16.979 7h.032A1 1 0 1 1 17 9h-.011c-.149.076-.256.474-.319.709a6.484 6.484 0 0 1-.311.951c-.429.973-.79 1.789-1.614 1.789\"\n                })\n            ]\n        }),\n        solid: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            viewBox: \"0 0 384 512\",\n            children: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"path\", {\n                d: \"M112.1 454.3c0 6.297 1.816 12.44 5.284 17.69l17.14 25.69c5.25 7.875 17.17 14.28 26.64 14.28h61.67c9.438 0 21.36-6.401 26.61-14.28l17.08-25.68c2.938-4.438 5.348-12.37 5.348-17.7L272 415.1h-160L112.1 454.3zM191.4 .0132C89.44 .3257 16 82.97 16 175.1c0 44.38 16.44 84.84 43.56 115.8c16.53 18.84 42.34 58.23 52.22 91.45c.0313 .25 .0938 .5166 .125 .7823h160.2c.0313-.2656 .0938-.5166 .125-.7823c9.875-33.22 35.69-72.61 52.22-91.45C351.6 260.8 368 220.4 368 175.1C368 78.61 288.9-.2837 191.4 .0132zM192 96.01c-44.13 0-80 35.89-80 79.1C112 184.8 104.8 192 96 192S80 184.8 80 176c0-61.76 50.25-111.1 112-111.1c8.844 0 16 7.159 16 16S200.8 96.01 192 96.01z\"\n            })\n        })\n    },\n    people: {\n        outline: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            viewBox: \"0 0 24 24\",\n            children: [\n                /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"path\", {\n                    d: \"M12 0C5.373 0 0 5.373 0 12s5.373 12 12 12 12-5.373 12-12S18.627 0 12 0m0 22C6.486 22 2 17.514 2 12S6.486 2 12 2s10 4.486 10 10-4.486 10-10 10\"\n                }),\n                /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"path\", {\n                    d: \"M8 7a2 2 0 1 0-.001 3.999A2 2 0 0 0 8 7M16 7a2 2 0 1 0-.001 3.999A2 2 0 0 0 16 7M15.232 15c-.693 1.195-1.87 2-3.349 2-1.477 0-2.655-.805-3.347-2H15m3-2H6a6 6 0 1 0 12 0\"\n                })\n            ]\n        }),\n        solid: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            viewBox: \"0 0 512 512\",\n            children: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"path\", {\n                d: \"M0 256C0 114.6 114.6 0 256 0C397.4 0 512 114.6 512 256C512 397.4 397.4 512 256 512C114.6 512 0 397.4 0 256zM256 432C332.1 432 396.2 382 415.2 314.1C419.1 300.4 407.8 288 393.6 288H118.4C104.2 288 92.92 300.4 96.76 314.1C115.8 382 179.9 432 256 432V432zM176.4 160C158.7 160 144.4 174.3 144.4 192C144.4 209.7 158.7 224 176.4 224C194 224 208.4 209.7 208.4 192C208.4 174.3 194 160 176.4 160zM336.4 224C354 224 368.4 209.7 368.4 192C368.4 174.3 354 160 336.4 160C318.7 160 304.4 174.3 304.4 192C304.4 209.7 318.7 224 336.4 224z\"\n            })\n        })\n    },\n    places: {\n        outline: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            viewBox: \"0 0 24 24\",\n            children: [\n                /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"path\", {\n                    d: \"M6.5 12C5.122 12 4 13.121 4 14.5S5.122 17 6.5 17 9 15.879 9 14.5 7.878 12 6.5 12m0 3c-.275 0-.5-.225-.5-.5s.225-.5.5-.5.5.225.5.5-.225.5-.5.5M17.5 12c-1.378 0-2.5 1.121-2.5 2.5s1.122 2.5 2.5 2.5 2.5-1.121 2.5-2.5-1.122-2.5-2.5-2.5m0 3c-.275 0-.5-.225-.5-.5s.225-.5.5-.5.5.225.5.5-.225.5-.5.5\"\n                }),\n                /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"path\", {\n                    d: \"M22.482 9.494l-1.039-.346L21.4 9h.6c.552 0 1-.439 1-.992 0-.006-.003-.008-.003-.008H23c0-1-.889-2-1.984-2h-.642l-.731-1.717C19.262 3.012 18.091 2 16.764 2H7.236C5.909 2 4.738 3.012 4.357 4.283L3.626 6h-.642C1.889 6 1 7 1 8h.003S1 8.002 1 8.008C1 8.561 1.448 9 2 9h.6l-.043.148-1.039.346a2.001 2.001 0 0 0-1.359 2.097l.751 7.508a1 1 0 0 0 .994.901H3v1c0 1.103.896 2 2 2h2c1.104 0 2-.897 2-2v-1h6v1c0 1.103.896 2 2 2h2c1.104 0 2-.897 2-2v-1h1.096a.999.999 0 0 0 .994-.901l.751-7.508a2.001 2.001 0 0 0-1.359-2.097M6.273 4.857C6.402 4.43 6.788 4 7.236 4h9.527c.448 0 .834.43.963.857L19.313 9H4.688l1.585-4.143zM7 21H5v-1h2v1zm12 0h-2v-1h2v1zm2.189-3H2.811l-.662-6.607L3 11h18l.852.393L21.189 18z\"\n                })\n            ]\n        }),\n        solid: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            viewBox: \"0 0 512 512\",\n            children: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"path\", {\n                d: \"M39.61 196.8L74.8 96.29C88.27 57.78 124.6 32 165.4 32H346.6C387.4 32 423.7 57.78 437.2 96.29L472.4 196.8C495.6 206.4 512 229.3 512 256V448C512 465.7 497.7 480 480 480H448C430.3 480 416 465.7 416 448V400H96V448C96 465.7 81.67 480 64 480H32C14.33 480 0 465.7 0 448V256C0 229.3 16.36 206.4 39.61 196.8V196.8zM109.1 192H402.9L376.8 117.4C372.3 104.6 360.2 96 346.6 96H165.4C151.8 96 139.7 104.6 135.2 117.4L109.1 192zM96 256C78.33 256 64 270.3 64 288C64 305.7 78.33 320 96 320C113.7 320 128 305.7 128 288C128 270.3 113.7 256 96 256zM416 320C433.7 320 448 305.7 448 288C448 270.3 433.7 256 416 256C398.3 256 384 270.3 384 288C384 305.7 398.3 320 416 320z\"\n            })\n        })\n    },\n    symbols: {\n        outline: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"path\", {\n                d: \"M0 0h11v2H0zM4 11h3V6h4V4H0v2h4zM15.5 17c1.381 0 2.5-1.116 2.5-2.493s-1.119-2.493-2.5-2.493S13 13.13 13 14.507 14.119 17 15.5 17m0-2.986c.276 0 .5.222.5.493 0 .272-.224.493-.5.493s-.5-.221-.5-.493.224-.493.5-.493M21.5 19.014c-1.381 0-2.5 1.116-2.5 2.493S20.119 24 21.5 24s2.5-1.116 2.5-2.493-1.119-2.493-2.5-2.493m0 2.986a.497.497 0 0 1-.5-.493c0-.271.224-.493.5-.493s.5.222.5.493a.497.497 0 0 1-.5.493M22 13l-9 9 1.513 1.5 8.99-9.009zM17 11c2.209 0 4-1.119 4-2.5V2s.985-.161 1.498.949C23.01 4.055 23 6 23 6s1-1.119 1-3.135C24-.02 21 0 21 0h-2v6.347A5.853 5.853 0 0 0 17 6c-2.209 0-4 1.119-4 2.5s1.791 2.5 4 2.5M10.297 20.482l-1.475-1.585a47.54 47.54 0 0 1-1.442 1.129c-.307-.288-.989-1.016-2.045-2.183.902-.836 1.479-1.466 1.729-1.892s.376-.871.376-1.336c0-.592-.273-1.178-.818-1.759-.546-.581-1.329-.871-2.349-.871-1.008 0-1.79.293-2.344.879-.556.587-.832 1.181-.832 1.784 0 .813.419 1.748 1.256 2.805-.847.614-1.444 1.208-1.794 1.784a3.465 3.465 0 0 0-.523 1.833c0 .857.308 1.56.924 2.107.616.549 1.423.823 2.42.823 1.173 0 2.444-.379 3.813-1.137L8.235 24h2.819l-2.09-2.383 1.333-1.135zm-6.736-6.389a1.02 1.02 0 0 1 .73-.286c.31 0 .559.085.747.254a.849.849 0 0 1 .283.659c0 .518-.419 1.112-1.257 1.784-.536-.651-.805-1.231-.805-1.742a.901.901 0 0 1 .302-.669M3.74 22c-.427 0-.778-.116-1.057-.349-.279-.232-.418-.487-.418-.766 0-.594.509-1.288 1.527-2.083.968 1.134 1.717 1.946 2.248 2.438-.921.507-1.686.76-2.3.76\"\n            })\n        }),\n        solid: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            viewBox: \"0 0 512 512\",\n            children: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"path\", {\n                d: \"M500.3 7.251C507.7 13.33 512 22.41 512 31.1V175.1C512 202.5 483.3 223.1 447.1 223.1C412.7 223.1 383.1 202.5 383.1 175.1C383.1 149.5 412.7 127.1 447.1 127.1V71.03L351.1 90.23V207.1C351.1 234.5 323.3 255.1 287.1 255.1C252.7 255.1 223.1 234.5 223.1 207.1C223.1 181.5 252.7 159.1 287.1 159.1V63.1C287.1 48.74 298.8 35.61 313.7 32.62L473.7 .6198C483.1-1.261 492.9 1.173 500.3 7.251H500.3zM74.66 303.1L86.5 286.2C92.43 277.3 102.4 271.1 113.1 271.1H174.9C185.6 271.1 195.6 277.3 201.5 286.2L213.3 303.1H239.1C266.5 303.1 287.1 325.5 287.1 351.1V463.1C287.1 490.5 266.5 511.1 239.1 511.1H47.1C21.49 511.1-.0019 490.5-.0019 463.1V351.1C-.0019 325.5 21.49 303.1 47.1 303.1H74.66zM143.1 359.1C117.5 359.1 95.1 381.5 95.1 407.1C95.1 434.5 117.5 455.1 143.1 455.1C170.5 455.1 191.1 434.5 191.1 407.1C191.1 381.5 170.5 359.1 143.1 359.1zM440.3 367.1H496C502.7 367.1 508.6 372.1 510.1 378.4C513.3 384.6 511.6 391.7 506.5 396L378.5 508C372.9 512.1 364.6 513.3 358.6 508.9C352.6 504.6 350.3 496.6 353.3 489.7L391.7 399.1H336C329.3 399.1 323.4 395.9 321 389.6C318.7 383.4 320.4 376.3 325.5 371.1L453.5 259.1C459.1 255 467.4 254.7 473.4 259.1C479.4 263.4 481.6 271.4 478.7 278.3L440.3 367.1zM116.7 219.1L19.85 119.2C-8.112 90.26-6.614 42.31 24.85 15.34C51.82-8.137 93.26-3.642 118.2 21.83L128.2 32.32L137.7 21.83C162.7-3.642 203.6-8.137 231.6 15.34C262.6 42.31 264.1 90.26 236.1 119.2L139.7 219.1C133.2 225.6 122.7 225.6 116.7 219.1H116.7z\"\n            })\n        })\n    }\n};\nconst $fcccfb36ed0cde68$var$search = {\n    loupe: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        viewBox: \"0 0 20 20\",\n        children: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"path\", {\n            d: \"M12.9 14.32a8 8 0 1 1 1.41-1.41l5.35 5.33-1.42 1.42-5.33-5.34zM8 14A6 6 0 1 0 8 2a6 6 0 0 0 0 12z\"\n        })\n    }),\n    delete: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        viewBox: \"0 0 20 20\",\n        children: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"path\", {\n            d: \"M10 8.586L2.929 1.515 1.515 2.929 8.586 10l-7.071 7.071 1.414 1.414L10 11.414l7.071 7.071 1.414-1.414L11.414 10l7.071-7.071-1.414-1.414L10 8.586z\"\n        })\n    })\n};\nvar $fcccfb36ed0cde68$export$2e2bcd8739ae039 = {\n    categories: $fcccfb36ed0cde68$var$categories,\n    search: $fcccfb36ed0cde68$var$search\n};\n\n\n\n\n\nfunction $254755d3f438722f$export$2e2bcd8739ae039(props) {\n    let { id: id , skin: skin , emoji: emoji  } = props;\n    if (props.shortcodes) {\n        const matches = props.shortcodes.match((0, $c4d155af13ad4d4b$export$2e2bcd8739ae039).SHORTCODES_REGEX);\n        if (matches) {\n            id = matches[1];\n            if (matches[2]) skin = matches[2];\n        }\n    }\n    emoji || (emoji = (0, $c4d155af13ad4d4b$export$2e2bcd8739ae039).get(id || props.native));\n    if (!emoji) return props.fallback;\n    const emojiSkin = emoji.skins[skin - 1] || emoji.skins[0];\n    const imageSrc = emojiSkin.src || (props.set != \"native\" && !props.spritesheet ? typeof props.getImageURL === \"function\" ? props.getImageURL(props.set, emojiSkin.unified) : `https://cdn.jsdelivr.net/npm/emoji-datasource-${props.set}@15.0.1/img/${props.set}/64/${emojiSkin.unified}.png` : undefined);\n    const spritesheetSrc = typeof props.getSpritesheetURL === \"function\" ? props.getSpritesheetURL(props.set) : `https://cdn.jsdelivr.net/npm/emoji-datasource-${props.set}@15.0.1/img/${props.set}/sheets-256/64.png`;\n    return /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"span\", {\n        class: \"emoji-mart-emoji\",\n        \"data-emoji-set\": props.set,\n        children: imageSrc ? /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"img\", {\n            style: {\n                maxWidth: props.size || \"1em\",\n                maxHeight: props.size || \"1em\",\n                display: \"inline-block\"\n            },\n            alt: emojiSkin.native || emojiSkin.shortcodes,\n            src: imageSrc\n        }) : props.set == \"native\" ? /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"span\", {\n            style: {\n                fontSize: props.size,\n                fontFamily: '\"EmojiMart\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Segoe UI\", \"Apple Color Emoji\", \"Twemoji Mozilla\", \"Noto Color Emoji\", \"Android Emoji\"'\n            },\n            children: emojiSkin.native\n        }) : /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"span\", {\n            style: {\n                display: \"block\",\n                width: props.size,\n                height: props.size,\n                backgroundImage: `url(${spritesheetSrc})`,\n                backgroundSize: `${100 * (0, $7adb23b0109cc36a$export$2d0294657ab35f1b).sheet.cols}% ${100 * (0, $7adb23b0109cc36a$export$2d0294657ab35f1b).sheet.rows}%`,\n                backgroundPosition: `${100 / ((0, $7adb23b0109cc36a$export$2d0294657ab35f1b).sheet.cols - 1) * emojiSkin.x}% ${100 / ((0, $7adb23b0109cc36a$export$2d0294657ab35f1b).sheet.rows - 1) * emojiSkin.y}%`\n            }\n        })\n    });\n}\n\n\n\n\n\n\n\nconst $6f57cc9cd54c5aaa$var$WindowHTMLElement = typeof window !== \"undefined\" && window.HTMLElement ? window.HTMLElement : Object;\nclass $6f57cc9cd54c5aaa$export$2e2bcd8739ae039 extends $6f57cc9cd54c5aaa$var$WindowHTMLElement {\n    static get observedAttributes() {\n        return Object.keys(this.Props);\n    }\n    update(props = {}) {\n        for(let k in props)this.attributeChangedCallback(k, null, props[k]);\n    }\n    attributeChangedCallback(attr, _, newValue) {\n        if (!this.component) return;\n        const value = (0, $7adb23b0109cc36a$export$88c9ddb45cea7241)(attr, {\n            [attr]: newValue\n        }, this.constructor.Props, this);\n        if (this.component.componentWillReceiveProps) this.component.componentWillReceiveProps({\n            [attr]: value\n        });\n        else {\n            this.component.props[attr] = value;\n            this.component.forceUpdate();\n        }\n    }\n    disconnectedCallback() {\n        this.disconnected = true;\n        if (this.component && this.component.unregister) this.component.unregister();\n    }\n    constructor(props = {}){\n        super();\n        this.props = props;\n        if (props.parent || props.ref) {\n            let ref = null;\n            const parent = props.parent || (ref = props.ref && props.ref.current);\n            if (ref) ref.innerHTML = \"\";\n            if (parent) parent.appendChild(this);\n        }\n    }\n}\n\n\n\nclass $26f27c338a96b1a6$export$2e2bcd8739ae039 extends (0, $6f57cc9cd54c5aaa$export$2e2bcd8739ae039) {\n    setShadow() {\n        this.attachShadow({\n            mode: \"open\"\n        });\n    }\n    injectStyles(styles) {\n        if (!styles) return;\n        const style = document.createElement(\"style\");\n        style.textContent = styles;\n        this.shadowRoot.insertBefore(style, this.shadowRoot.firstChild);\n    }\n    constructor(props, { styles: styles  } = {}){\n        super(props);\n        this.setShadow();\n        this.injectStyles(styles);\n    }\n}\n\n\n\n\n\n\nvar $3d90f6e46fb2dd47$export$2e2bcd8739ae039 = {\n    fallback: \"\",\n    id: \"\",\n    native: \"\",\n    shortcodes: \"\",\n    size: {\n        value: \"\",\n        transform: (value)=>{\n            // If the value is a number, then we assume it’s a pixel value.\n            if (!/\\D/.test(value)) return `${value}px`;\n            return value;\n        }\n    },\n    // Shared\n    set: (0, $b247ea80b67298d5$export$2e2bcd8739ae039).set,\n    skin: (0, $b247ea80b67298d5$export$2e2bcd8739ae039).skin\n};\n\n\nclass $331b4160623139bf$export$2e2bcd8739ae039 extends (0, $6f57cc9cd54c5aaa$export$2e2bcd8739ae039) {\n    async connectedCallback() {\n        const props = (0, $7adb23b0109cc36a$export$75fe5f91d452f94b)(this.props, (0, $3d90f6e46fb2dd47$export$2e2bcd8739ae039), this);\n        props.element = this;\n        props.ref = (component)=>{\n            this.component = component;\n        };\n        await (0, $7adb23b0109cc36a$export$2cd8252107eb640b)();\n        if (this.disconnected) return;\n        (0, $fb96b826c0c5f37a$export$b3890eb0ae9dca99)(/*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)((0, $254755d3f438722f$export$2e2bcd8739ae039), {\n            ...props\n        }), this);\n    }\n    constructor(props){\n        super(props);\n    }\n}\n(0, $c770c458706daa72$export$2e2bcd8739ae039)($331b4160623139bf$export$2e2bcd8739ae039, \"Props\", (0, $3d90f6e46fb2dd47$export$2e2bcd8739ae039));\nif (typeof customElements !== \"undefined\" && !customElements.get(\"em-emoji\")) customElements.define(\"em-emoji\", $331b4160623139bf$export$2e2bcd8739ae039);\n\n\n\n\n\n\nvar $1a9a8ef576b7773d$var$t, $1a9a8ef576b7773d$var$u, $1a9a8ef576b7773d$var$r, $1a9a8ef576b7773d$var$o = 0, $1a9a8ef576b7773d$var$i = [], $1a9a8ef576b7773d$var$c = (0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).__b, $1a9a8ef576b7773d$var$f = (0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).__r, $1a9a8ef576b7773d$var$e = (0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).diffed, $1a9a8ef576b7773d$var$a = (0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).__c, $1a9a8ef576b7773d$var$v = (0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).unmount;\nfunction $1a9a8ef576b7773d$var$m(t1, r1) {\n    (0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).__h && (0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).__h($1a9a8ef576b7773d$var$u, t1, $1a9a8ef576b7773d$var$o || r1), $1a9a8ef576b7773d$var$o = 0;\n    var i1 = $1a9a8ef576b7773d$var$u.__H || ($1a9a8ef576b7773d$var$u.__H = {\n        __: [],\n        __h: []\n    });\n    return t1 >= i1.__.length && i1.__.push({}), i1.__[t1];\n}\nfunction $1a9a8ef576b7773d$export$60241385465d0a34(n1) {\n    return $1a9a8ef576b7773d$var$o = 1, $1a9a8ef576b7773d$export$13e3392192263954($1a9a8ef576b7773d$var$w, n1);\n}\nfunction $1a9a8ef576b7773d$export$13e3392192263954(n2, r2, o1) {\n    var i2 = $1a9a8ef576b7773d$var$m($1a9a8ef576b7773d$var$t++, 2);\n    return i2.t = n2, i2.__c || (i2.__ = [\n        o1 ? o1(r2) : $1a9a8ef576b7773d$var$w(void 0, r2),\n        function(n3) {\n            var t2 = i2.t(i2.__[0], n3);\n            i2.__[0] !== t2 && (i2.__ = [\n                t2,\n                i2.__[1]\n            ], i2.__c.setState({}));\n        }\n    ], i2.__c = $1a9a8ef576b7773d$var$u), i2.__;\n}\nfunction $1a9a8ef576b7773d$export$6d9c69b0de29b591(r3, o2) {\n    var i3 = $1a9a8ef576b7773d$var$m($1a9a8ef576b7773d$var$t++, 3);\n    !(0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).__s && $1a9a8ef576b7773d$var$k(i3.__H, o2) && (i3.__ = r3, i3.__H = o2, $1a9a8ef576b7773d$var$u.__H.__h.push(i3));\n}\nfunction $1a9a8ef576b7773d$export$e5c5a5f917a5871c(r4, o3) {\n    var i4 = $1a9a8ef576b7773d$var$m($1a9a8ef576b7773d$var$t++, 4);\n    !(0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).__s && $1a9a8ef576b7773d$var$k(i4.__H, o3) && (i4.__ = r4, i4.__H = o3, $1a9a8ef576b7773d$var$u.__h.push(i4));\n}\nfunction $1a9a8ef576b7773d$export$b8f5890fc79d6aca(n4) {\n    return $1a9a8ef576b7773d$var$o = 5, $1a9a8ef576b7773d$export$1538c33de8887b59(function() {\n        return {\n            current: n4\n        };\n    }, []);\n}\nfunction $1a9a8ef576b7773d$export$d5a552a76deda3c2(n5, t3, u1) {\n    $1a9a8ef576b7773d$var$o = 6, $1a9a8ef576b7773d$export$e5c5a5f917a5871c(function() {\n        \"function\" == typeof n5 ? n5(t3()) : n5 && (n5.current = t3());\n    }, null == u1 ? u1 : u1.concat(n5));\n}\nfunction $1a9a8ef576b7773d$export$1538c33de8887b59(n6, u2) {\n    var r5 = $1a9a8ef576b7773d$var$m($1a9a8ef576b7773d$var$t++, 7);\n    return $1a9a8ef576b7773d$var$k(r5.__H, u2) && (r5.__ = n6(), r5.__H = u2, r5.__h = n6), r5.__;\n}\nfunction $1a9a8ef576b7773d$export$35808ee640e87ca7(n7, t4) {\n    return $1a9a8ef576b7773d$var$o = 8, $1a9a8ef576b7773d$export$1538c33de8887b59(function() {\n        return n7;\n    }, t4);\n}\nfunction $1a9a8ef576b7773d$export$fae74005e78b1a27(n8) {\n    var r6 = $1a9a8ef576b7773d$var$u.context[n8.__c], o4 = $1a9a8ef576b7773d$var$m($1a9a8ef576b7773d$var$t++, 9);\n    return o4.c = n8, r6 ? (null == o4.__ && (o4.__ = !0, r6.sub($1a9a8ef576b7773d$var$u)), r6.props.value) : n8.__;\n}\nfunction $1a9a8ef576b7773d$export$dc8fbce3eb94dc1e(t5, u3) {\n    (0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).useDebugValue && (0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).useDebugValue(u3 ? u3(t5) : t5);\n}\nfunction $1a9a8ef576b7773d$export$c052f6604b7d51fe(n9) {\n    var r7 = $1a9a8ef576b7773d$var$m($1a9a8ef576b7773d$var$t++, 10), o5 = $1a9a8ef576b7773d$export$60241385465d0a34();\n    return r7.__ = n9, $1a9a8ef576b7773d$var$u.componentDidCatch || ($1a9a8ef576b7773d$var$u.componentDidCatch = function(n10) {\n        r7.__ && r7.__(n10), o5[1](n10);\n    }), [\n        o5[0],\n        function() {\n            o5[1](void 0);\n        }\n    ];\n}\nfunction $1a9a8ef576b7773d$var$x() {\n    var t6;\n    for($1a9a8ef576b7773d$var$i.sort(function(n11, t7) {\n        return n11.__v.__b - t7.__v.__b;\n    }); t6 = $1a9a8ef576b7773d$var$i.pop();)if (t6.__P) try {\n        t6.__H.__h.forEach($1a9a8ef576b7773d$var$g), t6.__H.__h.forEach($1a9a8ef576b7773d$var$j), t6.__H.__h = [];\n    } catch (u4) {\n        t6.__H.__h = [], (0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).__e(u4, t6.__v);\n    }\n}\n(0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).__b = function(n12) {\n    $1a9a8ef576b7773d$var$u = null, $1a9a8ef576b7773d$var$c && $1a9a8ef576b7773d$var$c(n12);\n}, (0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).__r = function(n13) {\n    $1a9a8ef576b7773d$var$f && $1a9a8ef576b7773d$var$f(n13), $1a9a8ef576b7773d$var$t = 0;\n    var r8 = ($1a9a8ef576b7773d$var$u = n13.__c).__H;\n    r8 && (r8.__h.forEach($1a9a8ef576b7773d$var$g), r8.__h.forEach($1a9a8ef576b7773d$var$j), r8.__h = []);\n}, (0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).diffed = function(t8) {\n    $1a9a8ef576b7773d$var$e && $1a9a8ef576b7773d$var$e(t8);\n    var o6 = t8.__c;\n    o6 && o6.__H && o6.__H.__h.length && (1 !== $1a9a8ef576b7773d$var$i.push(o6) && $1a9a8ef576b7773d$var$r === (0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).requestAnimationFrame || (($1a9a8ef576b7773d$var$r = (0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).requestAnimationFrame) || function(n14) {\n        var t9, u5 = function() {\n            clearTimeout(r9), $1a9a8ef576b7773d$var$b && cancelAnimationFrame(t9), setTimeout(n14);\n        }, r9 = setTimeout(u5, 100);\n        $1a9a8ef576b7773d$var$b && (t9 = requestAnimationFrame(u5));\n    })($1a9a8ef576b7773d$var$x)), $1a9a8ef576b7773d$var$u = null;\n}, (0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).__c = function(t10, u6) {\n    u6.some(function(t11) {\n        try {\n            t11.__h.forEach($1a9a8ef576b7773d$var$g), t11.__h = t11.__h.filter(function(n15) {\n                return !n15.__ || $1a9a8ef576b7773d$var$j(n15);\n            });\n        } catch (r10) {\n            u6.some(function(n16) {\n                n16.__h && (n16.__h = []);\n            }), u6 = [], (0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).__e(r10, t11.__v);\n        }\n    }), $1a9a8ef576b7773d$var$a && $1a9a8ef576b7773d$var$a(t10, u6);\n}, (0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).unmount = function(t12) {\n    $1a9a8ef576b7773d$var$v && $1a9a8ef576b7773d$var$v(t12);\n    var u7, r11 = t12.__c;\n    r11 && r11.__H && (r11.__H.__.forEach(function(n17) {\n        try {\n            $1a9a8ef576b7773d$var$g(n17);\n        } catch (n18) {\n            u7 = n18;\n        }\n    }), u7 && (0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).__e(u7, r11.__v));\n};\nvar $1a9a8ef576b7773d$var$b = \"function\" == typeof requestAnimationFrame;\nfunction $1a9a8ef576b7773d$var$g(n19) {\n    var t13 = $1a9a8ef576b7773d$var$u, r12 = n19.__c;\n    \"function\" == typeof r12 && (n19.__c = void 0, r12()), $1a9a8ef576b7773d$var$u = t13;\n}\nfunction $1a9a8ef576b7773d$var$j(n20) {\n    var t14 = $1a9a8ef576b7773d$var$u;\n    n20.__c = n20.__(), $1a9a8ef576b7773d$var$u = t14;\n}\nfunction $1a9a8ef576b7773d$var$k(n21, t15) {\n    return !n21 || n21.length !== t15.length || t15.some(function(t16, u8) {\n        return t16 !== n21[u8];\n    });\n}\nfunction $1a9a8ef576b7773d$var$w(n22, t17) {\n    return \"function\" == typeof t17 ? t17(n22) : t17;\n}\n\n\n\n\n\nfunction $dc040a17866866fa$var$S(n1, t1) {\n    for(var e1 in t1)n1[e1] = t1[e1];\n    return n1;\n}\nfunction $dc040a17866866fa$var$C(n2, t2) {\n    for(var e2 in n2)if (\"__source\" !== e2 && !(e2 in t2)) return !0;\n    for(var r1 in t2)if (\"__source\" !== r1 && n2[r1] !== t2[r1]) return !0;\n    return !1;\n}\nfunction $dc040a17866866fa$export$221d75b3f55bb0bd(n3) {\n    this.props = n3;\n}\nfunction $dc040a17866866fa$export$7c73462e0d25e514(n4, t3) {\n    function e3(n5) {\n        var e4 = this.props.ref, r3 = e4 == n5.ref;\n        return !r3 && e4 && (e4.call ? e4(null) : e4.current = null), t3 ? !t3(this.props, n5) || !r3 : $dc040a17866866fa$var$C(this.props, n5);\n    }\n    function r2(t4) {\n        return this.shouldComponentUpdate = e3, (0, $fb96b826c0c5f37a$export$c8a8987d4410bf2d)(n4, t4);\n    }\n    return r2.displayName = \"Memo(\" + (n4.displayName || n4.name) + \")\", r2.prototype.isReactComponent = !0, r2.__f = !0, r2;\n}\n($dc040a17866866fa$export$221d75b3f55bb0bd.prototype = new (0, $fb96b826c0c5f37a$export$16fa2f45be04daa8)).isPureReactComponent = !0, $dc040a17866866fa$export$221d75b3f55bb0bd.prototype.shouldComponentUpdate = function(n6, t5) {\n    return $dc040a17866866fa$var$C(this.props, n6) || $dc040a17866866fa$var$C(this.state, t5);\n};\nvar $dc040a17866866fa$var$w = (0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).__b;\n(0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).__b = function(n7) {\n    n7.type && n7.type.__f && n7.ref && (n7.props.ref = n7.ref, n7.ref = null), $dc040a17866866fa$var$w && $dc040a17866866fa$var$w(n7);\n};\nvar $dc040a17866866fa$var$R = \"undefined\" != typeof Symbol && Symbol.for && Symbol.for(\"react.forward_ref\") || 3911;\nfunction $dc040a17866866fa$export$257a8862b851cb5b(n8) {\n    function t6(t7, e5) {\n        var r4 = $dc040a17866866fa$var$S({}, t7);\n        return delete r4.ref, n8(r4, (e5 = t7.ref || e5) && (\"object\" != typeof e5 || \"current\" in e5) ? e5 : null);\n    }\n    return t6.$$typeof = $dc040a17866866fa$var$R, t6.render = t6, t6.prototype.isReactComponent = t6.__f = !0, t6.displayName = \"ForwardRef(\" + (n8.displayName || n8.name) + \")\", t6;\n}\nvar $dc040a17866866fa$var$N = function(n9, t8) {\n    return null == n9 ? null : (0, $fb96b826c0c5f37a$export$47e4c5b300681277)((0, $fb96b826c0c5f37a$export$47e4c5b300681277)(n9).map(t8));\n}, $dc040a17866866fa$export$dca3b0875bd9a954 = {\n    map: $dc040a17866866fa$var$N,\n    forEach: $dc040a17866866fa$var$N,\n    count: function(n10) {\n        return n10 ? (0, $fb96b826c0c5f37a$export$47e4c5b300681277)(n10).length : 0;\n    },\n    only: function(n11) {\n        var t9 = (0, $fb96b826c0c5f37a$export$47e4c5b300681277)(n11);\n        if (1 !== t9.length) throw \"Children.only\";\n        return t9[0];\n    },\n    toArray: (0, $fb96b826c0c5f37a$export$47e4c5b300681277)\n}, $dc040a17866866fa$var$A = (0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).__e;\n(0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).__e = function(n12, t10, e6) {\n    if (n12.then) {\n        for(var r5, u1 = t10; u1 = u1.__;)if ((r5 = u1.__c) && r5.__c) return null == t10.__e && (t10.__e = e6.__e, t10.__k = e6.__k), r5.__c(n12, t10);\n    }\n    $dc040a17866866fa$var$A(n12, t10, e6);\n};\nvar $dc040a17866866fa$var$O = (0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).unmount;\nfunction $dc040a17866866fa$export$74bf444e3cd11ea5() {\n    this.__u = 0, this.t = null, this.__b = null;\n}\nfunction $dc040a17866866fa$var$U(n13) {\n    var t11 = n13.__.__c;\n    return t11 && t11.__e && t11.__e(n13);\n}\nfunction $dc040a17866866fa$export$488013bae63b21da(n14) {\n    var t12, e7, r6;\n    function u2(u3) {\n        if (t12 || (t12 = n14()).then(function(n15) {\n            e7 = n15.default || n15;\n        }, function(n16) {\n            r6 = n16;\n        }), r6) throw r6;\n        if (!e7) throw t12;\n        return (0, $fb96b826c0c5f37a$export$c8a8987d4410bf2d)(e7, u3);\n    }\n    return u2.displayName = \"Lazy\", u2.__f = !0, u2;\n}\nfunction $dc040a17866866fa$export$998bcd577473dd93() {\n    this.u = null, this.o = null;\n}\n(0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).unmount = function(n17) {\n    var t13 = n17.__c;\n    t13 && t13.__R && t13.__R(), t13 && !0 === n17.__h && (n17.type = null), $dc040a17866866fa$var$O && $dc040a17866866fa$var$O(n17);\n}, ($dc040a17866866fa$export$74bf444e3cd11ea5.prototype = new (0, $fb96b826c0c5f37a$export$16fa2f45be04daa8)).__c = function(n18, t14) {\n    var e8 = t14.__c, r7 = this;\n    null == r7.t && (r7.t = []), r7.t.push(e8);\n    var u4 = $dc040a17866866fa$var$U(r7.__v), o1 = !1, i1 = function() {\n        o1 || (o1 = !0, e8.__R = null, u4 ? u4(l1) : l1());\n    };\n    e8.__R = i1;\n    var l1 = function() {\n        if (!--r7.__u) {\n            if (r7.state.__e) {\n                var n19 = r7.state.__e;\n                r7.__v.__k[0] = function n22(t17, e9, r8) {\n                    return t17 && (t17.__v = null, t17.__k = t17.__k && t17.__k.map(function(t18) {\n                        return n22(t18, e9, r8);\n                    }), t17.__c && t17.__c.__P === e9 && (t17.__e && r8.insertBefore(t17.__e, t17.__d), t17.__c.__e = !0, t17.__c.__P = r8)), t17;\n                }(n19, n19.__c.__P, n19.__c.__O);\n            }\n            var t15;\n            for(r7.setState({\n                __e: r7.__b = null\n            }); t15 = r7.t.pop();)t15.forceUpdate();\n        }\n    }, c1 = !0 === t14.__h;\n    (r7.__u++) || c1 || r7.setState({\n        __e: r7.__b = r7.__v.__k[0]\n    }), n18.then(i1, i1);\n}, $dc040a17866866fa$export$74bf444e3cd11ea5.prototype.componentWillUnmount = function() {\n    this.t = [];\n}, $dc040a17866866fa$export$74bf444e3cd11ea5.prototype.render = function(n23, t19) {\n    if (this.__b) {\n        if (this.__v.__k) {\n            var e10 = document.createElement(\"div\"), r9 = this.__v.__k[0].__c;\n            this.__v.__k[0] = function n24(t20, e13, r12) {\n                return t20 && (t20.__c && t20.__c.__H && (t20.__c.__H.__.forEach(function(n25) {\n                    \"function\" == typeof n25.__c && n25.__c();\n                }), t20.__c.__H = null), null != (t20 = $dc040a17866866fa$var$S({}, t20)).__c && (t20.__c.__P === r12 && (t20.__c.__P = e13), t20.__c = null), t20.__k = t20.__k && t20.__k.map(function(t21) {\n                    return n24(t21, e13, r12);\n                })), t20;\n            }(this.__b, e10, r9.__O = r9.__P);\n        }\n        this.__b = null;\n    }\n    var u5 = t19.__e && (0, $fb96b826c0c5f37a$export$c8a8987d4410bf2d)((0, $fb96b826c0c5f37a$export$ffb0004e005737fa), null, n23.fallback);\n    return u5 && (u5.__h = null), [\n        (0, $fb96b826c0c5f37a$export$c8a8987d4410bf2d)((0, $fb96b826c0c5f37a$export$ffb0004e005737fa), null, t19.__e ? null : n23.children),\n        u5\n    ];\n};\nvar $dc040a17866866fa$var$T = function(n26, t22, e14) {\n    if (++e14[1] === e14[0] && n26.o.delete(t22), n26.props.revealOrder && (\"t\" !== n26.props.revealOrder[0] || !n26.o.size)) for(e14 = n26.u; e14;){\n        for(; e14.length > 3;)e14.pop()();\n        if (e14[1] < e14[0]) break;\n        n26.u = e14 = e14[2];\n    }\n};\nfunction $dc040a17866866fa$var$D(n27) {\n    return this.getChildContext = function() {\n        return n27.context;\n    }, n27.children;\n}\nfunction $dc040a17866866fa$var$I(n28) {\n    var t23 = this, e15 = n28.i;\n    t23.componentWillUnmount = function() {\n        (0, $fb96b826c0c5f37a$export$b3890eb0ae9dca99)(null, t23.l), t23.l = null, t23.i = null;\n    }, t23.i && t23.i !== e15 && t23.componentWillUnmount(), n28.__v ? (t23.l || (t23.i = e15, t23.l = {\n        nodeType: 1,\n        parentNode: e15,\n        childNodes: [],\n        appendChild: function(n29) {\n            this.childNodes.push(n29), t23.i.appendChild(n29);\n        },\n        insertBefore: function(n30, e) {\n            this.childNodes.push(n30), t23.i.appendChild(n30);\n        },\n        removeChild: function(n31) {\n            this.childNodes.splice(this.childNodes.indexOf(n31) >>> 1, 1), t23.i.removeChild(n31);\n        }\n    }), (0, $fb96b826c0c5f37a$export$b3890eb0ae9dca99)((0, $fb96b826c0c5f37a$export$c8a8987d4410bf2d)($dc040a17866866fa$var$D, {\n        context: t23.context\n    }, n28.__v), t23.l)) : t23.l && t23.componentWillUnmount();\n}\nfunction $dc040a17866866fa$export$d39a5bbd09211389(n32, t24) {\n    return (0, $fb96b826c0c5f37a$export$c8a8987d4410bf2d)($dc040a17866866fa$var$I, {\n        __v: n32,\n        i: t24\n    });\n}\n($dc040a17866866fa$export$998bcd577473dd93.prototype = new (0, $fb96b826c0c5f37a$export$16fa2f45be04daa8)).__e = function(n33) {\n    var t25 = this, e16 = $dc040a17866866fa$var$U(t25.__v), r13 = t25.o.get(n33);\n    return r13[0]++, function(u6) {\n        var o2 = function() {\n            t25.props.revealOrder ? (r13.push(u6), $dc040a17866866fa$var$T(t25, n33, r13)) : u6();\n        };\n        e16 ? e16(o2) : o2();\n    };\n}, $dc040a17866866fa$export$998bcd577473dd93.prototype.render = function(n34) {\n    this.u = null, this.o = new Map;\n    var t26 = (0, $fb96b826c0c5f37a$export$47e4c5b300681277)(n34.children);\n    n34.revealOrder && \"b\" === n34.revealOrder[0] && t26.reverse();\n    for(var e17 = t26.length; e17--;)this.o.set(t26[e17], this.u = [\n        1,\n        0,\n        this.u\n    ]);\n    return n34.children;\n}, $dc040a17866866fa$export$998bcd577473dd93.prototype.componentDidUpdate = $dc040a17866866fa$export$998bcd577473dd93.prototype.componentDidMount = function() {\n    var n35 = this;\n    this.o.forEach(function(t27, e18) {\n        $dc040a17866866fa$var$T(n35, e18, t27);\n    });\n};\nvar $dc040a17866866fa$var$j = \"undefined\" != typeof Symbol && Symbol.for && Symbol.for(\"react.element\") || 60103, $dc040a17866866fa$var$P = /^(?:accent|alignment|arabic|baseline|cap|clip(?!PathU)|color|dominant|fill|flood|font|glyph(?!R)|horiz|marker(?!H|W|U)|overline|paint|stop|strikethrough|stroke|text(?!L)|underline|unicode|units|v|vector|vert|word|writing|x(?!C))[A-Z]/, $dc040a17866866fa$var$V = \"undefined\" != typeof document, $dc040a17866866fa$var$z = function(n36) {\n    return (\"undefined\" != typeof Symbol && \"symbol\" == typeof Symbol() ? /fil|che|rad/i : /fil|che|ra/i).test(n36);\n};\nfunction $dc040a17866866fa$export$b3890eb0ae9dca99(n37, t28, e19) {\n    return null == t28.__k && (t28.textContent = \"\"), (0, $fb96b826c0c5f37a$export$b3890eb0ae9dca99)(n37, t28), \"function\" == typeof e19 && e19(), n37 ? n37.__c : null;\n}\nfunction $dc040a17866866fa$export$fa8d919ba61d84db(n38, t29, e20) {\n    return (0, $fb96b826c0c5f37a$export$fa8d919ba61d84db)(n38, t29), \"function\" == typeof e20 && e20(), n38 ? n38.__c : null;\n}\n(0, $fb96b826c0c5f37a$export$16fa2f45be04daa8).prototype.isReactComponent = {}, [\n    \"componentWillMount\",\n    \"componentWillReceiveProps\",\n    \"componentWillUpdate\"\n].forEach(function(n39) {\n    Object.defineProperty((0, $fb96b826c0c5f37a$export$16fa2f45be04daa8).prototype, n39, {\n        configurable: !0,\n        get: function() {\n            return this[\"UNSAFE_\" + n39];\n        },\n        set: function(t30) {\n            Object.defineProperty(this, n39, {\n                configurable: !0,\n                writable: !0,\n                value: t30\n            });\n        }\n    });\n});\nvar $dc040a17866866fa$var$H = (0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).event;\nfunction $dc040a17866866fa$var$Z() {}\nfunction $dc040a17866866fa$var$Y() {\n    return this.cancelBubble;\n}\nfunction $dc040a17866866fa$var$q() {\n    return this.defaultPrevented;\n}\n(0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).event = function(n40) {\n    return $dc040a17866866fa$var$H && (n40 = $dc040a17866866fa$var$H(n40)), n40.persist = $dc040a17866866fa$var$Z, n40.isPropagationStopped = $dc040a17866866fa$var$Y, n40.isDefaultPrevented = $dc040a17866866fa$var$q, n40.nativeEvent = n40;\n};\nvar $dc040a17866866fa$var$G, $dc040a17866866fa$var$J = {\n    configurable: !0,\n    get: function() {\n        return this.class;\n    }\n}, $dc040a17866866fa$var$K = (0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).vnode;\n(0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).vnode = function(n41) {\n    var t31 = n41.type, e21 = n41.props, r14 = e21;\n    if (\"string\" == typeof t31) {\n        var u7 = -1 === t31.indexOf(\"-\");\n        for(var o3 in r14 = {}, e21){\n            var i2 = e21[o3];\n            $dc040a17866866fa$var$V && \"children\" === o3 && \"noscript\" === t31 || \"value\" === o3 && \"defaultValue\" in e21 && null == i2 || (\"defaultValue\" === o3 && \"value\" in e21 && null == e21.value ? o3 = \"value\" : \"download\" === o3 && !0 === i2 ? i2 = \"\" : /ondoubleclick/i.test(o3) ? o3 = \"ondblclick\" : /^onchange(textarea|input)/i.test(o3 + t31) && !$dc040a17866866fa$var$z(e21.type) ? o3 = \"oninput\" : /^onfocus$/i.test(o3) ? o3 = \"onfocusin\" : /^onblur$/i.test(o3) ? o3 = \"onfocusout\" : /^on(Ani|Tra|Tou|BeforeInp)/.test(o3) ? o3 = o3.toLowerCase() : u7 && $dc040a17866866fa$var$P.test(o3) ? o3 = o3.replace(/[A-Z0-9]/, \"-$&\").toLowerCase() : null === i2 && (i2 = void 0), r14[o3] = i2);\n        }\n        \"select\" == t31 && r14.multiple && Array.isArray(r14.value) && (r14.value = (0, $fb96b826c0c5f37a$export$47e4c5b300681277)(e21.children).forEach(function(n42) {\n            n42.props.selected = -1 != r14.value.indexOf(n42.props.value);\n        })), \"select\" == t31 && null != r14.defaultValue && (r14.value = (0, $fb96b826c0c5f37a$export$47e4c5b300681277)(e21.children).forEach(function(n43) {\n            n43.props.selected = r14.multiple ? -1 != r14.defaultValue.indexOf(n43.props.value) : r14.defaultValue == n43.props.value;\n        })), n41.props = r14, e21.class != e21.className && ($dc040a17866866fa$var$J.enumerable = \"className\" in e21, null != e21.className && (r14.class = e21.className), Object.defineProperty(r14, \"className\", $dc040a17866866fa$var$J));\n    }\n    n41.$$typeof = $dc040a17866866fa$var$j, $dc040a17866866fa$var$K && $dc040a17866866fa$var$K(n41);\n};\nvar $dc040a17866866fa$var$Q = (0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).__r;\n(0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).__r = function(n44) {\n    $dc040a17866866fa$var$Q && $dc040a17866866fa$var$Q(n44), $dc040a17866866fa$var$G = n44.__c;\n};\nvar $dc040a17866866fa$export$ae55be85d98224ed = {\n    ReactCurrentDispatcher: {\n        current: {\n            readContext: function(n45) {\n                return $dc040a17866866fa$var$G.__n[n45.__c].props.value;\n            }\n        }\n    }\n}, $dc040a17866866fa$export$83d89fbfd8236492 = \"17.0.2\";\nfunction $dc040a17866866fa$export$d38cd72104c1f0e9(n46) {\n    return (0, $fb96b826c0c5f37a$export$c8a8987d4410bf2d).bind(null, n46);\n}\nfunction $dc040a17866866fa$export$a8257692ac88316c(n47) {\n    return !!n47 && n47.$$typeof === $dc040a17866866fa$var$j;\n}\nfunction $dc040a17866866fa$export$e530037191fcd5d7(n48) {\n    return $dc040a17866866fa$export$a8257692ac88316c(n48) ? (0, $fb96b826c0c5f37a$export$e530037191fcd5d7).apply(null, arguments) : n48;\n}\nfunction $dc040a17866866fa$export$502457920280e6be(n49) {\n    return !!n49.__k && ((0, $fb96b826c0c5f37a$export$b3890eb0ae9dca99)(null, n49), !0);\n}\nfunction $dc040a17866866fa$export$466bfc07425424d5(n50) {\n    return n50 && (n50.base || 1 === n50.nodeType && n50) || null;\n}\nvar $dc040a17866866fa$export$c78a37762a8d58e1 = function(n51, t32) {\n    return n51(t32);\n}, $dc040a17866866fa$export$cd75ccfd720a3cd4 = function(n52, t33) {\n    return n52(t33);\n}, $dc040a17866866fa$export$5f8d39834fd61797 = (0, $fb96b826c0c5f37a$export$ffb0004e005737fa);\nvar $dc040a17866866fa$export$2e2bcd8739ae039 = {\n    useState: (0, $1a9a8ef576b7773d$export$60241385465d0a34),\n    useReducer: (0, $1a9a8ef576b7773d$export$13e3392192263954),\n    useEffect: (0, $1a9a8ef576b7773d$export$6d9c69b0de29b591),\n    useLayoutEffect: (0, $1a9a8ef576b7773d$export$e5c5a5f917a5871c),\n    useRef: (0, $1a9a8ef576b7773d$export$b8f5890fc79d6aca),\n    useImperativeHandle: (0, $1a9a8ef576b7773d$export$d5a552a76deda3c2),\n    useMemo: (0, $1a9a8ef576b7773d$export$1538c33de8887b59),\n    useCallback: (0, $1a9a8ef576b7773d$export$35808ee640e87ca7),\n    useContext: (0, $1a9a8ef576b7773d$export$fae74005e78b1a27),\n    useDebugValue: (0, $1a9a8ef576b7773d$export$dc8fbce3eb94dc1e),\n    version: \"17.0.2\",\n    Children: $dc040a17866866fa$export$dca3b0875bd9a954,\n    render: $dc040a17866866fa$export$b3890eb0ae9dca99,\n    hydrate: $dc040a17866866fa$export$fa8d919ba61d84db,\n    unmountComponentAtNode: $dc040a17866866fa$export$502457920280e6be,\n    createPortal: $dc040a17866866fa$export$d39a5bbd09211389,\n    createElement: (0, $fb96b826c0c5f37a$export$c8a8987d4410bf2d),\n    createContext: (0, $fb96b826c0c5f37a$export$fd42f52fd3ae1109),\n    createFactory: $dc040a17866866fa$export$d38cd72104c1f0e9,\n    cloneElement: $dc040a17866866fa$export$e530037191fcd5d7,\n    createRef: (0, $fb96b826c0c5f37a$export$7d1e3a5e95ceca43),\n    Fragment: (0, $fb96b826c0c5f37a$export$ffb0004e005737fa),\n    isValidElement: $dc040a17866866fa$export$a8257692ac88316c,\n    findDOMNode: $dc040a17866866fa$export$466bfc07425424d5,\n    Component: (0, $fb96b826c0c5f37a$export$16fa2f45be04daa8),\n    PureComponent: $dc040a17866866fa$export$221d75b3f55bb0bd,\n    memo: $dc040a17866866fa$export$7c73462e0d25e514,\n    forwardRef: $dc040a17866866fa$export$257a8862b851cb5b,\n    flushSync: $dc040a17866866fa$export$cd75ccfd720a3cd4,\n    unstable_batchedUpdates: $dc040a17866866fa$export$c78a37762a8d58e1,\n    StrictMode: (0, $fb96b826c0c5f37a$export$ffb0004e005737fa),\n    Suspense: $dc040a17866866fa$export$74bf444e3cd11ea5,\n    SuspenseList: $dc040a17866866fa$export$998bcd577473dd93,\n    lazy: $dc040a17866866fa$export$488013bae63b21da,\n    __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED: $dc040a17866866fa$export$ae55be85d98224ed\n};\n\n\n\n\nconst $ec8c39fdad15601a$var$THEME_ICONS = {\n    light: \"outline\",\n    dark: \"solid\"\n};\nclass $ec8c39fdad15601a$export$2e2bcd8739ae039 extends (0, $dc040a17866866fa$export$221d75b3f55bb0bd) {\n    renderIcon(category) {\n        const { icon: icon  } = category;\n        if (icon) {\n            if (icon.svg) return /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"span\", {\n                class: \"flex\",\n                dangerouslySetInnerHTML: {\n                    __html: icon.svg\n                }\n            });\n            if (icon.src) return /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"img\", {\n                src: icon.src\n            });\n        }\n        const categoryIcons = (0, $fcccfb36ed0cde68$export$2e2bcd8739ae039).categories[category.id] || (0, $fcccfb36ed0cde68$export$2e2bcd8739ae039).categories.custom;\n        const style = this.props.icons == \"auto\" ? $ec8c39fdad15601a$var$THEME_ICONS[this.props.theme] : this.props.icons;\n        return categoryIcons[style] || categoryIcons;\n    }\n    render() {\n        let selectedCategoryIndex = null;\n        return /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"nav\", {\n            id: \"nav\",\n            class: \"padding\",\n            \"data-position\": this.props.position,\n            dir: this.props.dir,\n            children: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"div\", {\n                class: \"flex relative\",\n                children: [\n                    this.categories.map((category, i)=>{\n                        const title = category.name || (0, $7adb23b0109cc36a$export$dbe3113d60765c1a).categories[category.id];\n                        const selected = !this.props.unfocused && category.id == this.state.categoryId;\n                        if (selected) selectedCategoryIndex = i;\n                        return /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"button\", {\n                            \"aria-label\": title,\n                            \"aria-selected\": selected || undefined,\n                            title: title,\n                            type: \"button\",\n                            class: \"flex flex-grow flex-center\",\n                            onMouseDown: (e)=>e.preventDefault(),\n                            onClick: ()=>{\n                                this.props.onClick({\n                                    category: category,\n                                    i: i\n                                });\n                            },\n                            children: this.renderIcon(category)\n                        });\n                    }),\n                    /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"div\", {\n                        class: \"bar\",\n                        style: {\n                            width: `${100 / this.categories.length}%`,\n                            opacity: selectedCategoryIndex == null ? 0 : 1,\n                            transform: this.props.dir === \"rtl\" ? `scaleX(-1) translateX(${selectedCategoryIndex * 100}%)` : `translateX(${selectedCategoryIndex * 100}%)`\n                        }\n                    })\n                ]\n            })\n        });\n    }\n    constructor(){\n        super();\n        this.categories = (0, $7adb23b0109cc36a$export$2d0294657ab35f1b).categories.filter((category)=>{\n            return !category.target;\n        });\n        this.state = {\n            categoryId: this.categories[0].id\n        };\n    }\n}\n\n\n\n\n\nclass $e0d4dda61265ff1e$export$2e2bcd8739ae039 extends (0, $dc040a17866866fa$export$221d75b3f55bb0bd) {\n    shouldComponentUpdate(nextProps) {\n        for(let k in nextProps){\n            if (k == \"children\") continue;\n            if (nextProps[k] != this.props[k]) return true;\n        }\n        return false;\n    }\n    render() {\n        return this.props.children;\n    }\n}\n\n\n\n\nconst $89bd6bb200cc8fef$var$Performance = {\n    rowsPerRender: 10\n};\nclass $89bd6bb200cc8fef$export$2e2bcd8739ae039 extends (0, $fb96b826c0c5f37a$export$16fa2f45be04daa8) {\n    getInitialState(props = this.props) {\n        return {\n            skin: (0, $f72b75cf796873c7$export$2e2bcd8739ae039).get(\"skin\") || props.skin,\n            theme: this.initTheme(props.theme)\n        };\n    }\n    componentWillMount() {\n        this.dir = (0, $7adb23b0109cc36a$export$dbe3113d60765c1a).rtl ? \"rtl\" : \"ltr\";\n        this.refs = {\n            menu: (0, $fb96b826c0c5f37a$export$7d1e3a5e95ceca43)(),\n            navigation: (0, $fb96b826c0c5f37a$export$7d1e3a5e95ceca43)(),\n            scroll: (0, $fb96b826c0c5f37a$export$7d1e3a5e95ceca43)(),\n            search: (0, $fb96b826c0c5f37a$export$7d1e3a5e95ceca43)(),\n            searchInput: (0, $fb96b826c0c5f37a$export$7d1e3a5e95ceca43)(),\n            skinToneButton: (0, $fb96b826c0c5f37a$export$7d1e3a5e95ceca43)(),\n            skinToneRadio: (0, $fb96b826c0c5f37a$export$7d1e3a5e95ceca43)()\n        };\n        this.initGrid();\n        if (this.props.stickySearch == false && this.props.searchPosition == \"sticky\") {\n            console.warn(\"[EmojiMart] Deprecation warning: `stickySearch` has been renamed `searchPosition`.\");\n            this.props.searchPosition = \"static\";\n        }\n    }\n    componentDidMount() {\n        this.register();\n        this.shadowRoot = this.base.parentNode;\n        if (this.props.autoFocus) {\n            const { searchInput: searchInput  } = this.refs;\n            if (searchInput.current) searchInput.current.focus();\n        }\n    }\n    componentWillReceiveProps(nextProps) {\n        this.nextState || (this.nextState = {});\n        for(const k1 in nextProps)this.nextState[k1] = nextProps[k1];\n        clearTimeout(this.nextStateTimer);\n        this.nextStateTimer = setTimeout(()=>{\n            let requiresGridReset = false;\n            for(const k in this.nextState){\n                this.props[k] = this.nextState[k];\n                if (k === \"custom\" || k === \"categories\") requiresGridReset = true;\n            }\n            delete this.nextState;\n            const nextState = this.getInitialState();\n            if (requiresGridReset) return this.reset(nextState);\n            this.setState(nextState);\n        });\n    }\n    componentWillUnmount() {\n        this.unregister();\n    }\n    async reset(nextState = {}) {\n        await (0, $7adb23b0109cc36a$export$2cd8252107eb640b)(this.props);\n        this.initGrid();\n        this.unobserve();\n        this.setState(nextState, ()=>{\n            this.observeCategories();\n            this.observeRows();\n        });\n    }\n    register() {\n        document.addEventListener(\"click\", this.handleClickOutside);\n        this.observe();\n    }\n    unregister() {\n        document.removeEventListener(\"click\", this.handleClickOutside);\n        this.darkMedia?.removeEventListener(\"change\", this.darkMediaCallback);\n        this.unobserve();\n    }\n    observe() {\n        this.observeCategories();\n        this.observeRows();\n    }\n    unobserve({ except: except = []  } = {}) {\n        if (!Array.isArray(except)) except = [\n            except\n        ];\n        for (const observer of this.observers){\n            if (except.includes(observer)) continue;\n            observer.disconnect();\n        }\n        this.observers = [].concat(except);\n    }\n    initGrid() {\n        const { categories: categories  } = (0, $7adb23b0109cc36a$export$2d0294657ab35f1b);\n        this.refs.categories = new Map();\n        const navKey = (0, $7adb23b0109cc36a$export$2d0294657ab35f1b).categories.map((category)=>category.id).join(\",\");\n        if (this.navKey && this.navKey != navKey) this.refs.scroll.current && (this.refs.scroll.current.scrollTop = 0);\n        this.navKey = navKey;\n        this.grid = [];\n        this.grid.setsize = 0;\n        const addRow = (rows, category)=>{\n            const row = [];\n            row.__categoryId = category.id;\n            row.__index = rows.length;\n            this.grid.push(row);\n            const rowIndex = this.grid.length - 1;\n            const rowRef = rowIndex % $89bd6bb200cc8fef$var$Performance.rowsPerRender ? {} : (0, $fb96b826c0c5f37a$export$7d1e3a5e95ceca43)();\n            rowRef.index = rowIndex;\n            rowRef.posinset = this.grid.setsize + 1;\n            rows.push(rowRef);\n            return row;\n        };\n        for (let category1 of categories){\n            const rows = [];\n            let row = addRow(rows, category1);\n            for (let emoji of category1.emojis){\n                if (row.length == this.getPerLine()) row = addRow(rows, category1);\n                this.grid.setsize += 1;\n                row.push(emoji);\n            }\n            this.refs.categories.set(category1.id, {\n                root: (0, $fb96b826c0c5f37a$export$7d1e3a5e95ceca43)(),\n                rows: rows\n            });\n        }\n    }\n    initTheme(theme) {\n        if (theme != \"auto\") return theme;\n        if (!this.darkMedia) {\n            this.darkMedia = matchMedia(\"(prefers-color-scheme: dark)\");\n            if (this.darkMedia.media.match(/^not/)) return \"light\";\n            this.darkMedia.addEventListener(\"change\", this.darkMediaCallback);\n        }\n        return this.darkMedia.matches ? \"dark\" : \"light\";\n    }\n    initDynamicPerLine(props = this.props) {\n        if (!props.dynamicWidth) return;\n        const { element: element , emojiButtonSize: emojiButtonSize  } = props;\n        const calculatePerLine = ()=>{\n            const { width: width  } = element.getBoundingClientRect();\n            return Math.floor(width / emojiButtonSize);\n        };\n        const observer = new ResizeObserver(()=>{\n            this.unobserve({\n                except: observer\n            });\n            this.setState({\n                perLine: calculatePerLine()\n            }, ()=>{\n                this.initGrid();\n                this.forceUpdate(()=>{\n                    this.observeCategories();\n                    this.observeRows();\n                });\n            });\n        });\n        observer.observe(element);\n        this.observers.push(observer);\n        return calculatePerLine();\n    }\n    getPerLine() {\n        return this.state.perLine || this.props.perLine;\n    }\n    getEmojiByPos([p1, p2]) {\n        const grid = this.state.searchResults || this.grid;\n        const emoji = grid[p1] && grid[p1][p2];\n        if (!emoji) return;\n        return (0, $c4d155af13ad4d4b$export$2e2bcd8739ae039).get(emoji);\n    }\n    observeCategories() {\n        const navigation = this.refs.navigation.current;\n        if (!navigation) return;\n        const visibleCategories = new Map();\n        const setFocusedCategory = (categoryId)=>{\n            if (categoryId != navigation.state.categoryId) navigation.setState({\n                categoryId: categoryId\n            });\n        };\n        const observerOptions = {\n            root: this.refs.scroll.current,\n            threshold: [\n                0.0,\n                1.0\n            ]\n        };\n        const observer = new IntersectionObserver((entries)=>{\n            for (const entry of entries){\n                const id = entry.target.dataset.id;\n                visibleCategories.set(id, entry.intersectionRatio);\n            }\n            const ratios = [\n                ...visibleCategories\n            ];\n            for (const [id, ratio] of ratios)if (ratio) {\n                setFocusedCategory(id);\n                break;\n            }\n        }, observerOptions);\n        for (const { root: root  } of this.refs.categories.values())observer.observe(root.current);\n        this.observers.push(observer);\n    }\n    observeRows() {\n        const visibleRows = {\n            ...this.state.visibleRows\n        };\n        const observer = new IntersectionObserver((entries)=>{\n            for (const entry of entries){\n                const index = parseInt(entry.target.dataset.index);\n                if (entry.isIntersecting) visibleRows[index] = true;\n                else delete visibleRows[index];\n            }\n            this.setState({\n                visibleRows: visibleRows\n            });\n        }, {\n            root: this.refs.scroll.current,\n            rootMargin: `${this.props.emojiButtonSize * ($89bd6bb200cc8fef$var$Performance.rowsPerRender + 5)}px 0px ${this.props.emojiButtonSize * $89bd6bb200cc8fef$var$Performance.rowsPerRender}px`\n        });\n        for (const { rows: rows  } of this.refs.categories.values()){\n            for (const row of rows)if (row.current) observer.observe(row.current);\n        }\n        this.observers.push(observer);\n    }\n    preventDefault(e) {\n        e.preventDefault();\n    }\n    unfocusSearch() {\n        const input = this.refs.searchInput.current;\n        if (!input) return;\n        input.blur();\n    }\n    navigate({ e: e , input: input , left: left , right: right , up: up , down: down  }) {\n        const grid = this.state.searchResults || this.grid;\n        if (!grid.length) return;\n        let [p1, p2] = this.state.pos;\n        const pos = (()=>{\n            if (p1 == 0) {\n                if (p2 == 0 && !e.repeat && (left || up)) return null;\n            }\n            if (p1 == -1) {\n                if (!e.repeat && (right || down) && input.selectionStart == input.value.length) return [\n                    0,\n                    0\n                ];\n                return null;\n            }\n            if (left || right) {\n                let row = grid[p1];\n                const increment = left ? -1 : 1;\n                p2 += increment;\n                if (!row[p2]) {\n                    p1 += increment;\n                    row = grid[p1];\n                    if (!row) {\n                        p1 = left ? 0 : grid.length - 1;\n                        p2 = left ? 0 : grid[p1].length - 1;\n                        return [\n                            p1,\n                            p2\n                        ];\n                    }\n                    p2 = left ? row.length - 1 : 0;\n                }\n                return [\n                    p1,\n                    p2\n                ];\n            }\n            if (up || down) {\n                p1 += up ? -1 : 1;\n                const row = grid[p1];\n                if (!row) {\n                    p1 = up ? 0 : grid.length - 1;\n                    p2 = up ? 0 : grid[p1].length - 1;\n                    return [\n                        p1,\n                        p2\n                    ];\n                }\n                if (!row[p2]) p2 = row.length - 1;\n                return [\n                    p1,\n                    p2\n                ];\n            }\n        })();\n        if (pos) e.preventDefault();\n        else {\n            if (this.state.pos[0] > -1) this.setState({\n                pos: [\n                    -1,\n                    -1\n                ]\n            });\n            return;\n        }\n        this.setState({\n            pos: pos,\n            keyboard: true\n        }, ()=>{\n            this.scrollTo({\n                row: pos[0]\n            });\n        });\n    }\n    scrollTo({ categoryId: categoryId , row: row  }) {\n        const grid = this.state.searchResults || this.grid;\n        if (!grid.length) return;\n        const scroll = this.refs.scroll.current;\n        const scrollRect = scroll.getBoundingClientRect();\n        let scrollTop = 0;\n        if (row >= 0) categoryId = grid[row].__categoryId;\n        if (categoryId) {\n            const ref = this.refs[categoryId] || this.refs.categories.get(categoryId).root;\n            const categoryRect = ref.current.getBoundingClientRect();\n            scrollTop = categoryRect.top - (scrollRect.top - scroll.scrollTop) + 1;\n        }\n        if (row >= 0) {\n            if (!row) scrollTop = 0;\n            else {\n                const rowIndex = grid[row].__index;\n                const rowTop = scrollTop + rowIndex * this.props.emojiButtonSize;\n                const rowBot = rowTop + this.props.emojiButtonSize + this.props.emojiButtonSize * 0.88;\n                if (rowTop < scroll.scrollTop) scrollTop = rowTop;\n                else if (rowBot > scroll.scrollTop + scrollRect.height) scrollTop = rowBot - scrollRect.height;\n                else return;\n            }\n        }\n        this.ignoreMouse();\n        scroll.scrollTop = scrollTop;\n    }\n    ignoreMouse() {\n        this.mouseIsIgnored = true;\n        clearTimeout(this.ignoreMouseTimer);\n        this.ignoreMouseTimer = setTimeout(()=>{\n            delete this.mouseIsIgnored;\n        }, 100);\n    }\n    handleEmojiOver(pos) {\n        if (this.mouseIsIgnored || this.state.showSkins) return;\n        this.setState({\n            pos: pos || [\n                -1,\n                -1\n            ],\n            keyboard: false\n        });\n    }\n    handleEmojiClick({ e: e , emoji: emoji , pos: pos  }) {\n        if (!this.props.onEmojiSelect) return;\n        if (!emoji && pos) emoji = this.getEmojiByPos(pos);\n        if (emoji) {\n            const emojiData = (0, $693b183b0a78708f$export$d10ac59fbe52a745)(emoji, {\n                skinIndex: this.state.skin - 1\n            });\n            if (this.props.maxFrequentRows) (0, $b22cfd0a55410b4f$export$2e2bcd8739ae039).add(emojiData, this.props);\n            this.props.onEmojiSelect(emojiData, e);\n        }\n    }\n    closeSkins() {\n        if (!this.state.showSkins) return;\n        this.setState({\n            showSkins: null,\n            tempSkin: null\n        });\n        this.base.removeEventListener(\"click\", this.handleBaseClick);\n        this.base.removeEventListener(\"keydown\", this.handleBaseKeydown);\n    }\n    handleSkinMouseOver(tempSkin) {\n        this.setState({\n            tempSkin: tempSkin\n        });\n    }\n    handleSkinClick(skin) {\n        this.ignoreMouse();\n        this.closeSkins();\n        this.setState({\n            skin: skin,\n            tempSkin: null\n        });\n        (0, $f72b75cf796873c7$export$2e2bcd8739ae039).set(\"skin\", skin);\n    }\n    renderNav() {\n        return /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)((0, $ec8c39fdad15601a$export$2e2bcd8739ae039), {\n            ref: this.refs.navigation,\n            icons: this.props.icons,\n            theme: this.state.theme,\n            dir: this.dir,\n            unfocused: !!this.state.searchResults,\n            position: this.props.navPosition,\n            onClick: this.handleCategoryClick\n        }, this.navKey);\n    }\n    renderPreview() {\n        const emoji = this.getEmojiByPos(this.state.pos);\n        const noSearchResults = this.state.searchResults && !this.state.searchResults.length;\n        return /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"div\", {\n            id: \"preview\",\n            class: \"flex flex-middle\",\n            dir: this.dir,\n            \"data-position\": this.props.previewPosition,\n            children: [\n                /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"div\", {\n                    class: \"flex flex-middle flex-grow\",\n                    children: [\n                        /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"div\", {\n                            class: \"flex flex-auto flex-middle flex-center\",\n                            style: {\n                                height: this.props.emojiButtonSize,\n                                fontSize: this.props.emojiButtonSize\n                            },\n                            children: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)((0, $254755d3f438722f$export$2e2bcd8739ae039), {\n                                emoji: emoji,\n                                id: noSearchResults ? this.props.noResultsEmoji || \"cry\" : this.props.previewEmoji || (this.props.previewPosition == \"top\" ? \"point_down\" : \"point_up\"),\n                                set: this.props.set,\n                                size: this.props.emojiButtonSize,\n                                skin: this.state.tempSkin || this.state.skin,\n                                spritesheet: true,\n                                getSpritesheetURL: this.props.getSpritesheetURL\n                            })\n                        }),\n                        /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"div\", {\n                            class: `margin-${this.dir[0]}`,\n                            children: emoji || noSearchResults ? /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"div\", {\n                                class: `padding-${this.dir[2]} align-${this.dir[0]}`,\n                                children: [\n                                    /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"div\", {\n                                        class: \"preview-title ellipsis\",\n                                        children: emoji ? emoji.name : (0, $7adb23b0109cc36a$export$dbe3113d60765c1a).search_no_results_1\n                                    }),\n                                    /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"div\", {\n                                        class: \"preview-subtitle ellipsis color-c\",\n                                        children: emoji ? emoji.skins[0].shortcodes : (0, $7adb23b0109cc36a$export$dbe3113d60765c1a).search_no_results_2\n                                    })\n                                ]\n                            }) : /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"div\", {\n                                class: \"preview-placeholder color-c\",\n                                children: (0, $7adb23b0109cc36a$export$dbe3113d60765c1a).pick\n                            })\n                        })\n                    ]\n                }),\n                !emoji && this.props.skinTonePosition == \"preview\" && this.renderSkinToneButton()\n            ]\n        });\n    }\n    renderEmojiButton(emoji, { pos: pos , posinset: posinset , grid: grid  }) {\n        const size = this.props.emojiButtonSize;\n        const skin = this.state.tempSkin || this.state.skin;\n        const emojiSkin = emoji.skins[skin - 1] || emoji.skins[0];\n        const native = emojiSkin.native;\n        const selected = (0, $693b183b0a78708f$export$9cb4719e2e525b7a)(this.state.pos, pos);\n        const key = pos.concat(emoji.id).join(\"\");\n        return /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)((0, $e0d4dda61265ff1e$export$2e2bcd8739ae039), {\n            selected: selected,\n            skin: skin,\n            size: size,\n            children: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"button\", {\n                \"aria-label\": native,\n                \"aria-selected\": selected || undefined,\n                \"aria-posinset\": posinset,\n                \"aria-setsize\": grid.setsize,\n                \"data-keyboard\": this.state.keyboard,\n                title: this.props.previewPosition == \"none\" ? emoji.name : undefined,\n                type: \"button\",\n                class: \"flex flex-center flex-middle\",\n                tabindex: \"-1\",\n                onClick: (e)=>this.handleEmojiClick({\n                        e: e,\n                        emoji: emoji\n                    }),\n                onMouseEnter: ()=>this.handleEmojiOver(pos),\n                onMouseLeave: ()=>this.handleEmojiOver(),\n                style: {\n                    width: this.props.emojiButtonSize,\n                    height: this.props.emojiButtonSize,\n                    fontSize: this.props.emojiSize,\n                    lineHeight: 0\n                },\n                children: [\n                    /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"div\", {\n                        \"aria-hidden\": \"true\",\n                        class: \"background\",\n                        style: {\n                            borderRadius: this.props.emojiButtonRadius,\n                            backgroundColor: this.props.emojiButtonColors ? this.props.emojiButtonColors[(posinset - 1) % this.props.emojiButtonColors.length] : undefined\n                        }\n                    }),\n                    /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)((0, $254755d3f438722f$export$2e2bcd8739ae039), {\n                        emoji: emoji,\n                        set: this.props.set,\n                        size: this.props.emojiSize,\n                        skin: skin,\n                        spritesheet: true,\n                        getSpritesheetURL: this.props.getSpritesheetURL\n                    })\n                ]\n            })\n        }, key);\n    }\n    renderSearch() {\n        const renderSkinTone = this.props.previewPosition == \"none\" || this.props.skinTonePosition == \"search\";\n        return /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"div\", {\n            children: [\n                /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"div\", {\n                    class: \"spacer\"\n                }),\n                /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"div\", {\n                    class: \"flex flex-middle\",\n                    children: [\n                        /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"div\", {\n                            class: \"search relative flex-grow\",\n                            children: [\n                                /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"input\", {\n                                    type: \"search\",\n                                    ref: this.refs.searchInput,\n                                    placeholder: (0, $7adb23b0109cc36a$export$dbe3113d60765c1a).search,\n                                    onClick: this.handleSearchClick,\n                                    onInput: this.handleSearchInput,\n                                    onKeyDown: this.handleSearchKeyDown,\n                                    autoComplete: \"off\"\n                                }),\n                                /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"span\", {\n                                    class: \"icon loupe flex\",\n                                    children: (0, $fcccfb36ed0cde68$export$2e2bcd8739ae039).search.loupe\n                                }),\n                                this.state.searchResults && /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"button\", {\n                                    title: \"Clear\",\n                                    \"aria-label\": \"Clear\",\n                                    type: \"button\",\n                                    class: \"icon delete flex\",\n                                    onClick: this.clearSearch,\n                                    onMouseDown: this.preventDefault,\n                                    children: (0, $fcccfb36ed0cde68$export$2e2bcd8739ae039).search.delete\n                                })\n                            ]\n                        }),\n                        renderSkinTone && this.renderSkinToneButton()\n                    ]\n                })\n            ]\n        });\n    }\n    renderSearchResults() {\n        const { searchResults: searchResults  } = this.state;\n        if (!searchResults) return null;\n        return /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"div\", {\n            class: \"category\",\n            ref: this.refs.search,\n            children: [\n                /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"div\", {\n                    class: `sticky padding-small align-${this.dir[0]}`,\n                    children: (0, $7adb23b0109cc36a$export$dbe3113d60765c1a).categories.search\n                }),\n                /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"div\", {\n                    children: !searchResults.length ? /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"div\", {\n                        class: `padding-small align-${this.dir[0]}`,\n                        children: this.props.onAddCustomEmoji && /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"a\", {\n                            onClick: this.props.onAddCustomEmoji,\n                            children: (0, $7adb23b0109cc36a$export$dbe3113d60765c1a).add_custom\n                        })\n                    }) : searchResults.map((row, i)=>{\n                        return /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"div\", {\n                            class: \"flex\",\n                            children: row.map((emoji, ii)=>{\n                                return this.renderEmojiButton(emoji, {\n                                    pos: [\n                                        i,\n                                        ii\n                                    ],\n                                    posinset: i * this.props.perLine + ii + 1,\n                                    grid: searchResults\n                                });\n                            })\n                        });\n                    })\n                })\n            ]\n        });\n    }\n    renderCategories() {\n        const { categories: categories  } = (0, $7adb23b0109cc36a$export$2d0294657ab35f1b);\n        const hidden = !!this.state.searchResults;\n        const perLine = this.getPerLine();\n        return /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"div\", {\n            style: {\n                visibility: hidden ? \"hidden\" : undefined,\n                display: hidden ? \"none\" : undefined,\n                height: \"100%\"\n            },\n            children: categories.map((category)=>{\n                const { root: root , rows: rows  } = this.refs.categories.get(category.id);\n                return /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"div\", {\n                    \"data-id\": category.target ? category.target.id : category.id,\n                    class: \"category\",\n                    ref: root,\n                    children: [\n                        /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"div\", {\n                            class: `sticky padding-small align-${this.dir[0]}`,\n                            children: category.name || (0, $7adb23b0109cc36a$export$dbe3113d60765c1a).categories[category.id]\n                        }),\n                        /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"div\", {\n                            class: \"relative\",\n                            style: {\n                                height: rows.length * this.props.emojiButtonSize\n                            },\n                            children: rows.map((row, i)=>{\n                                const targetRow = row.index - row.index % $89bd6bb200cc8fef$var$Performance.rowsPerRender;\n                                const visible = this.state.visibleRows[targetRow];\n                                const ref = \"current\" in row ? row : undefined;\n                                if (!visible && !ref) return null;\n                                const start = i * perLine;\n                                const end = start + perLine;\n                                const emojiIds = category.emojis.slice(start, end);\n                                if (emojiIds.length < perLine) emojiIds.push(...new Array(perLine - emojiIds.length));\n                                return /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"div\", {\n                                    \"data-index\": row.index,\n                                    ref: ref,\n                                    class: \"flex row\",\n                                    style: {\n                                        top: i * this.props.emojiButtonSize\n                                    },\n                                    children: visible && emojiIds.map((emojiId, ii)=>{\n                                        if (!emojiId) return /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"div\", {\n                                            style: {\n                                                width: this.props.emojiButtonSize,\n                                                height: this.props.emojiButtonSize\n                                            }\n                                        });\n                                        const emoji = (0, $c4d155af13ad4d4b$export$2e2bcd8739ae039).get(emojiId);\n                                        return this.renderEmojiButton(emoji, {\n                                            pos: [\n                                                row.index,\n                                                ii\n                                            ],\n                                            posinset: row.posinset + ii,\n                                            grid: this.grid\n                                        });\n                                    })\n                                }, row.index);\n                            })\n                        })\n                    ]\n                });\n            })\n        });\n    }\n    renderSkinToneButton() {\n        if (this.props.skinTonePosition == \"none\") return null;\n        return /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"div\", {\n            class: \"flex flex-auto flex-center flex-middle\",\n            style: {\n                position: \"relative\",\n                width: this.props.emojiButtonSize,\n                height: this.props.emojiButtonSize\n            },\n            children: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"button\", {\n                type: \"button\",\n                ref: this.refs.skinToneButton,\n                class: \"skin-tone-button flex flex-auto flex-center flex-middle\",\n                \"aria-selected\": this.state.showSkins ? \"\" : undefined,\n                \"aria-label\": (0, $7adb23b0109cc36a$export$dbe3113d60765c1a).skins.choose,\n                title: (0, $7adb23b0109cc36a$export$dbe3113d60765c1a).skins.choose,\n                onClick: this.openSkins,\n                style: {\n                    width: this.props.emojiSize,\n                    height: this.props.emojiSize\n                },\n                children: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"span\", {\n                    class: `skin-tone skin-tone-${this.state.skin}`\n                })\n            })\n        });\n    }\n    renderLiveRegion() {\n        const emoji = this.getEmojiByPos(this.state.pos);\n        const contents = emoji ? emoji.name : \"\";\n        return /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"div\", {\n            \"aria-live\": \"polite\",\n            class: \"sr-only\",\n            children: contents\n        });\n    }\n    renderSkins() {\n        const skinToneButton = this.refs.skinToneButton.current;\n        const skinToneButtonRect = skinToneButton.getBoundingClientRect();\n        const baseRect = this.base.getBoundingClientRect();\n        const position = {};\n        if (this.dir == \"ltr\") position.right = baseRect.right - skinToneButtonRect.right - 3;\n        else position.left = skinToneButtonRect.left - baseRect.left - 3;\n        if (this.props.previewPosition == \"bottom\" && this.props.skinTonePosition == \"preview\") position.bottom = baseRect.bottom - skinToneButtonRect.top + 6;\n        else {\n            position.top = skinToneButtonRect.bottom - baseRect.top + 3;\n            position.bottom = \"auto\";\n        }\n        return /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"div\", {\n            ref: this.refs.menu,\n            role: \"radiogroup\",\n            dir: this.dir,\n            \"aria-label\": (0, $7adb23b0109cc36a$export$dbe3113d60765c1a).skins.choose,\n            class: \"menu hidden\",\n            \"data-position\": position.top ? \"top\" : \"bottom\",\n            style: position,\n            children: [\n                ...Array(6).keys()\n            ].map((i)=>{\n                const skin = i + 1;\n                const checked = this.state.skin == skin;\n                return /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"input\", {\n                            type: \"radio\",\n                            name: \"skin-tone\",\n                            value: skin,\n                            \"aria-label\": (0, $7adb23b0109cc36a$export$dbe3113d60765c1a).skins[skin],\n                            ref: checked ? this.refs.skinToneRadio : null,\n                            defaultChecked: checked,\n                            onChange: ()=>this.handleSkinMouseOver(skin),\n                            onKeyDown: (e)=>{\n                                if (e.code == \"Enter\" || e.code == \"Space\" || e.code == \"Tab\") {\n                                    e.preventDefault();\n                                    this.handleSkinClick(skin);\n                                }\n                            }\n                        }),\n                        /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"button\", {\n                            \"aria-hidden\": \"true\",\n                            tabindex: \"-1\",\n                            onClick: ()=>this.handleSkinClick(skin),\n                            onMouseEnter: ()=>this.handleSkinMouseOver(skin),\n                            onMouseLeave: ()=>this.handleSkinMouseOver(),\n                            class: \"option flex flex-grow flex-middle\",\n                            children: [\n                                /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"span\", {\n                                    class: `skin-tone skin-tone-${skin}`\n                                }),\n                                /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"span\", {\n                                    class: \"margin-small-lr\",\n                                    children: (0, $7adb23b0109cc36a$export$dbe3113d60765c1a).skins[skin]\n                                })\n                            ]\n                        })\n                    ]\n                });\n            })\n        });\n    }\n    render() {\n        const lineWidth = this.props.perLine * this.props.emojiButtonSize;\n        return /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"section\", {\n            id: \"root\",\n            class: \"flex flex-column\",\n            dir: this.dir,\n            style: {\n                width: this.props.dynamicWidth ? \"100%\" : `calc(${lineWidth}px + (var(--padding) + var(--sidebar-width)))`\n            },\n            \"data-emoji-set\": this.props.set,\n            \"data-theme\": this.state.theme,\n            \"data-menu\": this.state.showSkins ? \"\" : undefined,\n            children: [\n                this.props.previewPosition == \"top\" && this.renderPreview(),\n                this.props.navPosition == \"top\" && this.renderNav(),\n                this.props.searchPosition == \"sticky\" && /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"div\", {\n                    class: \"padding-lr\",\n                    children: this.renderSearch()\n                }),\n                /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"div\", {\n                    ref: this.refs.scroll,\n                    class: \"scroll flex-grow padding-lr\",\n                    children: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"div\", {\n                        style: {\n                            width: this.props.dynamicWidth ? \"100%\" : lineWidth,\n                            height: \"100%\"\n                        },\n                        children: [\n                            this.props.searchPosition == \"static\" && this.renderSearch(),\n                            this.renderSearchResults(),\n                            this.renderCategories()\n                        ]\n                    })\n                }),\n                this.props.navPosition == \"bottom\" && this.renderNav(),\n                this.props.previewPosition == \"bottom\" && this.renderPreview(),\n                this.state.showSkins && this.renderSkins(),\n                this.renderLiveRegion()\n            ]\n        });\n    }\n    constructor(props){\n        super();\n        (0, $c770c458706daa72$export$2e2bcd8739ae039)(this, \"darkMediaCallback\", ()=>{\n            if (this.props.theme != \"auto\") return;\n            this.setState({\n                theme: this.darkMedia.matches ? \"dark\" : \"light\"\n            });\n        });\n        (0, $c770c458706daa72$export$2e2bcd8739ae039)(this, \"handleClickOutside\", (e)=>{\n            const { element: element  } = this.props;\n            if (e.target != element) {\n                if (this.state.showSkins) this.closeSkins();\n                if (this.props.onClickOutside) this.props.onClickOutside(e);\n            }\n        });\n        (0, $c770c458706daa72$export$2e2bcd8739ae039)(this, \"handleBaseClick\", (e)=>{\n            if (!this.state.showSkins) return;\n            if (!e.target.closest(\".menu\")) {\n                e.preventDefault();\n                e.stopImmediatePropagation();\n                this.closeSkins();\n            }\n        });\n        (0, $c770c458706daa72$export$2e2bcd8739ae039)(this, \"handleBaseKeydown\", (e)=>{\n            if (!this.state.showSkins) return;\n            if (e.key == \"Escape\") {\n                e.preventDefault();\n                e.stopImmediatePropagation();\n                this.closeSkins();\n            }\n        });\n        (0, $c770c458706daa72$export$2e2bcd8739ae039)(this, \"handleSearchClick\", ()=>{\n            const emoji = this.getEmojiByPos(this.state.pos);\n            if (!emoji) return;\n            this.setState({\n                pos: [\n                    -1,\n                    -1\n                ]\n            });\n        });\n        (0, $c770c458706daa72$export$2e2bcd8739ae039)(this, \"handleSearchInput\", async ()=>{\n            const input = this.refs.searchInput.current;\n            if (!input) return;\n            const { value: value  } = input;\n            const searchResults = await (0, $c4d155af13ad4d4b$export$2e2bcd8739ae039).search(value);\n            const afterRender = ()=>{\n                if (!this.refs.scroll.current) return;\n                this.refs.scroll.current.scrollTop = 0;\n            };\n            if (!searchResults) return this.setState({\n                searchResults: searchResults,\n                pos: [\n                    -1,\n                    -1\n                ]\n            }, afterRender);\n            const pos = input.selectionStart == input.value.length ? [\n                0,\n                0\n            ] : [\n                -1,\n                -1\n            ];\n            const grid = [];\n            grid.setsize = searchResults.length;\n            let row = null;\n            for (let emoji of searchResults){\n                if (!grid.length || row.length == this.getPerLine()) {\n                    row = [];\n                    row.__categoryId = \"search\";\n                    row.__index = grid.length;\n                    grid.push(row);\n                }\n                row.push(emoji);\n            }\n            this.ignoreMouse();\n            this.setState({\n                searchResults: grid,\n                pos: pos\n            }, afterRender);\n        });\n        (0, $c770c458706daa72$export$2e2bcd8739ae039)(this, \"handleSearchKeyDown\", (e)=>{\n            // const specialKey = e.altKey || e.ctrlKey || e.metaKey\n            const input = e.currentTarget;\n            e.stopImmediatePropagation();\n            switch(e.key){\n                case \"ArrowLeft\":\n                    // if (specialKey) return\n                    // e.preventDefault()\n                    this.navigate({\n                        e: e,\n                        input: input,\n                        left: true\n                    });\n                    break;\n                case \"ArrowRight\":\n                    // if (specialKey) return\n                    // e.preventDefault()\n                    this.navigate({\n                        e: e,\n                        input: input,\n                        right: true\n                    });\n                    break;\n                case \"ArrowUp\":\n                    // if (specialKey) return\n                    // e.preventDefault()\n                    this.navigate({\n                        e: e,\n                        input: input,\n                        up: true\n                    });\n                    break;\n                case \"ArrowDown\":\n                    // if (specialKey) return\n                    // e.preventDefault()\n                    this.navigate({\n                        e: e,\n                        input: input,\n                        down: true\n                    });\n                    break;\n                case \"Enter\":\n                    e.preventDefault();\n                    this.handleEmojiClick({\n                        e: e,\n                        pos: this.state.pos\n                    });\n                    break;\n                case \"Escape\":\n                    e.preventDefault();\n                    if (this.state.searchResults) this.clearSearch();\n                    else this.unfocusSearch();\n                    break;\n                default:\n                    break;\n            }\n        });\n        (0, $c770c458706daa72$export$2e2bcd8739ae039)(this, \"clearSearch\", ()=>{\n            const input = this.refs.searchInput.current;\n            if (!input) return;\n            input.value = \"\";\n            input.focus();\n            this.handleSearchInput();\n        });\n        (0, $c770c458706daa72$export$2e2bcd8739ae039)(this, \"handleCategoryClick\", ({ category: category , i: i  })=>{\n            this.scrollTo(i == 0 ? {\n                row: -1\n            } : {\n                categoryId: category.id\n            });\n        });\n        (0, $c770c458706daa72$export$2e2bcd8739ae039)(this, \"openSkins\", (e)=>{\n            const { currentTarget: currentTarget  } = e;\n            const rect = currentTarget.getBoundingClientRect();\n            this.setState({\n                showSkins: rect\n            }, async ()=>{\n                // Firefox requires 2 frames for the transition to consistenly work\n                await (0, $693b183b0a78708f$export$e772c8ff12451969)(2);\n                const menu = this.refs.menu.current;\n                if (!menu) return;\n                menu.classList.remove(\"hidden\");\n                this.refs.skinToneRadio.current.focus();\n                this.base.addEventListener(\"click\", this.handleBaseClick, true);\n                this.base.addEventListener(\"keydown\", this.handleBaseKeydown, true);\n            });\n        });\n        this.observers = [];\n        this.state = {\n            pos: [\n                -1,\n                -1\n            ],\n            perLine: this.initDynamicPerLine(props),\n            visibleRows: {\n                0: true\n            },\n            ...this.getInitialState(props)\n        };\n    }\n}\n\n\n\n\n\n\n\n\n\nclass $efa000751917694d$export$2e2bcd8739ae039 extends (0, $26f27c338a96b1a6$export$2e2bcd8739ae039) {\n    async connectedCallback() {\n        const props = (0, $7adb23b0109cc36a$export$75fe5f91d452f94b)(this.props, (0, $b247ea80b67298d5$export$2e2bcd8739ae039), this);\n        props.element = this;\n        props.ref = (component)=>{\n            this.component = component;\n        };\n        await (0, $7adb23b0109cc36a$export$2cd8252107eb640b)(props);\n        if (this.disconnected) return;\n        (0, $fb96b826c0c5f37a$export$b3890eb0ae9dca99)(/*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)((0, $89bd6bb200cc8fef$export$2e2bcd8739ae039), {\n            ...props\n        }), this.shadowRoot);\n    }\n    constructor(props){\n        super(props, {\n            styles: (0, (/*@__PURE__*/$parcel$interopDefault($329d53ba9fd7125f$exports)))\n        });\n    }\n}\n(0, $c770c458706daa72$export$2e2bcd8739ae039)($efa000751917694d$export$2e2bcd8739ae039, \"Props\", (0, $b247ea80b67298d5$export$2e2bcd8739ae039));\nif (typeof customElements !== \"undefined\" && !customElements.get(\"em-emoji-picker\")) customElements.define(\"em-emoji-picker\", $efa000751917694d$export$2e2bcd8739ae039);\n\n\nvar $329d53ba9fd7125f$exports = {};\n$329d53ba9fd7125f$exports = \":host {\\n  width: min-content;\\n  height: 435px;\\n  min-height: 230px;\\n  border-radius: var(--border-radius);\\n  box-shadow: var(--shadow);\\n  --border-radius: 10px;\\n  --category-icon-size: 18px;\\n  --font-family: -apple-system, BlinkMacSystemFont, \\\"Helvetica Neue\\\", sans-serif;\\n  --font-size: 15px;\\n  --preview-placeholder-size: 21px;\\n  --preview-title-size: 1.1em;\\n  --preview-subtitle-size: .9em;\\n  --shadow-color: 0deg 0% 0%;\\n  --shadow: .3px .5px 2.7px hsl(var(--shadow-color) / .14), .4px .8px 1px -3.2px hsl(var(--shadow-color) / .14), 1px 2px 2.5px -4.5px hsl(var(--shadow-color) / .14);\\n  display: flex;\\n}\\n\\n[data-theme=\\\"light\\\"] {\\n  --em-rgb-color: var(--rgb-color, 34, 36, 39);\\n  --em-rgb-accent: var(--rgb-accent, 34, 102, 237);\\n  --em-rgb-background: var(--rgb-background, 255, 255, 255);\\n  --em-rgb-input: var(--rgb-input, 255, 255, 255);\\n  --em-color-border: var(--color-border, rgba(0, 0, 0, .05));\\n  --em-color-border-over: var(--color-border-over, rgba(0, 0, 0, .1));\\n}\\n\\n[data-theme=\\\"dark\\\"] {\\n  --em-rgb-color: var(--rgb-color, 222, 222, 221);\\n  --em-rgb-accent: var(--rgb-accent, 58, 130, 247);\\n  --em-rgb-background: var(--rgb-background, 21, 22, 23);\\n  --em-rgb-input: var(--rgb-input, 0, 0, 0);\\n  --em-color-border: var(--color-border, rgba(255, 255, 255, .1));\\n  --em-color-border-over: var(--color-border-over, rgba(255, 255, 255, .2));\\n}\\n\\n#root {\\n  --color-a: rgb(var(--em-rgb-color));\\n  --color-b: rgba(var(--em-rgb-color), .65);\\n  --color-c: rgba(var(--em-rgb-color), .45);\\n  --padding: 12px;\\n  --padding-small: calc(var(--padding) / 2);\\n  --sidebar-width: 16px;\\n  --duration: 225ms;\\n  --duration-fast: 125ms;\\n  --duration-instant: 50ms;\\n  --easing: cubic-bezier(.4, 0, .2, 1);\\n  width: 100%;\\n  text-align: left;\\n  border-radius: var(--border-radius);\\n  background-color: rgb(var(--em-rgb-background));\\n  position: relative;\\n}\\n\\n@media (prefers-reduced-motion) {\\n  #root {\\n    --duration: 0;\\n    --duration-fast: 0;\\n    --duration-instant: 0;\\n  }\\n}\\n\\n#root[data-menu] button {\\n  cursor: auto;\\n}\\n\\n#root[data-menu] .menu button {\\n  cursor: pointer;\\n}\\n\\n:host, #root, input, button {\\n  color: rgb(var(--em-rgb-color));\\n  font-family: var(--font-family);\\n  font-size: var(--font-size);\\n  -webkit-font-smoothing: antialiased;\\n  -moz-osx-font-smoothing: grayscale;\\n  line-height: normal;\\n}\\n\\n*, :before, :after {\\n  box-sizing: border-box;\\n  min-width: 0;\\n  margin: 0;\\n  padding: 0;\\n}\\n\\n.relative {\\n  position: relative;\\n}\\n\\n.flex {\\n  display: flex;\\n}\\n\\n.flex-auto {\\n  flex: none;\\n}\\n\\n.flex-center {\\n  justify-content: center;\\n}\\n\\n.flex-column {\\n  flex-direction: column;\\n}\\n\\n.flex-grow {\\n  flex: auto;\\n}\\n\\n.flex-middle {\\n  align-items: center;\\n}\\n\\n.flex-wrap {\\n  flex-wrap: wrap;\\n}\\n\\n.padding {\\n  padding: var(--padding);\\n}\\n\\n.padding-t {\\n  padding-top: var(--padding);\\n}\\n\\n.padding-lr {\\n  padding-left: var(--padding);\\n  padding-right: var(--padding);\\n}\\n\\n.padding-r {\\n  padding-right: var(--padding);\\n}\\n\\n.padding-small {\\n  padding: var(--padding-small);\\n}\\n\\n.padding-small-b {\\n  padding-bottom: var(--padding-small);\\n}\\n\\n.padding-small-lr {\\n  padding-left: var(--padding-small);\\n  padding-right: var(--padding-small);\\n}\\n\\n.margin {\\n  margin: var(--padding);\\n}\\n\\n.margin-r {\\n  margin-right: var(--padding);\\n}\\n\\n.margin-l {\\n  margin-left: var(--padding);\\n}\\n\\n.margin-small-l {\\n  margin-left: var(--padding-small);\\n}\\n\\n.margin-small-lr {\\n  margin-left: var(--padding-small);\\n  margin-right: var(--padding-small);\\n}\\n\\n.align-l {\\n  text-align: left;\\n}\\n\\n.align-r {\\n  text-align: right;\\n}\\n\\n.color-a {\\n  color: var(--color-a);\\n}\\n\\n.color-b {\\n  color: var(--color-b);\\n}\\n\\n.color-c {\\n  color: var(--color-c);\\n}\\n\\n.ellipsis {\\n  white-space: nowrap;\\n  max-width: 100%;\\n  width: auto;\\n  text-overflow: ellipsis;\\n  overflow: hidden;\\n}\\n\\n.sr-only {\\n  width: 1px;\\n  height: 1px;\\n  position: absolute;\\n  top: auto;\\n  left: -10000px;\\n  overflow: hidden;\\n}\\n\\na {\\n  cursor: pointer;\\n  color: rgb(var(--em-rgb-accent));\\n}\\n\\na:hover {\\n  text-decoration: underline;\\n}\\n\\n.spacer {\\n  height: 10px;\\n}\\n\\n[dir=\\\"rtl\\\"] .scroll {\\n  padding-left: 0;\\n  padding-right: var(--padding);\\n}\\n\\n.scroll {\\n  padding-right: 0;\\n  overflow-x: hidden;\\n  overflow-y: auto;\\n}\\n\\n.scroll::-webkit-scrollbar {\\n  width: var(--sidebar-width);\\n  height: var(--sidebar-width);\\n}\\n\\n.scroll::-webkit-scrollbar-track {\\n  border: 0;\\n}\\n\\n.scroll::-webkit-scrollbar-button {\\n  width: 0;\\n  height: 0;\\n  display: none;\\n}\\n\\n.scroll::-webkit-scrollbar-corner {\\n  background-color: rgba(0, 0, 0, 0);\\n}\\n\\n.scroll::-webkit-scrollbar-thumb {\\n  min-height: 20%;\\n  min-height: 65px;\\n  border: 4px solid rgb(var(--em-rgb-background));\\n  border-radius: 8px;\\n}\\n\\n.scroll::-webkit-scrollbar-thumb:hover {\\n  background-color: var(--em-color-border-over) !important;\\n}\\n\\n.scroll:hover::-webkit-scrollbar-thumb {\\n  background-color: var(--em-color-border);\\n}\\n\\n.sticky {\\n  z-index: 1;\\n  background-color: rgba(var(--em-rgb-background), .9);\\n  -webkit-backdrop-filter: blur(4px);\\n  backdrop-filter: blur(4px);\\n  font-weight: 500;\\n  position: sticky;\\n  top: -1px;\\n}\\n\\n[dir=\\\"rtl\\\"] .search input[type=\\\"search\\\"] {\\n  padding: 10px 2.2em 10px 2em;\\n}\\n\\n[dir=\\\"rtl\\\"] .search .loupe {\\n  left: auto;\\n  right: .7em;\\n}\\n\\n[dir=\\\"rtl\\\"] .search .delete {\\n  left: .7em;\\n  right: auto;\\n}\\n\\n.search {\\n  z-index: 2;\\n  position: relative;\\n}\\n\\n.search input, .search button {\\n  font-size: calc(var(--font-size)  - 1px);\\n}\\n\\n.search input[type=\\\"search\\\"] {\\n  width: 100%;\\n  background-color: var(--em-color-border);\\n  transition-duration: var(--duration);\\n  transition-property: background-color, box-shadow;\\n  transition-timing-function: var(--easing);\\n  border: 0;\\n  border-radius: 10px;\\n  outline: 0;\\n  padding: 10px 2em 10px 2.2em;\\n  display: block;\\n}\\n\\n.search input[type=\\\"search\\\"]::-ms-input-placeholder {\\n  color: inherit;\\n  opacity: .6;\\n}\\n\\n.search input[type=\\\"search\\\"]::placeholder {\\n  color: inherit;\\n  opacity: .6;\\n}\\n\\n.search input[type=\\\"search\\\"], .search input[type=\\\"search\\\"]::-webkit-search-decoration, .search input[type=\\\"search\\\"]::-webkit-search-cancel-button, .search input[type=\\\"search\\\"]::-webkit-search-results-button, .search input[type=\\\"search\\\"]::-webkit-search-results-decoration {\\n  -webkit-appearance: none;\\n  -ms-appearance: none;\\n  appearance: none;\\n}\\n\\n.search input[type=\\\"search\\\"]:focus {\\n  background-color: rgb(var(--em-rgb-input));\\n  box-shadow: inset 0 0 0 1px rgb(var(--em-rgb-accent)), 0 1px 3px rgba(65, 69, 73, .2);\\n}\\n\\n.search .icon {\\n  z-index: 1;\\n  color: rgba(var(--em-rgb-color), .7);\\n  position: absolute;\\n  top: 50%;\\n  transform: translateY(-50%);\\n}\\n\\n.search .loupe {\\n  pointer-events: none;\\n  left: .7em;\\n}\\n\\n.search .delete {\\n  right: .7em;\\n}\\n\\nsvg {\\n  fill: currentColor;\\n  width: 1em;\\n  height: 1em;\\n}\\n\\nbutton {\\n  -webkit-appearance: none;\\n  -ms-appearance: none;\\n  appearance: none;\\n  cursor: pointer;\\n  color: currentColor;\\n  background-color: rgba(0, 0, 0, 0);\\n  border: 0;\\n}\\n\\n#nav {\\n  z-index: 2;\\n  padding-top: 12px;\\n  padding-bottom: 12px;\\n  padding-right: var(--sidebar-width);\\n  position: relative;\\n}\\n\\n#nav button {\\n  color: var(--color-b);\\n  transition: color var(--duration) var(--easing);\\n}\\n\\n#nav button:hover {\\n  color: var(--color-a);\\n}\\n\\n#nav svg, #nav img {\\n  width: var(--category-icon-size);\\n  height: var(--category-icon-size);\\n}\\n\\n#nav[dir=\\\"rtl\\\"] .bar {\\n  left: auto;\\n  right: 0;\\n}\\n\\n#nav .bar {\\n  width: 100%;\\n  height: 3px;\\n  background-color: rgb(var(--em-rgb-accent));\\n  transition: transform var(--duration) var(--easing);\\n  border-radius: 3px 3px 0 0;\\n  position: absolute;\\n  bottom: -12px;\\n  left: 0;\\n}\\n\\n#nav button[aria-selected] {\\n  color: rgb(var(--em-rgb-accent));\\n}\\n\\n#preview {\\n  z-index: 2;\\n  padding: calc(var(--padding)  + 4px) var(--padding);\\n  padding-right: var(--sidebar-width);\\n  position: relative;\\n}\\n\\n#preview .preview-placeholder {\\n  font-size: var(--preview-placeholder-size);\\n}\\n\\n#preview .preview-title {\\n  font-size: var(--preview-title-size);\\n}\\n\\n#preview .preview-subtitle {\\n  font-size: var(--preview-subtitle-size);\\n}\\n\\n#nav:before, #preview:before {\\n  content: \\\"\\\";\\n  height: 2px;\\n  position: absolute;\\n  left: 0;\\n  right: 0;\\n}\\n\\n#nav[data-position=\\\"top\\\"]:before, #preview[data-position=\\\"top\\\"]:before {\\n  background: linear-gradient(to bottom, var(--em-color-border), transparent);\\n  top: 100%;\\n}\\n\\n#nav[data-position=\\\"bottom\\\"]:before, #preview[data-position=\\\"bottom\\\"]:before {\\n  background: linear-gradient(to top, var(--em-color-border), transparent);\\n  bottom: 100%;\\n}\\n\\n.category:last-child {\\n  min-height: calc(100% + 1px);\\n}\\n\\n.category button {\\n  font-family: -apple-system, BlinkMacSystemFont, Helvetica Neue, sans-serif;\\n  position: relative;\\n}\\n\\n.category button > * {\\n  position: relative;\\n}\\n\\n.category button .background {\\n  opacity: 0;\\n  background-color: var(--em-color-border);\\n  transition: opacity var(--duration-fast) var(--easing) var(--duration-instant);\\n  position: absolute;\\n  top: 0;\\n  bottom: 0;\\n  left: 0;\\n  right: 0;\\n}\\n\\n.category button:hover .background {\\n  transition-duration: var(--duration-instant);\\n  transition-delay: 0s;\\n}\\n\\n.category button[aria-selected] .background {\\n  opacity: 1;\\n}\\n\\n.category button[data-keyboard] .background {\\n  transition: none;\\n}\\n\\n.row {\\n  width: 100%;\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n}\\n\\n.skin-tone-button {\\n  border: 1px solid rgba(0, 0, 0, 0);\\n  border-radius: 100%;\\n}\\n\\n.skin-tone-button:hover {\\n  border-color: var(--em-color-border);\\n}\\n\\n.skin-tone-button:active .skin-tone {\\n  transform: scale(.85) !important;\\n}\\n\\n.skin-tone-button .skin-tone {\\n  transition: transform var(--duration) var(--easing);\\n}\\n\\n.skin-tone-button[aria-selected] {\\n  background-color: var(--em-color-border);\\n  border-top-color: rgba(0, 0, 0, .05);\\n  border-bottom-color: rgba(0, 0, 0, 0);\\n  border-left-width: 0;\\n  border-right-width: 0;\\n}\\n\\n.skin-tone-button[aria-selected] .skin-tone {\\n  transform: scale(.9);\\n}\\n\\n.menu {\\n  z-index: 2;\\n  white-space: nowrap;\\n  border: 1px solid var(--em-color-border);\\n  background-color: rgba(var(--em-rgb-background), .9);\\n  -webkit-backdrop-filter: blur(4px);\\n  backdrop-filter: blur(4px);\\n  transition-property: opacity, transform;\\n  transition-duration: var(--duration);\\n  transition-timing-function: var(--easing);\\n  border-radius: 10px;\\n  padding: 4px;\\n  position: absolute;\\n  box-shadow: 1px 1px 5px rgba(0, 0, 0, .05);\\n}\\n\\n.menu.hidden {\\n  opacity: 0;\\n}\\n\\n.menu[data-position=\\\"bottom\\\"] {\\n  transform-origin: 100% 100%;\\n}\\n\\n.menu[data-position=\\\"bottom\\\"].hidden {\\n  transform: scale(.9)rotate(-3deg)translateY(5%);\\n}\\n\\n.menu[data-position=\\\"top\\\"] {\\n  transform-origin: 100% 0;\\n}\\n\\n.menu[data-position=\\\"top\\\"].hidden {\\n  transform: scale(.9)rotate(3deg)translateY(-5%);\\n}\\n\\n.menu input[type=\\\"radio\\\"] {\\n  clip: rect(0 0 0 0);\\n  width: 1px;\\n  height: 1px;\\n  border: 0;\\n  margin: 0;\\n  padding: 0;\\n  position: absolute;\\n  overflow: hidden;\\n}\\n\\n.menu input[type=\\\"radio\\\"]:checked + .option {\\n  box-shadow: 0 0 0 2px rgb(var(--em-rgb-accent));\\n}\\n\\n.option {\\n  width: 100%;\\n  border-radius: 6px;\\n  padding: 4px 6px;\\n}\\n\\n.option:hover {\\n  color: #fff;\\n  background-color: rgb(var(--em-rgb-accent));\\n}\\n\\n.skin-tone {\\n  width: 16px;\\n  height: 16px;\\n  border-radius: 100%;\\n  display: inline-block;\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.skin-tone:after {\\n  content: \\\"\\\";\\n  mix-blend-mode: overlay;\\n  background: linear-gradient(rgba(255, 255, 255, .2), rgba(0, 0, 0, 0));\\n  border: 1px solid rgba(0, 0, 0, .8);\\n  border-radius: 100%;\\n  position: absolute;\\n  top: 0;\\n  bottom: 0;\\n  left: 0;\\n  right: 0;\\n  box-shadow: inset 0 -2px 3px #000, inset 0 1px 2px #fff;\\n}\\n\\n.skin-tone-1 {\\n  background-color: #ffc93a;\\n}\\n\\n.skin-tone-2 {\\n  background-color: #ffdab7;\\n}\\n\\n.skin-tone-3 {\\n  background-color: #e7b98f;\\n}\\n\\n.skin-tone-4 {\\n  background-color: #c88c61;\\n}\\n\\n.skin-tone-5 {\\n  background-color: #a46134;\\n}\\n\\n.skin-tone-6 {\\n  background-color: #5d4437;\\n}\\n\\n[data-index] {\\n  justify-content: space-between;\\n}\\n\\n[data-emoji-set=\\\"twitter\\\"] .skin-tone:after {\\n  box-shadow: none;\\n  border-color: rgba(0, 0, 0, .5);\\n}\\n\\n[data-emoji-set=\\\"twitter\\\"] .skin-tone-1 {\\n  background-color: #fade72;\\n}\\n\\n[data-emoji-set=\\\"twitter\\\"] .skin-tone-2 {\\n  background-color: #f3dfd0;\\n}\\n\\n[data-emoji-set=\\\"twitter\\\"] .skin-tone-3 {\\n  background-color: #eed3a8;\\n}\\n\\n[data-emoji-set=\\\"twitter\\\"] .skin-tone-4 {\\n  background-color: #cfad8d;\\n}\\n\\n[data-emoji-set=\\\"twitter\\\"] .skin-tone-5 {\\n  background-color: #a8805d;\\n}\\n\\n[data-emoji-set=\\\"twitter\\\"] .skin-tone-6 {\\n  background-color: #765542;\\n}\\n\\n[data-emoji-set=\\\"google\\\"] .skin-tone:after {\\n  box-shadow: inset 0 0 2px 2px rgba(0, 0, 0, .4);\\n}\\n\\n[data-emoji-set=\\\"google\\\"] .skin-tone-1 {\\n  background-color: #f5c748;\\n}\\n\\n[data-emoji-set=\\\"google\\\"] .skin-tone-2 {\\n  background-color: #f1d5aa;\\n}\\n\\n[data-emoji-set=\\\"google\\\"] .skin-tone-3 {\\n  background-color: #d4b48d;\\n}\\n\\n[data-emoji-set=\\\"google\\\"] .skin-tone-4 {\\n  background-color: #aa876b;\\n}\\n\\n[data-emoji-set=\\\"google\\\"] .skin-tone-5 {\\n  background-color: #916544;\\n}\\n\\n[data-emoji-set=\\\"google\\\"] .skin-tone-6 {\\n  background-color: #61493f;\\n}\\n\\n[data-emoji-set=\\\"facebook\\\"] .skin-tone:after {\\n  border-color: rgba(0, 0, 0, .4);\\n  box-shadow: inset 0 -2px 3px #000, inset 0 1px 4px #fff;\\n}\\n\\n[data-emoji-set=\\\"facebook\\\"] .skin-tone-1 {\\n  background-color: #f5c748;\\n}\\n\\n[data-emoji-set=\\\"facebook\\\"] .skin-tone-2 {\\n  background-color: #f1d5aa;\\n}\\n\\n[data-emoji-set=\\\"facebook\\\"] .skin-tone-3 {\\n  background-color: #d4b48d;\\n}\\n\\n[data-emoji-set=\\\"facebook\\\"] .skin-tone-4 {\\n  background-color: #aa876b;\\n}\\n\\n[data-emoji-set=\\\"facebook\\\"] .skin-tone-5 {\\n  background-color: #916544;\\n}\\n\\n[data-emoji-set=\\\"facebook\\\"] .skin-tone-6 {\\n  background-color: #61493f;\\n}\\n\\n\";\n\n\n\n\n\n\n\n\n\n\n\n//# sourceMappingURL=module.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/emoji-mart/dist/module.js\n"));

/***/ })

}]);