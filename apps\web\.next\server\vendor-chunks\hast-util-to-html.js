"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/hast-util-to-html";
exports.ids = ["vendor-chunks/hast-util-to-html"];
exports.modules = {

/***/ "(ssr)/../../node_modules/hast-util-to-html/lib/handle/comment.js":
/*!******************************************************************!*\
  !*** ../../node_modules/hast-util-to-html/lib/handle/comment.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   comment: () => (/* binding */ comment)\n/* harmony export */ });\n/* harmony import */ var stringify_entities__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! stringify-entities */ \"(ssr)/../../node_modules/stringify-entities/lib/index.js\");\n/**\n * @typedef {import('../types.js').Comment} Comment\n * @typedef {import('../types.js').Parent} Parent\n * @typedef {import('../types.js').State} State\n */\n\n\n\n/**\n * Serialize a comment.\n *\n * @param {Comment} node\n *   Node to handle.\n * @param {number | undefined} _1\n *   Index of `node` in `parent.\n * @param {Parent | undefined} _2\n *   Parent of `node`.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {string}\n *   Serialized node.\n */\nfunction comment(node, _1, _2, state) {\n  // See: <https://html.spec.whatwg.org/multipage/syntax.html#comments>\n  return state.settings.bogusComments\n    ? '<?' +\n        (0,stringify_entities__WEBPACK_IMPORTED_MODULE_0__.stringifyEntities)(\n          node.value,\n          Object.assign({}, state.settings.characterReferences, {subset: ['>']})\n        ) +\n        '>'\n    : '<!--' + node.value.replace(/^>|^->|<!--|-->|--!>|<!-$/g, encode) + '-->'\n\n  /**\n   * @param {string} $0\n   */\n  function encode($0) {\n    return (0,stringify_entities__WEBPACK_IMPORTED_MODULE_0__.stringifyEntities)(\n      $0,\n      Object.assign({}, state.settings.characterReferences, {\n        subset: ['<', '>']\n      })\n    )\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/hast-util-to-html/lib/handle/comment.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/hast-util-to-html/lib/handle/doctype.js":
/*!******************************************************************!*\
  !*** ../../node_modules/hast-util-to-html/lib/handle/doctype.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   doctype: () => (/* binding */ doctype)\n/* harmony export */ });\n/**\n * @typedef {import('../types.js').DocType} DocType\n * @typedef {import('../types.js').Parent} Parent\n * @typedef {import('../types.js').State} State\n */\n\n/**\n * Serialize a doctype.\n *\n * @param {DocType} _1\n *   Node to handle.\n * @param {number | undefined} _2\n *   Index of `node` in `parent.\n * @param {Parent | undefined} _3\n *   Parent of `node`.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {string}\n *   Serialized node.\n */\nfunction doctype(_1, _2, _3, state) {\n  return (\n    '<!' +\n    (state.settings.upperDoctype ? 'DOCTYPE' : 'doctype') +\n    (state.settings.tightDoctype ? '' : ' ') +\n    'html>'\n  )\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2hhc3QtdXRpbC10by1odG1sL2xpYi9oYW5kbGUvZG9jdHlwZS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQSxhQUFhLCtCQUErQjtBQUM1QyxhQUFhLDhCQUE4QjtBQUMzQyxhQUFhLDZCQUE2QjtBQUMxQzs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFNBQVM7QUFDcEI7QUFDQSxXQUFXLG9CQUFvQjtBQUMvQjtBQUNBLFdBQVcsb0JBQW9CO0FBQy9CO0FBQ0EsV0FBVyxPQUFPO0FBQ2xCO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQG9wZW5jYW52YXMvd2ViLy4uLy4uL25vZGVfbW9kdWxlcy9oYXN0LXV0aWwtdG8taHRtbC9saWIvaGFuZGxlL2RvY3R5cGUuanM/ZDE1NyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuRG9jVHlwZX0gRG9jVHlwZVxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5QYXJlbnR9IFBhcmVudFxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5TdGF0ZX0gU3RhdGVcbiAqL1xuXG4vKipcbiAqIFNlcmlhbGl6ZSBhIGRvY3R5cGUuXG4gKlxuICogQHBhcmFtIHtEb2NUeXBlfSBfMVxuICogICBOb2RlIHRvIGhhbmRsZS5cbiAqIEBwYXJhbSB7bnVtYmVyIHwgdW5kZWZpbmVkfSBfMlxuICogICBJbmRleCBvZiBgbm9kZWAgaW4gYHBhcmVudC5cbiAqIEBwYXJhbSB7UGFyZW50IHwgdW5kZWZpbmVkfSBfM1xuICogICBQYXJlbnQgb2YgYG5vZGVgLlxuICogQHBhcmFtIHtTdGF0ZX0gc3RhdGVcbiAqICAgSW5mbyBwYXNzZWQgYXJvdW5kIGFib3V0IHRoZSBjdXJyZW50IHN0YXRlLlxuICogQHJldHVybnMge3N0cmluZ31cbiAqICAgU2VyaWFsaXplZCBub2RlLlxuICovXG5leHBvcnQgZnVuY3Rpb24gZG9jdHlwZShfMSwgXzIsIF8zLCBzdGF0ZSkge1xuICByZXR1cm4gKFxuICAgICc8IScgK1xuICAgIChzdGF0ZS5zZXR0aW5ncy51cHBlckRvY3R5cGUgPyAnRE9DVFlQRScgOiAnZG9jdHlwZScpICtcbiAgICAoc3RhdGUuc2V0dGluZ3MudGlnaHREb2N0eXBlID8gJycgOiAnICcpICtcbiAgICAnaHRtbD4nXG4gIClcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/hast-util-to-html/lib/handle/doctype.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/hast-util-to-html/lib/handle/element.js":
/*!******************************************************************!*\
  !*** ../../node_modules/hast-util-to-html/lib/handle/element.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   element: () => (/* binding */ element)\n/* harmony export */ });\n/* harmony import */ var ccount__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ccount */ \"(ssr)/../../node_modules/ccount/index.js\");\n/* harmony import */ var comma_separated_tokens__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! comma-separated-tokens */ \"(ssr)/../../node_modules/comma-separated-tokens/index.js\");\n/* harmony import */ var property_information__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! property-information */ \"(ssr)/../../node_modules/property-information/index.js\");\n/* harmony import */ var property_information__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! property-information */ \"(ssr)/../../node_modules/property-information/lib/find.js\");\n/* harmony import */ var space_separated_tokens__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! space-separated-tokens */ \"(ssr)/../../node_modules/space-separated-tokens/index.js\");\n/* harmony import */ var stringify_entities__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! stringify-entities */ \"(ssr)/../../node_modules/stringify-entities/lib/index.js\");\n/* harmony import */ var _omission_opening_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../omission/opening.js */ \"(ssr)/../../node_modules/hast-util-to-html/lib/omission/opening.js\");\n/* harmony import */ var _omission_closing_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../omission/closing.js */ \"(ssr)/../../node_modules/hast-util-to-html/lib/omission/closing.js\");\n/**\n * @typedef {import('../types.js').State} State\n * @typedef {import('../types.js').Parent} Parent\n * @typedef {import('../types.js').Element} Element\n * @typedef {import('../types.js').Properties} Properties\n * @typedef {import('../types.js').PropertyValue} PropertyValue\n */\n\n\n\n\n\n\n\n\n\n/**\n * Maps of subsets.\n *\n * Each value is a matrix of tuples.\n * The value at `0` causes parse errors, the value at `1` is valid.\n * Of both, the value at `0` is unsafe, and the value at `1` is safe.\n *\n * @type {Record<'name' | 'unquoted' | 'single' | 'double', Array<[Array<string>, Array<string>]>>}\n */\nconst constants = {\n  // See: <https://html.spec.whatwg.org/#attribute-name-state>.\n  name: [\n    ['\\t\\n\\f\\r &/=>'.split(''), '\\t\\n\\f\\r \"&\\'/=>`'.split('')],\n    ['\\0\\t\\n\\f\\r \"&\\'/<=>'.split(''), '\\0\\t\\n\\f\\r \"&\\'/<=>`'.split('')]\n  ],\n  // See: <https://html.spec.whatwg.org/#attribute-value-(unquoted)-state>.\n  unquoted: [\n    ['\\t\\n\\f\\r &>'.split(''), '\\0\\t\\n\\f\\r \"&\\'<=>`'.split('')],\n    ['\\0\\t\\n\\f\\r \"&\\'<=>`'.split(''), '\\0\\t\\n\\f\\r \"&\\'<=>`'.split('')]\n  ],\n  // See: <https://html.spec.whatwg.org/#attribute-value-(single-quoted)-state>.\n  single: [\n    [\"&'\".split(''), '\"&\\'`'.split('')],\n    [\"\\0&'\".split(''), '\\0\"&\\'`'.split('')]\n  ],\n  // See: <https://html.spec.whatwg.org/#attribute-value-(double-quoted)-state>.\n  double: [\n    ['\"&'.split(''), '\"&\\'`'.split('')],\n    ['\\0\"&'.split(''), '\\0\"&\\'`'.split('')]\n  ]\n}\n\n/**\n * Serialize an element node.\n *\n * @param {Element} node\n *   Node to handle.\n * @param {number | undefined} index\n *   Index of `node` in `parent.\n * @param {Parent | undefined} parent\n *   Parent of `node`.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {string}\n *   Serialized node.\n */\n// eslint-disable-next-line complexity\nfunction element(node, index, parent, state) {\n  const schema = state.schema\n  const omit = schema.space === 'svg' ? false : state.settings.omitOptionalTags\n  let selfClosing =\n    schema.space === 'svg'\n      ? state.settings.closeEmptyElements\n      : state.settings.voids.includes(node.tagName.toLowerCase())\n  /** @type {Array<string>} */\n  const parts = []\n  /** @type {string} */\n  let last\n\n  if (schema.space === 'html' && node.tagName === 'svg') {\n    state.schema = property_information__WEBPACK_IMPORTED_MODULE_0__.svg\n  }\n\n  const attrs = serializeAttributes(state, node.properties)\n\n  const content = state.all(\n    schema.space === 'html' && node.tagName === 'template' ? node.content : node\n  )\n\n  state.schema = schema\n\n  // If the node is categorised as void, but it has children, remove the\n  // categorisation.\n  // This enables for example `menuitem`s, which are void in W3C HTML but not\n  // void in WHATWG HTML, to be stringified properly.\n  if (content) selfClosing = false\n\n  if (attrs || !omit || !(0,_omission_opening_js__WEBPACK_IMPORTED_MODULE_1__.opening)(node, index, parent)) {\n    parts.push('<', node.tagName, attrs ? ' ' + attrs : '')\n\n    if (\n      selfClosing &&\n      (schema.space === 'svg' || state.settings.closeSelfClosing)\n    ) {\n      last = attrs.charAt(attrs.length - 1)\n      if (\n        !state.settings.tightSelfClosing ||\n        last === '/' ||\n        (last && last !== '\"' && last !== \"'\")\n      ) {\n        parts.push(' ')\n      }\n\n      parts.push('/')\n    }\n\n    parts.push('>')\n  }\n\n  parts.push(content)\n\n  if (!selfClosing && (!omit || !(0,_omission_closing_js__WEBPACK_IMPORTED_MODULE_2__.closing)(node, index, parent))) {\n    parts.push('</' + node.tagName + '>')\n  }\n\n  return parts.join('')\n}\n\n/**\n * @param {State} state\n * @param {Properties | null | undefined} props\n * @returns {string}\n */\nfunction serializeAttributes(state, props) {\n  /** @type {Array<string>} */\n  const values = []\n  let index = -1\n  /** @type {string} */\n  let key\n\n  if (props) {\n    for (key in props) {\n      if (props[key] !== undefined && props[key] !== null) {\n        const value = serializeAttribute(state, key, props[key])\n        if (value) values.push(value)\n      }\n    }\n  }\n\n  while (++index < values.length) {\n    const last = state.settings.tightAttributes\n      ? values[index].charAt(values[index].length - 1)\n      : null\n\n    // In tight mode, don’t add a space after quoted attributes.\n    if (index !== values.length - 1 && last !== '\"' && last !== \"'\") {\n      values[index] += ' '\n    }\n  }\n\n  return values.join('')\n}\n\n/**\n * @param {State} state\n * @param {string} key\n * @param {PropertyValue} value\n * @returns {string}\n */\n// eslint-disable-next-line complexity\nfunction serializeAttribute(state, key, value) {\n  const info = (0,property_information__WEBPACK_IMPORTED_MODULE_3__.find)(state.schema, key)\n  const x =\n    state.settings.allowParseErrors && state.schema.space === 'html' ? 0 : 1\n  const y = state.settings.allowDangerousCharacters ? 0 : 1\n  let quote = state.quote\n  /** @type {string | undefined} */\n  let result\n\n  if (info.overloadedBoolean && (value === info.attribute || value === '')) {\n    value = true\n  } else if (\n    info.boolean ||\n    (info.overloadedBoolean && typeof value !== 'string')\n  ) {\n    value = Boolean(value)\n  }\n\n  if (\n    value === undefined ||\n    value === null ||\n    value === false ||\n    (typeof value === 'number' && Number.isNaN(value))\n  ) {\n    return ''\n  }\n\n  const name = (0,stringify_entities__WEBPACK_IMPORTED_MODULE_4__.stringifyEntities)(\n    info.attribute,\n    Object.assign({}, state.settings.characterReferences, {\n      // Always encode without parse errors in non-HTML.\n      subset: constants.name[x][y]\n    })\n  )\n\n  // No value.\n  // There is currently only one boolean property in SVG: `[download]` on\n  // `<a>`.\n  // This property does not seem to work in browsers (Firefox, Safari, Chrome),\n  // so I can’t test if dropping the value works.\n  // But I assume that it should:\n  //\n  // ```html\n  // <!doctype html>\n  // <svg viewBox=\"0 0 100 100\">\n  //   <a href=https://example.com download>\n  //     <circle cx=50 cy=40 r=35 />\n  //   </a>\n  // </svg>\n  // ```\n  //\n  // See: <https://github.com/wooorm/property-information/blob/main/lib/svg.js>\n  if (value === true) return name\n\n  // `spaces` doesn’t accept a second argument, but it’s given here just to\n  // keep the code cleaner.\n  value = Array.isArray(value)\n    ? (info.commaSeparated ? comma_separated_tokens__WEBPACK_IMPORTED_MODULE_5__.stringify : space_separated_tokens__WEBPACK_IMPORTED_MODULE_6__.stringify)(value, {\n        padLeft: !state.settings.tightCommaSeparatedLists\n      })\n    : String(value)\n\n  if (state.settings.collapseEmptyAttributes && !value) return name\n\n  // Check unquoted value.\n  if (state.settings.preferUnquoted) {\n    result = (0,stringify_entities__WEBPACK_IMPORTED_MODULE_4__.stringifyEntities)(\n      value,\n      Object.assign({}, state.settings.characterReferences, {\n        subset: constants.unquoted[x][y],\n        attribute: true\n      })\n    )\n  }\n\n  // If we don’t want unquoted, or if `value` contains character references when\n  // unquoted…\n  if (result !== value) {\n    // If the alternative is less common than `quote`, switch.\n    if (\n      state.settings.quoteSmart &&\n      (0,ccount__WEBPACK_IMPORTED_MODULE_7__.ccount)(value, quote) > (0,ccount__WEBPACK_IMPORTED_MODULE_7__.ccount)(value, state.alternative)\n    ) {\n      quote = state.alternative\n    }\n\n    result =\n      quote +\n      (0,stringify_entities__WEBPACK_IMPORTED_MODULE_4__.stringifyEntities)(\n        value,\n        Object.assign({}, state.settings.characterReferences, {\n          // Always encode without parse errors in non-HTML.\n          subset: (quote === \"'\" ? constants.single : constants.double)[x][y],\n          attribute: true\n        })\n      ) +\n      quote\n  }\n\n  // Don’t add a `=` for unquoted empties.\n  return name + (result ? '=' + result : result)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/hast-util-to-html/lib/handle/element.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/hast-util-to-html/lib/handle/index.js":
/*!****************************************************************!*\
  !*** ../../node_modules/hast-util-to-html/lib/handle/index.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   handle: () => (/* binding */ handle)\n/* harmony export */ });\n/* harmony import */ var zwitch__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zwitch */ \"(ssr)/../../node_modules/zwitch/index.js\");\n/* harmony import */ var _comment_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./comment.js */ \"(ssr)/../../node_modules/hast-util-to-html/lib/handle/comment.js\");\n/* harmony import */ var _doctype_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./doctype.js */ \"(ssr)/../../node_modules/hast-util-to-html/lib/handle/doctype.js\");\n/* harmony import */ var _element_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./element.js */ \"(ssr)/../../node_modules/hast-util-to-html/lib/handle/element.js\");\n/* harmony import */ var _raw_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./raw.js */ \"(ssr)/../../node_modules/hast-util-to-html/lib/handle/raw.js\");\n/* harmony import */ var _root_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./root.js */ \"(ssr)/../../node_modules/hast-util-to-html/lib/handle/root.js\");\n/* harmony import */ var _text_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./text.js */ \"(ssr)/../../node_modules/hast-util-to-html/lib/handle/text.js\");\n/**\n * @typedef {import('../types.js').State} State\n * @typedef {import('../types.js').Node} Node\n * @typedef {import('../types.js').Parent} Parent\n */\n\n\n\n\n\n\n\n\n\n/**\n * @type {(node: Node, index: number | undefined, parent: Parent | undefined, state: State) => string}\n */\nconst handle = (0,zwitch__WEBPACK_IMPORTED_MODULE_0__.zwitch)('type', {\n  invalid,\n  unknown,\n  handlers: {comment: _comment_js__WEBPACK_IMPORTED_MODULE_1__.comment, doctype: _doctype_js__WEBPACK_IMPORTED_MODULE_2__.doctype, element: _element_js__WEBPACK_IMPORTED_MODULE_3__.element, raw: _raw_js__WEBPACK_IMPORTED_MODULE_4__.raw, root: _root_js__WEBPACK_IMPORTED_MODULE_5__.root, text: _text_js__WEBPACK_IMPORTED_MODULE_6__.text}\n})\n\n/**\n * Fail when a non-node is found in the tree.\n *\n * @param {unknown} node\n *   Unknown value.\n * @returns {never}\n *   Never.\n */\nfunction invalid(node) {\n  throw new Error('Expected node, not `' + node + '`')\n}\n\n/**\n * Fail when a node with an unknown type is found in the tree.\n *\n * @param {unknown} node\n *  Unknown node.\n * @returns {never}\n *   Never.\n */\nfunction unknown(node) {\n  // @ts-expect-error: `type` is defined.\n  throw new Error('Cannot compile unknown node `' + node.type + '`')\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/hast-util-to-html/lib/handle/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/hast-util-to-html/lib/handle/raw.js":
/*!**************************************************************!*\
  !*** ../../node_modules/hast-util-to-html/lib/handle/raw.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   raw: () => (/* binding */ raw)\n/* harmony export */ });\n/* harmony import */ var _text_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./text.js */ \"(ssr)/../../node_modules/hast-util-to-html/lib/handle/text.js\");\n/**\n * @typedef {import('../types.js').State} State\n * @typedef {import('../types.js').Parent} Parent\n * @typedef {import('../types.js').Raw} Raw\n */\n\n\n\n/**\n * Serialize a raw node.\n *\n * @param {Raw} node\n *   Node to handle.\n * @param {number | undefined} index\n *   Index of `node` in `parent.\n * @param {Parent | undefined} parent\n *   Parent of `node`.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {string}\n *   Serialized node.\n */\nfunction raw(node, index, parent, state) {\n  return state.settings.allowDangerousHtml\n    ? node.value\n    : (0,_text_js__WEBPACK_IMPORTED_MODULE_0__.text)(node, index, parent, state)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2hhc3QtdXRpbC10by1odG1sL2xpYi9oYW5kbGUvcmF3LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDQSxhQUFhLDZCQUE2QjtBQUMxQyxhQUFhLDhCQUE4QjtBQUMzQyxhQUFhLDJCQUEyQjtBQUN4Qzs7QUFFOEI7O0FBRTlCO0FBQ0E7QUFDQTtBQUNBLFdBQVcsS0FBSztBQUNoQjtBQUNBLFdBQVcsb0JBQW9CO0FBQy9CO0FBQ0EsV0FBVyxvQkFBb0I7QUFDL0I7QUFDQSxXQUFXLE9BQU87QUFDbEI7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBLE1BQU0sOENBQUk7QUFDViIsInNvdXJjZXMiOlsid2VicGFjazovL0BvcGVuY2FudmFzL3dlYi8uLi8uLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLWh0bWwvbGliL2hhbmRsZS9yYXcuanM/YTViZiJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuU3RhdGV9IFN0YXRlXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLlBhcmVudH0gUGFyZW50XG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLlJhd30gUmF3XG4gKi9cblxuaW1wb3J0IHt0ZXh0fSBmcm9tICcuL3RleHQuanMnXG5cbi8qKlxuICogU2VyaWFsaXplIGEgcmF3IG5vZGUuXG4gKlxuICogQHBhcmFtIHtSYXd9IG5vZGVcbiAqICAgTm9kZSB0byBoYW5kbGUuXG4gKiBAcGFyYW0ge251bWJlciB8IHVuZGVmaW5lZH0gaW5kZXhcbiAqICAgSW5kZXggb2YgYG5vZGVgIGluIGBwYXJlbnQuXG4gKiBAcGFyYW0ge1BhcmVudCB8IHVuZGVmaW5lZH0gcGFyZW50XG4gKiAgIFBhcmVudCBvZiBgbm9kZWAuXG4gKiBAcGFyYW0ge1N0YXRlfSBzdGF0ZVxuICogICBJbmZvIHBhc3NlZCBhcm91bmQgYWJvdXQgdGhlIGN1cnJlbnQgc3RhdGUuXG4gKiBAcmV0dXJucyB7c3RyaW5nfVxuICogICBTZXJpYWxpemVkIG5vZGUuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiByYXcobm9kZSwgaW5kZXgsIHBhcmVudCwgc3RhdGUpIHtcbiAgcmV0dXJuIHN0YXRlLnNldHRpbmdzLmFsbG93RGFuZ2Vyb3VzSHRtbFxuICAgID8gbm9kZS52YWx1ZVxuICAgIDogdGV4dChub2RlLCBpbmRleCwgcGFyZW50LCBzdGF0ZSlcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/hast-util-to-html/lib/handle/raw.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/hast-util-to-html/lib/handle/root.js":
/*!***************************************************************!*\
  !*** ../../node_modules/hast-util-to-html/lib/handle/root.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   root: () => (/* binding */ root)\n/* harmony export */ });\n/**\n * @typedef {import('../types.js').Root} Root\n * @typedef {import('../types.js').Parent} Parent\n * @typedef {import('../types.js').State} State\n */\n\n/**\n * Serialize a root.\n *\n * @param {Root} node\n *   Node to handle.\n * @param {number | undefined} _1\n *   Index of `node` in `parent.\n * @param {Parent | undefined} _2\n *   Parent of `node`.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {string}\n *   Serialized node.\n */\nfunction root(node, _1, _2, state) {\n  return state.all(node)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2hhc3QtdXRpbC10by1odG1sL2xpYi9oYW5kbGUvcm9vdC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQSxhQUFhLDRCQUE0QjtBQUN6QyxhQUFhLDhCQUE4QjtBQUMzQyxhQUFhLDZCQUE2QjtBQUMxQzs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLE1BQU07QUFDakI7QUFDQSxXQUFXLG9CQUFvQjtBQUMvQjtBQUNBLFdBQVcsb0JBQW9CO0FBQy9CO0FBQ0EsV0FBVyxPQUFPO0FBQ2xCO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDTztBQUNQO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9Ab3BlbmNhbnZhcy93ZWIvLi4vLi4vbm9kZV9tb2R1bGVzL2hhc3QtdXRpbC10by1odG1sL2xpYi9oYW5kbGUvcm9vdC5qcz9mOTg5Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5Sb290fSBSb290XG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLlBhcmVudH0gUGFyZW50XG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLlN0YXRlfSBTdGF0ZVxuICovXG5cbi8qKlxuICogU2VyaWFsaXplIGEgcm9vdC5cbiAqXG4gKiBAcGFyYW0ge1Jvb3R9IG5vZGVcbiAqICAgTm9kZSB0byBoYW5kbGUuXG4gKiBAcGFyYW0ge251bWJlciB8IHVuZGVmaW5lZH0gXzFcbiAqICAgSW5kZXggb2YgYG5vZGVgIGluIGBwYXJlbnQuXG4gKiBAcGFyYW0ge1BhcmVudCB8IHVuZGVmaW5lZH0gXzJcbiAqICAgUGFyZW50IG9mIGBub2RlYC5cbiAqIEBwYXJhbSB7U3RhdGV9IHN0YXRlXG4gKiAgIEluZm8gcGFzc2VkIGFyb3VuZCBhYm91dCB0aGUgY3VycmVudCBzdGF0ZS5cbiAqIEByZXR1cm5zIHtzdHJpbmd9XG4gKiAgIFNlcmlhbGl6ZWQgbm9kZS5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHJvb3Qobm9kZSwgXzEsIF8yLCBzdGF0ZSkge1xuICByZXR1cm4gc3RhdGUuYWxsKG5vZGUpXG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/hast-util-to-html/lib/handle/root.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/hast-util-to-html/lib/handle/text.js":
/*!***************************************************************!*\
  !*** ../../node_modules/hast-util-to-html/lib/handle/text.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   text: () => (/* binding */ text)\n/* harmony export */ });\n/* harmony import */ var stringify_entities__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! stringify-entities */ \"(ssr)/../../node_modules/stringify-entities/lib/index.js\");\n/**\n * @typedef {import('../types.js').State} State\n * @typedef {import('../types.js').Parent} Parent\n * @typedef {import('../types.js').Raw} Raw\n * @typedef {import('../types.js').Text} Text\n */\n\n\n\n/**\n * Serialize a text node.\n *\n * @param {Text | Raw} node\n *   Node to handle.\n * @param {number | undefined} _\n *   Index of `node` in `parent.\n * @param {Parent | undefined} parent\n *   Parent of `node`.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {string}\n *   Serialized node.\n */\nfunction text(node, _, parent, state) {\n  // Check if content of `node` should be escaped.\n  return parent &&\n    parent.type === 'element' &&\n    (parent.tagName === 'script' || parent.tagName === 'style')\n    ? node.value\n    : (0,stringify_entities__WEBPACK_IMPORTED_MODULE_0__.stringifyEntities)(\n        node.value,\n        Object.assign({}, state.settings.characterReferences, {\n          subset: ['<', '&']\n        })\n      )\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/hast-util-to-html/lib/handle/text.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/hast-util-to-html/lib/index.js":
/*!*********************************************************!*\
  !*** ../../node_modules/hast-util-to-html/lib/index.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   all: () => (/* binding */ all),\n/* harmony export */   toHtml: () => (/* binding */ toHtml)\n/* harmony export */ });\n/* harmony import */ var property_information__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! property-information */ \"(ssr)/../../node_modules/property-information/index.js\");\n/* harmony import */ var html_void_elements__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! html-void-elements */ \"(ssr)/../../node_modules/html-void-elements/index.js\");\n/* harmony import */ var _handle_index_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./handle/index.js */ \"(ssr)/../../node_modules/hast-util-to-html/lib/handle/index.js\");\n/**\n * @typedef {import('./types.js').Node} Node\n * @typedef {import('./types.js').Parent} Parent\n * @typedef {import('./types.js').Content} Content\n * @typedef {import('./types.js').Options} Options\n * @typedef {import('./types.js').State} State\n */\n\n\n\n\n\n/**\n * Serialize hast as HTML.\n *\n * @param {Node | Array<Content>} tree\n *   Tree to serialize.\n * @param {Options | null | undefined} [options]\n *   Configuration.\n * @returns {string}\n *   Serialized HTML.\n */\n// eslint-disable-next-line complexity\nfunction toHtml(tree, options) {\n  const options_ = options || {}\n  const quote = options_.quote || '\"'\n  const alternative = quote === '\"' ? \"'\" : '\"'\n\n  if (quote !== '\"' && quote !== \"'\") {\n    throw new Error('Invalid quote `' + quote + '`, expected `\\'` or `\"`')\n  }\n\n  /** @type {State} */\n  const state = {\n    one,\n    all,\n    settings: {\n      omitOptionalTags: options_.omitOptionalTags || false,\n      allowParseErrors: options_.allowParseErrors || false,\n      allowDangerousCharacters: options_.allowDangerousCharacters || false,\n      quoteSmart: options_.quoteSmart || false,\n      preferUnquoted: options_.preferUnquoted || false,\n      tightAttributes: options_.tightAttributes || false,\n      upperDoctype: options_.upperDoctype || false,\n      tightDoctype: options_.tightDoctype || false,\n      bogusComments: options_.bogusComments || false,\n      tightCommaSeparatedLists: options_.tightCommaSeparatedLists || false,\n      tightSelfClosing: options_.tightSelfClosing || false,\n      collapseEmptyAttributes: options_.collapseEmptyAttributes || false,\n      allowDangerousHtml: options_.allowDangerousHtml || false,\n      voids: options_.voids || html_void_elements__WEBPACK_IMPORTED_MODULE_0__.htmlVoidElements,\n      characterReferences:\n        options_.characterReferences || options_.entities || {},\n      closeSelfClosing: options_.closeSelfClosing || false,\n      closeEmptyElements: options_.closeEmptyElements || false\n    },\n    schema: options_.space === 'svg' ? property_information__WEBPACK_IMPORTED_MODULE_1__.svg : property_information__WEBPACK_IMPORTED_MODULE_1__.html,\n    quote,\n    alternative\n  }\n\n  return state.one(\n    Array.isArray(tree) ? {type: 'root', children: tree} : tree,\n    undefined,\n    undefined\n  )\n}\n\n/**\n * Serialize a node.\n *\n * @this {State}\n *   Info passed around about the current state.\n * @param {Node} node\n *   Node to handle.\n * @param {number | undefined} index\n *   Index of `node` in `parent.\n * @param {Parent | undefined} parent\n *   Parent of `node`.\n * @returns {string}\n *   Serialized node.\n */\nfunction one(node, index, parent) {\n  return (0,_handle_index_js__WEBPACK_IMPORTED_MODULE_2__.handle)(node, index, parent, this)\n}\n\n/**\n * Serialize all children of `parent`.\n *\n * @this {State}\n *   Info passed around about the current state.\n * @param {Parent | undefined} parent\n *   Parent whose children to serialize.\n * @returns {string}\n */\nfunction all(parent) {\n  /** @type {Array<string>} */\n  const results = []\n  const children = (parent && parent.children) || []\n  let index = -1\n\n  while (++index < children.length) {\n    results[index] = this.one(children[index], index, parent)\n  }\n\n  return results.join('')\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/hast-util-to-html/lib/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/hast-util-to-html/lib/omission/closing.js":
/*!********************************************************************!*\
  !*** ../../node_modules/hast-util-to-html/lib/omission/closing.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   closing: () => (/* binding */ closing)\n/* harmony export */ });\n/* harmony import */ var hast_util_whitespace__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! hast-util-whitespace */ \"(ssr)/../../node_modules/hast-util-to-html/node_modules/hast-util-whitespace/index.js\");\n/* harmony import */ var _util_siblings_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./util/siblings.js */ \"(ssr)/../../node_modules/hast-util-to-html/lib/omission/util/siblings.js\");\n/* harmony import */ var _omission_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./omission.js */ \"(ssr)/../../node_modules/hast-util-to-html/lib/omission/omission.js\");\n/**\n * @typedef {import('../types.js').Element} Element\n * @typedef {import('../types.js').Parent} Parent\n */\n\n\n\n\n\nconst closing = (0,_omission_js__WEBPACK_IMPORTED_MODULE_0__.omission)({\n  html,\n  head: headOrColgroupOrCaption,\n  body,\n  p,\n  li,\n  dt,\n  dd,\n  rt: rubyElement,\n  rp: rubyElement,\n  optgroup,\n  option,\n  menuitem,\n  colgroup: headOrColgroupOrCaption,\n  caption: headOrColgroupOrCaption,\n  thead,\n  tbody,\n  tfoot,\n  tr,\n  td: cells,\n  th: cells\n})\n\n/**\n * Macro for `</head>`, `</colgroup>`, and `</caption>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parent | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction headOrColgroupOrCaption(_, index, parent) {\n  const next = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(parent, index, true)\n  return (\n    !next ||\n    (next.type !== 'comment' &&\n      !(next.type === 'text' && (0,hast_util_whitespace__WEBPACK_IMPORTED_MODULE_2__.whitespace)(next.value.charAt(0))))\n  )\n}\n\n/**\n * Whether to omit `</html>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parent | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction html(_, index, parent) {\n  const next = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(parent, index)\n  return !next || next.type !== 'comment'\n}\n\n/**\n * Whether to omit `</body>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parent | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction body(_, index, parent) {\n  const next = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(parent, index)\n  return !next || next.type !== 'comment'\n}\n\n/**\n * Whether to omit `</p>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parent | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\n// eslint-disable-next-line complexity\nfunction p(_, index, parent) {\n  const next = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(parent, index)\n  return next\n    ? next.type === 'element' &&\n        (next.tagName === 'address' ||\n          next.tagName === 'article' ||\n          next.tagName === 'aside' ||\n          next.tagName === 'blockquote' ||\n          next.tagName === 'details' ||\n          next.tagName === 'div' ||\n          next.tagName === 'dl' ||\n          next.tagName === 'fieldset' ||\n          next.tagName === 'figcaption' ||\n          next.tagName === 'figure' ||\n          next.tagName === 'footer' ||\n          next.tagName === 'form' ||\n          next.tagName === 'h1' ||\n          next.tagName === 'h2' ||\n          next.tagName === 'h3' ||\n          next.tagName === 'h4' ||\n          next.tagName === 'h5' ||\n          next.tagName === 'h6' ||\n          next.tagName === 'header' ||\n          next.tagName === 'hgroup' ||\n          next.tagName === 'hr' ||\n          next.tagName === 'main' ||\n          next.tagName === 'menu' ||\n          next.tagName === 'nav' ||\n          next.tagName === 'ol' ||\n          next.tagName === 'p' ||\n          next.tagName === 'pre' ||\n          next.tagName === 'section' ||\n          next.tagName === 'table' ||\n          next.tagName === 'ul')\n    : !parent ||\n        // Confusing parent.\n        !(\n          parent.type === 'element' &&\n          (parent.tagName === 'a' ||\n            parent.tagName === 'audio' ||\n            parent.tagName === 'del' ||\n            parent.tagName === 'ins' ||\n            parent.tagName === 'map' ||\n            parent.tagName === 'noscript' ||\n            parent.tagName === 'video')\n        )\n}\n\n/**\n * Whether to omit `</li>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parent | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction li(_, index, parent) {\n  const next = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(parent, index)\n  return !next || (next.type === 'element' && next.tagName === 'li')\n}\n\n/**\n * Whether to omit `</dt>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parent | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction dt(_, index, parent) {\n  const next = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(parent, index)\n  return (\n    next &&\n    next.type === 'element' &&\n    (next.tagName === 'dt' || next.tagName === 'dd')\n  )\n}\n\n/**\n * Whether to omit `</dd>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parent | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction dd(_, index, parent) {\n  const next = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(parent, index)\n  return (\n    !next ||\n    (next.type === 'element' &&\n      (next.tagName === 'dt' || next.tagName === 'dd'))\n  )\n}\n\n/**\n * Whether to omit `</rt>` or `</rp>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parent | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction rubyElement(_, index, parent) {\n  const next = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(parent, index)\n  return (\n    !next ||\n    (next.type === 'element' &&\n      (next.tagName === 'rp' || next.tagName === 'rt'))\n  )\n}\n\n/**\n * Whether to omit `</optgroup>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parent | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction optgroup(_, index, parent) {\n  const next = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(parent, index)\n  return !next || (next.type === 'element' && next.tagName === 'optgroup')\n}\n\n/**\n * Whether to omit `</option>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parent | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction option(_, index, parent) {\n  const next = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(parent, index)\n  return (\n    !next ||\n    (next.type === 'element' &&\n      (next.tagName === 'option' || next.tagName === 'optgroup'))\n  )\n}\n\n/**\n * Whether to omit `</menuitem>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parent | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction menuitem(_, index, parent) {\n  const next = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(parent, index)\n  return (\n    !next ||\n    (next.type === 'element' &&\n      (next.tagName === 'menuitem' ||\n        next.tagName === 'hr' ||\n        next.tagName === 'menu'))\n  )\n}\n\n/**\n * Whether to omit `</thead>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parent | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction thead(_, index, parent) {\n  const next = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(parent, index)\n  return (\n    next &&\n    next.type === 'element' &&\n    (next.tagName === 'tbody' || next.tagName === 'tfoot')\n  )\n}\n\n/**\n * Whether to omit `</tbody>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parent | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction tbody(_, index, parent) {\n  const next = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(parent, index)\n  return (\n    !next ||\n    (next.type === 'element' &&\n      (next.tagName === 'tbody' || next.tagName === 'tfoot'))\n  )\n}\n\n/**\n * Whether to omit `</tfoot>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parent | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction tfoot(_, index, parent) {\n  return !(0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(parent, index)\n}\n\n/**\n * Whether to omit `</tr>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parent | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction tr(_, index, parent) {\n  const next = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(parent, index)\n  return !next || (next.type === 'element' && next.tagName === 'tr')\n}\n\n/**\n * Whether to omit `</td>` or `</th>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parent | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction cells(_, index, parent) {\n  const next = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(parent, index)\n  return (\n    !next ||\n    (next.type === 'element' &&\n      (next.tagName === 'td' || next.tagName === 'th'))\n  )\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/hast-util-to-html/lib/omission/closing.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/hast-util-to-html/lib/omission/omission.js":
/*!*********************************************************************!*\
  !*** ../../node_modules/hast-util-to-html/lib/omission/omission.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   omission: () => (/* binding */ omission)\n/* harmony export */ });\n/**\n * @typedef {import('../types.js').OmitHandle} OmitHandle\n */\n\nconst own = {}.hasOwnProperty\n\n/**\n * Factory to check if a given node can have a tag omitted.\n *\n * @param {Record<string, OmitHandle>} handlers\n *   Omission handlers, where each key is a tag name, and each value is the\n *   corresponding handler.\n * @returns {OmitHandle}\n *   Whether to omit a tag of an element.\n */\nfunction omission(handlers) {\n  return omit\n\n  /**\n   * Check if a given node can have a tag omitted.\n   *\n   * @type {OmitHandle}\n   */\n  function omit(node, index, parent) {\n    return (\n      own.call(handlers, node.tagName) &&\n      handlers[node.tagName](node, index, parent)\n    )\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2hhc3QtdXRpbC10by1odG1sL2xpYi9vbWlzc2lvbi9vbWlzc2lvbi5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQSxhQUFhLGtDQUFrQztBQUMvQzs7QUFFQSxjQUFjOztBQUVkO0FBQ0E7QUFDQTtBQUNBLFdBQVcsNEJBQTRCO0FBQ3ZDO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNPO0FBQ1A7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsWUFBWTtBQUNaO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9Ab3BlbmNhbnZhcy93ZWIvLi4vLi4vbm9kZV9tb2R1bGVzL2hhc3QtdXRpbC10by1odG1sL2xpYi9vbWlzc2lvbi9vbWlzc2lvbi5qcz81OTI0Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5PbWl0SGFuZGxlfSBPbWl0SGFuZGxlXG4gKi9cblxuY29uc3Qgb3duID0ge30uaGFzT3duUHJvcGVydHlcblxuLyoqXG4gKiBGYWN0b3J5IHRvIGNoZWNrIGlmIGEgZ2l2ZW4gbm9kZSBjYW4gaGF2ZSBhIHRhZyBvbWl0dGVkLlxuICpcbiAqIEBwYXJhbSB7UmVjb3JkPHN0cmluZywgT21pdEhhbmRsZT59IGhhbmRsZXJzXG4gKiAgIE9taXNzaW9uIGhhbmRsZXJzLCB3aGVyZSBlYWNoIGtleSBpcyBhIHRhZyBuYW1lLCBhbmQgZWFjaCB2YWx1ZSBpcyB0aGVcbiAqICAgY29ycmVzcG9uZGluZyBoYW5kbGVyLlxuICogQHJldHVybnMge09taXRIYW5kbGV9XG4gKiAgIFdoZXRoZXIgdG8gb21pdCBhIHRhZyBvZiBhbiBlbGVtZW50LlxuICovXG5leHBvcnQgZnVuY3Rpb24gb21pc3Npb24oaGFuZGxlcnMpIHtcbiAgcmV0dXJuIG9taXRcblxuICAvKipcbiAgICogQ2hlY2sgaWYgYSBnaXZlbiBub2RlIGNhbiBoYXZlIGEgdGFnIG9taXR0ZWQuXG4gICAqXG4gICAqIEB0eXBlIHtPbWl0SGFuZGxlfVxuICAgKi9cbiAgZnVuY3Rpb24gb21pdChub2RlLCBpbmRleCwgcGFyZW50KSB7XG4gICAgcmV0dXJuIChcbiAgICAgIG93bi5jYWxsKGhhbmRsZXJzLCBub2RlLnRhZ05hbWUpICYmXG4gICAgICBoYW5kbGVyc1tub2RlLnRhZ05hbWVdKG5vZGUsIGluZGV4LCBwYXJlbnQpXG4gICAgKVxuICB9XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/hast-util-to-html/lib/omission/omission.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/hast-util-to-html/lib/omission/opening.js":
/*!********************************************************************!*\
  !*** ../../node_modules/hast-util-to-html/lib/omission/opening.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   opening: () => (/* binding */ opening)\n/* harmony export */ });\n/* harmony import */ var hast_util_whitespace__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! hast-util-whitespace */ \"(ssr)/../../node_modules/hast-util-to-html/node_modules/hast-util-whitespace/index.js\");\n/* harmony import */ var _util_siblings_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./util/siblings.js */ \"(ssr)/../../node_modules/hast-util-to-html/lib/omission/util/siblings.js\");\n/* harmony import */ var _closing_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./closing.js */ \"(ssr)/../../node_modules/hast-util-to-html/lib/omission/closing.js\");\n/* harmony import */ var _omission_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./omission.js */ \"(ssr)/../../node_modules/hast-util-to-html/lib/omission/omission.js\");\n/**\n * @typedef {import('../types.js').Element} Element\n * @typedef {import('../types.js').Parent} Parent\n * @typedef {import('../types.js').Content} Content\n */\n\n\n\n\n\n\nconst opening = (0,_omission_js__WEBPACK_IMPORTED_MODULE_0__.omission)({\n  html,\n  head,\n  body,\n  colgroup,\n  tbody\n})\n\n/**\n * Whether to omit `<html>`.\n *\n * @param {Element} node\n *   Element.\n * @returns {boolean}\n *   Whether the opening tag can be omitted.\n */\nfunction html(node) {\n  const head = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(node, -1)\n  return !head || head.type !== 'comment'\n}\n\n/**\n * Whether to omit `<head>`.\n *\n * @param {Element} node\n *   Element.\n * @returns {boolean}\n *   Whether the opening tag can be omitted.\n */\nfunction head(node) {\n  const children = node.children\n  /** @type {Array<string>} */\n  const seen = []\n  let index = -1\n\n  while (++index < children.length) {\n    const child = children[index]\n    if (\n      child.type === 'element' &&\n      (child.tagName === 'title' || child.tagName === 'base')\n    ) {\n      if (seen.includes(child.tagName)) return false\n      seen.push(child.tagName)\n    }\n  }\n\n  return children.length > 0\n}\n\n/**\n * Whether to omit `<body>`.\n *\n * @param {Element} node\n *   Element.\n * @returns {boolean}\n *   Whether the opening tag can be omitted.\n */\nfunction body(node) {\n  const head = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(node, -1, true)\n\n  return (\n    !head ||\n    (head.type !== 'comment' &&\n      !(head.type === 'text' && (0,hast_util_whitespace__WEBPACK_IMPORTED_MODULE_2__.whitespace)(head.value.charAt(0))) &&\n      !(\n        head.type === 'element' &&\n        (head.tagName === 'meta' ||\n          head.tagName === 'link' ||\n          head.tagName === 'script' ||\n          head.tagName === 'style' ||\n          head.tagName === 'template')\n      ))\n  )\n}\n\n/**\n * Whether to omit `<colgroup>`.\n * The spec describes some logic for the opening tag, but it’s easier to\n * implement in the closing tag, to the same effect, so we handle it there\n * instead.\n *\n * @param {Element} node\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parent | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the opening tag can be omitted.\n */\nfunction colgroup(node, index, parent) {\n  const previous = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingBefore)(parent, index)\n  const head = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(node, -1, true)\n\n  // Previous colgroup was already omitted.\n  if (\n    parent &&\n    previous &&\n    previous.type === 'element' &&\n    previous.tagName === 'colgroup' &&\n    (0,_closing_js__WEBPACK_IMPORTED_MODULE_3__.closing)(previous, parent.children.indexOf(previous), parent)\n  ) {\n    return false\n  }\n\n  return head && head.type === 'element' && head.tagName === 'col'\n}\n\n/**\n * Whether to omit `<tbody>`.\n *\n * @param {Element} node\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parent | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the opening tag can be omitted.\n */\nfunction tbody(node, index, parent) {\n  const previous = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingBefore)(parent, index)\n  const head = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(node, -1)\n\n  // Previous table section was already omitted.\n  if (\n    parent &&\n    previous &&\n    previous.type === 'element' &&\n    (previous.tagName === 'thead' || previous.tagName === 'tbody') &&\n    (0,_closing_js__WEBPACK_IMPORTED_MODULE_3__.closing)(previous, parent.children.indexOf(previous), parent)\n  ) {\n    return false\n  }\n\n  return head && head.type === 'element' && head.tagName === 'tr'\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/hast-util-to-html/lib/omission/opening.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/hast-util-to-html/lib/omission/util/siblings.js":
/*!**************************************************************************!*\
  !*** ../../node_modules/hast-util-to-html/lib/omission/util/siblings.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   siblingAfter: () => (/* binding */ siblingAfter),\n/* harmony export */   siblingBefore: () => (/* binding */ siblingBefore)\n/* harmony export */ });\n/* harmony import */ var hast_util_whitespace__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! hast-util-whitespace */ \"(ssr)/../../node_modules/hast-util-to-html/node_modules/hast-util-whitespace/index.js\");\n/**\n * @typedef {import('../../types.js').Parent} Parent\n * @typedef {import('../../types.js').Content} Content\n */\n\n\n\nconst siblingAfter = siblings(1)\nconst siblingBefore = siblings(-1)\n\n/**\n * Factory to check siblings in a direction.\n *\n * @param {number} increment\n */\nfunction siblings(increment) {\n  return sibling\n\n  /**\n   * Find applicable siblings in a direction.\n   *\n   * @param {Parent | null | undefined} parent\n   * @param {number | null | undefined} index\n   * @param {boolean | null | undefined} [includeWhitespace=false]\n   * @returns {Content}\n   */\n  function sibling(parent, index, includeWhitespace) {\n    const siblings = parent ? parent.children : []\n    let offset = (index || 0) + increment\n    let next = siblings && siblings[offset]\n\n    if (!includeWhitespace) {\n      while (next && (0,hast_util_whitespace__WEBPACK_IMPORTED_MODULE_0__.whitespace)(next)) {\n        offset += increment\n        next = siblings[offset]\n      }\n    }\n\n    return next\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/hast-util-to-html/lib/omission/util/siblings.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/hast-util-to-html/node_modules/hast-util-whitespace/index.js":
/*!***************************************************************************************!*\
  !*** ../../node_modules/hast-util-to-html/node_modules/hast-util-whitespace/index.js ***!
  \***************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   whitespace: () => (/* binding */ whitespace)\n/* harmony export */ });\n/**\n * Check if the given value is *inter-element whitespace*.\n *\n * @param {unknown} thing\n *   Thing to check (typically `Node` or `string`).\n * @returns {boolean}\n *   Whether the `value` is inter-element whitespace (`boolean`): consisting of\n *   zero or more of space, tab (`\\t`), line feed (`\\n`), carriage return\n *   (`\\r`), or form feed (`\\f`).\n *   If a node is passed it must be a `Text` node, whose `value` field is\n *   checked.\n */\nfunction whitespace(thing) {\n  /** @type {string} */\n  const value =\n    // @ts-expect-error looks like a node.\n    thing && typeof thing === 'object' && thing.type === 'text'\n      ? // @ts-expect-error looks like a text.\n        thing.value || ''\n      : thing\n\n  // HTML whitespace expression.\n  // See <https://infra.spec.whatwg.org/#ascii-whitespace>.\n  return typeof value === 'string' && value.replace(/[ \\t\\n\\f\\r]/g, '') === ''\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2hhc3QtdXRpbC10by1odG1sL25vZGVfbW9kdWxlcy9oYXN0LXV0aWwtd2hpdGVzcGFjZS9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0EsV0FBVyxTQUFTO0FBQ3BCO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1AsYUFBYSxRQUFRO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL0BvcGVuY2FudmFzL3dlYi8uLi8uLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLWh0bWwvbm9kZV9tb2R1bGVzL2hhc3QtdXRpbC13aGl0ZXNwYWNlL2luZGV4LmpzP2VlZGMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBDaGVjayBpZiB0aGUgZ2l2ZW4gdmFsdWUgaXMgKmludGVyLWVsZW1lbnQgd2hpdGVzcGFjZSouXG4gKlxuICogQHBhcmFtIHt1bmtub3dufSB0aGluZ1xuICogICBUaGluZyB0byBjaGVjayAodHlwaWNhbGx5IGBOb2RlYCBvciBgc3RyaW5nYCkuXG4gKiBAcmV0dXJucyB7Ym9vbGVhbn1cbiAqICAgV2hldGhlciB0aGUgYHZhbHVlYCBpcyBpbnRlci1lbGVtZW50IHdoaXRlc3BhY2UgKGBib29sZWFuYCk6IGNvbnNpc3Rpbmcgb2ZcbiAqICAgemVybyBvciBtb3JlIG9mIHNwYWNlLCB0YWIgKGBcXHRgKSwgbGluZSBmZWVkIChgXFxuYCksIGNhcnJpYWdlIHJldHVyblxuICogICAoYFxccmApLCBvciBmb3JtIGZlZWQgKGBcXGZgKS5cbiAqICAgSWYgYSBub2RlIGlzIHBhc3NlZCBpdCBtdXN0IGJlIGEgYFRleHRgIG5vZGUsIHdob3NlIGB2YWx1ZWAgZmllbGQgaXNcbiAqICAgY2hlY2tlZC5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHdoaXRlc3BhY2UodGhpbmcpIHtcbiAgLyoqIEB0eXBlIHtzdHJpbmd9ICovXG4gIGNvbnN0IHZhbHVlID1cbiAgICAvLyBAdHMtZXhwZWN0LWVycm9yIGxvb2tzIGxpa2UgYSBub2RlLlxuICAgIHRoaW5nICYmIHR5cGVvZiB0aGluZyA9PT0gJ29iamVjdCcgJiYgdGhpbmcudHlwZSA9PT0gJ3RleHQnXG4gICAgICA/IC8vIEB0cy1leHBlY3QtZXJyb3IgbG9va3MgbGlrZSBhIHRleHQuXG4gICAgICAgIHRoaW5nLnZhbHVlIHx8ICcnXG4gICAgICA6IHRoaW5nXG5cbiAgLy8gSFRNTCB3aGl0ZXNwYWNlIGV4cHJlc3Npb24uXG4gIC8vIFNlZSA8aHR0cHM6Ly9pbmZyYS5zcGVjLndoYXR3Zy5vcmcvI2FzY2lpLXdoaXRlc3BhY2U+LlxuICByZXR1cm4gdHlwZW9mIHZhbHVlID09PSAnc3RyaW5nJyAmJiB2YWx1ZS5yZXBsYWNlKC9bIFxcdFxcblxcZlxccl0vZywgJycpID09PSAnJ1xufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/hast-util-to-html/node_modules/hast-util-whitespace/index.js\n");

/***/ })

};
;