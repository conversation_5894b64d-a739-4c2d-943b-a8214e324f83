"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/rehype-minify-whitespace";
exports.ids = ["vendor-chunks/rehype-minify-whitespace"];
exports.modules = {

/***/ "(ssr)/../../node_modules/rehype-minify-whitespace/block.js":
/*!************************************************************!*\
  !*** ../../node_modules/rehype-minify-whitespace/block.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   blocks: () => (/* binding */ blocks)\n/* harmony export */ });\n// See: <https://html.spec.whatwg.org/#the-css-user-agent-style-sheet-and-presentational-hints>\nconst blocks = [\n  'address', // Flow content.\n  'article', // Sections and headings.\n  'aside', // Sections and headings.\n  'blockquote', // Flow content.\n  'body', // Page.\n  'br', // Contribute whitespace intrinsically.\n  'caption', // Similar to block.\n  'center', // Flow content, legacy.\n  'col', // Similar to block.\n  'colgroup', // Similar to block.\n  'dd', // Lists.\n  'dialog', // Flow content.\n  'dir', // Lists, legacy.\n  'div', // Flow content.\n  'dl', // Lists.\n  'dt', // Lists.\n  'figcaption', // Flow content.\n  'figure', // Flow content.\n  'footer', // Flow content.\n  'form', // Flow content.\n  'h1', // Sections and headings.\n  'h2', // Sections and headings.\n  'h3', // Sections and headings.\n  'h4', // Sections and headings.\n  'h5', // Sections and headings.\n  'h6', // Sections and headings.\n  'head', // Page.\n  'header', // Flow content.\n  'hgroup', // Sections and headings.\n  'hr', // Flow content.\n  'html', // Page.\n  'legend', // Flow content.\n  'li', // Block-like.\n  'li', // Similar to block.\n  'listing', // Flow content, legacy\n  'main', // Flow content.\n  'menu', // Lists.\n  'nav', // Sections and headings.\n  'ol', // Lists.\n  'optgroup', // Similar to block.\n  'option', // Similar to block.\n  'p', // Flow content.\n  'plaintext', // Flow content, legacy\n  'pre', // Flow content.\n  'section', // Sections and headings.\n  'summary', // Similar to block.\n  'table', // Similar to block.\n  'tbody', // Similar to block.\n  'td', // Block-like.\n  'td', // Similar to block.\n  'tfoot', // Similar to block.\n  'th', // Block-like.\n  'th', // Similar to block.\n  'thead', // Similar to block.\n  'tr', // Similar to block.\n  'ul', // Lists.\n  'wbr', // Contribute whitespace intrinsically.\n  'xmp' // Flow content, legacy\n]\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/rehype-minify-whitespace/block.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/rehype-minify-whitespace/content.js":
/*!**************************************************************!*\
  !*** ../../node_modules/rehype-minify-whitespace/content.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   content: () => (/* binding */ content)\n/* harmony export */ });\nconst content = [\n  // Form.\n  'button',\n  'input',\n  'select',\n  'textarea'\n]\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL3JlaHlwZS1taW5pZnktd2hpdGVzcGFjZS9jb250ZW50LmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL0BvcGVuY2FudmFzL3dlYi8uLi8uLi9ub2RlX21vZHVsZXMvcmVoeXBlLW1pbmlmeS13aGl0ZXNwYWNlL2NvbnRlbnQuanM/ODA4NSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3QgY29udGVudCA9IFtcbiAgLy8gRm9ybS5cbiAgJ2J1dHRvbicsXG4gICdpbnB1dCcsXG4gICdzZWxlY3QnLFxuICAndGV4dGFyZWEnXG5dXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/rehype-minify-whitespace/content.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/rehype-minify-whitespace/index.js":
/*!************************************************************!*\
  !*** ../../node_modules/rehype-minify-whitespace/index.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ rehypeMinifyWhitespace)\n/* harmony export */ });\n/* harmony import */ var hast_util_is_element__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! hast-util-is-element */ \"(ssr)/../../node_modules/hast-util-is-element/index.js\");\n/* harmony import */ var hast_util_embedded__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! hast-util-embedded */ \"(ssr)/../../node_modules/rehype-minify-whitespace/node_modules/hast-util-embedded/lib/index.js\");\n/* harmony import */ var unist_util_is__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! unist-util-is */ \"(ssr)/../../node_modules/unist-util-is/lib/index.js\");\n/* harmony import */ var hast_util_whitespace__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! hast-util-whitespace */ \"(ssr)/../../node_modules/rehype-minify-whitespace/node_modules/hast-util-whitespace/index.js\");\n/* harmony import */ var _block_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./block.js */ \"(ssr)/../../node_modules/rehype-minify-whitespace/block.js\");\n/* harmony import */ var _content_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./content.js */ \"(ssr)/../../node_modules/rehype-minify-whitespace/content.js\");\n/* harmony import */ var _skippable_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./skippable.js */ \"(ssr)/../../node_modules/rehype-minify-whitespace/skippable.js\");\n/**\n * rehype plugin to minify whitespace between elements.\n *\n * ## What is this?\n *\n * This package is a plugin that can minify the whitespace between elements.\n *\n * ## When should I use this?\n *\n * You can use this plugin when you want to improve the size of HTML documents.\n *\n * ## API\n *\n * ### `unified().use(rehypeMinifyWhitespace[, options])`\n *\n * Minify whitespace.\n *\n * ##### `options`\n *\n * Configuration (optional).\n *\n * ##### `options.newlines`\n *\n * Whether to collapse runs of whitespace that include line endings to one\n * line ending (`boolean`, default: `false`).\n * The default is to collapse everything to one space.\n *\n * @example\n *   <h1>Heading</h1>\n *   <p><strong>This</strong> and <em>that</em></p>\n */\n\n/**\n * @typedef {import('hast').Root} Root\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Text} Text\n * @typedef {Root|Root['children'][number]} Node\n *\n * @typedef Options\n * @property {boolean} [newlines=false]\n *   If `newlines: true`, collapses whitespace containing newlines to `'\\n'`\n *   instead of `' '`.\n *   The default is to collapse to a single space.\n *\n * @typedef {'pre'|'nowrap'|'pre-wrap'|'normal'} Whitespace\n *\n * @typedef Context\n * @property {ReturnType<collapseFactory>} collapse\n * @property {Whitespace} whitespace\n * @property {boolean} [before]\n * @property {boolean} [after]\n *\n * @typedef Result\n * @property {boolean} remove\n * @property {boolean} ignore\n * @property {boolean} stripAtStart\n */\n\n\n\n\n\n\n\n\n\nconst ignorableNode = (0,unist_util_is__WEBPACK_IMPORTED_MODULE_0__.convert)(['doctype', 'comment'])\n\n/**\n * Minify whitespace.\n *\n * @type {import('unified').Plugin<[Options?]|Array<void>, Root>}\n */\nfunction rehypeMinifyWhitespace(options = {}) {\n  const collapse = collapseFactory(\n    options.newlines ? replaceNewlines : replaceWhitespace\n  )\n\n  return (tree) => {\n    minify(tree, {collapse, whitespace: 'normal'})\n  }\n}\n\n/**\n * @param {Node} node\n * @param {Context} context\n * @returns {Result}\n */\nfunction minify(node, context) {\n  if ('children' in node) {\n    const settings = Object.assign({}, context)\n\n    if (node.type === 'root' || blocklike(node)) {\n      settings.before = true\n      settings.after = true\n    }\n\n    settings.whitespace = inferWhiteSpace(node, context)\n\n    return all(node, settings)\n  }\n\n  if (node.type === 'text') {\n    if (context.whitespace === 'normal') {\n      return minifyText(node, context)\n    }\n\n    // Naïve collapse, but no trimming:\n    if (context.whitespace === 'nowrap') {\n      node.value = context.collapse(node.value)\n    }\n\n    // The `pre-wrap` or `pre` whitespace settings are neither collapsed nor\n    // trimmed.\n  }\n\n  return {remove: false, ignore: ignorableNode(node), stripAtStart: false}\n}\n\n/**\n * @param {Text} node\n * @param {Context} context\n * @returns {Result}\n */\nfunction minifyText(node, context) {\n  const value = context.collapse(node.value)\n  const result = {remove: false, ignore: false, stripAtStart: false}\n  let start = 0\n  let end = value.length\n\n  if (context.before && removable(value.charAt(0))) {\n    start++\n  }\n\n  if (start !== end && removable(value.charAt(end - 1))) {\n    if (context.after) {\n      end--\n    } else {\n      result.stripAtStart = true\n    }\n  }\n\n  if (start === end) {\n    result.remove = true\n  } else {\n    node.value = value.slice(start, end)\n  }\n\n  return result\n}\n\n/**\n * @param {Root|Element} parent\n * @param {Context} context\n * @returns {Result}\n */\nfunction all(parent, context) {\n  let before = context.before\n  const after = context.after\n  const children = parent.children\n  let length = children.length\n  let index = -1\n\n  while (++index < length) {\n    const result = minify(\n      children[index],\n      Object.assign({}, context, {\n        before,\n        after: collapsableAfter(children, index, after)\n      })\n    )\n\n    if (result.remove) {\n      children.splice(index, 1)\n      index--\n      length--\n    } else if (!result.ignore) {\n      before = result.stripAtStart\n    }\n\n    // If this element, such as a `<select>` or `<img>`, contributes content\n    // somehow, allow whitespace again.\n    if (content(children[index])) {\n      before = false\n    }\n  }\n\n  return {remove: false, ignore: false, stripAtStart: Boolean(before || after)}\n}\n\n/**\n * @param {Array<Node>} nodes\n * @param {number} index\n * @param {boolean|undefined} [after]\n * @returns {boolean|undefined}\n */\nfunction collapsableAfter(nodes, index, after) {\n  while (++index < nodes.length) {\n    const node = nodes[index]\n    let result = inferBoundary(node)\n\n    if (result === undefined && 'children' in node && !skippable(node)) {\n      result = collapsableAfter(node.children, -1)\n    }\n\n    if (typeof result === 'boolean') {\n      return result\n    }\n  }\n\n  return after\n}\n\n/**\n * Infer two types of boundaries:\n *\n * 1. `true` — boundary for which whitespace around it does not contribute\n *    anything\n * 2. `false` — boundary for which whitespace around it *does* contribute\n *\n * No result (`undefined`) is returned if it is unknown.\n *\n * @param {Node} node\n * @returns {boolean|undefined}\n */\nfunction inferBoundary(node) {\n  if (node.type === 'element') {\n    if (content(node)) {\n      return false\n    }\n\n    if (blocklike(node)) {\n      return true\n    }\n\n    // Unknown: either depends on siblings if embedded or metadata, or on\n    // children.\n  } else if (node.type === 'text') {\n    if (!(0,hast_util_whitespace__WEBPACK_IMPORTED_MODULE_1__.whitespace)(node)) {\n      return false\n    }\n  } else if (!ignorableNode(node)) {\n    return false\n  }\n}\n\n/**\n * Infer whether a node is skippable.\n *\n * @param {Node} node\n * @returns {boolean}\n */\nfunction content(node) {\n  return (0,hast_util_embedded__WEBPACK_IMPORTED_MODULE_2__.embedded)(node) || (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_3__.isElement)(node, _content_js__WEBPACK_IMPORTED_MODULE_4__.content)\n}\n\n/**\n * See: <https://html.spec.whatwg.org/#the-css-user-agent-style-sheet-and-presentational-hints>\n *\n * @param {Element} node\n * @returns {boolean}\n */\nfunction blocklike(node) {\n  return (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_3__.isElement)(node, _block_js__WEBPACK_IMPORTED_MODULE_5__.blocks)\n}\n\n/**\n * @param {Element|Root} node\n * @returns {boolean}\n */\nfunction skippable(node) {\n  return (\n    Boolean(\n      'properties' in node && node.properties && node.properties.hidden\n    ) ||\n    ignorableNode(node) ||\n    (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_3__.isElement)(node, _skippable_js__WEBPACK_IMPORTED_MODULE_6__.skippable)\n  )\n}\n\n/**\n * @param {string} character\n * @returns {boolean}\n */\nfunction removable(character) {\n  return character === ' ' || character === '\\n'\n}\n\n/**\n * @param {string} value\n * @returns {string}\n */\nfunction replaceNewlines(value) {\n  const match = /\\r?\\n|\\r/.exec(value)\n  return match ? match[0] : ' '\n}\n\n/**\n * @returns {string}\n */\nfunction replaceWhitespace() {\n  return ' '\n}\n\n/**\n * @param {(value: string) => string} replace\n */\nfunction collapseFactory(replace) {\n  return collapse\n\n  /**\n   * @param {string} value\n   * @returns {string}\n   */\n  function collapse(value) {\n    return String(value).replace(/[\\t\\n\\v\\f\\r ]+/g, replace)\n  }\n}\n\n/**\n * We don’t need to support void elements here (so `nobr wbr` -> `normal` is\n * ignored).\n *\n * @param {Root|Element} node\n * @param {Context} context\n * @returns {Whitespace}\n */\nfunction inferWhiteSpace(node, context) {\n  if ('tagName' in node && node.properties) {\n    switch (node.tagName) {\n      // Whitespace in script/style, while not displayed by CSS as significant,\n      // could have some meaning in JS/CSS, so we can’t touch them.\n      case 'listing':\n      case 'plaintext':\n      case 'script':\n      case 'style':\n      case 'xmp':\n        return 'pre'\n      case 'nobr':\n        return 'nowrap'\n      case 'pre':\n        return node.properties.wrap ? 'pre-wrap' : 'pre'\n      case 'td':\n      case 'th':\n        return node.properties.noWrap ? 'nowrap' : context.whitespace\n      case 'textarea':\n        return 'pre-wrap'\n      default:\n    }\n  }\n\n  return context.whitespace\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/rehype-minify-whitespace/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/rehype-minify-whitespace/node_modules/hast-util-embedded/lib/index.js":
/*!************************************************************************************************!*\
  !*** ../../node_modules/rehype-minify-whitespace/node_modules/hast-util-embedded/lib/index.js ***!
  \************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   embedded: () => (/* binding */ embedded)\n/* harmony export */ });\n/* harmony import */ var hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! hast-util-is-element */ \"(ssr)/../../node_modules/hast-util-is-element/index.js\");\n/**\n * @typedef {import('hast').Element} Element\n */\n\n\n\n/**\n * Check if a node is a *embedded content*.\n *\n * @type {import('hast-util-is-element').AssertPredicate<Element & {tagName: 'audio' | 'canvas' | 'embed' | 'iframe' | 'img' | 'math' | 'object' | 'picture' | 'svg' | 'video'}>}\n * @param value\n *   Thing to check (typically `Node`).\n * @returns\n *   Whether `value` is an element considered embedded content.\n *\n *   The elements `audio`, `canvas`, `embed`, `iframe`, `img`, `math`,\n *   `object`, `picture`, `svg`, and `video` are embedded content.\n */\n// @ts-expect-error Sure, the assertion matches.\nconst embedded = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)([\n  'audio',\n  'canvas',\n  'embed',\n  'iframe',\n  'img',\n  'math',\n  'object',\n  'picture',\n  'svg',\n  'video'\n])\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL3JlaHlwZS1taW5pZnktd2hpdGVzcGFjZS9ub2RlX21vZHVsZXMvaGFzdC11dGlsLWVtYmVkZGVkL2xpYi9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ0EsYUFBYSx3QkFBd0I7QUFDckM7O0FBRW1EOztBQUVuRDtBQUNBO0FBQ0E7QUFDQSxVQUFVLDBEQUEwRCwyR0FBMkc7QUFDL0s7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ08saUJBQWlCLG9FQUFjO0FBQ3RDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9Ab3BlbmNhbnZhcy93ZWIvLi4vLi4vbm9kZV9tb2R1bGVzL3JlaHlwZS1taW5pZnktd2hpdGVzcGFjZS9ub2RlX21vZHVsZXMvaGFzdC11dGlsLWVtYmVkZGVkL2xpYi9pbmRleC5qcz82ODI2Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnaGFzdCcpLkVsZW1lbnR9IEVsZW1lbnRcbiAqL1xuXG5pbXBvcnQge2NvbnZlcnRFbGVtZW50fSBmcm9tICdoYXN0LXV0aWwtaXMtZWxlbWVudCdcblxuLyoqXG4gKiBDaGVjayBpZiBhIG5vZGUgaXMgYSAqZW1iZWRkZWQgY29udGVudCouXG4gKlxuICogQHR5cGUge2ltcG9ydCgnaGFzdC11dGlsLWlzLWVsZW1lbnQnKS5Bc3NlcnRQcmVkaWNhdGU8RWxlbWVudCAmIHt0YWdOYW1lOiAnYXVkaW8nIHwgJ2NhbnZhcycgfCAnZW1iZWQnIHwgJ2lmcmFtZScgfCAnaW1nJyB8ICdtYXRoJyB8ICdvYmplY3QnIHwgJ3BpY3R1cmUnIHwgJ3N2ZycgfCAndmlkZW8nfT59XG4gKiBAcGFyYW0gdmFsdWVcbiAqICAgVGhpbmcgdG8gY2hlY2sgKHR5cGljYWxseSBgTm9kZWApLlxuICogQHJldHVybnNcbiAqICAgV2hldGhlciBgdmFsdWVgIGlzIGFuIGVsZW1lbnQgY29uc2lkZXJlZCBlbWJlZGRlZCBjb250ZW50LlxuICpcbiAqICAgVGhlIGVsZW1lbnRzIGBhdWRpb2AsIGBjYW52YXNgLCBgZW1iZWRgLCBgaWZyYW1lYCwgYGltZ2AsIGBtYXRoYCxcbiAqICAgYG9iamVjdGAsIGBwaWN0dXJlYCwgYHN2Z2AsIGFuZCBgdmlkZW9gIGFyZSBlbWJlZGRlZCBjb250ZW50LlxuICovXG4vLyBAdHMtZXhwZWN0LWVycm9yIFN1cmUsIHRoZSBhc3NlcnRpb24gbWF0Y2hlcy5cbmV4cG9ydCBjb25zdCBlbWJlZGRlZCA9IGNvbnZlcnRFbGVtZW50KFtcbiAgJ2F1ZGlvJyxcbiAgJ2NhbnZhcycsXG4gICdlbWJlZCcsXG4gICdpZnJhbWUnLFxuICAnaW1nJyxcbiAgJ21hdGgnLFxuICAnb2JqZWN0JyxcbiAgJ3BpY3R1cmUnLFxuICAnc3ZnJyxcbiAgJ3ZpZGVvJ1xuXSlcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/rehype-minify-whitespace/node_modules/hast-util-embedded/lib/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/rehype-minify-whitespace/node_modules/hast-util-whitespace/index.js":
/*!**********************************************************************************************!*\
  !*** ../../node_modules/rehype-minify-whitespace/node_modules/hast-util-whitespace/index.js ***!
  \**********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   whitespace: () => (/* binding */ whitespace)\n/* harmony export */ });\n/**\n * Check if the given value is *inter-element whitespace*.\n *\n * @param {unknown} thing\n *   Thing to check (typically `Node` or `string`).\n * @returns {boolean}\n *   Whether the `value` is inter-element whitespace (`boolean`): consisting of\n *   zero or more of space, tab (`\\t`), line feed (`\\n`), carriage return\n *   (`\\r`), or form feed (`\\f`).\n *   If a node is passed it must be a `Text` node, whose `value` field is\n *   checked.\n */\nfunction whitespace(thing) {\n  /** @type {string} */\n  const value =\n    // @ts-expect-error looks like a node.\n    thing && typeof thing === 'object' && thing.type === 'text'\n      ? // @ts-expect-error looks like a text.\n        thing.value || ''\n      : thing\n\n  // HTML whitespace expression.\n  // See <https://infra.spec.whatwg.org/#ascii-whitespace>.\n  return typeof value === 'string' && value.replace(/[ \\t\\n\\f\\r]/g, '') === ''\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL3JlaHlwZS1taW5pZnktd2hpdGVzcGFjZS9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXdoaXRlc3BhY2UvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsU0FBUztBQUNwQjtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQLGFBQWEsUUFBUTtBQUNyQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9Ab3BlbmNhbnZhcy93ZWIvLi4vLi4vbm9kZV9tb2R1bGVzL3JlaHlwZS1taW5pZnktd2hpdGVzcGFjZS9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXdoaXRlc3BhY2UvaW5kZXguanM/MWE2MiJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIENoZWNrIGlmIHRoZSBnaXZlbiB2YWx1ZSBpcyAqaW50ZXItZWxlbWVudCB3aGl0ZXNwYWNlKi5cbiAqXG4gKiBAcGFyYW0ge3Vua25vd259IHRoaW5nXG4gKiAgIFRoaW5nIHRvIGNoZWNrICh0eXBpY2FsbHkgYE5vZGVgIG9yIGBzdHJpbmdgKS5cbiAqIEByZXR1cm5zIHtib29sZWFufVxuICogICBXaGV0aGVyIHRoZSBgdmFsdWVgIGlzIGludGVyLWVsZW1lbnQgd2hpdGVzcGFjZSAoYGJvb2xlYW5gKTogY29uc2lzdGluZyBvZlxuICogICB6ZXJvIG9yIG1vcmUgb2Ygc3BhY2UsIHRhYiAoYFxcdGApLCBsaW5lIGZlZWQgKGBcXG5gKSwgY2FycmlhZ2UgcmV0dXJuXG4gKiAgIChgXFxyYCksIG9yIGZvcm0gZmVlZCAoYFxcZmApLlxuICogICBJZiBhIG5vZGUgaXMgcGFzc2VkIGl0IG11c3QgYmUgYSBgVGV4dGAgbm9kZSwgd2hvc2UgYHZhbHVlYCBmaWVsZCBpc1xuICogICBjaGVja2VkLlxuICovXG5leHBvcnQgZnVuY3Rpb24gd2hpdGVzcGFjZSh0aGluZykge1xuICAvKiogQHR5cGUge3N0cmluZ30gKi9cbiAgY29uc3QgdmFsdWUgPVxuICAgIC8vIEB0cy1leHBlY3QtZXJyb3IgbG9va3MgbGlrZSBhIG5vZGUuXG4gICAgdGhpbmcgJiYgdHlwZW9mIHRoaW5nID09PSAnb2JqZWN0JyAmJiB0aGluZy50eXBlID09PSAndGV4dCdcbiAgICAgID8gLy8gQHRzLWV4cGVjdC1lcnJvciBsb29rcyBsaWtlIGEgdGV4dC5cbiAgICAgICAgdGhpbmcudmFsdWUgfHwgJydcbiAgICAgIDogdGhpbmdcblxuICAvLyBIVE1MIHdoaXRlc3BhY2UgZXhwcmVzc2lvbi5cbiAgLy8gU2VlIDxodHRwczovL2luZnJhLnNwZWMud2hhdHdnLm9yZy8jYXNjaWktd2hpdGVzcGFjZT4uXG4gIHJldHVybiB0eXBlb2YgdmFsdWUgPT09ICdzdHJpbmcnICYmIHZhbHVlLnJlcGxhY2UoL1sgXFx0XFxuXFxmXFxyXS9nLCAnJykgPT09ICcnXG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/rehype-minify-whitespace/node_modules/hast-util-whitespace/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/rehype-minify-whitespace/skippable.js":
/*!****************************************************************!*\
  !*** ../../node_modules/rehype-minify-whitespace/skippable.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   skippable: () => (/* binding */ skippable)\n/* harmony export */ });\nconst skippable = [\n  'area',\n  'base',\n  'basefont',\n  'dialog',\n  'datalist',\n  'head',\n  'link',\n  'meta',\n  'noembed',\n  'noframes',\n  'param',\n  'rp',\n  'script',\n  'source',\n  'style',\n  'template',\n  'track',\n  'title'\n]\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL3JlaHlwZS1taW5pZnktd2hpdGVzcGFjZS9za2lwcGFibGUuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9Ab3BlbmNhbnZhcy93ZWIvLi4vLi4vbm9kZV9tb2R1bGVzL3JlaHlwZS1taW5pZnktd2hpdGVzcGFjZS9za2lwcGFibGUuanM/YjU2YiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3Qgc2tpcHBhYmxlID0gW1xuICAnYXJlYScsXG4gICdiYXNlJyxcbiAgJ2Jhc2Vmb250JyxcbiAgJ2RpYWxvZycsXG4gICdkYXRhbGlzdCcsXG4gICdoZWFkJyxcbiAgJ2xpbmsnLFxuICAnbWV0YScsXG4gICdub2VtYmVkJyxcbiAgJ25vZnJhbWVzJyxcbiAgJ3BhcmFtJyxcbiAgJ3JwJyxcbiAgJ3NjcmlwdCcsXG4gICdzb3VyY2UnLFxuICAnc3R5bGUnLFxuICAndGVtcGxhdGUnLFxuICAndHJhY2snLFxuICAndGl0bGUnXG5dXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/rehype-minify-whitespace/skippable.js\n");

/***/ })

};
;