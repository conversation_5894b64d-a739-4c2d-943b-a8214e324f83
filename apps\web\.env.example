# Feature flags for hiding/showing specific models
NEXT_PUBLIC_FIREWORKS_ENABLED=true
NEXT_PUBLIC_GEMINI_ENABLED=true
NEXT_PUBLIC_ANTHROPIC_ENABLED=true
NEXT_PUBLIC_OPENAI_ENABLED=true
NEXT_PUBLIC_GLM_ENABLED=true
# Set to false by default since the base OpenAI API is more common than the Azure OpenAI API.
NEXT_PUBLIC_AZURE_ENABLED=false
NEXT_PUBLIC_OLLAMA_ENABLED=false
NEXT_PUBLIC_GROQ_ENABLED=false

# If using Ollama, set the API URL here. Only needs to be set if using the non default Ollama server port.
# It will default to `http://host.docker.internal:11434` if not set.
# OLLAMA_API_URL="http://host.docker.internal:11434"

# Supabase for authentication
# Public keys
NEXT_PUBLIC_SUPABASE_URL=
NEXT_PUBLIC_SUPABASE_ANON_KEY=
# For document uploading (can be the same as the above keys)
NEXT_PUBLIC_SUPABASE_URL_DOCUMENTS=
NEXT_PUBLIC_SUPABASE_ANON_KEY_DOCUMENTS=

# For transcription
GROQ_API_KEY=

# For web scraping
FIRECRAWL_API_KEY=

# Azure OpenAI Configuration
# ENSURE THEY ARE PREFIXED WITH AN UNDERSCORE.
# _AZURE_OPENAI_API_KEY=your-azure-openai-api-key
# _AZURE_OPENAI_API_INSTANCE_NAME=your-instance-name
# _AZURE_OPENAI_API_DEPLOYMENT_NAME=your-deployment-name
# _AZURE_OPENAI_API_VERSION=2024-08-01-preview
# Optional: Azure OpenAI Base Path (if using a different domain)
# _AZURE_OPENAI_API_BASE_PATH=https://your-custom-domain.com/openai/deployments
