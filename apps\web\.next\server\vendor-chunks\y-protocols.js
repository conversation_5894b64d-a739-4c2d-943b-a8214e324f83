"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/y-protocols";
exports.ids = ["vendor-chunks/y-protocols"];
exports.modules = {

/***/ "(ssr)/../../node_modules/y-protocols/awareness.js":
/*!***************************************************!*\
  !*** ../../node_modules/y-protocols/awareness.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Awareness: () => (/* binding */ Awareness),\n/* harmony export */   applyAwarenessUpdate: () => (/* binding */ applyAwarenessUpdate),\n/* harmony export */   encodeAwarenessUpdate: () => (/* binding */ encodeAwarenessUpdate),\n/* harmony export */   modifyAwarenessUpdate: () => (/* binding */ modifyAwarenessUpdate),\n/* harmony export */   outdatedTimeout: () => (/* binding */ outdatedTimeout),\n/* harmony export */   removeAwarenessStates: () => (/* binding */ removeAwarenessStates)\n/* harmony export */ });\n/* harmony import */ var lib0_encoding__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! lib0/encoding */ \"(ssr)/../../node_modules/lib0/encoding.js\");\n/* harmony import */ var lib0_decoding__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! lib0/decoding */ \"(ssr)/../../node_modules/lib0/decoding.js\");\n/* harmony import */ var lib0_time__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! lib0/time */ \"(ssr)/../../node_modules/lib0/time.js\");\n/* harmony import */ var lib0_math__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! lib0/math */ \"(ssr)/../../node_modules/lib0/math.js\");\n/* harmony import */ var lib0_observable__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lib0/observable */ \"(ssr)/../../node_modules/lib0/observable.js\");\n/* harmony import */ var lib0_function__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! lib0/function */ \"(ssr)/../../node_modules/lib0/function.js\");\n/**\n * @module awareness-protocol\n */\n\n\n\n\n\n\n\n // eslint-disable-line\n\nconst outdatedTimeout = 30000\n\n/**\n * @typedef {Object} MetaClientState\n * @property {number} MetaClientState.clock\n * @property {number} MetaClientState.lastUpdated unix timestamp\n */\n\n/**\n * The Awareness class implements a simple shared state protocol that can be used for non-persistent data like awareness information\n * (cursor, username, status, ..). Each client can update its own local state and listen to state changes of\n * remote clients. Every client may set a state of a remote peer to `null` to mark the client as offline.\n *\n * Each client is identified by a unique client id (something we borrow from `doc.clientID`). A client can override\n * its own state by propagating a message with an increasing timestamp (`clock`). If such a message is received, it is\n * applied if the known state of that client is older than the new state (`clock < newClock`). If a client thinks that\n * a remote client is offline, it may propagate a message with\n * `{ clock: currentClientClock, state: null, client: remoteClient }`. If such a\n * message is received, and the known clock of that client equals the received clock, it will override the state with `null`.\n *\n * Before a client disconnects, it should propagate a `null` state with an updated clock.\n *\n * Awareness states must be updated every 30 seconds. Otherwise the Awareness instance will delete the client state.\n *\n * @extends {Observable<string>}\n */\nclass Awareness extends lib0_observable__WEBPACK_IMPORTED_MODULE_0__.Observable {\n  /**\n   * @param {Y.Doc} doc\n   */\n  constructor (doc) {\n    super()\n    this.doc = doc\n    /**\n     * @type {number}\n     */\n    this.clientID = doc.clientID\n    /**\n     * Maps from client id to client state\n     * @type {Map<number, Object<string, any>>}\n     */\n    this.states = new Map()\n    /**\n     * @type {Map<number, MetaClientState>}\n     */\n    this.meta = new Map()\n    this._checkInterval = /** @type {any} */ (setInterval(() => {\n      const now = lib0_time__WEBPACK_IMPORTED_MODULE_1__.getUnixTime()\n      if (this.getLocalState() !== null && (outdatedTimeout / 2 <= now - /** @type {{lastUpdated:number}} */ (this.meta.get(this.clientID)).lastUpdated)) {\n        // renew local clock\n        this.setLocalState(this.getLocalState())\n      }\n      /**\n       * @type {Array<number>}\n       */\n      const remove = []\n      this.meta.forEach((meta, clientid) => {\n        if (clientid !== this.clientID && outdatedTimeout <= now - meta.lastUpdated && this.states.has(clientid)) {\n          remove.push(clientid)\n        }\n      })\n      if (remove.length > 0) {\n        removeAwarenessStates(this, remove, 'timeout')\n      }\n    }, lib0_math__WEBPACK_IMPORTED_MODULE_2__.floor(outdatedTimeout / 10)))\n    doc.on('destroy', () => {\n      this.destroy()\n    })\n    this.setLocalState({})\n  }\n\n  destroy () {\n    this.emit('destroy', [this])\n    this.setLocalState(null)\n    super.destroy()\n    clearInterval(this._checkInterval)\n  }\n\n  /**\n   * @return {Object<string,any>|null}\n   */\n  getLocalState () {\n    return this.states.get(this.clientID) || null\n  }\n\n  /**\n   * @param {Object<string,any>|null} state\n   */\n  setLocalState (state) {\n    const clientID = this.clientID\n    const currLocalMeta = this.meta.get(clientID)\n    const clock = currLocalMeta === undefined ? 0 : currLocalMeta.clock + 1\n    const prevState = this.states.get(clientID)\n    if (state === null) {\n      this.states.delete(clientID)\n    } else {\n      this.states.set(clientID, state)\n    }\n    this.meta.set(clientID, {\n      clock,\n      lastUpdated: lib0_time__WEBPACK_IMPORTED_MODULE_1__.getUnixTime()\n    })\n    const added = []\n    const updated = []\n    const filteredUpdated = []\n    const removed = []\n    if (state === null) {\n      removed.push(clientID)\n    } else if (prevState == null) {\n      if (state != null) {\n        added.push(clientID)\n      }\n    } else {\n      updated.push(clientID)\n      if (!lib0_function__WEBPACK_IMPORTED_MODULE_3__.equalityDeep(prevState, state)) {\n        filteredUpdated.push(clientID)\n      }\n    }\n    if (added.length > 0 || filteredUpdated.length > 0 || removed.length > 0) {\n      this.emit('change', [{ added, updated: filteredUpdated, removed }, 'local'])\n    }\n    this.emit('update', [{ added, updated, removed }, 'local'])\n  }\n\n  /**\n   * @param {string} field\n   * @param {any} value\n   */\n  setLocalStateField (field, value) {\n    const state = this.getLocalState()\n    if (state !== null) {\n      this.setLocalState({\n        ...state,\n        [field]: value\n      })\n    }\n  }\n\n  /**\n   * @return {Map<number,Object<string,any>>}\n   */\n  getStates () {\n    return this.states\n  }\n}\n\n/**\n * Mark (remote) clients as inactive and remove them from the list of active peers.\n * This change will be propagated to remote clients.\n *\n * @param {Awareness} awareness\n * @param {Array<number>} clients\n * @param {any} origin\n */\nconst removeAwarenessStates = (awareness, clients, origin) => {\n  const removed = []\n  for (let i = 0; i < clients.length; i++) {\n    const clientID = clients[i]\n    if (awareness.states.has(clientID)) {\n      awareness.states.delete(clientID)\n      if (clientID === awareness.clientID) {\n        const curMeta = /** @type {MetaClientState} */ (awareness.meta.get(clientID))\n        awareness.meta.set(clientID, {\n          clock: curMeta.clock + 1,\n          lastUpdated: lib0_time__WEBPACK_IMPORTED_MODULE_1__.getUnixTime()\n        })\n      }\n      removed.push(clientID)\n    }\n  }\n  if (removed.length > 0) {\n    awareness.emit('change', [{ added: [], updated: [], removed }, origin])\n    awareness.emit('update', [{ added: [], updated: [], removed }, origin])\n  }\n}\n\n/**\n * @param {Awareness} awareness\n * @param {Array<number>} clients\n * @return {Uint8Array}\n */\nconst encodeAwarenessUpdate = (awareness, clients, states = awareness.states) => {\n  const len = clients.length\n  const encoder = lib0_encoding__WEBPACK_IMPORTED_MODULE_4__.createEncoder()\n  lib0_encoding__WEBPACK_IMPORTED_MODULE_4__.writeVarUint(encoder, len)\n  for (let i = 0; i < len; i++) {\n    const clientID = clients[i]\n    const state = states.get(clientID) || null\n    const clock = /** @type {MetaClientState} */ (awareness.meta.get(clientID)).clock\n    lib0_encoding__WEBPACK_IMPORTED_MODULE_4__.writeVarUint(encoder, clientID)\n    lib0_encoding__WEBPACK_IMPORTED_MODULE_4__.writeVarUint(encoder, clock)\n    lib0_encoding__WEBPACK_IMPORTED_MODULE_4__.writeVarString(encoder, JSON.stringify(state))\n  }\n  return lib0_encoding__WEBPACK_IMPORTED_MODULE_4__.toUint8Array(encoder)\n}\n\n/**\n * Modify the content of an awareness update before re-encoding it to an awareness update.\n *\n * This might be useful when you have a central server that wants to ensure that clients\n * cant hijack somebody elses identity.\n *\n * @param {Uint8Array} update\n * @param {function(any):any} modify\n * @return {Uint8Array}\n */\nconst modifyAwarenessUpdate = (update, modify) => {\n  const decoder = lib0_decoding__WEBPACK_IMPORTED_MODULE_5__.createDecoder(update)\n  const encoder = lib0_encoding__WEBPACK_IMPORTED_MODULE_4__.createEncoder()\n  const len = lib0_decoding__WEBPACK_IMPORTED_MODULE_5__.readVarUint(decoder)\n  lib0_encoding__WEBPACK_IMPORTED_MODULE_4__.writeVarUint(encoder, len)\n  for (let i = 0; i < len; i++) {\n    const clientID = lib0_decoding__WEBPACK_IMPORTED_MODULE_5__.readVarUint(decoder)\n    const clock = lib0_decoding__WEBPACK_IMPORTED_MODULE_5__.readVarUint(decoder)\n    const state = JSON.parse(lib0_decoding__WEBPACK_IMPORTED_MODULE_5__.readVarString(decoder))\n    const modifiedState = modify(state)\n    lib0_encoding__WEBPACK_IMPORTED_MODULE_4__.writeVarUint(encoder, clientID)\n    lib0_encoding__WEBPACK_IMPORTED_MODULE_4__.writeVarUint(encoder, clock)\n    lib0_encoding__WEBPACK_IMPORTED_MODULE_4__.writeVarString(encoder, JSON.stringify(modifiedState))\n  }\n  return lib0_encoding__WEBPACK_IMPORTED_MODULE_4__.toUint8Array(encoder)\n}\n\n/**\n * @param {Awareness} awareness\n * @param {Uint8Array} update\n * @param {any} origin This will be added to the emitted change event\n */\nconst applyAwarenessUpdate = (awareness, update, origin) => {\n  const decoder = lib0_decoding__WEBPACK_IMPORTED_MODULE_5__.createDecoder(update)\n  const timestamp = lib0_time__WEBPACK_IMPORTED_MODULE_1__.getUnixTime()\n  const added = []\n  const updated = []\n  const filteredUpdated = []\n  const removed = []\n  const len = lib0_decoding__WEBPACK_IMPORTED_MODULE_5__.readVarUint(decoder)\n  for (let i = 0; i < len; i++) {\n    const clientID = lib0_decoding__WEBPACK_IMPORTED_MODULE_5__.readVarUint(decoder)\n    let clock = lib0_decoding__WEBPACK_IMPORTED_MODULE_5__.readVarUint(decoder)\n    const state = JSON.parse(lib0_decoding__WEBPACK_IMPORTED_MODULE_5__.readVarString(decoder))\n    const clientMeta = awareness.meta.get(clientID)\n    const prevState = awareness.states.get(clientID)\n    const currClock = clientMeta === undefined ? 0 : clientMeta.clock\n    if (currClock < clock || (currClock === clock && state === null && awareness.states.has(clientID))) {\n      if (state === null) {\n        // never let a remote client remove this local state\n        if (clientID === awareness.clientID && awareness.getLocalState() != null) {\n          // remote client removed the local state. Do not remote state. Broadcast a message indicating\n          // that this client still exists by increasing the clock\n          clock++\n        } else {\n          awareness.states.delete(clientID)\n        }\n      } else {\n        awareness.states.set(clientID, state)\n      }\n      awareness.meta.set(clientID, {\n        clock,\n        lastUpdated: timestamp\n      })\n      if (clientMeta === undefined && state !== null) {\n        added.push(clientID)\n      } else if (clientMeta !== undefined && state === null) {\n        removed.push(clientID)\n      } else if (state !== null) {\n        if (!lib0_function__WEBPACK_IMPORTED_MODULE_3__.equalityDeep(state, prevState)) {\n          filteredUpdated.push(clientID)\n        }\n        updated.push(clientID)\n      }\n    }\n  }\n  if (added.length > 0 || filteredUpdated.length > 0 || removed.length > 0) {\n    awareness.emit('change', [{\n      added, updated: filteredUpdated, removed\n    }, origin])\n  }\n  if (added.length > 0 || updated.length > 0 || removed.length > 0) {\n    awareness.emit('update', [{\n      added, updated, removed\n    }, origin])\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/y-protocols/awareness.js\n");

/***/ })

};
;