"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/web-namespaces";
exports.ids = ["vendor-chunks/web-namespaces"];
exports.modules = {

/***/ "(ssr)/../../node_modules/web-namespaces/index.js":
/*!**************************************************!*\
  !*** ../../node_modules/web-namespaces/index.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   webNamespaces: () => (/* binding */ webNamespaces)\n/* harmony export */ });\n/**\n * Map of web namespaces.\n *\n * @type {Record<string, string>}\n */\nconst webNamespaces = {\n  html: 'http://www.w3.org/1999/xhtml',\n  mathml: 'http://www.w3.org/1998/Math/MathML',\n  svg: 'http://www.w3.org/2000/svg',\n  xlink: 'http://www.w3.org/1999/xlink',\n  xml: 'http://www.w3.org/XML/1998/namespace',\n  xmlns: 'http://www.w3.org/2000/xmlns/'\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL3dlYi1uYW1lc3BhY2VzL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQSxVQUFVO0FBQ1Y7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQG9wZW5jYW52YXMvd2ViLy4uLy4uL25vZGVfbW9kdWxlcy93ZWItbmFtZXNwYWNlcy9pbmRleC5qcz8xNWU1Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogTWFwIG9mIHdlYiBuYW1lc3BhY2VzLlxuICpcbiAqIEB0eXBlIHtSZWNvcmQ8c3RyaW5nLCBzdHJpbmc+fVxuICovXG5leHBvcnQgY29uc3Qgd2ViTmFtZXNwYWNlcyA9IHtcbiAgaHRtbDogJ2h0dHA6Ly93d3cudzMub3JnLzE5OTkveGh0bWwnLFxuICBtYXRobWw6ICdodHRwOi8vd3d3LnczLm9yZy8xOTk4L01hdGgvTWF0aE1MJyxcbiAgc3ZnOiAnaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnLFxuICB4bGluazogJ2h0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsnLFxuICB4bWw6ICdodHRwOi8vd3d3LnczLm9yZy9YTUwvMTk5OC9uYW1lc3BhY2UnLFxuICB4bWxuczogJ2h0dHA6Ly93d3cudzMub3JnLzIwMDAveG1sbnMvJ1xufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/web-namespaces/index.js\n");

/***/ })

};
;