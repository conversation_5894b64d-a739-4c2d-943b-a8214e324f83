"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/hast-util-format";
exports.ids = ["vendor-chunks/hast-util-format"];
exports.modules = {

/***/ "(ssr)/../../node_modules/hast-util-format/lib/index.js":
/*!********************************************************!*\
  !*** ../../node_modules/hast-util-format/lib/index.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   format: () => (/* binding */ format)\n/* harmony export */ });\n/* harmony import */ var hast_util_embedded__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! hast-util-embedded */ \"(ssr)/../../node_modules/hast-util-embedded/lib/index.js\");\n/* harmony import */ var hast_util_minify_whitespace__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! hast-util-minify-whitespace */ \"(ssr)/../../node_modules/hast-util-minify-whitespace/lib/index.js\");\n/* harmony import */ var hast_util_phrasing__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! hast-util-phrasing */ \"(ssr)/../../node_modules/hast-util-format/node_modules/hast-util-phrasing/lib/index.js\");\n/* harmony import */ var hast_util_whitespace__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! hast-util-whitespace */ \"(ssr)/../../node_modules/hast-util-whitespace/lib/index.js\");\n/* harmony import */ var html_whitespace_sensitive_tag_names__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! html-whitespace-sensitive-tag-names */ \"(ssr)/../../node_modules/html-whitespace-sensitive-tag-names/lib/index.js\");\n/* harmony import */ var unist_util_visit_parents__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! unist-util-visit-parents */ \"(ssr)/../../node_modules/unist-util-visit-parents/lib/index.js\");\n/**\n * @import {Nodes, RootContent, Root} from 'hast'\n * @import {BuildVisitor} from 'unist-util-visit-parents'\n * @import {Options, State} from './types.js'\n */\n\n\n\n\n\n\n\n\n/** @type {Options} */\nconst emptyOptions = {}\n\n/**\n * Format whitespace in HTML.\n *\n * @param {Root} tree\n *   Tree.\n * @param {Options | null | undefined} [options]\n *   Configuration (optional).\n * @returns {undefined}\n *   Nothing.\n */\nfunction format(tree, options) {\n  const settings = options || emptyOptions\n\n  /** @type {State} */\n  const state = {\n    blanks: settings.blanks || [],\n    head: false,\n    indentInitial: settings.indentInitial !== false,\n    indent:\n      typeof settings.indent === 'number'\n        ? ' '.repeat(settings.indent)\n        : typeof settings.indent === 'string'\n          ? settings.indent\n          : '  '\n  }\n\n  ;(0,hast_util_minify_whitespace__WEBPACK_IMPORTED_MODULE_0__.minifyWhitespace)(tree, {newlines: true})\n\n  ;(0,unist_util_visit_parents__WEBPACK_IMPORTED_MODULE_1__.visitParents)(tree, visitor)\n\n  /**\n   * @type {BuildVisitor<Root>}\n   */\n  function visitor(node, parents) {\n    if (!('children' in node)) {\n      return\n    }\n\n    if (node.type === 'element' && node.tagName === 'head') {\n      state.head = true\n    }\n\n    if (state.head && node.type === 'element' && node.tagName === 'body') {\n      state.head = false\n    }\n\n    if (\n      node.type === 'element' &&\n      html_whitespace_sensitive_tag_names__WEBPACK_IMPORTED_MODULE_2__.whitespaceSensitiveTagNames.includes(node.tagName)\n    ) {\n      return unist_util_visit_parents__WEBPACK_IMPORTED_MODULE_1__.SKIP\n    }\n\n    // Don’t indent content of whitespace-sensitive nodes / inlines.\n    if (node.children.length === 0 || !padding(state, node)) {\n      return\n    }\n\n    let level = parents.length\n\n    if (!state.indentInitial) {\n      level--\n    }\n\n    let eol = false\n\n    // Indent newlines in `text`.\n    for (const child of node.children) {\n      if (child.type === 'comment' || child.type === 'text') {\n        if (child.value.includes('\\n')) {\n          eol = true\n        }\n\n        child.value = child.value.replace(\n          / *\\n/g,\n          '$&' + state.indent.repeat(level)\n        )\n      }\n    }\n\n    /** @type {Array<RootContent>} */\n    const result = []\n    /** @type {RootContent | undefined} */\n    let previous\n\n    for (const child of node.children) {\n      if (padding(state, child) || (eol && !previous)) {\n        addBreak(result, level, child)\n        eol = true\n      }\n\n      previous = child\n      result.push(child)\n    }\n\n    if (previous && (eol || padding(state, previous))) {\n      // Ignore trailing whitespace (if that already existed), as we’ll add\n      // properly indented whitespace.\n      if ((0,hast_util_whitespace__WEBPACK_IMPORTED_MODULE_3__.whitespace)(previous)) {\n        result.pop()\n        previous = result[result.length - 1]\n      }\n\n      addBreak(result, level - 1)\n    }\n\n    node.children = result\n  }\n\n  /**\n   * @param {Array<RootContent>} list\n   *   Nodes.\n   * @param {number} level\n   *   Indentation level.\n   * @param {RootContent | undefined} [next]\n   *   Next node.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  function addBreak(list, level, next) {\n    const tail = list[list.length - 1]\n    const previous = tail && (0,hast_util_whitespace__WEBPACK_IMPORTED_MODULE_3__.whitespace)(tail) ? list[list.length - 2] : tail\n    const replace =\n      (blank(state, previous) && blank(state, next) ? '\\n\\n' : '\\n') +\n      state.indent.repeat(Math.max(level, 0))\n\n    if (tail && tail.type === 'text') {\n      tail.value = (0,hast_util_whitespace__WEBPACK_IMPORTED_MODULE_3__.whitespace)(tail) ? replace : tail.value + replace\n    } else {\n      list.push({type: 'text', value: replace})\n    }\n  }\n}\n\n/**\n * @param {State} state\n *   Info passed around.\n * @param {Nodes | undefined} node\n *   Node.\n * @returns {boolean}\n *   Whether `node` is a blank.\n */\nfunction blank(state, node) {\n  return Boolean(\n    node &&\n      node.type === 'element' &&\n      state.blanks.length > 0 &&\n      state.blanks.includes(node.tagName)\n  )\n}\n\n/**\n * @param {State} state\n *   Info passed around.\n * @param {Nodes} node\n *   Node.\n * @returns {boolean}\n *   Whether `node` should be padded.\n */\nfunction padding(state, node) {\n  return (\n    node.type === 'root' ||\n    (node.type === 'element'\n      ? state.head ||\n        node.tagName === 'script' ||\n        (0,hast_util_embedded__WEBPACK_IMPORTED_MODULE_4__.embedded)(node) ||\n        !(0,hast_util_phrasing__WEBPACK_IMPORTED_MODULE_5__.phrasing)(node)\n      : false)\n  )\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/hast-util-format/lib/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/hast-util-format/node_modules/hast-util-has-property/lib/index.js":
/*!********************************************************************************************!*\
  !*** ../../node_modules/hast-util-format/node_modules/hast-util-has-property/lib/index.js ***!
  \********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   hasProperty: () => (/* binding */ hasProperty)\n/* harmony export */ });\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Nodes} Nodes\n */\n\nconst own = {}.hasOwnProperty\n\n/**\n * Check if `node` is an element and has a `name` property.\n *\n * @template {string} Key\n *   Type of key.\n * @param {Nodes} node\n *   Node to check (typically `Element`).\n * @param {Key} name\n *   Property name to check.\n * @returns {node is Element & {properties: Record<Key, Array<number | string> | number | string | true>}}}\n *   Whether `node` is an element that has a `name` property.\n *\n *   Note: see <https://github.com/DefinitelyTyped/DefinitelyTyped/blob/27c9274/types/hast/index.d.ts#L37C29-L37C98>.\n */\nfunction hasProperty(node, name) {\n  const value =\n    node.type === 'element' &&\n    own.call(node.properties, name) &&\n    node.properties[name]\n\n  return value !== null && value !== undefined && value !== false\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2hhc3QtdXRpbC1mb3JtYXQvbm9kZV9tb2R1bGVzL2hhc3QtdXRpbC1oYXMtcHJvcGVydHkvbGliL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBLGFBQWEsd0JBQXdCO0FBQ3JDLGFBQWEsc0JBQXNCO0FBQ25DOztBQUVBLGNBQWM7O0FBRWQ7QUFDQTtBQUNBO0FBQ0EsY0FBYyxRQUFRO0FBQ3RCO0FBQ0EsV0FBVyxPQUFPO0FBQ2xCO0FBQ0EsV0FBVyxLQUFLO0FBQ2hCO0FBQ0EsYUFBYSxtQkFBbUI7QUFDaEM7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9Ab3BlbmNhbnZhcy93ZWIvLi4vLi4vbm9kZV9tb2R1bGVzL2hhc3QtdXRpbC1mb3JtYXQvbm9kZV9tb2R1bGVzL2hhc3QtdXRpbC1oYXMtcHJvcGVydHkvbGliL2luZGV4LmpzPzhjOGIiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdoYXN0JykuRWxlbWVudH0gRWxlbWVudFxuICogQHR5cGVkZWYge2ltcG9ydCgnaGFzdCcpLk5vZGVzfSBOb2Rlc1xuICovXG5cbmNvbnN0IG93biA9IHt9Lmhhc093blByb3BlcnR5XG5cbi8qKlxuICogQ2hlY2sgaWYgYG5vZGVgIGlzIGFuIGVsZW1lbnQgYW5kIGhhcyBhIGBuYW1lYCBwcm9wZXJ0eS5cbiAqXG4gKiBAdGVtcGxhdGUge3N0cmluZ30gS2V5XG4gKiAgIFR5cGUgb2Yga2V5LlxuICogQHBhcmFtIHtOb2Rlc30gbm9kZVxuICogICBOb2RlIHRvIGNoZWNrICh0eXBpY2FsbHkgYEVsZW1lbnRgKS5cbiAqIEBwYXJhbSB7S2V5fSBuYW1lXG4gKiAgIFByb3BlcnR5IG5hbWUgdG8gY2hlY2suXG4gKiBAcmV0dXJucyB7bm9kZSBpcyBFbGVtZW50ICYge3Byb3BlcnRpZXM6IFJlY29yZDxLZXksIEFycmF5PG51bWJlciB8IHN0cmluZz4gfCBudW1iZXIgfCBzdHJpbmcgfCB0cnVlPn19fVxuICogICBXaGV0aGVyIGBub2RlYCBpcyBhbiBlbGVtZW50IHRoYXQgaGFzIGEgYG5hbWVgIHByb3BlcnR5LlxuICpcbiAqICAgTm90ZTogc2VlIDxodHRwczovL2dpdGh1Yi5jb20vRGVmaW5pdGVseVR5cGVkL0RlZmluaXRlbHlUeXBlZC9ibG9iLzI3YzkyNzQvdHlwZXMvaGFzdC9pbmRleC5kLnRzI0wzN0MyOS1MMzdDOTg+LlxuICovXG5leHBvcnQgZnVuY3Rpb24gaGFzUHJvcGVydHkobm9kZSwgbmFtZSkge1xuICBjb25zdCB2YWx1ZSA9XG4gICAgbm9kZS50eXBlID09PSAnZWxlbWVudCcgJiZcbiAgICBvd24uY2FsbChub2RlLnByb3BlcnRpZXMsIG5hbWUpICYmXG4gICAgbm9kZS5wcm9wZXJ0aWVzW25hbWVdXG5cbiAgcmV0dXJuIHZhbHVlICE9PSBudWxsICYmIHZhbHVlICE9PSB1bmRlZmluZWQgJiYgdmFsdWUgIT09IGZhbHNlXG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/hast-util-format/node_modules/hast-util-has-property/lib/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/hast-util-format/node_modules/hast-util-is-body-ok-link/lib/index.js":
/*!***********************************************************************************************!*\
  !*** ../../node_modules/hast-util-format/node_modules/hast-util-is-body-ok-link/lib/index.js ***!
  \***********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isBodyOkLink: () => (/* binding */ isBodyOkLink)\n/* harmony export */ });\n/**\n * @import {Nodes} from 'hast'\n */\n\nconst list = new Set(['pingback', 'prefetch', 'stylesheet'])\n\n/**\n * Checks whether a node is a “body OK” link.\n *\n * @param {Nodes} node\n *   Node to check.\n * @returns {boolean}\n *   Whether `node` is a “body OK” link.\n */\nfunction isBodyOkLink(node) {\n  if (node.type !== 'element' || node.tagName !== 'link') {\n    return false\n  }\n\n  if (node.properties.itemProp) {\n    return true\n  }\n\n  const value = node.properties.rel\n  let index = -1\n\n  if (!Array.isArray(value) || value.length === 0) {\n    return false\n  }\n\n  while (++index < value.length) {\n    if (!list.has(String(value[index]))) {\n      return false\n    }\n  }\n\n  return true\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2hhc3QtdXRpbC1mb3JtYXQvbm9kZV9tb2R1bGVzL2hhc3QtdXRpbC1pcy1ib2R5LW9rLWxpbmsvbGliL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBLFlBQVksT0FBTztBQUNuQjs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLE9BQU87QUFDbEI7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9Ab3BlbmNhbnZhcy93ZWIvLi4vLi4vbm9kZV9tb2R1bGVzL2hhc3QtdXRpbC1mb3JtYXQvbm9kZV9tb2R1bGVzL2hhc3QtdXRpbC1pcy1ib2R5LW9rLWxpbmsvbGliL2luZGV4LmpzP2QxN2YiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAaW1wb3J0IHtOb2Rlc30gZnJvbSAnaGFzdCdcbiAqL1xuXG5jb25zdCBsaXN0ID0gbmV3IFNldChbJ3BpbmdiYWNrJywgJ3ByZWZldGNoJywgJ3N0eWxlc2hlZXQnXSlcblxuLyoqXG4gKiBDaGVja3Mgd2hldGhlciBhIG5vZGUgaXMgYSDigJxib2R5IE9L4oCdIGxpbmsuXG4gKlxuICogQHBhcmFtIHtOb2Rlc30gbm9kZVxuICogICBOb2RlIHRvIGNoZWNrLlxuICogQHJldHVybnMge2Jvb2xlYW59XG4gKiAgIFdoZXRoZXIgYG5vZGVgIGlzIGEg4oCcYm9keSBPS+KAnSBsaW5rLlxuICovXG5leHBvcnQgZnVuY3Rpb24gaXNCb2R5T2tMaW5rKG5vZGUpIHtcbiAgaWYgKG5vZGUudHlwZSAhPT0gJ2VsZW1lbnQnIHx8IG5vZGUudGFnTmFtZSAhPT0gJ2xpbmsnKSB7XG4gICAgcmV0dXJuIGZhbHNlXG4gIH1cblxuICBpZiAobm9kZS5wcm9wZXJ0aWVzLml0ZW1Qcm9wKSB7XG4gICAgcmV0dXJuIHRydWVcbiAgfVxuXG4gIGNvbnN0IHZhbHVlID0gbm9kZS5wcm9wZXJ0aWVzLnJlbFxuICBsZXQgaW5kZXggPSAtMVxuXG4gIGlmICghQXJyYXkuaXNBcnJheSh2YWx1ZSkgfHwgdmFsdWUubGVuZ3RoID09PSAwKSB7XG4gICAgcmV0dXJuIGZhbHNlXG4gIH1cblxuICB3aGlsZSAoKytpbmRleCA8IHZhbHVlLmxlbmd0aCkge1xuICAgIGlmICghbGlzdC5oYXMoU3RyaW5nKHZhbHVlW2luZGV4XSkpKSB7XG4gICAgICByZXR1cm4gZmFsc2VcbiAgICB9XG4gIH1cblxuICByZXR1cm4gdHJ1ZVxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/hast-util-format/node_modules/hast-util-is-body-ok-link/lib/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/hast-util-format/node_modules/hast-util-is-element/lib/index.js":
/*!******************************************************************************************!*\
  !*** ../../node_modules/hast-util-format/node_modules/hast-util-is-element/lib/index.js ***!
  \******************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   convertElement: () => (/* binding */ convertElement),\n/* harmony export */   isElement: () => (/* binding */ isElement)\n/* harmony export */ });\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Parents} Parents\n */\n\n/**\n * @template Fn\n * @template Fallback\n * @typedef {Fn extends (value: any) => value is infer Thing ? Thing : Fallback} Predicate\n */\n\n/**\n * @callback Check\n *   Check that an arbitrary value is an element.\n * @param {unknown} this\n *   Context object (`this`) to call `test` with\n * @param {unknown} [element]\n *   Anything (typically a node).\n * @param {number | null | undefined} [index]\n *   Position of `element` in its parent.\n * @param {Parents | null | undefined} [parent]\n *   Parent of `element`.\n * @returns {boolean}\n *   Whether this is an element and passes a test.\n *\n * @typedef {Array<TestFunction | string> | TestFunction | string | null | undefined} Test\n *   Check for an arbitrary element.\n *\n *   * when `string`, checks that the element has that tag name\n *   * when `function`, see `TestFunction`\n *   * when `Array`, checks if one of the subtests pass\n *\n * @callback TestFunction\n *   Check if an element passes a test.\n * @param {unknown} this\n *   The given context.\n * @param {Element} element\n *   An element.\n * @param {number | undefined} [index]\n *   Position of `element` in its parent.\n * @param {Parents | undefined} [parent]\n *   Parent of `element`.\n * @returns {boolean | undefined | void}\n *   Whether this element passes the test.\n *\n *   Note: `void` is included until TS sees no return as `undefined`.\n */\n\n/**\n * Check if `element` is an `Element` and whether it passes the given test.\n *\n * @param element\n *   Thing to check, typically `element`.\n * @param test\n *   Check for a specific element.\n * @param index\n *   Position of `element` in its parent.\n * @param parent\n *   Parent of `element`.\n * @param context\n *   Context object (`this`) to call `test` with.\n * @returns\n *   Whether `element` is an `Element` and passes a test.\n * @throws\n *   When an incorrect `test`, `index`, or `parent` is given; there is no error\n *   thrown when `element` is not a node or not an element.\n */\nconst isElement =\n  // Note: overloads in JSDoc can’t yet use different `@template`s.\n  /**\n   * @type {(\n   *   (<Condition extends TestFunction>(element: unknown, test: Condition, index?: number | null | undefined, parent?: Parents | null | undefined, context?: unknown) => element is Element & Predicate<Condition, Element>) &\n   *   (<Condition extends string>(element: unknown, test: Condition, index?: number | null | undefined, parent?: Parents | null | undefined, context?: unknown) => element is Element & {tagName: Condition}) &\n   *   ((element?: null | undefined) => false) &\n   *   ((element: unknown, test?: null | undefined, index?: number | null | undefined, parent?: Parents | null | undefined, context?: unknown) => element is Element) &\n   *   ((element: unknown, test?: Test, index?: number | null | undefined, parent?: Parents | null | undefined, context?: unknown) => boolean)\n   * )}\n   */\n  (\n    /**\n     * @param {unknown} [element]\n     * @param {Test | undefined} [test]\n     * @param {number | null | undefined} [index]\n     * @param {Parents | null | undefined} [parent]\n     * @param {unknown} [context]\n     * @returns {boolean}\n     */\n    // eslint-disable-next-line max-params\n    function (element, test, index, parent, context) {\n      const check = convertElement(test)\n\n      if (\n        index !== null &&\n        index !== undefined &&\n        (typeof index !== 'number' ||\n          index < 0 ||\n          index === Number.POSITIVE_INFINITY)\n      ) {\n        throw new Error('Expected positive finite `index`')\n      }\n\n      if (\n        parent !== null &&\n        parent !== undefined &&\n        (!parent.type || !parent.children)\n      ) {\n        throw new Error('Expected valid `parent`')\n      }\n\n      if (\n        (index === null || index === undefined) !==\n        (parent === null || parent === undefined)\n      ) {\n        throw new Error('Expected both `index` and `parent`')\n      }\n\n      return looksLikeAnElement(element)\n        ? check.call(context, element, index, parent)\n        : false\n    }\n  )\n\n/**\n * Generate a check from a test.\n *\n * Useful if you’re going to test many nodes, for example when creating a\n * utility where something else passes a compatible test.\n *\n * The created function is a bit faster because it expects valid input only:\n * an `element`, `index`, and `parent`.\n *\n * @param test\n *   A test for a specific element.\n * @returns\n *   A check.\n */\nconst convertElement =\n  // Note: overloads in JSDoc can’t yet use different `@template`s.\n  /**\n   * @type {(\n   *   (<Condition extends TestFunction>(test: Condition) => (element: unknown, index?: number | null | undefined, parent?: Parents | null | undefined, context?: unknown) => element is Element & Predicate<Condition, Element>) &\n   *   (<Condition extends string>(test: Condition) => (element: unknown, index?: number | null | undefined, parent?: Parents | null | undefined, context?: unknown) => element is Element & {tagName: Condition}) &\n   *   ((test?: null | undefined) => (element?: unknown, index?: number | null | undefined, parent?: Parents | null | undefined, context?: unknown) => element is Element) &\n   *   ((test?: Test) => Check)\n   * )}\n   */\n  (\n    /**\n     * @param {Test | null | undefined} [test]\n     * @returns {Check}\n     */\n    function (test) {\n      if (test === null || test === undefined) {\n        return element\n      }\n\n      if (typeof test === 'string') {\n        return tagNameFactory(test)\n      }\n\n      // Assume array.\n      if (typeof test === 'object') {\n        return anyFactory(test)\n      }\n\n      if (typeof test === 'function') {\n        return castFactory(test)\n      }\n\n      throw new Error('Expected function, string, or array as `test`')\n    }\n  )\n\n/**\n * Handle multiple tests.\n *\n * @param {Array<TestFunction | string>} tests\n * @returns {Check}\n */\nfunction anyFactory(tests) {\n  /** @type {Array<Check>} */\n  const checks = []\n  let index = -1\n\n  while (++index < tests.length) {\n    checks[index] = convertElement(tests[index])\n  }\n\n  return castFactory(any)\n\n  /**\n   * @this {unknown}\n   * @type {TestFunction}\n   */\n  function any(...parameters) {\n    let index = -1\n\n    while (++index < checks.length) {\n      if (checks[index].apply(this, parameters)) return true\n    }\n\n    return false\n  }\n}\n\n/**\n * Turn a string into a test for an element with a certain type.\n *\n * @param {string} check\n * @returns {Check}\n */\nfunction tagNameFactory(check) {\n  return castFactory(tagName)\n\n  /**\n   * @param {Element} element\n   * @returns {boolean}\n   */\n  function tagName(element) {\n    return element.tagName === check\n  }\n}\n\n/**\n * Turn a custom test into a test for an element that passes that test.\n *\n * @param {TestFunction} testFunction\n * @returns {Check}\n */\nfunction castFactory(testFunction) {\n  return check\n\n  /**\n   * @this {unknown}\n   * @type {Check}\n   */\n  function check(value, index, parent) {\n    return Boolean(\n      looksLikeAnElement(value) &&\n        testFunction.call(\n          this,\n          value,\n          typeof index === 'number' ? index : undefined,\n          parent || undefined\n        )\n    )\n  }\n}\n\n/**\n * Make sure something is an element.\n *\n * @param {unknown} element\n * @returns {element is Element}\n */\nfunction element(element) {\n  return Boolean(\n    element &&\n      typeof element === 'object' &&\n      'type' in element &&\n      element.type === 'element' &&\n      'tagName' in element &&\n      typeof element.tagName === 'string'\n  )\n}\n\n/**\n * @param {unknown} value\n * @returns {value is Element}\n */\nfunction looksLikeAnElement(value) {\n  return (\n    value !== null &&\n    typeof value === 'object' &&\n    'type' in value &&\n    'tagName' in value\n  )\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/hast-util-format/node_modules/hast-util-is-element/lib/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/hast-util-format/node_modules/hast-util-phrasing/lib/index.js":
/*!****************************************************************************************!*\
  !*** ../../node_modules/hast-util-format/node_modules/hast-util-phrasing/lib/index.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   phrasing: () => (/* binding */ phrasing)\n/* harmony export */ });\n/* harmony import */ var hast_util_embedded__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! hast-util-embedded */ \"(ssr)/../../node_modules/hast-util-embedded/lib/index.js\");\n/* harmony import */ var hast_util_has_property__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! hast-util-has-property */ \"(ssr)/../../node_modules/hast-util-format/node_modules/hast-util-has-property/lib/index.js\");\n/* harmony import */ var hast_util_is_body_ok_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! hast-util-is-body-ok-link */ \"(ssr)/../../node_modules/hast-util-format/node_modules/hast-util-is-body-ok-link/lib/index.js\");\n/* harmony import */ var hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! hast-util-is-element */ \"(ssr)/../../node_modules/hast-util-format/node_modules/hast-util-is-element/lib/index.js\");\n/**\n * @typedef {import('hast').Nodes} Nodes\n */\n\n\n\n\n\n\nconst basic = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)([\n  'a',\n  'abbr',\n  // `area` is in fact only phrasing if it is inside a `map` element.\n  // However, since `area`s are required to be inside a `map` element, and it’s\n  // a rather involved check, it’s ignored here for now.\n  'area',\n  'b',\n  'bdi',\n  'bdo',\n  'br',\n  'button',\n  'cite',\n  'code',\n  'data',\n  'datalist',\n  'del',\n  'dfn',\n  'em',\n  'i',\n  'input',\n  'ins',\n  'kbd',\n  'keygen',\n  'label',\n  'map',\n  'mark',\n  'meter',\n  'noscript',\n  'output',\n  'progress',\n  'q',\n  'ruby',\n  's',\n  'samp',\n  'script',\n  'select',\n  'small',\n  'span',\n  'strong',\n  'sub',\n  'sup',\n  'template',\n  'textarea',\n  'time',\n  'u',\n  'var',\n  'wbr'\n])\n\nconst meta = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)('meta')\n\n/**\n * Check if the given value is *phrasing* content.\n *\n * @param {Nodes} value\n *   Node to check.\n * @returns {boolean}\n *   Whether `value` is phrasing content.\n */\nfunction phrasing(value) {\n  return Boolean(\n    value.type === 'text' ||\n      basic(value) ||\n      (0,hast_util_embedded__WEBPACK_IMPORTED_MODULE_1__.embedded)(value) ||\n      (0,hast_util_is_body_ok_link__WEBPACK_IMPORTED_MODULE_2__.isBodyOkLink)(value) ||\n      (meta(value) && (0,hast_util_has_property__WEBPACK_IMPORTED_MODULE_3__.hasProperty)(value, 'itemProp'))\n  )\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/hast-util-format/node_modules/hast-util-phrasing/lib/index.js\n");

/***/ })

};
;