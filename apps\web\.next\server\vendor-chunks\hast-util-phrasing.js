"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/hast-util-phrasing";
exports.ids = ["vendor-chunks/hast-util-phrasing"];
exports.modules = {

/***/ "(ssr)/../../node_modules/hast-util-phrasing/lib/index.js":
/*!**********************************************************!*\
  !*** ../../node_modules/hast-util-phrasing/lib/index.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   phrasing: () => (/* binding */ phrasing)\n/* harmony export */ });\n/* harmony import */ var hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! hast-util-is-element */ \"(ssr)/../../node_modules/hast-util-is-element/index.js\");\n/* harmony import */ var hast_util_has_property__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! hast-util-has-property */ \"(ssr)/../../node_modules/hast-util-has-property/lib/index.js\");\n/* harmony import */ var hast_util_embedded__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! hast-util-embedded */ \"(ssr)/../../node_modules/hast-util-phrasing/node_modules/hast-util-embedded/lib/index.js\");\n/* harmony import */ var hast_util_is_body_ok_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! hast-util-is-body-ok-link */ \"(ssr)/../../node_modules/hast-util-is-body-ok-link/index.js\");\n/**\n * @typedef {import('hast').Root} Root\n * @typedef {import('hast').Content} Content\n */\n\n/**\n * @typedef {Root | Content} Node\n */\n\n\n\n\n\n\nconst basic = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)([\n  'a',\n  'abbr',\n  // `area` is in fact only phrasing if it is inside a `map` element.\n  // However, since `area`s are required to be inside a `map` element, and it’s\n  // a rather involved check, it’s ignored here for now.\n  'area',\n  'b',\n  'bdi',\n  'bdo',\n  'br',\n  'button',\n  'cite',\n  'code',\n  'data',\n  'datalist',\n  'del',\n  'dfn',\n  'em',\n  'i',\n  'input',\n  'ins',\n  'kbd',\n  'keygen',\n  'label',\n  'map',\n  'mark',\n  'meter',\n  'noscript',\n  'output',\n  'progress',\n  'q',\n  'ruby',\n  's',\n  'samp',\n  'script',\n  'select',\n  'small',\n  'span',\n  'strong',\n  'sub',\n  'sup',\n  'template',\n  'textarea',\n  'time',\n  'u',\n  'var',\n  'wbr'\n])\n\nconst meta = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)('meta')\n\n/**\n * Check if the given value is *phrasing* content.\n *\n * @param {unknown} value\n *   Thing to check, typically `Node`.\n * @returns {boolean}\n *   Whether `value` is phrasing content.\n */\nfunction phrasing(value) {\n  return Boolean(\n    node(value) &&\n      (value.type === 'text' ||\n        basic(value) ||\n        (0,hast_util_embedded__WEBPACK_IMPORTED_MODULE_1__.embedded)(value) ||\n        (0,hast_util_is_body_ok_link__WEBPACK_IMPORTED_MODULE_2__.isBodyOkLink)(value) ||\n        (meta(value) && (0,hast_util_has_property__WEBPACK_IMPORTED_MODULE_3__.hasProperty)(value, 'itemProp')))\n  )\n}\n\n/**\n * @param {unknown} value\n * @returns {value is Node}\n */\nfunction node(value) {\n  // @ts-expect-error: looks like an object.\n  return value && typeof value === 'object' && 'type' in value\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/hast-util-phrasing/lib/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/hast-util-phrasing/node_modules/hast-util-embedded/lib/index.js":
/*!******************************************************************************************!*\
  !*** ../../node_modules/hast-util-phrasing/node_modules/hast-util-embedded/lib/index.js ***!
  \******************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   embedded: () => (/* binding */ embedded)\n/* harmony export */ });\n/* harmony import */ var hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! hast-util-is-element */ \"(ssr)/../../node_modules/hast-util-is-element/index.js\");\n/**\n * @typedef {import('hast').Element} Element\n */\n\n\n\n/**\n * Check if a node is a *embedded content*.\n *\n * @type {import('hast-util-is-element').AssertPredicate<Element & {tagName: 'audio' | 'canvas' | 'embed' | 'iframe' | 'img' | 'math' | 'object' | 'picture' | 'svg' | 'video'}>}\n * @param value\n *   Thing to check (typically `Node`).\n * @returns\n *   Whether `value` is an element considered embedded content.\n *\n *   The elements `audio`, `canvas`, `embed`, `iframe`, `img`, `math`,\n *   `object`, `picture`, `svg`, and `video` are embedded content.\n */\n// @ts-expect-error Sure, the assertion matches.\nconst embedded = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)([\n  'audio',\n  'canvas',\n  'embed',\n  'iframe',\n  'img',\n  'math',\n  'object',\n  'picture',\n  'svg',\n  'video'\n])\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2hhc3QtdXRpbC1waHJhc2luZy9ub2RlX21vZHVsZXMvaGFzdC11dGlsLWVtYmVkZGVkL2xpYi9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ0EsYUFBYSx3QkFBd0I7QUFDckM7O0FBRW1EOztBQUVuRDtBQUNBO0FBQ0E7QUFDQSxVQUFVLDBEQUEwRCwyR0FBMkc7QUFDL0s7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ08saUJBQWlCLG9FQUFjO0FBQ3RDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9Ab3BlbmNhbnZhcy93ZWIvLi4vLi4vbm9kZV9tb2R1bGVzL2hhc3QtdXRpbC1waHJhc2luZy9ub2RlX21vZHVsZXMvaGFzdC11dGlsLWVtYmVkZGVkL2xpYi9pbmRleC5qcz82OWFjIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnaGFzdCcpLkVsZW1lbnR9IEVsZW1lbnRcbiAqL1xuXG5pbXBvcnQge2NvbnZlcnRFbGVtZW50fSBmcm9tICdoYXN0LXV0aWwtaXMtZWxlbWVudCdcblxuLyoqXG4gKiBDaGVjayBpZiBhIG5vZGUgaXMgYSAqZW1iZWRkZWQgY29udGVudCouXG4gKlxuICogQHR5cGUge2ltcG9ydCgnaGFzdC11dGlsLWlzLWVsZW1lbnQnKS5Bc3NlcnRQcmVkaWNhdGU8RWxlbWVudCAmIHt0YWdOYW1lOiAnYXVkaW8nIHwgJ2NhbnZhcycgfCAnZW1iZWQnIHwgJ2lmcmFtZScgfCAnaW1nJyB8ICdtYXRoJyB8ICdvYmplY3QnIHwgJ3BpY3R1cmUnIHwgJ3N2ZycgfCAndmlkZW8nfT59XG4gKiBAcGFyYW0gdmFsdWVcbiAqICAgVGhpbmcgdG8gY2hlY2sgKHR5cGljYWxseSBgTm9kZWApLlxuICogQHJldHVybnNcbiAqICAgV2hldGhlciBgdmFsdWVgIGlzIGFuIGVsZW1lbnQgY29uc2lkZXJlZCBlbWJlZGRlZCBjb250ZW50LlxuICpcbiAqICAgVGhlIGVsZW1lbnRzIGBhdWRpb2AsIGBjYW52YXNgLCBgZW1iZWRgLCBgaWZyYW1lYCwgYGltZ2AsIGBtYXRoYCxcbiAqICAgYG9iamVjdGAsIGBwaWN0dXJlYCwgYHN2Z2AsIGFuZCBgdmlkZW9gIGFyZSBlbWJlZGRlZCBjb250ZW50LlxuICovXG4vLyBAdHMtZXhwZWN0LWVycm9yIFN1cmUsIHRoZSBhc3NlcnRpb24gbWF0Y2hlcy5cbmV4cG9ydCBjb25zdCBlbWJlZGRlZCA9IGNvbnZlcnRFbGVtZW50KFtcbiAgJ2F1ZGlvJyxcbiAgJ2NhbnZhcycsXG4gICdlbWJlZCcsXG4gICdpZnJhbWUnLFxuICAnaW1nJyxcbiAgJ21hdGgnLFxuICAnb2JqZWN0JyxcbiAgJ3BpY3R1cmUnLFxuICAnc3ZnJyxcbiAgJ3ZpZGVvJ1xuXSlcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/hast-util-phrasing/node_modules/hast-util-embedded/lib/index.js\n");

/***/ })

};
;