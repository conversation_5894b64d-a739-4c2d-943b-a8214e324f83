/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_blocknote_core_node_modules_unified_index_js"],{

/***/ "(app-pages-browser)/../../node_modules/is-buffer/index.js":
/*!*********************************************!*\
  !*** ../../node_modules/is-buffer/index.js ***!
  \*********************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("/*!\n * Determine if an object is a Buffer\n *\n * <AUTHOR> Aboukhadijeh <https://feross.org>\n * @license  MIT\n */\n\nmodule.exports = function isBuffer (obj) {\n  return obj != null && obj.constructor != null &&\n    typeof obj.constructor.isBuffer === 'function' && obj.constructor.isBuffer(obj)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvaXMtYnVmZmVyL2luZGV4LmpzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vbm9kZV9tb2R1bGVzL2lzLWJ1ZmZlci9pbmRleC5qcz9mNDI1Il0sInNvdXJjZXNDb250ZW50IjpbIi8qIVxuICogRGV0ZXJtaW5lIGlmIGFuIG9iamVjdCBpcyBhIEJ1ZmZlclxuICpcbiAqIEBhdXRob3IgICBGZXJvc3MgQWJvdWtoYWRpamVoIDxodHRwczovL2Zlcm9zcy5vcmc+XG4gKiBAbGljZW5zZSAgTUlUXG4gKi9cblxubW9kdWxlLmV4cG9ydHMgPSBmdW5jdGlvbiBpc0J1ZmZlciAob2JqKSB7XG4gIHJldHVybiBvYmogIT0gbnVsbCAmJiBvYmouY29uc3RydWN0b3IgIT0gbnVsbCAmJlxuICAgIHR5cGVvZiBvYmouY29uc3RydWN0b3IuaXNCdWZmZXIgPT09ICdmdW5jdGlvbicgJiYgb2JqLmNvbnN0cnVjdG9yLmlzQnVmZmVyKG9iailcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/is-buffer/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/@blocknote/core/node_modules/unified/index.js":
/*!************************************************************************!*\
  !*** ../../node_modules/@blocknote/core/node_modules/unified/index.js ***!
  \************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   unified: function() { return /* reexport safe */ _lib_index_js__WEBPACK_IMPORTED_MODULE_0__.unified; }\n/* harmony export */ });\n/* harmony import */ var _lib_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./lib/index.js */ \"(app-pages-browser)/../../node_modules/@blocknote/core/node_modules/unified/lib/index.js\");\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvQGJsb2Nrbm90ZS9jb3JlL25vZGVfbW9kdWxlcy91bmlmaWVkL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQXNDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9ub2RlX21vZHVsZXMvQGJsb2Nrbm90ZS9jb3JlL25vZGVfbW9kdWxlcy91bmlmaWVkL2luZGV4LmpzP2FjYmUiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHt1bmlmaWVkfSBmcm9tICcuL2xpYi9pbmRleC5qcydcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/@blocknote/core/node_modules/unified/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/@blocknote/core/node_modules/unified/lib/index.js":
/*!****************************************************************************!*\
  !*** ../../node_modules/@blocknote/core/node_modules/unified/lib/index.js ***!
  \****************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   unified: function() { return /* binding */ unified; }\n/* harmony export */ });\n/* harmony import */ var bail__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! bail */ \"(app-pages-browser)/../../node_modules/bail/index.js\");\n/* harmony import */ var is_buffer__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! is-buffer */ \"(app-pages-browser)/../../node_modules/is-buffer/index.js\");\n/* harmony import */ var extend__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! extend */ \"(app-pages-browser)/../../node_modules/extend/index.js\");\n/* harmony import */ var is_plain_obj__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! is-plain-obj */ \"(app-pages-browser)/../../node_modules/is-plain-obj/index.js\");\n/* harmony import */ var trough__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! trough */ \"(app-pages-browser)/../../node_modules/trough/lib/index.js\");\n/* harmony import */ var vfile__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! vfile */ \"(app-pages-browser)/../../node_modules/@blocknote/core/node_modules/vfile/lib/index.js\");\n/**\n * @typedef {import('unist').Node} Node\n * @typedef {import('vfile').VFileCompatible} VFileCompatible\n * @typedef {import('vfile').VFileValue} VFileValue\n * @typedef {import('..').Processor} Processor\n * @typedef {import('..').Plugin} Plugin\n * @typedef {import('..').Preset} Preset\n * @typedef {import('..').Pluggable} Pluggable\n * @typedef {import('..').PluggableList} PluggableList\n * @typedef {import('..').Transformer} Transformer\n * @typedef {import('..').Parser} Parser\n * @typedef {import('..').Compiler} Compiler\n * @typedef {import('..').RunCallback} RunCallback\n * @typedef {import('..').ProcessCallback} ProcessCallback\n *\n * @typedef Context\n * @property {Node} tree\n * @property {VFile} file\n */\n\n\n\n\n\n\n\n\n// Expose a frozen processor.\nconst unified = base().freeze()\n\nconst own = {}.hasOwnProperty\n\n// Function to create the first processor.\n/**\n * @returns {Processor}\n */\nfunction base() {\n  const transformers = (0,trough__WEBPACK_IMPORTED_MODULE_3__.trough)()\n  /** @type {Processor['attachers']} */\n  const attachers = []\n  /** @type {Record<string, unknown>} */\n  let namespace = {}\n  /** @type {boolean|undefined} */\n  let frozen\n  let freezeIndex = -1\n\n  // Data management.\n  // @ts-expect-error: overloads are handled.\n  processor.data = data\n  processor.Parser = undefined\n  processor.Compiler = undefined\n\n  // Lock.\n  processor.freeze = freeze\n\n  // Plugins.\n  processor.attachers = attachers\n  // @ts-expect-error: overloads are handled.\n  processor.use = use\n\n  // API.\n  processor.parse = parse\n  processor.stringify = stringify\n  // @ts-expect-error: overloads are handled.\n  processor.run = run\n  processor.runSync = runSync\n  // @ts-expect-error: overloads are handled.\n  processor.process = process\n  processor.processSync = processSync\n\n  // Expose.\n  return processor\n\n  // Create a new processor based on the processor in the current scope.\n  /** @type {Processor} */\n  function processor() {\n    const destination = base()\n    let index = -1\n\n    while (++index < attachers.length) {\n      destination.use(...attachers[index])\n    }\n\n    destination.data(extend__WEBPACK_IMPORTED_MODULE_1__(true, {}, namespace))\n\n    return destination\n  }\n\n  /**\n   * @param {string|Record<string, unknown>} [key]\n   * @param {unknown} [value]\n   * @returns {unknown}\n   */\n  function data(key, value) {\n    if (typeof key === 'string') {\n      // Set `key`.\n      if (arguments.length === 2) {\n        assertUnfrozen('data', frozen)\n        namespace[key] = value\n        return processor\n      }\n\n      // Get `key`.\n      return (own.call(namespace, key) && namespace[key]) || null\n    }\n\n    // Set space.\n    if (key) {\n      assertUnfrozen('data', frozen)\n      namespace = key\n      return processor\n    }\n\n    // Get space.\n    return namespace\n  }\n\n  /** @type {Processor['freeze']} */\n  function freeze() {\n    if (frozen) {\n      return processor\n    }\n\n    while (++freezeIndex < attachers.length) {\n      const [attacher, ...options] = attachers[freezeIndex]\n\n      if (options[0] === false) {\n        continue\n      }\n\n      if (options[0] === true) {\n        options[0] = undefined\n      }\n\n      /** @type {Transformer|void} */\n      const transformer = attacher.call(processor, ...options)\n\n      if (typeof transformer === 'function') {\n        transformers.use(transformer)\n      }\n    }\n\n    frozen = true\n    freezeIndex = Number.POSITIVE_INFINITY\n\n    return processor\n  }\n\n  /**\n   * @param {Pluggable|null|undefined} [value]\n   * @param {...unknown} options\n   * @returns {Processor}\n   */\n  function use(value, ...options) {\n    /** @type {Record<string, unknown>|undefined} */\n    let settings\n\n    assertUnfrozen('use', frozen)\n\n    if (value === null || value === undefined) {\n      // Empty.\n    } else if (typeof value === 'function') {\n      addPlugin(value, ...options)\n    } else if (typeof value === 'object') {\n      if (Array.isArray(value)) {\n        addList(value)\n      } else {\n        addPreset(value)\n      }\n    } else {\n      throw new TypeError('Expected usable value, not `' + value + '`')\n    }\n\n    if (settings) {\n      namespace.settings = Object.assign(namespace.settings || {}, settings)\n    }\n\n    return processor\n\n    /**\n     * @param {import('..').Pluggable<unknown[]>} value\n     * @returns {void}\n     */\n    function add(value) {\n      if (typeof value === 'function') {\n        addPlugin(value)\n      } else if (typeof value === 'object') {\n        if (Array.isArray(value)) {\n          const [plugin, ...options] = value\n          addPlugin(plugin, ...options)\n        } else {\n          addPreset(value)\n        }\n      } else {\n        throw new TypeError('Expected usable value, not `' + value + '`')\n      }\n    }\n\n    /**\n     * @param {Preset} result\n     * @returns {void}\n     */\n    function addPreset(result) {\n      addList(result.plugins)\n\n      if (result.settings) {\n        settings = Object.assign(settings || {}, result.settings)\n      }\n    }\n\n    /**\n     * @param {PluggableList|null|undefined} [plugins]\n     * @returns {void}\n     */\n    function addList(plugins) {\n      let index = -1\n\n      if (plugins === null || plugins === undefined) {\n        // Empty.\n      } else if (Array.isArray(plugins)) {\n        while (++index < plugins.length) {\n          const thing = plugins[index]\n          add(thing)\n        }\n      } else {\n        throw new TypeError('Expected a list of plugins, not `' + plugins + '`')\n      }\n    }\n\n    /**\n     * @param {Plugin} plugin\n     * @param {...unknown} [value]\n     * @returns {void}\n     */\n    function addPlugin(plugin, value) {\n      let index = -1\n      /** @type {Processor['attachers'][number]|undefined} */\n      let entry\n\n      while (++index < attachers.length) {\n        if (attachers[index][0] === plugin) {\n          entry = attachers[index]\n          break\n        }\n      }\n\n      if (entry) {\n        if ((0,is_plain_obj__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(entry[1]) && (0,is_plain_obj__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(value)) {\n          value = extend__WEBPACK_IMPORTED_MODULE_1__(true, entry[1], value)\n        }\n\n        entry[1] = value\n      } else {\n        // @ts-expect-error: fine.\n        attachers.push([...arguments])\n      }\n    }\n  }\n\n  /** @type {Processor['parse']} */\n  function parse(doc) {\n    processor.freeze()\n    const file = vfile(doc)\n    const Parser = processor.Parser\n    assertParser('parse', Parser)\n\n    if (newable(Parser, 'parse')) {\n      // @ts-expect-error: `newable` checks this.\n      return new Parser(String(file), file).parse()\n    }\n\n    // @ts-expect-error: `newable` checks this.\n    return Parser(String(file), file) // eslint-disable-line new-cap\n  }\n\n  /** @type {Processor['stringify']} */\n  function stringify(node, doc) {\n    processor.freeze()\n    const file = vfile(doc)\n    const Compiler = processor.Compiler\n    assertCompiler('stringify', Compiler)\n    assertNode(node)\n\n    if (newable(Compiler, 'compile')) {\n      // @ts-expect-error: `newable` checks this.\n      return new Compiler(node, file).compile()\n    }\n\n    // @ts-expect-error: `newable` checks this.\n    return Compiler(node, file) // eslint-disable-line new-cap\n  }\n\n  /**\n   * @param {Node} node\n   * @param {VFileCompatible|RunCallback} [doc]\n   * @param {RunCallback} [callback]\n   * @returns {Promise<Node>|void}\n   */\n  function run(node, doc, callback) {\n    assertNode(node)\n    processor.freeze()\n\n    if (!callback && typeof doc === 'function') {\n      callback = doc\n      doc = undefined\n    }\n\n    if (!callback) {\n      return new Promise(executor)\n    }\n\n    executor(null, callback)\n\n    /**\n     * @param {null|((node: Node) => void)} resolve\n     * @param {(error: Error) => void} reject\n     * @returns {void}\n     */\n    function executor(resolve, reject) {\n      // @ts-expect-error: `doc` can’t be a callback anymore, we checked.\n      transformers.run(node, vfile(doc), done)\n\n      /**\n       * @param {Error|null} error\n       * @param {Node} tree\n       * @param {VFile} file\n       * @returns {void}\n       */\n      function done(error, tree, file) {\n        tree = tree || node\n        if (error) {\n          reject(error)\n        } else if (resolve) {\n          resolve(tree)\n        } else {\n          // @ts-expect-error: `callback` is defined if `resolve` is not.\n          callback(null, tree, file)\n        }\n      }\n    }\n  }\n\n  /** @type {Processor['runSync']} */\n  function runSync(node, file) {\n    /** @type {Node|undefined} */\n    let result\n    /** @type {boolean|undefined} */\n    let complete\n\n    processor.run(node, file, done)\n\n    assertDone('runSync', 'run', complete)\n\n    // @ts-expect-error: we either bailed on an error or have a tree.\n    return result\n\n    /**\n     * @param {Error|null} [error]\n     * @param {Node} [tree]\n     * @returns {void}\n     */\n    function done(error, tree) {\n      ;(0,bail__WEBPACK_IMPORTED_MODULE_4__.bail)(error)\n      result = tree\n      complete = true\n    }\n  }\n\n  /**\n   * @param {VFileCompatible} doc\n   * @param {ProcessCallback} [callback]\n   * @returns {Promise<VFile>|undefined}\n   */\n  function process(doc, callback) {\n    processor.freeze()\n    assertParser('process', processor.Parser)\n    assertCompiler('process', processor.Compiler)\n\n    if (!callback) {\n      return new Promise(executor)\n    }\n\n    executor(null, callback)\n\n    /**\n     * @param {null|((file: VFile) => void)} resolve\n     * @param {(error?: Error|null|undefined) => void} reject\n     * @returns {void}\n     */\n    function executor(resolve, reject) {\n      const file = vfile(doc)\n\n      processor.run(processor.parse(file), file, (error, tree, file) => {\n        if (error || !tree || !file) {\n          done(error)\n        } else {\n          /** @type {unknown} */\n          const result = processor.stringify(tree, file)\n\n          if (result === undefined || result === null) {\n            // Empty.\n          } else if (looksLikeAVFileValue(result)) {\n            file.value = result\n          } else {\n            file.result = result\n          }\n\n          done(error, file)\n        }\n      })\n\n      /**\n       * @param {Error|null|undefined} [error]\n       * @param {VFile|undefined} [file]\n       * @returns {void}\n       */\n      function done(error, file) {\n        if (error || !file) {\n          reject(error)\n        } else if (resolve) {\n          resolve(file)\n        } else {\n          // @ts-expect-error: `callback` is defined if `resolve` is not.\n          callback(null, file)\n        }\n      }\n    }\n  }\n\n  /** @type {Processor['processSync']} */\n  function processSync(doc) {\n    /** @type {boolean|undefined} */\n    let complete\n\n    processor.freeze()\n    assertParser('processSync', processor.Parser)\n    assertCompiler('processSync', processor.Compiler)\n\n    const file = vfile(doc)\n\n    processor.process(file, done)\n\n    assertDone('processSync', 'process', complete)\n\n    return file\n\n    /**\n     * @param {Error|null|undefined} [error]\n     * @returns {void}\n     */\n    function done(error) {\n      complete = true\n      ;(0,bail__WEBPACK_IMPORTED_MODULE_4__.bail)(error)\n    }\n  }\n}\n\n/**\n * Check if `value` is a constructor.\n *\n * @param {unknown} value\n * @param {string} name\n * @returns {boolean}\n */\nfunction newable(value, name) {\n  return (\n    typeof value === 'function' &&\n    // Prototypes do exist.\n    // type-coverage:ignore-next-line\n    value.prototype &&\n    // A function with keys in its prototype is probably a constructor.\n    // Classes’ prototype methods are not enumerable, so we check if some value\n    // exists in the prototype.\n    // type-coverage:ignore-next-line\n    (keys(value.prototype) || name in value.prototype)\n  )\n}\n\n/**\n * Check if `value` is an object with keys.\n *\n * @param {Record<string, unknown>} value\n * @returns {boolean}\n */\nfunction keys(value) {\n  /** @type {string} */\n  let key\n\n  for (key in value) {\n    if (own.call(value, key)) {\n      return true\n    }\n  }\n\n  return false\n}\n\n/**\n * Assert a parser is available.\n *\n * @param {string} name\n * @param {unknown} value\n * @returns {asserts value is Parser}\n */\nfunction assertParser(name, value) {\n  if (typeof value !== 'function') {\n    throw new TypeError('Cannot `' + name + '` without `Parser`')\n  }\n}\n\n/**\n * Assert a compiler is available.\n *\n * @param {string} name\n * @param {unknown} value\n * @returns {asserts value is Compiler}\n */\nfunction assertCompiler(name, value) {\n  if (typeof value !== 'function') {\n    throw new TypeError('Cannot `' + name + '` without `Compiler`')\n  }\n}\n\n/**\n * Assert the processor is not frozen.\n *\n * @param {string} name\n * @param {unknown} frozen\n * @returns {asserts frozen is false}\n */\nfunction assertUnfrozen(name, frozen) {\n  if (frozen) {\n    throw new Error(\n      'Cannot call `' +\n        name +\n        '` on a frozen processor.\\nCreate a new processor first, by calling it: use `processor()` instead of `processor`.'\n    )\n  }\n}\n\n/**\n * Assert `node` is a unist node.\n *\n * @param {unknown} node\n * @returns {asserts node is Node}\n */\nfunction assertNode(node) {\n  // `isPlainObj` unfortunately uses `any` instead of `unknown`.\n  // type-coverage:ignore-next-line\n  if (!(0,is_plain_obj__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(node) || typeof node.type !== 'string') {\n    throw new TypeError('Expected node, got `' + node + '`')\n    // Fine.\n  }\n}\n\n/**\n * Assert that `complete` is `true`.\n *\n * @param {string} name\n * @param {string} asyncName\n * @param {unknown} complete\n * @returns {asserts complete is true}\n */\nfunction assertDone(name, asyncName, complete) {\n  if (!complete) {\n    throw new Error(\n      '`' + name + '` finished async. Use `' + asyncName + '` instead'\n    )\n  }\n}\n\n/**\n * @param {VFileCompatible} [value]\n * @returns {VFile}\n */\nfunction vfile(value) {\n  return looksLikeAVFile(value) ? value : new vfile__WEBPACK_IMPORTED_MODULE_5__.VFile(value)\n}\n\n/**\n * @param {VFileCompatible} [value]\n * @returns {value is VFile}\n */\nfunction looksLikeAVFile(value) {\n  return Boolean(\n    value &&\n      typeof value === 'object' &&\n      'message' in value &&\n      'messages' in value\n  )\n}\n\n/**\n * @param {unknown} [value]\n * @returns {value is VFileValue}\n */\nfunction looksLikeAVFileValue(value) {\n  return typeof value === 'string' || is_buffer__WEBPACK_IMPORTED_MODULE_0__(value)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/@blocknote/core/node_modules/unified/lib/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/@blocknote/core/node_modules/vfile-message/lib/index.js":
/*!**********************************************************************************!*\
  !*** ../../node_modules/@blocknote/core/node_modules/vfile-message/lib/index.js ***!
  \**********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   VFileMessage: function() { return /* binding */ VFileMessage; }\n/* harmony export */ });\n/* harmony import */ var unist_util_stringify_position__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! unist-util-stringify-position */ \"(app-pages-browser)/../../node_modules/unist-util-stringify-position/lib/index.js\");\n/**\n * @typedef {import('unist').Node} Node\n * @typedef {import('unist').Position} Position\n * @typedef {import('unist').Point} Point\n * @typedef {object & {type: string, position?: Position | undefined}} NodeLike\n */\n\n\n\n/**\n * Message.\n */\nclass VFileMessage extends Error {\n  /**\n   * Create a message for `reason` at `place` from `origin`.\n   *\n   * When an error is passed in as `reason`, the `stack` is copied.\n   *\n   * @param {string | Error | VFileMessage} reason\n   *   Reason for message, uses the stack and message of the error if given.\n   *\n   *   > 👉 **Note**: you should use markdown.\n   * @param {Node | NodeLike | Position | Point | null | undefined} [place]\n   *   Place in file where the message occurred.\n   * @param {string | null | undefined} [origin]\n   *   Place in code where the message originates (example:\n   *   `'my-package:my-rule'` or `'my-rule'`).\n   * @returns\n   *   Instance of `VFileMessage`.\n   */\n  // To do: next major: expose `undefined` everywhere instead of `null`.\n  constructor(reason, place, origin) {\n    /** @type {[string | null, string | null]} */\n    const parts = [null, null]\n    /** @type {Position} */\n    let position = {\n      // @ts-expect-error: we always follows the structure of `position`.\n      start: {line: null, column: null},\n      // @ts-expect-error: \"\n      end: {line: null, column: null}\n    }\n\n    super()\n\n    if (typeof place === 'string') {\n      origin = place\n      place = undefined\n    }\n\n    if (typeof origin === 'string') {\n      const index = origin.indexOf(':')\n\n      if (index === -1) {\n        parts[1] = origin\n      } else {\n        parts[0] = origin.slice(0, index)\n        parts[1] = origin.slice(index + 1)\n      }\n    }\n\n    if (place) {\n      // Node.\n      if ('type' in place || 'position' in place) {\n        if (place.position) {\n          // To do: next major: deep clone.\n          // @ts-expect-error: looks like a position.\n          position = place.position\n        }\n      }\n      // Position.\n      else if ('start' in place || 'end' in place) {\n        // @ts-expect-error: looks like a position.\n        // To do: next major: deep clone.\n        position = place\n      }\n      // Point.\n      else if ('line' in place || 'column' in place) {\n        // To do: next major: deep clone.\n        position.start = place\n      }\n    }\n\n    // Fields from `Error`.\n    /**\n     * Serialized positional info of error.\n     *\n     * On normal errors, this would be something like `ParseError`, buit in\n     * `VFile` messages we use this space to show where an error happened.\n     */\n    this.name = (0,unist_util_stringify_position__WEBPACK_IMPORTED_MODULE_0__.stringifyPosition)(place) || '1:1'\n\n    /**\n     * Reason for message.\n     *\n     * @type {string}\n     */\n    this.message = typeof reason === 'object' ? reason.message : reason\n\n    /**\n     * Stack of message.\n     *\n     * This is used by normal errors to show where something happened in\n     * programming code, irrelevant for `VFile` messages,\n     *\n     * @type {string}\n     */\n    this.stack = ''\n\n    if (typeof reason === 'object' && reason.stack) {\n      this.stack = reason.stack\n    }\n\n    /**\n     * Reason for message.\n     *\n     * > 👉 **Note**: you should use markdown.\n     *\n     * @type {string}\n     */\n    this.reason = this.message\n\n    /* eslint-disable no-unused-expressions */\n    /**\n     * State of problem.\n     *\n     * * `true` — marks associated file as no longer processable (error)\n     * * `false` — necessitates a (potential) change (warning)\n     * * `null | undefined` — for things that might not need changing (info)\n     *\n     * @type {boolean | null | undefined}\n     */\n    this.fatal\n\n    /**\n     * Starting line of error.\n     *\n     * @type {number | null}\n     */\n    this.line = position.start.line\n\n    /**\n     * Starting column of error.\n     *\n     * @type {number | null}\n     */\n    this.column = position.start.column\n\n    /**\n     * Full unist position.\n     *\n     * @type {Position | null}\n     */\n    this.position = position\n\n    /**\n     * Namespace of message (example: `'my-package'`).\n     *\n     * @type {string | null}\n     */\n    this.source = parts[0]\n\n    /**\n     * Category of message (example: `'my-rule'`).\n     *\n     * @type {string | null}\n     */\n    this.ruleId = parts[1]\n\n    /**\n     * Path of a file (used throughout the `VFile` ecosystem).\n     *\n     * @type {string | null}\n     */\n    this.file\n\n    // The following fields are “well known”.\n    // Not standard.\n    // Feel free to add other non-standard fields to your messages.\n\n    /**\n     * Specify the source value that’s being reported, which is deemed\n     * incorrect.\n     *\n     * @type {string | null}\n     */\n    this.actual\n\n    /**\n     * Suggest acceptable values that can be used instead of `actual`.\n     *\n     * @type {Array<string> | null}\n     */\n    this.expected\n\n    /**\n     * Link to docs for the message.\n     *\n     * > 👉 **Note**: this must be an absolute URL that can be passed as `x`\n     * > to `new URL(x)`.\n     *\n     * @type {string | null}\n     */\n    this.url\n\n    /**\n     * Long form description of the message (you should use markdown).\n     *\n     * @type {string | null}\n     */\n    this.note\n    /* eslint-enable no-unused-expressions */\n  }\n}\n\nVFileMessage.prototype.file = ''\nVFileMessage.prototype.name = ''\nVFileMessage.prototype.reason = ''\nVFileMessage.prototype.message = ''\nVFileMessage.prototype.stack = ''\nVFileMessage.prototype.fatal = null\nVFileMessage.prototype.column = null\nVFileMessage.prototype.line = null\nVFileMessage.prototype.source = null\nVFileMessage.prototype.ruleId = null\nVFileMessage.prototype.position = null\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/@blocknote/core/node_modules/vfile-message/lib/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/@blocknote/core/node_modules/vfile/lib/index.js":
/*!**************************************************************************!*\
  !*** ../../node_modules/@blocknote/core/node_modules/vfile/lib/index.js ***!
  \**************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   VFile: function() { return /* binding */ VFile; }\n/* harmony export */ });\n/* harmony import */ var is_buffer__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! is-buffer */ \"(app-pages-browser)/../../node_modules/is-buffer/index.js\");\n/* harmony import */ var vfile_message__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! vfile-message */ \"(app-pages-browser)/../../node_modules/@blocknote/core/node_modules/vfile-message/lib/index.js\");\n/* harmony import */ var _minpath_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./minpath.js */ \"(app-pages-browser)/../../node_modules/@blocknote/core/node_modules/vfile/lib/minpath.browser.js\");\n/* harmony import */ var _minproc_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./minproc.js */ \"(app-pages-browser)/../../node_modules/@blocknote/core/node_modules/vfile/lib/minproc.browser.js\");\n/* harmony import */ var _minurl_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./minurl.js */ \"(app-pages-browser)/../../node_modules/@blocknote/core/node_modules/vfile/lib/minurl.shared.js\");\n/* harmony import */ var _minurl_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./minurl.js */ \"(app-pages-browser)/../../node_modules/@blocknote/core/node_modules/vfile/lib/minurl.browser.js\");\n/**\n * @typedef {import('unist').Node} Node\n * @typedef {import('unist').Position} Position\n * @typedef {import('unist').Point} Point\n * @typedef {import('./minurl.shared.js').URL} URL\n * @typedef {import('../index.js').Data} Data\n * @typedef {import('../index.js').Value} Value\n */\n\n/**\n * @typedef {Record<string, unknown> & {type: string, position?: Position | undefined}} NodeLike\n *\n * @typedef {'ascii' | 'utf8' | 'utf-8' | 'utf16le' | 'ucs2' | 'ucs-2' | 'base64' | 'base64url' | 'latin1' | 'binary' | 'hex'} BufferEncoding\n *   Encodings supported by the buffer class.\n *\n *   This is a copy of the types from Node, copied to prevent Node globals from\n *   being needed.\n *   Copied from: <https://github.com/DefinitelyTyped/DefinitelyTyped/blob/90a4ec8/types/node/buffer.d.ts#L170>\n *\n * @typedef {Options | URL | Value | VFile} Compatible\n *   Things that can be passed to the constructor.\n *\n * @typedef VFileCoreOptions\n *   Set multiple values.\n * @property {Value | null | undefined} [value]\n *   Set `value`.\n * @property {string | null | undefined} [cwd]\n *   Set `cwd`.\n * @property {Array<string> | null | undefined} [history]\n *   Set `history`.\n * @property {URL | string | null | undefined} [path]\n *   Set `path`.\n * @property {string | null | undefined} [basename]\n *   Set `basename`.\n * @property {string | null | undefined} [stem]\n *   Set `stem`.\n * @property {string | null | undefined} [extname]\n *   Set `extname`.\n * @property {string | null | undefined} [dirname]\n *   Set `dirname`.\n * @property {Data | null | undefined} [data]\n *   Set `data`.\n *\n * @typedef Map\n *   Raw source map.\n *\n *   See:\n *   <https://github.com/mozilla/source-map/blob/58819f0/source-map.d.ts#L15-L23>.\n * @property {number} version\n *   Which version of the source map spec this map is following.\n * @property {Array<string>} sources\n *   An array of URLs to the original source files.\n * @property {Array<string>} names\n *   An array of identifiers which can be referenced by individual mappings.\n * @property {string | undefined} [sourceRoot]\n *   The URL root from which all sources are relative.\n * @property {Array<string> | undefined} [sourcesContent]\n *   An array of contents of the original source files.\n * @property {string} mappings\n *   A string of base64 VLQs which contain the actual mappings.\n * @property {string} file\n *   The generated file this source map is associated with.\n *\n * @typedef {{[key: string]: unknown} & VFileCoreOptions} Options\n *   Configuration.\n *\n *   A bunch of keys that will be shallow copied over to the new file.\n *\n * @typedef {Record<string, unknown>} ReporterSettings\n *   Configuration for reporters.\n */\n\n/**\n * @template {ReporterSettings} Settings\n *   Options type.\n * @callback Reporter\n *   Type for a reporter.\n * @param {Array<VFile>} files\n *   Files to report.\n * @param {Settings} options\n *   Configuration.\n * @returns {string}\n *   Report.\n */\n\n\n\n\n\n\n\n/**\n * Order of setting (least specific to most), we need this because otherwise\n * `{stem: 'a', path: '~/b.js'}` would throw, as a path is needed before a\n * stem can be set.\n *\n * @type {Array<'basename' | 'dirname' | 'extname' | 'history' | 'path' | 'stem'>}\n */\nconst order = ['history', 'path', 'basename', 'stem', 'extname', 'dirname']\n\nclass VFile {\n  /**\n   * Create a new virtual file.\n   *\n   * `options` is treated as:\n   *\n   * *   `string` or `Buffer` — `{value: options}`\n   * *   `URL` — `{path: options}`\n   * *   `VFile` — shallow copies its data over to the new file\n   * *   `object` — all fields are shallow copied over to the new file\n   *\n   * Path related fields are set in the following order (least specific to\n   * most specific): `history`, `path`, `basename`, `stem`, `extname`,\n   * `dirname`.\n   *\n   * You cannot set `dirname` or `extname` without setting either `history`,\n   * `path`, `basename`, or `stem` too.\n   *\n   * @param {Compatible | null | undefined} [value]\n   *   File value.\n   * @returns\n   *   New instance.\n   */\n  constructor(value) {\n    /** @type {Options | VFile} */\n    let options\n\n    if (!value) {\n      options = {}\n    } else if (typeof value === 'string' || buffer(value)) {\n      options = {value}\n    } else if ((0,_minurl_js__WEBPACK_IMPORTED_MODULE_1__.isUrl)(value)) {\n      options = {path: value}\n    } else {\n      options = value\n    }\n\n    /**\n     * Place to store custom information (default: `{}`).\n     *\n     * It’s OK to store custom data directly on the file but moving it to\n     * `data` is recommended.\n     *\n     * @type {Data}\n     */\n    this.data = {}\n\n    /**\n     * List of messages associated with the file.\n     *\n     * @type {Array<VFileMessage>}\n     */\n    this.messages = []\n\n    /**\n     * List of filepaths the file moved between.\n     *\n     * The first is the original path and the last is the current path.\n     *\n     * @type {Array<string>}\n     */\n    this.history = []\n\n    /**\n     * Base of `path` (default: `process.cwd()` or `'/'` in browsers).\n     *\n     * @type {string}\n     */\n    this.cwd = _minproc_js__WEBPACK_IMPORTED_MODULE_2__.proc.cwd()\n\n    /* eslint-disable no-unused-expressions */\n    /**\n     * Raw value.\n     *\n     * @type {Value}\n     */\n    this.value\n\n    // The below are non-standard, they are “well-known”.\n    // As in, used in several tools.\n\n    /**\n     * Whether a file was saved to disk.\n     *\n     * This is used by vfile reporters.\n     *\n     * @type {boolean}\n     */\n    this.stored\n\n    /**\n     * Custom, non-string, compiled, representation.\n     *\n     * This is used by unified to store non-string results.\n     * One example is when turning markdown into React nodes.\n     *\n     * @type {unknown}\n     */\n    this.result\n\n    /**\n     * Source map.\n     *\n     * This type is equivalent to the `RawSourceMap` type from the `source-map`\n     * module.\n     *\n     * @type {Map | null | undefined}\n     */\n    this.map\n    /* eslint-enable no-unused-expressions */\n\n    // Set path related properties in the correct order.\n    let index = -1\n\n    while (++index < order.length) {\n      const prop = order[index]\n\n      // Note: we specifically use `in` instead of `hasOwnProperty` to accept\n      // `vfile`s too.\n      if (\n        prop in options &&\n        options[prop] !== undefined &&\n        options[prop] !== null\n      ) {\n        // @ts-expect-error: TS doesn’t understand basic reality.\n        this[prop] = prop === 'history' ? [...options[prop]] : options[prop]\n      }\n    }\n\n    /** @type {string} */\n    let prop\n\n    // Set non-path related properties.\n    for (prop in options) {\n      // @ts-expect-error: fine to set other things.\n      if (!order.includes(prop)) {\n        // @ts-expect-error: fine to set other things.\n        this[prop] = options[prop]\n      }\n    }\n  }\n\n  /**\n   * Get the full path (example: `'~/index.min.js'`).\n   *\n   * @returns {string}\n   */\n  get path() {\n    return this.history[this.history.length - 1]\n  }\n\n  /**\n   * Set the full path (example: `'~/index.min.js'`).\n   *\n   * Cannot be nullified.\n   * You can set a file URL (a `URL` object with a `file:` protocol) which will\n   * be turned into a path with `url.fileURLToPath`.\n   *\n   * @param {string | URL} path\n   */\n  set path(path) {\n    if ((0,_minurl_js__WEBPACK_IMPORTED_MODULE_1__.isUrl)(path)) {\n      path = (0,_minurl_js__WEBPACK_IMPORTED_MODULE_3__.urlToPath)(path)\n    }\n\n    assertNonEmpty(path, 'path')\n\n    if (this.path !== path) {\n      this.history.push(path)\n    }\n  }\n\n  /**\n   * Get the parent path (example: `'~'`).\n   */\n  get dirname() {\n    return typeof this.path === 'string' ? _minpath_js__WEBPACK_IMPORTED_MODULE_4__.path.dirname(this.path) : undefined\n  }\n\n  /**\n   * Set the parent path (example: `'~'`).\n   *\n   * Cannot be set if there’s no `path` yet.\n   */\n  set dirname(dirname) {\n    assertPath(this.basename, 'dirname')\n    this.path = _minpath_js__WEBPACK_IMPORTED_MODULE_4__.path.join(dirname || '', this.basename)\n  }\n\n  /**\n   * Get the basename (including extname) (example: `'index.min.js'`).\n   */\n  get basename() {\n    return typeof this.path === 'string' ? _minpath_js__WEBPACK_IMPORTED_MODULE_4__.path.basename(this.path) : undefined\n  }\n\n  /**\n   * Set basename (including extname) (`'index.min.js'`).\n   *\n   * Cannot contain path separators (`'/'` on unix, macOS, and browsers, `'\\'`\n   * on windows).\n   * Cannot be nullified (use `file.path = file.dirname` instead).\n   */\n  set basename(basename) {\n    assertNonEmpty(basename, 'basename')\n    assertPart(basename, 'basename')\n    this.path = _minpath_js__WEBPACK_IMPORTED_MODULE_4__.path.join(this.dirname || '', basename)\n  }\n\n  /**\n   * Get the extname (including dot) (example: `'.js'`).\n   */\n  get extname() {\n    return typeof this.path === 'string' ? _minpath_js__WEBPACK_IMPORTED_MODULE_4__.path.extname(this.path) : undefined\n  }\n\n  /**\n   * Set the extname (including dot) (example: `'.js'`).\n   *\n   * Cannot contain path separators (`'/'` on unix, macOS, and browsers, `'\\'`\n   * on windows).\n   * Cannot be set if there’s no `path` yet.\n   */\n  set extname(extname) {\n    assertPart(extname, 'extname')\n    assertPath(this.dirname, 'extname')\n\n    if (extname) {\n      if (extname.charCodeAt(0) !== 46 /* `.` */) {\n        throw new Error('`extname` must start with `.`')\n      }\n\n      if (extname.includes('.', 1)) {\n        throw new Error('`extname` cannot contain multiple dots')\n      }\n    }\n\n    this.path = _minpath_js__WEBPACK_IMPORTED_MODULE_4__.path.join(this.dirname, this.stem + (extname || ''))\n  }\n\n  /**\n   * Get the stem (basename w/o extname) (example: `'index.min'`).\n   */\n  get stem() {\n    return typeof this.path === 'string'\n      ? _minpath_js__WEBPACK_IMPORTED_MODULE_4__.path.basename(this.path, this.extname)\n      : undefined\n  }\n\n  /**\n   * Set the stem (basename w/o extname) (example: `'index.min'`).\n   *\n   * Cannot contain path separators (`'/'` on unix, macOS, and browsers, `'\\'`\n   * on windows).\n   * Cannot be nullified (use `file.path = file.dirname` instead).\n   */\n  set stem(stem) {\n    assertNonEmpty(stem, 'stem')\n    assertPart(stem, 'stem')\n    this.path = _minpath_js__WEBPACK_IMPORTED_MODULE_4__.path.join(this.dirname || '', stem + (this.extname || ''))\n  }\n\n  /**\n   * Serialize the file.\n   *\n   * @param {BufferEncoding | null | undefined} [encoding='utf8']\n   *   Character encoding to understand `value` as when it’s a `Buffer`\n   *   (default: `'utf8'`).\n   * @returns {string}\n   *   Serialized file.\n   */\n  toString(encoding) {\n    return (this.value || '').toString(encoding || undefined)\n  }\n\n  /**\n   * Create a warning message associated with the file.\n   *\n   * Its `fatal` is set to `false` and `file` is set to the current file path.\n   * Its added to `file.messages`.\n   *\n   * @param {string | Error | VFileMessage} reason\n   *   Reason for message, uses the stack and message of the error if given.\n   * @param {Node | NodeLike | Position | Point | null | undefined} [place]\n   *   Place in file where the message occurred.\n   * @param {string | null | undefined} [origin]\n   *   Place in code where the message originates (example:\n   *   `'my-package:my-rule'` or `'my-rule'`).\n   * @returns {VFileMessage}\n   *   Message.\n   */\n  message(reason, place, origin) {\n    const message = new vfile_message__WEBPACK_IMPORTED_MODULE_5__.VFileMessage(reason, place, origin)\n\n    if (this.path) {\n      message.name = this.path + ':' + message.name\n      message.file = this.path\n    }\n\n    message.fatal = false\n\n    this.messages.push(message)\n\n    return message\n  }\n\n  /**\n   * Create an info message associated with the file.\n   *\n   * Its `fatal` is set to `null` and `file` is set to the current file path.\n   * Its added to `file.messages`.\n   *\n   * @param {string | Error | VFileMessage} reason\n   *   Reason for message, uses the stack and message of the error if given.\n   * @param {Node | NodeLike | Position | Point | null | undefined} [place]\n   *   Place in file where the message occurred.\n   * @param {string | null | undefined} [origin]\n   *   Place in code where the message originates (example:\n   *   `'my-package:my-rule'` or `'my-rule'`).\n   * @returns {VFileMessage}\n   *   Message.\n   */\n  info(reason, place, origin) {\n    const message = this.message(reason, place, origin)\n\n    message.fatal = null\n\n    return message\n  }\n\n  /**\n   * Create a fatal error associated with the file.\n   *\n   * Its `fatal` is set to `true` and `file` is set to the current file path.\n   * Its added to `file.messages`.\n   *\n   * > 👉 **Note**: a fatal error means that a file is no longer processable.\n   *\n   * @param {string | Error | VFileMessage} reason\n   *   Reason for message, uses the stack and message of the error if given.\n   * @param {Node | NodeLike | Position | Point | null | undefined} [place]\n   *   Place in file where the message occurred.\n   * @param {string | null | undefined} [origin]\n   *   Place in code where the message originates (example:\n   *   `'my-package:my-rule'` or `'my-rule'`).\n   * @returns {never}\n   *   Message.\n   * @throws {VFileMessage}\n   *   Message.\n   */\n  fail(reason, place, origin) {\n    const message = this.message(reason, place, origin)\n\n    message.fatal = true\n\n    throw message\n  }\n}\n\n/**\n * Assert that `part` is not a path (as in, does not contain `path.sep`).\n *\n * @param {string | null | undefined} part\n *   File path part.\n * @param {string} name\n *   Part name.\n * @returns {void}\n *   Nothing.\n */\nfunction assertPart(part, name) {\n  if (part && part.includes(_minpath_js__WEBPACK_IMPORTED_MODULE_4__.path.sep)) {\n    throw new Error(\n      '`' + name + '` cannot be a path: did not expect `' + _minpath_js__WEBPACK_IMPORTED_MODULE_4__.path.sep + '`'\n    )\n  }\n}\n\n/**\n * Assert that `part` is not empty.\n *\n * @param {string | undefined} part\n *   Thing.\n * @param {string} name\n *   Part name.\n * @returns {asserts part is string}\n *   Nothing.\n */\nfunction assertNonEmpty(part, name) {\n  if (!part) {\n    throw new Error('`' + name + '` cannot be empty')\n  }\n}\n\n/**\n * Assert `path` exists.\n *\n * @param {string | undefined} path\n *   Path.\n * @param {string} name\n *   Dependency name.\n * @returns {asserts path is string}\n *   Nothing.\n */\nfunction assertPath(path, name) {\n  if (!path) {\n    throw new Error('Setting `' + name + '` requires `path` to be set too')\n  }\n}\n\n/**\n * Assert `value` is a buffer.\n *\n * @param {unknown} value\n *   thing.\n * @returns {value is Buffer}\n *   Whether `value` is a Node.js buffer.\n */\nfunction buffer(value) {\n  return is_buffer__WEBPACK_IMPORTED_MODULE_0__(value)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/@blocknote/core/node_modules/vfile/lib/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/@blocknote/core/node_modules/vfile/lib/minpath.browser.js":
/*!************************************************************************************!*\
  !*** ../../node_modules/@blocknote/core/node_modules/vfile/lib/minpath.browser.js ***!
  \************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   path: function() { return /* binding */ path; }\n/* harmony export */ });\n// A derivative work based on:\n// <https://github.com/browserify/path-browserify>.\n// Which is licensed:\n//\n// MIT License\n//\n// Copyright (c) 2013 James Halliday\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy of\n// this software and associated documentation files (the \"Software\"), to deal in\n// the Software without restriction, including without limitation the rights to\n// use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of\n// the Software, and to permit persons to whom the Software is furnished to do so,\n// subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in all\n// copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS\n// FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR\n// COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER\n// IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n// CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n// A derivative work based on:\n//\n// Parts of that are extracted from Node’s internal `path` module:\n// <https://github.com/nodejs/node/blob/master/lib/path.js>.\n// Which is licensed:\n//\n// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\nconst path = {basename, dirname, extname, join, sep: '/'}\n\n/* eslint-disable max-depth, complexity */\n\n/**\n * Get the basename from a path.\n *\n * @param {string} path\n *   File path.\n * @param {string | undefined} [ext]\n *   Extension to strip.\n * @returns {string}\n *   Stem or basename.\n */\nfunction basename(path, ext) {\n  if (ext !== undefined && typeof ext !== 'string') {\n    throw new TypeError('\"ext\" argument must be a string')\n  }\n\n  assertPath(path)\n  let start = 0\n  let end = -1\n  let index = path.length\n  /** @type {boolean | undefined} */\n  let seenNonSlash\n\n  if (ext === undefined || ext.length === 0 || ext.length > path.length) {\n    while (index--) {\n      if (path.charCodeAt(index) === 47 /* `/` */) {\n        // If we reached a path separator that was not part of a set of path\n        // separators at the end of the string, stop now.\n        if (seenNonSlash) {\n          start = index + 1\n          break\n        }\n      } else if (end < 0) {\n        // We saw the first non-path separator, mark this as the end of our\n        // path component.\n        seenNonSlash = true\n        end = index + 1\n      }\n    }\n\n    return end < 0 ? '' : path.slice(start, end)\n  }\n\n  if (ext === path) {\n    return ''\n  }\n\n  let firstNonSlashEnd = -1\n  let extIndex = ext.length - 1\n\n  while (index--) {\n    if (path.charCodeAt(index) === 47 /* `/` */) {\n      // If we reached a path separator that was not part of a set of path\n      // separators at the end of the string, stop now.\n      if (seenNonSlash) {\n        start = index + 1\n        break\n      }\n    } else {\n      if (firstNonSlashEnd < 0) {\n        // We saw the first non-path separator, remember this index in case\n        // we need it if the extension ends up not matching.\n        seenNonSlash = true\n        firstNonSlashEnd = index + 1\n      }\n\n      if (extIndex > -1) {\n        // Try to match the explicit extension.\n        if (path.charCodeAt(index) === ext.charCodeAt(extIndex--)) {\n          if (extIndex < 0) {\n            // We matched the extension, so mark this as the end of our path\n            // component\n            end = index\n          }\n        } else {\n          // Extension does not match, so our result is the entire path\n          // component\n          extIndex = -1\n          end = firstNonSlashEnd\n        }\n      }\n    }\n  }\n\n  if (start === end) {\n    end = firstNonSlashEnd\n  } else if (end < 0) {\n    end = path.length\n  }\n\n  return path.slice(start, end)\n}\n\n/**\n * Get the dirname from a path.\n *\n * @param {string} path\n *   File path.\n * @returns {string}\n *   File path.\n */\nfunction dirname(path) {\n  assertPath(path)\n\n  if (path.length === 0) {\n    return '.'\n  }\n\n  let end = -1\n  let index = path.length\n  /** @type {boolean | undefined} */\n  let unmatchedSlash\n\n  // Prefix `--` is important to not run on `0`.\n  while (--index) {\n    if (path.charCodeAt(index) === 47 /* `/` */) {\n      if (unmatchedSlash) {\n        end = index\n        break\n      }\n    } else if (!unmatchedSlash) {\n      // We saw the first non-path separator\n      unmatchedSlash = true\n    }\n  }\n\n  return end < 0\n    ? path.charCodeAt(0) === 47 /* `/` */\n      ? '/'\n      : '.'\n    : end === 1 && path.charCodeAt(0) === 47 /* `/` */\n    ? '//'\n    : path.slice(0, end)\n}\n\n/**\n * Get an extname from a path.\n *\n * @param {string} path\n *   File path.\n * @returns {string}\n *   Extname.\n */\nfunction extname(path) {\n  assertPath(path)\n\n  let index = path.length\n\n  let end = -1\n  let startPart = 0\n  let startDot = -1\n  // Track the state of characters (if any) we see before our first dot and\n  // after any path separator we find.\n  let preDotState = 0\n  /** @type {boolean | undefined} */\n  let unmatchedSlash\n\n  while (index--) {\n    const code = path.charCodeAt(index)\n\n    if (code === 47 /* `/` */) {\n      // If we reached a path separator that was not part of a set of path\n      // separators at the end of the string, stop now.\n      if (unmatchedSlash) {\n        startPart = index + 1\n        break\n      }\n\n      continue\n    }\n\n    if (end < 0) {\n      // We saw the first non-path separator, mark this as the end of our\n      // extension.\n      unmatchedSlash = true\n      end = index + 1\n    }\n\n    if (code === 46 /* `.` */) {\n      // If this is our first dot, mark it as the start of our extension.\n      if (startDot < 0) {\n        startDot = index\n      } else if (preDotState !== 1) {\n        preDotState = 1\n      }\n    } else if (startDot > -1) {\n      // We saw a non-dot and non-path separator before our dot, so we should\n      // have a good chance at having a non-empty extension.\n      preDotState = -1\n    }\n  }\n\n  if (\n    startDot < 0 ||\n    end < 0 ||\n    // We saw a non-dot character immediately before the dot.\n    preDotState === 0 ||\n    // The (right-most) trimmed path component is exactly `..`.\n    (preDotState === 1 && startDot === end - 1 && startDot === startPart + 1)\n  ) {\n    return ''\n  }\n\n  return path.slice(startDot, end)\n}\n\n/**\n * Join segments from a path.\n *\n * @param {Array<string>} segments\n *   Path segments.\n * @returns {string}\n *   File path.\n */\nfunction join(...segments) {\n  let index = -1\n  /** @type {string | undefined} */\n  let joined\n\n  while (++index < segments.length) {\n    assertPath(segments[index])\n\n    if (segments[index]) {\n      joined =\n        joined === undefined ? segments[index] : joined + '/' + segments[index]\n    }\n  }\n\n  return joined === undefined ? '.' : normalize(joined)\n}\n\n/**\n * Normalize a basic file path.\n *\n * @param {string} path\n *   File path.\n * @returns {string}\n *   File path.\n */\n// Note: `normalize` is not exposed as `path.normalize`, so some code is\n// manually removed from it.\nfunction normalize(path) {\n  assertPath(path)\n\n  const absolute = path.charCodeAt(0) === 47 /* `/` */\n\n  // Normalize the path according to POSIX rules.\n  let value = normalizeString(path, !absolute)\n\n  if (value.length === 0 && !absolute) {\n    value = '.'\n  }\n\n  if (value.length > 0 && path.charCodeAt(path.length - 1) === 47 /* / */) {\n    value += '/'\n  }\n\n  return absolute ? '/' + value : value\n}\n\n/**\n * Resolve `.` and `..` elements in a path with directory names.\n *\n * @param {string} path\n *   File path.\n * @param {boolean} allowAboveRoot\n *   Whether `..` can move above root.\n * @returns {string}\n *   File path.\n */\nfunction normalizeString(path, allowAboveRoot) {\n  let result = ''\n  let lastSegmentLength = 0\n  let lastSlash = -1\n  let dots = 0\n  let index = -1\n  /** @type {number | undefined} */\n  let code\n  /** @type {number} */\n  let lastSlashIndex\n\n  while (++index <= path.length) {\n    if (index < path.length) {\n      code = path.charCodeAt(index)\n    } else if (code === 47 /* `/` */) {\n      break\n    } else {\n      code = 47 /* `/` */\n    }\n\n    if (code === 47 /* `/` */) {\n      if (lastSlash === index - 1 || dots === 1) {\n        // Empty.\n      } else if (lastSlash !== index - 1 && dots === 2) {\n        if (\n          result.length < 2 ||\n          lastSegmentLength !== 2 ||\n          result.charCodeAt(result.length - 1) !== 46 /* `.` */ ||\n          result.charCodeAt(result.length - 2) !== 46 /* `.` */\n        ) {\n          if (result.length > 2) {\n            lastSlashIndex = result.lastIndexOf('/')\n\n            if (lastSlashIndex !== result.length - 1) {\n              if (lastSlashIndex < 0) {\n                result = ''\n                lastSegmentLength = 0\n              } else {\n                result = result.slice(0, lastSlashIndex)\n                lastSegmentLength = result.length - 1 - result.lastIndexOf('/')\n              }\n\n              lastSlash = index\n              dots = 0\n              continue\n            }\n          } else if (result.length > 0) {\n            result = ''\n            lastSegmentLength = 0\n            lastSlash = index\n            dots = 0\n            continue\n          }\n        }\n\n        if (allowAboveRoot) {\n          result = result.length > 0 ? result + '/..' : '..'\n          lastSegmentLength = 2\n        }\n      } else {\n        if (result.length > 0) {\n          result += '/' + path.slice(lastSlash + 1, index)\n        } else {\n          result = path.slice(lastSlash + 1, index)\n        }\n\n        lastSegmentLength = index - lastSlash - 1\n      }\n\n      lastSlash = index\n      dots = 0\n    } else if (code === 46 /* `.` */ && dots > -1) {\n      dots++\n    } else {\n      dots = -1\n    }\n  }\n\n  return result\n}\n\n/**\n * Make sure `path` is a string.\n *\n * @param {string} path\n *   File path.\n * @returns {asserts path is string}\n *   Nothing.\n */\nfunction assertPath(path) {\n  if (typeof path !== 'string') {\n    throw new TypeError(\n      'Path must be a string. Received ' + JSON.stringify(path)\n    )\n  }\n}\n\n/* eslint-enable max-depth, complexity */\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvQGJsb2Nrbm90ZS9jb3JlL25vZGVfbW9kdWxlcy92ZmlsZS9saWIvbWlucGF0aC5icm93c2VyLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVPLGNBQWM7O0FBRXJCOztBQUVBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsUUFBUTtBQUNuQjtBQUNBLFdBQVcsb0JBQW9CO0FBQy9CO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWEscUJBQXFCO0FBQ2xDOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsVUFBVTtBQUNWO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsV0FBVyxRQUFRO0FBQ25CO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0EsYUFBYSxxQkFBcUI7QUFDbEM7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFFBQVE7QUFDbkI7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYSxxQkFBcUI7QUFDbEM7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLGVBQWU7QUFDMUI7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhLG9CQUFvQjtBQUNqQzs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFFBQVE7QUFDbkI7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsUUFBUTtBQUNuQjtBQUNBLFdBQVcsU0FBUztBQUNwQjtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYSxvQkFBb0I7QUFDakM7QUFDQSxhQUFhLFFBQVE7QUFDckI7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0I7QUFDaEI7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWTtBQUNaO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQSxVQUFVO0FBQ1Y7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsV0FBVyxRQUFRO0FBQ25CO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vbm9kZV9tb2R1bGVzL0BibG9ja25vdGUvY29yZS9ub2RlX21vZHVsZXMvdmZpbGUvbGliL21pbnBhdGguYnJvd3Nlci5qcz8zMjJkIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIEEgZGVyaXZhdGl2ZSB3b3JrIGJhc2VkIG9uOlxuLy8gPGh0dHBzOi8vZ2l0aHViLmNvbS9icm93c2VyaWZ5L3BhdGgtYnJvd3NlcmlmeT4uXG4vLyBXaGljaCBpcyBsaWNlbnNlZDpcbi8vXG4vLyBNSVQgTGljZW5zZVxuLy9cbi8vIENvcHlyaWdodCAoYykgMjAxMyBKYW1lcyBIYWxsaWRheVxuLy9cbi8vIFBlcm1pc3Npb24gaXMgaGVyZWJ5IGdyYW50ZWQsIGZyZWUgb2YgY2hhcmdlLCB0byBhbnkgcGVyc29uIG9idGFpbmluZyBhIGNvcHkgb2Zcbi8vIHRoaXMgc29mdHdhcmUgYW5kIGFzc29jaWF0ZWQgZG9jdW1lbnRhdGlvbiBmaWxlcyAodGhlIFwiU29mdHdhcmVcIiksIHRvIGRlYWwgaW5cbi8vIHRoZSBTb2Z0d2FyZSB3aXRob3V0IHJlc3RyaWN0aW9uLCBpbmNsdWRpbmcgd2l0aG91dCBsaW1pdGF0aW9uIHRoZSByaWdodHMgdG9cbi8vIHVzZSwgY29weSwgbW9kaWZ5LCBtZXJnZSwgcHVibGlzaCwgZGlzdHJpYnV0ZSwgc3VibGljZW5zZSwgYW5kL29yIHNlbGwgY29waWVzIG9mXG4vLyB0aGUgU29mdHdhcmUsIGFuZCB0byBwZXJtaXQgcGVyc29ucyB0byB3aG9tIHRoZSBTb2Z0d2FyZSBpcyBmdXJuaXNoZWQgdG8gZG8gc28sXG4vLyBzdWJqZWN0IHRvIHRoZSBmb2xsb3dpbmcgY29uZGl0aW9uczpcbi8vXG4vLyBUaGUgYWJvdmUgY29weXJpZ2h0IG5vdGljZSBhbmQgdGhpcyBwZXJtaXNzaW9uIG5vdGljZSBzaGFsbCBiZSBpbmNsdWRlZCBpbiBhbGxcbi8vIGNvcGllcyBvciBzdWJzdGFudGlhbCBwb3J0aW9ucyBvZiB0aGUgU29mdHdhcmUuXG4vL1xuLy8gVEhFIFNPRlRXQVJFIElTIFBST1ZJREVEIFwiQVMgSVNcIiwgV0lUSE9VVCBXQVJSQU5UWSBPRiBBTlkgS0lORCwgRVhQUkVTUyBPUlxuLy8gSU1QTElFRCwgSU5DTFVESU5HIEJVVCBOT1QgTElNSVRFRCBUTyBUSEUgV0FSUkFOVElFUyBPRiBNRVJDSEFOVEFCSUxJVFksIEZJVE5FU1Ncbi8vIEZPUiBBIFBBUlRJQ1VMQVIgUFVSUE9TRSBBTkQgTk9OSU5GUklOR0VNRU5ULiBJTiBOTyBFVkVOVCBTSEFMTCBUSEUgQVVUSE9SUyBPUlxuLy8gQ09QWVJJR0hUIEhPTERFUlMgQkUgTElBQkxFIEZPUiBBTlkgQ0xBSU0sIERBTUFHRVMgT1IgT1RIRVIgTElBQklMSVRZLCBXSEVUSEVSXG4vLyBJTiBBTiBBQ1RJT04gT0YgQ09OVFJBQ1QsIFRPUlQgT1IgT1RIRVJXSVNFLCBBUklTSU5HIEZST00sIE9VVCBPRiBPUiBJTlxuLy8gQ09OTkVDVElPTiBXSVRIIFRIRSBTT0ZUV0FSRSBPUiBUSEUgVVNFIE9SIE9USEVSIERFQUxJTkdTIElOIFRIRSBTT0ZUV0FSRS5cbi8vIEEgZGVyaXZhdGl2ZSB3b3JrIGJhc2VkIG9uOlxuLy9cbi8vIFBhcnRzIG9mIHRoYXQgYXJlIGV4dHJhY3RlZCBmcm9tIE5vZGXigJlzIGludGVybmFsIGBwYXRoYCBtb2R1bGU6XG4vLyA8aHR0cHM6Ly9naXRodWIuY29tL25vZGVqcy9ub2RlL2Jsb2IvbWFzdGVyL2xpYi9wYXRoLmpzPi5cbi8vIFdoaWNoIGlzIGxpY2Vuc2VkOlxuLy9cbi8vIENvcHlyaWdodCBKb3llbnQsIEluYy4gYW5kIG90aGVyIE5vZGUgY29udHJpYnV0b3JzLlxuLy9cbi8vIFBlcm1pc3Npb24gaXMgaGVyZWJ5IGdyYW50ZWQsIGZyZWUgb2YgY2hhcmdlLCB0byBhbnkgcGVyc29uIG9idGFpbmluZyBhXG4vLyBjb3B5IG9mIHRoaXMgc29mdHdhcmUgYW5kIGFzc29jaWF0ZWQgZG9jdW1lbnRhdGlvbiBmaWxlcyAodGhlXG4vLyBcIlNvZnR3YXJlXCIpLCB0byBkZWFsIGluIHRoZSBTb2Z0d2FyZSB3aXRob3V0IHJlc3RyaWN0aW9uLCBpbmNsdWRpbmdcbi8vIHdpdGhvdXQgbGltaXRhdGlvbiB0aGUgcmlnaHRzIHRvIHVzZSwgY29weSwgbW9kaWZ5LCBtZXJnZSwgcHVibGlzaCxcbi8vIGRpc3RyaWJ1dGUsIHN1YmxpY2Vuc2UsIGFuZC9vciBzZWxsIGNvcGllcyBvZiB0aGUgU29mdHdhcmUsIGFuZCB0byBwZXJtaXRcbi8vIHBlcnNvbnMgdG8gd2hvbSB0aGUgU29mdHdhcmUgaXMgZnVybmlzaGVkIHRvIGRvIHNvLCBzdWJqZWN0IHRvIHRoZVxuLy8gZm9sbG93aW5nIGNvbmRpdGlvbnM6XG4vL1xuLy8gVGhlIGFib3ZlIGNvcHlyaWdodCBub3RpY2UgYW5kIHRoaXMgcGVybWlzc2lvbiBub3RpY2Ugc2hhbGwgYmUgaW5jbHVkZWRcbi8vIGluIGFsbCBjb3BpZXMgb3Igc3Vic3RhbnRpYWwgcG9ydGlvbnMgb2YgdGhlIFNvZnR3YXJlLlxuLy9cbi8vIFRIRSBTT0ZUV0FSRSBJUyBQUk9WSURFRCBcIkFTIElTXCIsIFdJVEhPVVQgV0FSUkFOVFkgT0YgQU5ZIEtJTkQsIEVYUFJFU1Ncbi8vIE9SIElNUExJRUQsIElOQ0xVRElORyBCVVQgTk9UIExJTUlURUQgVE8gVEhFIFdBUlJBTlRJRVMgT0Zcbi8vIE1FUkNIQU5UQUJJTElUWSwgRklUTkVTUyBGT1IgQSBQQVJUSUNVTEFSIFBVUlBPU0UgQU5EIE5PTklORlJJTkdFTUVOVC4gSU5cbi8vIE5PIEVWRU5UIFNIQUxMIFRIRSBBVVRIT1JTIE9SIENPUFlSSUdIVCBIT0xERVJTIEJFIExJQUJMRSBGT1IgQU5ZIENMQUlNLFxuLy8gREFNQUdFUyBPUiBPVEhFUiBMSUFCSUxJVFksIFdIRVRIRVIgSU4gQU4gQUNUSU9OIE9GIENPTlRSQUNULCBUT1JUIE9SXG4vLyBPVEhFUldJU0UsIEFSSVNJTkcgRlJPTSwgT1VUIE9GIE9SIElOIENPTk5FQ1RJT04gV0lUSCBUSEUgU09GVFdBUkUgT1IgVEhFXG4vLyBVU0UgT1IgT1RIRVIgREVBTElOR1MgSU4gVEhFIFNPRlRXQVJFLlxuXG5leHBvcnQgY29uc3QgcGF0aCA9IHtiYXNlbmFtZSwgZGlybmFtZSwgZXh0bmFtZSwgam9pbiwgc2VwOiAnLyd9XG5cbi8qIGVzbGludC1kaXNhYmxlIG1heC1kZXB0aCwgY29tcGxleGl0eSAqL1xuXG4vKipcbiAqIEdldCB0aGUgYmFzZW5hbWUgZnJvbSBhIHBhdGguXG4gKlxuICogQHBhcmFtIHtzdHJpbmd9IHBhdGhcbiAqICAgRmlsZSBwYXRoLlxuICogQHBhcmFtIHtzdHJpbmcgfCB1bmRlZmluZWR9IFtleHRdXG4gKiAgIEV4dGVuc2lvbiB0byBzdHJpcC5cbiAqIEByZXR1cm5zIHtzdHJpbmd9XG4gKiAgIFN0ZW0gb3IgYmFzZW5hbWUuXG4gKi9cbmZ1bmN0aW9uIGJhc2VuYW1lKHBhdGgsIGV4dCkge1xuICBpZiAoZXh0ICE9PSB1bmRlZmluZWQgJiYgdHlwZW9mIGV4dCAhPT0gJ3N0cmluZycpIHtcbiAgICB0aHJvdyBuZXcgVHlwZUVycm9yKCdcImV4dFwiIGFyZ3VtZW50IG11c3QgYmUgYSBzdHJpbmcnKVxuICB9XG5cbiAgYXNzZXJ0UGF0aChwYXRoKVxuICBsZXQgc3RhcnQgPSAwXG4gIGxldCBlbmQgPSAtMVxuICBsZXQgaW5kZXggPSBwYXRoLmxlbmd0aFxuICAvKiogQHR5cGUge2Jvb2xlYW4gfCB1bmRlZmluZWR9ICovXG4gIGxldCBzZWVuTm9uU2xhc2hcblxuICBpZiAoZXh0ID09PSB1bmRlZmluZWQgfHwgZXh0Lmxlbmd0aCA9PT0gMCB8fCBleHQubGVuZ3RoID4gcGF0aC5sZW5ndGgpIHtcbiAgICB3aGlsZSAoaW5kZXgtLSkge1xuICAgICAgaWYgKHBhdGguY2hhckNvZGVBdChpbmRleCkgPT09IDQ3IC8qIGAvYCAqLykge1xuICAgICAgICAvLyBJZiB3ZSByZWFjaGVkIGEgcGF0aCBzZXBhcmF0b3IgdGhhdCB3YXMgbm90IHBhcnQgb2YgYSBzZXQgb2YgcGF0aFxuICAgICAgICAvLyBzZXBhcmF0b3JzIGF0IHRoZSBlbmQgb2YgdGhlIHN0cmluZywgc3RvcCBub3cuXG4gICAgICAgIGlmIChzZWVuTm9uU2xhc2gpIHtcbiAgICAgICAgICBzdGFydCA9IGluZGV4ICsgMVxuICAgICAgICAgIGJyZWFrXG4gICAgICAgIH1cbiAgICAgIH0gZWxzZSBpZiAoZW5kIDwgMCkge1xuICAgICAgICAvLyBXZSBzYXcgdGhlIGZpcnN0IG5vbi1wYXRoIHNlcGFyYXRvciwgbWFyayB0aGlzIGFzIHRoZSBlbmQgb2Ygb3VyXG4gICAgICAgIC8vIHBhdGggY29tcG9uZW50LlxuICAgICAgICBzZWVuTm9uU2xhc2ggPSB0cnVlXG4gICAgICAgIGVuZCA9IGluZGV4ICsgMVxuICAgICAgfVxuICAgIH1cblxuICAgIHJldHVybiBlbmQgPCAwID8gJycgOiBwYXRoLnNsaWNlKHN0YXJ0LCBlbmQpXG4gIH1cblxuICBpZiAoZXh0ID09PSBwYXRoKSB7XG4gICAgcmV0dXJuICcnXG4gIH1cblxuICBsZXQgZmlyc3ROb25TbGFzaEVuZCA9IC0xXG4gIGxldCBleHRJbmRleCA9IGV4dC5sZW5ndGggLSAxXG5cbiAgd2hpbGUgKGluZGV4LS0pIHtcbiAgICBpZiAocGF0aC5jaGFyQ29kZUF0KGluZGV4KSA9PT0gNDcgLyogYC9gICovKSB7XG4gICAgICAvLyBJZiB3ZSByZWFjaGVkIGEgcGF0aCBzZXBhcmF0b3IgdGhhdCB3YXMgbm90IHBhcnQgb2YgYSBzZXQgb2YgcGF0aFxuICAgICAgLy8gc2VwYXJhdG9ycyBhdCB0aGUgZW5kIG9mIHRoZSBzdHJpbmcsIHN0b3Agbm93LlxuICAgICAgaWYgKHNlZW5Ob25TbGFzaCkge1xuICAgICAgICBzdGFydCA9IGluZGV4ICsgMVxuICAgICAgICBicmVha1xuICAgICAgfVxuICAgIH0gZWxzZSB7XG4gICAgICBpZiAoZmlyc3ROb25TbGFzaEVuZCA8IDApIHtcbiAgICAgICAgLy8gV2Ugc2F3IHRoZSBmaXJzdCBub24tcGF0aCBzZXBhcmF0b3IsIHJlbWVtYmVyIHRoaXMgaW5kZXggaW4gY2FzZVxuICAgICAgICAvLyB3ZSBuZWVkIGl0IGlmIHRoZSBleHRlbnNpb24gZW5kcyB1cCBub3QgbWF0Y2hpbmcuXG4gICAgICAgIHNlZW5Ob25TbGFzaCA9IHRydWVcbiAgICAgICAgZmlyc3ROb25TbGFzaEVuZCA9IGluZGV4ICsgMVxuICAgICAgfVxuXG4gICAgICBpZiAoZXh0SW5kZXggPiAtMSkge1xuICAgICAgICAvLyBUcnkgdG8gbWF0Y2ggdGhlIGV4cGxpY2l0IGV4dGVuc2lvbi5cbiAgICAgICAgaWYgKHBhdGguY2hhckNvZGVBdChpbmRleCkgPT09IGV4dC5jaGFyQ29kZUF0KGV4dEluZGV4LS0pKSB7XG4gICAgICAgICAgaWYgKGV4dEluZGV4IDwgMCkge1xuICAgICAgICAgICAgLy8gV2UgbWF0Y2hlZCB0aGUgZXh0ZW5zaW9uLCBzbyBtYXJrIHRoaXMgYXMgdGhlIGVuZCBvZiBvdXIgcGF0aFxuICAgICAgICAgICAgLy8gY29tcG9uZW50XG4gICAgICAgICAgICBlbmQgPSBpbmRleFxuICAgICAgICAgIH1cbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAvLyBFeHRlbnNpb24gZG9lcyBub3QgbWF0Y2gsIHNvIG91ciByZXN1bHQgaXMgdGhlIGVudGlyZSBwYXRoXG4gICAgICAgICAgLy8gY29tcG9uZW50XG4gICAgICAgICAgZXh0SW5kZXggPSAtMVxuICAgICAgICAgIGVuZCA9IGZpcnN0Tm9uU2xhc2hFbmRcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cbiAgfVxuXG4gIGlmIChzdGFydCA9PT0gZW5kKSB7XG4gICAgZW5kID0gZmlyc3ROb25TbGFzaEVuZFxuICB9IGVsc2UgaWYgKGVuZCA8IDApIHtcbiAgICBlbmQgPSBwYXRoLmxlbmd0aFxuICB9XG5cbiAgcmV0dXJuIHBhdGguc2xpY2Uoc3RhcnQsIGVuZClcbn1cblxuLyoqXG4gKiBHZXQgdGhlIGRpcm5hbWUgZnJvbSBhIHBhdGguXG4gKlxuICogQHBhcmFtIHtzdHJpbmd9IHBhdGhcbiAqICAgRmlsZSBwYXRoLlxuICogQHJldHVybnMge3N0cmluZ31cbiAqICAgRmlsZSBwYXRoLlxuICovXG5mdW5jdGlvbiBkaXJuYW1lKHBhdGgpIHtcbiAgYXNzZXJ0UGF0aChwYXRoKVxuXG4gIGlmIChwYXRoLmxlbmd0aCA9PT0gMCkge1xuICAgIHJldHVybiAnLidcbiAgfVxuXG4gIGxldCBlbmQgPSAtMVxuICBsZXQgaW5kZXggPSBwYXRoLmxlbmd0aFxuICAvKiogQHR5cGUge2Jvb2xlYW4gfCB1bmRlZmluZWR9ICovXG4gIGxldCB1bm1hdGNoZWRTbGFzaFxuXG4gIC8vIFByZWZpeCBgLS1gIGlzIGltcG9ydGFudCB0byBub3QgcnVuIG9uIGAwYC5cbiAgd2hpbGUgKC0taW5kZXgpIHtcbiAgICBpZiAocGF0aC5jaGFyQ29kZUF0KGluZGV4KSA9PT0gNDcgLyogYC9gICovKSB7XG4gICAgICBpZiAodW5tYXRjaGVkU2xhc2gpIHtcbiAgICAgICAgZW5kID0gaW5kZXhcbiAgICAgICAgYnJlYWtcbiAgICAgIH1cbiAgICB9IGVsc2UgaWYgKCF1bm1hdGNoZWRTbGFzaCkge1xuICAgICAgLy8gV2Ugc2F3IHRoZSBmaXJzdCBub24tcGF0aCBzZXBhcmF0b3JcbiAgICAgIHVubWF0Y2hlZFNsYXNoID0gdHJ1ZVxuICAgIH1cbiAgfVxuXG4gIHJldHVybiBlbmQgPCAwXG4gICAgPyBwYXRoLmNoYXJDb2RlQXQoMCkgPT09IDQ3IC8qIGAvYCAqL1xuICAgICAgPyAnLydcbiAgICAgIDogJy4nXG4gICAgOiBlbmQgPT09IDEgJiYgcGF0aC5jaGFyQ29kZUF0KDApID09PSA0NyAvKiBgL2AgKi9cbiAgICA/ICcvLydcbiAgICA6IHBhdGguc2xpY2UoMCwgZW5kKVxufVxuXG4vKipcbiAqIEdldCBhbiBleHRuYW1lIGZyb20gYSBwYXRoLlxuICpcbiAqIEBwYXJhbSB7c3RyaW5nfSBwYXRoXG4gKiAgIEZpbGUgcGF0aC5cbiAqIEByZXR1cm5zIHtzdHJpbmd9XG4gKiAgIEV4dG5hbWUuXG4gKi9cbmZ1bmN0aW9uIGV4dG5hbWUocGF0aCkge1xuICBhc3NlcnRQYXRoKHBhdGgpXG5cbiAgbGV0IGluZGV4ID0gcGF0aC5sZW5ndGhcblxuICBsZXQgZW5kID0gLTFcbiAgbGV0IHN0YXJ0UGFydCA9IDBcbiAgbGV0IHN0YXJ0RG90ID0gLTFcbiAgLy8gVHJhY2sgdGhlIHN0YXRlIG9mIGNoYXJhY3RlcnMgKGlmIGFueSkgd2Ugc2VlIGJlZm9yZSBvdXIgZmlyc3QgZG90IGFuZFxuICAvLyBhZnRlciBhbnkgcGF0aCBzZXBhcmF0b3Igd2UgZmluZC5cbiAgbGV0IHByZURvdFN0YXRlID0gMFxuICAvKiogQHR5cGUge2Jvb2xlYW4gfCB1bmRlZmluZWR9ICovXG4gIGxldCB1bm1hdGNoZWRTbGFzaFxuXG4gIHdoaWxlIChpbmRleC0tKSB7XG4gICAgY29uc3QgY29kZSA9IHBhdGguY2hhckNvZGVBdChpbmRleClcblxuICAgIGlmIChjb2RlID09PSA0NyAvKiBgL2AgKi8pIHtcbiAgICAgIC8vIElmIHdlIHJlYWNoZWQgYSBwYXRoIHNlcGFyYXRvciB0aGF0IHdhcyBub3QgcGFydCBvZiBhIHNldCBvZiBwYXRoXG4gICAgICAvLyBzZXBhcmF0b3JzIGF0IHRoZSBlbmQgb2YgdGhlIHN0cmluZywgc3RvcCBub3cuXG4gICAgICBpZiAodW5tYXRjaGVkU2xhc2gpIHtcbiAgICAgICAgc3RhcnRQYXJ0ID0gaW5kZXggKyAxXG4gICAgICAgIGJyZWFrXG4gICAgICB9XG5cbiAgICAgIGNvbnRpbnVlXG4gICAgfVxuXG4gICAgaWYgKGVuZCA8IDApIHtcbiAgICAgIC8vIFdlIHNhdyB0aGUgZmlyc3Qgbm9uLXBhdGggc2VwYXJhdG9yLCBtYXJrIHRoaXMgYXMgdGhlIGVuZCBvZiBvdXJcbiAgICAgIC8vIGV4dGVuc2lvbi5cbiAgICAgIHVubWF0Y2hlZFNsYXNoID0gdHJ1ZVxuICAgICAgZW5kID0gaW5kZXggKyAxXG4gICAgfVxuXG4gICAgaWYgKGNvZGUgPT09IDQ2IC8qIGAuYCAqLykge1xuICAgICAgLy8gSWYgdGhpcyBpcyBvdXIgZmlyc3QgZG90LCBtYXJrIGl0IGFzIHRoZSBzdGFydCBvZiBvdXIgZXh0ZW5zaW9uLlxuICAgICAgaWYgKHN0YXJ0RG90IDwgMCkge1xuICAgICAgICBzdGFydERvdCA9IGluZGV4XG4gICAgICB9IGVsc2UgaWYgKHByZURvdFN0YXRlICE9PSAxKSB7XG4gICAgICAgIHByZURvdFN0YXRlID0gMVxuICAgICAgfVxuICAgIH0gZWxzZSBpZiAoc3RhcnREb3QgPiAtMSkge1xuICAgICAgLy8gV2Ugc2F3IGEgbm9uLWRvdCBhbmQgbm9uLXBhdGggc2VwYXJhdG9yIGJlZm9yZSBvdXIgZG90LCBzbyB3ZSBzaG91bGRcbiAgICAgIC8vIGhhdmUgYSBnb29kIGNoYW5jZSBhdCBoYXZpbmcgYSBub24tZW1wdHkgZXh0ZW5zaW9uLlxuICAgICAgcHJlRG90U3RhdGUgPSAtMVxuICAgIH1cbiAgfVxuXG4gIGlmIChcbiAgICBzdGFydERvdCA8IDAgfHxcbiAgICBlbmQgPCAwIHx8XG4gICAgLy8gV2Ugc2F3IGEgbm9uLWRvdCBjaGFyYWN0ZXIgaW1tZWRpYXRlbHkgYmVmb3JlIHRoZSBkb3QuXG4gICAgcHJlRG90U3RhdGUgPT09IDAgfHxcbiAgICAvLyBUaGUgKHJpZ2h0LW1vc3QpIHRyaW1tZWQgcGF0aCBjb21wb25lbnQgaXMgZXhhY3RseSBgLi5gLlxuICAgIChwcmVEb3RTdGF0ZSA9PT0gMSAmJiBzdGFydERvdCA9PT0gZW5kIC0gMSAmJiBzdGFydERvdCA9PT0gc3RhcnRQYXJ0ICsgMSlcbiAgKSB7XG4gICAgcmV0dXJuICcnXG4gIH1cblxuICByZXR1cm4gcGF0aC5zbGljZShzdGFydERvdCwgZW5kKVxufVxuXG4vKipcbiAqIEpvaW4gc2VnbWVudHMgZnJvbSBhIHBhdGguXG4gKlxuICogQHBhcmFtIHtBcnJheTxzdHJpbmc+fSBzZWdtZW50c1xuICogICBQYXRoIHNlZ21lbnRzLlxuICogQHJldHVybnMge3N0cmluZ31cbiAqICAgRmlsZSBwYXRoLlxuICovXG5mdW5jdGlvbiBqb2luKC4uLnNlZ21lbnRzKSB7XG4gIGxldCBpbmRleCA9IC0xXG4gIC8qKiBAdHlwZSB7c3RyaW5nIHwgdW5kZWZpbmVkfSAqL1xuICBsZXQgam9pbmVkXG5cbiAgd2hpbGUgKCsraW5kZXggPCBzZWdtZW50cy5sZW5ndGgpIHtcbiAgICBhc3NlcnRQYXRoKHNlZ21lbnRzW2luZGV4XSlcblxuICAgIGlmIChzZWdtZW50c1tpbmRleF0pIHtcbiAgICAgIGpvaW5lZCA9XG4gICAgICAgIGpvaW5lZCA9PT0gdW5kZWZpbmVkID8gc2VnbWVudHNbaW5kZXhdIDogam9pbmVkICsgJy8nICsgc2VnbWVudHNbaW5kZXhdXG4gICAgfVxuICB9XG5cbiAgcmV0dXJuIGpvaW5lZCA9PT0gdW5kZWZpbmVkID8gJy4nIDogbm9ybWFsaXplKGpvaW5lZClcbn1cblxuLyoqXG4gKiBOb3JtYWxpemUgYSBiYXNpYyBmaWxlIHBhdGguXG4gKlxuICogQHBhcmFtIHtzdHJpbmd9IHBhdGhcbiAqICAgRmlsZSBwYXRoLlxuICogQHJldHVybnMge3N0cmluZ31cbiAqICAgRmlsZSBwYXRoLlxuICovXG4vLyBOb3RlOiBgbm9ybWFsaXplYCBpcyBub3QgZXhwb3NlZCBhcyBgcGF0aC5ub3JtYWxpemVgLCBzbyBzb21lIGNvZGUgaXNcbi8vIG1hbnVhbGx5IHJlbW92ZWQgZnJvbSBpdC5cbmZ1bmN0aW9uIG5vcm1hbGl6ZShwYXRoKSB7XG4gIGFzc2VydFBhdGgocGF0aClcblxuICBjb25zdCBhYnNvbHV0ZSA9IHBhdGguY2hhckNvZGVBdCgwKSA9PT0gNDcgLyogYC9gICovXG5cbiAgLy8gTm9ybWFsaXplIHRoZSBwYXRoIGFjY29yZGluZyB0byBQT1NJWCBydWxlcy5cbiAgbGV0IHZhbHVlID0gbm9ybWFsaXplU3RyaW5nKHBhdGgsICFhYnNvbHV0ZSlcblxuICBpZiAodmFsdWUubGVuZ3RoID09PSAwICYmICFhYnNvbHV0ZSkge1xuICAgIHZhbHVlID0gJy4nXG4gIH1cblxuICBpZiAodmFsdWUubGVuZ3RoID4gMCAmJiBwYXRoLmNoYXJDb2RlQXQocGF0aC5sZW5ndGggLSAxKSA9PT0gNDcgLyogLyAqLykge1xuICAgIHZhbHVlICs9ICcvJ1xuICB9XG5cbiAgcmV0dXJuIGFic29sdXRlID8gJy8nICsgdmFsdWUgOiB2YWx1ZVxufVxuXG4vKipcbiAqIFJlc29sdmUgYC5gIGFuZCBgLi5gIGVsZW1lbnRzIGluIGEgcGF0aCB3aXRoIGRpcmVjdG9yeSBuYW1lcy5cbiAqXG4gKiBAcGFyYW0ge3N0cmluZ30gcGF0aFxuICogICBGaWxlIHBhdGguXG4gKiBAcGFyYW0ge2Jvb2xlYW59IGFsbG93QWJvdmVSb290XG4gKiAgIFdoZXRoZXIgYC4uYCBjYW4gbW92ZSBhYm92ZSByb290LlxuICogQHJldHVybnMge3N0cmluZ31cbiAqICAgRmlsZSBwYXRoLlxuICovXG5mdW5jdGlvbiBub3JtYWxpemVTdHJpbmcocGF0aCwgYWxsb3dBYm92ZVJvb3QpIHtcbiAgbGV0IHJlc3VsdCA9ICcnXG4gIGxldCBsYXN0U2VnbWVudExlbmd0aCA9IDBcbiAgbGV0IGxhc3RTbGFzaCA9IC0xXG4gIGxldCBkb3RzID0gMFxuICBsZXQgaW5kZXggPSAtMVxuICAvKiogQHR5cGUge251bWJlciB8IHVuZGVmaW5lZH0gKi9cbiAgbGV0IGNvZGVcbiAgLyoqIEB0eXBlIHtudW1iZXJ9ICovXG4gIGxldCBsYXN0U2xhc2hJbmRleFxuXG4gIHdoaWxlICgrK2luZGV4IDw9IHBhdGgubGVuZ3RoKSB7XG4gICAgaWYgKGluZGV4IDwgcGF0aC5sZW5ndGgpIHtcbiAgICAgIGNvZGUgPSBwYXRoLmNoYXJDb2RlQXQoaW5kZXgpXG4gICAgfSBlbHNlIGlmIChjb2RlID09PSA0NyAvKiBgL2AgKi8pIHtcbiAgICAgIGJyZWFrXG4gICAgfSBlbHNlIHtcbiAgICAgIGNvZGUgPSA0NyAvKiBgL2AgKi9cbiAgICB9XG5cbiAgICBpZiAoY29kZSA9PT0gNDcgLyogYC9gICovKSB7XG4gICAgICBpZiAobGFzdFNsYXNoID09PSBpbmRleCAtIDEgfHwgZG90cyA9PT0gMSkge1xuICAgICAgICAvLyBFbXB0eS5cbiAgICAgIH0gZWxzZSBpZiAobGFzdFNsYXNoICE9PSBpbmRleCAtIDEgJiYgZG90cyA9PT0gMikge1xuICAgICAgICBpZiAoXG4gICAgICAgICAgcmVzdWx0Lmxlbmd0aCA8IDIgfHxcbiAgICAgICAgICBsYXN0U2VnbWVudExlbmd0aCAhPT0gMiB8fFxuICAgICAgICAgIHJlc3VsdC5jaGFyQ29kZUF0KHJlc3VsdC5sZW5ndGggLSAxKSAhPT0gNDYgLyogYC5gICovIHx8XG4gICAgICAgICAgcmVzdWx0LmNoYXJDb2RlQXQocmVzdWx0Lmxlbmd0aCAtIDIpICE9PSA0NiAvKiBgLmAgKi9cbiAgICAgICAgKSB7XG4gICAgICAgICAgaWYgKHJlc3VsdC5sZW5ndGggPiAyKSB7XG4gICAgICAgICAgICBsYXN0U2xhc2hJbmRleCA9IHJlc3VsdC5sYXN0SW5kZXhPZignLycpXG5cbiAgICAgICAgICAgIGlmIChsYXN0U2xhc2hJbmRleCAhPT0gcmVzdWx0Lmxlbmd0aCAtIDEpIHtcbiAgICAgICAgICAgICAgaWYgKGxhc3RTbGFzaEluZGV4IDwgMCkge1xuICAgICAgICAgICAgICAgIHJlc3VsdCA9ICcnXG4gICAgICAgICAgICAgICAgbGFzdFNlZ21lbnRMZW5ndGggPSAwXG4gICAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgICAgcmVzdWx0ID0gcmVzdWx0LnNsaWNlKDAsIGxhc3RTbGFzaEluZGV4KVxuICAgICAgICAgICAgICAgIGxhc3RTZWdtZW50TGVuZ3RoID0gcmVzdWx0Lmxlbmd0aCAtIDEgLSByZXN1bHQubGFzdEluZGV4T2YoJy8nKVxuICAgICAgICAgICAgICB9XG5cbiAgICAgICAgICAgICAgbGFzdFNsYXNoID0gaW5kZXhcbiAgICAgICAgICAgICAgZG90cyA9IDBcbiAgICAgICAgICAgICAgY29udGludWVcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9IGVsc2UgaWYgKHJlc3VsdC5sZW5ndGggPiAwKSB7XG4gICAgICAgICAgICByZXN1bHQgPSAnJ1xuICAgICAgICAgICAgbGFzdFNlZ21lbnRMZW5ndGggPSAwXG4gICAgICAgICAgICBsYXN0U2xhc2ggPSBpbmRleFxuICAgICAgICAgICAgZG90cyA9IDBcbiAgICAgICAgICAgIGNvbnRpbnVlXG4gICAgICAgICAgfVxuICAgICAgICB9XG5cbiAgICAgICAgaWYgKGFsbG93QWJvdmVSb290KSB7XG4gICAgICAgICAgcmVzdWx0ID0gcmVzdWx0Lmxlbmd0aCA+IDAgPyByZXN1bHQgKyAnLy4uJyA6ICcuLidcbiAgICAgICAgICBsYXN0U2VnbWVudExlbmd0aCA9IDJcbiAgICAgICAgfVxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgaWYgKHJlc3VsdC5sZW5ndGggPiAwKSB7XG4gICAgICAgICAgcmVzdWx0ICs9ICcvJyArIHBhdGguc2xpY2UobGFzdFNsYXNoICsgMSwgaW5kZXgpXG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgcmVzdWx0ID0gcGF0aC5zbGljZShsYXN0U2xhc2ggKyAxLCBpbmRleClcbiAgICAgICAgfVxuXG4gICAgICAgIGxhc3RTZWdtZW50TGVuZ3RoID0gaW5kZXggLSBsYXN0U2xhc2ggLSAxXG4gICAgICB9XG5cbiAgICAgIGxhc3RTbGFzaCA9IGluZGV4XG4gICAgICBkb3RzID0gMFxuICAgIH0gZWxzZSBpZiAoY29kZSA9PT0gNDYgLyogYC5gICovICYmIGRvdHMgPiAtMSkge1xuICAgICAgZG90cysrXG4gICAgfSBlbHNlIHtcbiAgICAgIGRvdHMgPSAtMVxuICAgIH1cbiAgfVxuXG4gIHJldHVybiByZXN1bHRcbn1cblxuLyoqXG4gKiBNYWtlIHN1cmUgYHBhdGhgIGlzIGEgc3RyaW5nLlxuICpcbiAqIEBwYXJhbSB7c3RyaW5nfSBwYXRoXG4gKiAgIEZpbGUgcGF0aC5cbiAqIEByZXR1cm5zIHthc3NlcnRzIHBhdGggaXMgc3RyaW5nfVxuICogICBOb3RoaW5nLlxuICovXG5mdW5jdGlvbiBhc3NlcnRQYXRoKHBhdGgpIHtcbiAgaWYgKHR5cGVvZiBwYXRoICE9PSAnc3RyaW5nJykge1xuICAgIHRocm93IG5ldyBUeXBlRXJyb3IoXG4gICAgICAnUGF0aCBtdXN0IGJlIGEgc3RyaW5nLiBSZWNlaXZlZCAnICsgSlNPTi5zdHJpbmdpZnkocGF0aClcbiAgICApXG4gIH1cbn1cblxuLyogZXNsaW50LWVuYWJsZSBtYXgtZGVwdGgsIGNvbXBsZXhpdHkgKi9cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/@blocknote/core/node_modules/vfile/lib/minpath.browser.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/@blocknote/core/node_modules/vfile/lib/minproc.browser.js":
/*!************************************************************************************!*\
  !*** ../../node_modules/@blocknote/core/node_modules/vfile/lib/minproc.browser.js ***!
  \************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   proc: function() { return /* binding */ proc; }\n/* harmony export */ });\n// Somewhat based on:\n// <https://github.com/defunctzombie/node-process/blob/master/browser.js>.\n// But I don’t think one tiny line of code can be copyrighted. 😅\nconst proc = {cwd}\n\nfunction cwd() {\n  return '/'\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvQGJsb2Nrbm90ZS9jb3JlL25vZGVfbW9kdWxlcy92ZmlsZS9saWIvbWlucHJvYy5icm93c2VyLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDTyxjQUFjOztBQUVyQjtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL25vZGVfbW9kdWxlcy9AYmxvY2tub3RlL2NvcmUvbm9kZV9tb2R1bGVzL3ZmaWxlL2xpYi9taW5wcm9jLmJyb3dzZXIuanM/YmMwMiJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBTb21ld2hhdCBiYXNlZCBvbjpcbi8vIDxodHRwczovL2dpdGh1Yi5jb20vZGVmdW5jdHpvbWJpZS9ub2RlLXByb2Nlc3MvYmxvYi9tYXN0ZXIvYnJvd3Nlci5qcz4uXG4vLyBCdXQgSSBkb27igJl0IHRoaW5rIG9uZSB0aW55IGxpbmUgb2YgY29kZSBjYW4gYmUgY29weXJpZ2h0ZWQuIPCfmIVcbmV4cG9ydCBjb25zdCBwcm9jID0ge2N3ZH1cblxuZnVuY3Rpb24gY3dkKCkge1xuICByZXR1cm4gJy8nXG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/@blocknote/core/node_modules/vfile/lib/minproc.browser.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/@blocknote/core/node_modules/vfile/lib/minurl.browser.js":
/*!***********************************************************************************!*\
  !*** ../../node_modules/@blocknote/core/node_modules/vfile/lib/minurl.browser.js ***!
  \***********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isUrl: function() { return /* reexport safe */ _minurl_shared_js__WEBPACK_IMPORTED_MODULE_0__.isUrl; },\n/* harmony export */   urlToPath: function() { return /* binding */ urlToPath; }\n/* harmony export */ });\n/* harmony import */ var _minurl_shared_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./minurl.shared.js */ \"(app-pages-browser)/../../node_modules/@blocknote/core/node_modules/vfile/lib/minurl.shared.js\");\n/// <reference lib=\"dom\" />\n\n\n\n// See: <https://github.com/nodejs/node/blob/fcf8ba4/lib/internal/url.js>\n\n/**\n * @param {string | URL} path\n *   File URL.\n * @returns {string}\n *   File URL.\n */\nfunction urlToPath(path) {\n  if (typeof path === 'string') {\n    path = new URL(path)\n  } else if (!(0,_minurl_shared_js__WEBPACK_IMPORTED_MODULE_0__.isUrl)(path)) {\n    /** @type {NodeJS.ErrnoException} */\n    const error = new TypeError(\n      'The \"path\" argument must be of type string or an instance of URL. Received `' +\n        path +\n        '`'\n    )\n    error.code = 'ERR_INVALID_ARG_TYPE'\n    throw error\n  }\n\n  if (path.protocol !== 'file:') {\n    /** @type {NodeJS.ErrnoException} */\n    const error = new TypeError('The URL must be of scheme file')\n    error.code = 'ERR_INVALID_URL_SCHEME'\n    throw error\n  }\n\n  return getPathFromURLPosix(path)\n}\n\n/**\n * Get a path from a POSIX URL.\n *\n * @param {URL} url\n *   URL.\n * @returns {string}\n *   File path.\n */\nfunction getPathFromURLPosix(url) {\n  if (url.hostname !== '') {\n    /** @type {NodeJS.ErrnoException} */\n    const error = new TypeError(\n      'File URL host must be \"localhost\" or empty on darwin'\n    )\n    error.code = 'ERR_INVALID_FILE_URL_HOST'\n    throw error\n  }\n\n  const pathname = url.pathname\n  let index = -1\n\n  while (++index < pathname.length) {\n    if (\n      pathname.charCodeAt(index) === 37 /* `%` */ &&\n      pathname.charCodeAt(index + 1) === 50 /* `2` */\n    ) {\n      const third = pathname.charCodeAt(index + 2)\n      if (third === 70 /* `F` */ || third === 102 /* `f` */) {\n        /** @type {NodeJS.ErrnoException} */\n        const error = new TypeError(\n          'File URL path must not include encoded / characters'\n        )\n        error.code = 'ERR_INVALID_FILE_URL_PATH'\n        throw error\n      }\n    }\n  }\n\n  return decodeURIComponent(pathname)\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/@blocknote/core/node_modules/vfile/lib/minurl.browser.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/@blocknote/core/node_modules/vfile/lib/minurl.shared.js":
/*!**********************************************************************************!*\
  !*** ../../node_modules/@blocknote/core/node_modules/vfile/lib/minurl.shared.js ***!
  \**********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isUrl: function() { return /* binding */ isUrl; }\n/* harmony export */ });\n/**\n * @typedef URL\n * @property {string} hash\n * @property {string} host\n * @property {string} hostname\n * @property {string} href\n * @property {string} origin\n * @property {string} password\n * @property {string} pathname\n * @property {string} port\n * @property {string} protocol\n * @property {string} search\n * @property {any} searchParams\n * @property {string} username\n * @property {() => string} toString\n * @property {() => string} toJSON\n */\n\n/**\n * Check if `fileUrlOrPath` looks like a URL.\n *\n * @param {unknown} fileUrlOrPath\n *   File path or URL.\n * @returns {fileUrlOrPath is URL}\n *   Whether it’s a URL.\n */\n// From: <https://github.com/nodejs/node/blob/fcf8ba4/lib/internal/url.js#L1501>\nfunction isUrl(fileUrlOrPath) {\n  return (\n    fileUrlOrPath !== null &&\n    typeof fileUrlOrPath === 'object' &&\n    // @ts-expect-error: indexable.\n    fileUrlOrPath.href &&\n    // @ts-expect-error: indexable.\n    fileUrlOrPath.origin\n  )\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/@blocknote/core/node_modules/vfile/lib/minurl.shared.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/unist-util-stringify-position/lib/index.js":
/*!*********************************************************************!*\
  !*** ../../node_modules/unist-util-stringify-position/lib/index.js ***!
  \*********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   stringifyPosition: function() { return /* binding */ stringifyPosition; }\n/* harmony export */ });\n/**\n * @typedef {import('unist').Node} Node\n * @typedef {import('unist').Point} Point\n * @typedef {import('unist').Position} Position\n */\n\n/**\n * @typedef NodeLike\n * @property {string} type\n * @property {PositionLike | null | undefined} [position]\n *\n * @typedef PositionLike\n * @property {PointLike | null | undefined} [start]\n * @property {PointLike | null | undefined} [end]\n *\n * @typedef PointLike\n * @property {number | null | undefined} [line]\n * @property {number | null | undefined} [column]\n * @property {number | null | undefined} [offset]\n */\n\n/**\n * Serialize the positional info of a point, position (start and end points),\n * or node.\n *\n * @param {Node | NodeLike | Position | PositionLike | Point | PointLike | null | undefined} [value]\n *   Node, position, or point.\n * @returns {string}\n *   Pretty printed positional info of a node (`string`).\n *\n *   In the format of a range `ls:cs-le:ce` (when given `node` or `position`)\n *   or a point `l:c` (when given `point`), where `l` stands for line, `c` for\n *   column, `s` for `start`, and `e` for end.\n *   An empty string (`''`) is returned if the given value is neither `node`,\n *   `position`, nor `point`.\n */\nfunction stringifyPosition(value) {\n  // Nothing.\n  if (!value || typeof value !== 'object') {\n    return ''\n  }\n\n  // Node.\n  if ('position' in value || 'type' in value) {\n    return position(value.position)\n  }\n\n  // Position.\n  if ('start' in value || 'end' in value) {\n    return position(value)\n  }\n\n  // Point.\n  if ('line' in value || 'column' in value) {\n    return point(value)\n  }\n\n  // ?\n  return ''\n}\n\n/**\n * @param {Point | PointLike | null | undefined} point\n * @returns {string}\n */\nfunction point(point) {\n  return index(point && point.line) + ':' + index(point && point.column)\n}\n\n/**\n * @param {Position | PositionLike | null | undefined} pos\n * @returns {string}\n */\nfunction position(pos) {\n  return point(pos && pos.start) + '-' + point(pos && pos.end)\n}\n\n/**\n * @param {number | null | undefined} value\n * @returns {number}\n */\nfunction index(value) {\n  return value && typeof value === 'number' ? value : 1\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/unist-util-stringify-position/lib/index.js\n"));

/***/ })

}]);